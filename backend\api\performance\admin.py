"""
履职管理Django Admin配置

提供以下管理界面：
1. PerformanceRecordAdmin - 履职记录管理
2. PerformanceAttachmentAdmin - 附件管理

设计原则：
- 简洁明了的界面布局
- 便于搜索和筛选
- 只读展示关键信息
- 安全的操作权限
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import PerformanceRecord, PerformanceAttachment


@admin.register(PerformanceRecord)
class PerformanceRecordAdmin(admin.ModelAdmin):
    """
    履职记录管理界面
    """
    
    # 列表页显示字段
    list_display = [
        'id', 'representative_name', 'performance_date', 'performance_type_display',
        'content_preview', 'activity_location', 'performance_status_display',
        'has_attachments', 'attachment_count_display', 'created_at'
    ]
    
    # 列表页筛选器
    list_filter = [
        'performance_type', 'performance_status', 'has_attachments',
        'performance_date', 'created_at', 'representative__level'
    ]
    
    # 搜索字段
    search_fields = [
        'representative__name', 'performance_content', 'activity_location',
        'detailed_description'
    ]
    
    # 只读字段
    readonly_fields = [
        'id', 'representative', 'has_attachments', 'created_at', 'updated_at',
        'attachment_list_display'
    ]
    
    # 字段分组
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'representative', 'performance_date', 'performance_type')
        }),
        ('履职内容', {
            'fields': ('performance_content', 'detailed_description', 'activity_location')
        }),
        ('状态信息', {
            'fields': ('performance_status', 'has_attachments')
        }),
        ('附件信息', {
            'fields': ('attachment_list_display',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    # 分页设置
    list_per_page = 20
    
    # 排序
    ordering = ['-performance_date', '-created_at']
    
    # 日期筛选
    date_hierarchy = 'performance_date'
    
    def representative_name(self, obj):
        """显示代表姓名"""
        return obj.representative.name
    representative_name.short_description = '代表姓名'
    representative_name.admin_order_field = 'representative__name'
    
    def performance_type_display(self, obj):
        """显示履职类型"""
        return obj.get_performance_type_display()
    performance_type_display.short_description = '履职类型'
    performance_type_display.admin_order_field = 'performance_type'
    
    def performance_status_display(self, obj):
        """显示履职状态（带颜色）"""
        status_colors = {
            '已完成': 'green',
            '进行中': 'orange',
            '已暂停': 'red'
        }
        color = status_colors.get(obj.performance_status, 'black')
        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            obj.get_performance_status_display()
        )
    performance_status_display.short_description = '履职状态'
    performance_status_display.admin_order_field = 'performance_status'
    
    def content_preview(self, obj):
        """内容预览"""
        content = obj.performance_content
        if len(content) > 50:
            return content[:50] + '...'
        return content
    content_preview.short_description = '内容预览'
    
    def attachment_count_display(self, obj):
        """附件数量显示"""
        count = obj.get_attachment_count()
        if count > 0:
            url = reverse('admin:performance_performanceattachment_changelist')
            return format_html(
                '<a href="{}?performance_record__id__exact={}">{} 个</a>',
                url, obj.id, count
            )
        return '无'
    attachment_count_display.short_description = '附件数量'
    
    def attachment_list_display(self, obj):
        """附件列表显示"""
        attachments = obj.attachments.all()
        if not attachments:
            return '无附件'
        
        html_parts = []
        for attachment in attachments:
            file_type_icons = {
                'image': '🖼️',
                'audio': '🎵',
                'video': '🎬',
                'document': '📄'
            }
            icon = file_type_icons.get(attachment.file_type, '📎')
            
            html_parts.append(
                f'{icon} {attachment.original_filename} '
                f'({attachment.get_file_size_display()})'
            )
        
        return mark_safe('<br>'.join(html_parts))
    attachment_list_display.short_description = '附件列表'
    
    def has_add_permission(self, request):
        """禁止在管理界面创建履职记录"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """只允许查看，不允许编辑"""
        return request.user.is_superuser
    
    def has_delete_permission(self, request, obj=None):
        """只允许超级管理员删除"""
        return request.user.is_superuser


@admin.register(PerformanceAttachment)
class PerformanceAttachmentAdmin(admin.ModelAdmin):
    """
    履职记录附件管理界面
    """
    
    # 列表页显示字段
    list_display = [
        'id', 'performance_record_link', 'representative_name', 'file_type_display',
        'original_filename', 'file_size_display', 'upload_status_display',
        'thumbnail_preview', 'duration_display', 'created_at'
    ]
    
    # 列表页筛选器
    list_filter = [
        'file_type', 'upload_status', 'created_at',
        'performance_record__performance_type',
        'performance_record__representative__name'
    ]
    
    # 搜索字段
    search_fields = [
        'original_filename', 'performance_record__representative__name',
        'performance_record__performance_content'
    ]
    
    # 只读字段
    readonly_fields = [
        'id', 'performance_record', 'file_hash', 'file_path', 'stored_filename',
        'thumbnail_path', 'duration', 'width', 'height', 'created_at', 'updated_at',
        'file_info_display', 'thumbnail_preview'
    ]
    
    # 字段分组
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'performance_record', 'file_type', 'original_filename')
        }),
        ('文件信息', {
            'fields': ('file_size', 'mime_type', 'file_hash', 'upload_status')
        }),
        ('存储信息', {
            'fields': ('stored_filename', 'file_path', 'thumbnail_path'),
            'classes': ('collapse',)
        }),
        ('媒体信息', {
            'fields': ('duration', 'width', 'height', 'thumbnail_preview'),
            'classes': ('collapse',)
        }),
        ('排序信息', {
            'fields': ('sort_order',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    # 分页设置
    list_per_page = 25
    
    # 排序
    ordering = ['-created_at']
    
    # 日期筛选
    date_hierarchy = 'created_at'
    
    def performance_record_link(self, obj):
        """履职记录链接"""
        url = reverse('admin:performance_performancerecord_change', 
                     args=[obj.performance_record.id])
        return format_html(
            '<a href="{}">{}</a>',
            url,
            f"#{obj.performance_record.id}"
        )
    performance_record_link.short_description = '履职记录'
    performance_record_link.admin_order_field = 'performance_record'
    
    def representative_name(self, obj):
        """代表姓名"""
        return obj.performance_record.representative.name
    representative_name.short_description = '代表'
    representative_name.admin_order_field = 'performance_record__representative__name'
    
    def file_type_display(self, obj):
        """文件类型显示（带图标）"""
        type_icons = {
            'image': '🖼️ 图片',
            'audio': '🎵 音频',
            'video': '🎬 视频',
            'document': '📄 文档'
        }
        return type_icons.get(obj.file_type, obj.get_file_type_display())
    file_type_display.short_description = '文件类型'
    file_type_display.admin_order_field = 'file_type'
    
    def file_size_display(self, obj):
        """文件大小显示"""
        return obj.get_file_size_display()
    file_size_display.short_description = '文件大小'
    file_size_display.admin_order_field = 'file_size'
    
    def upload_status_display(self, obj):
        """上传状态显示（带颜色）"""
        status_colors = {
            'uploaded': 'green',
            'uploading': 'orange',
            'processing': 'blue',
            'failed': 'red'
        }
        color = status_colors.get(obj.upload_status, 'black')
        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            obj.get_upload_status_display()
        )
    upload_status_display.short_description = '上传状态'
    upload_status_display.admin_order_field = 'upload_status'
    
    def duration_display(self, obj):
        """时长显示"""
        return obj.get_duration_display() or '-'
    duration_display.short_description = '时长'
    
    def thumbnail_preview(self, obj):
        """缩略图预览"""
        if obj.thumbnail_path and obj.file_type in ['image', 'video']:
            thumbnail_url = obj.get_thumbnail_url()
            if thumbnail_url:
                return format_html(
                    '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                    thumbnail_url
                )
        return '无预览'
    thumbnail_preview.short_description = '缩略图预览'
    
    def file_info_display(self, obj):
        """文件信息展示"""
        info_parts = [
            f"原始文件名: {obj.original_filename}",
            f"存储文件名: {obj.stored_filename}",
            f"文件大小: {obj.get_file_size_display()}",
            f"MIME类型: {obj.mime_type}",
            f"文件哈希: {obj.file_hash[:16]}..."
        ]
        
        if obj.width and obj.height:
            info_parts.append(f"尺寸: {obj.width}x{obj.height}")
        
        if obj.duration:
            info_parts.append(f"时长: {obj.get_duration_display()}")
        
        return mark_safe('<br>'.join(info_parts))
    file_info_display.short_description = '文件详细信息'
    
    def has_add_permission(self, request):
        """禁止在管理界面创建附件"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """只允许查看和少量编辑"""
        return request.user.is_staff
    
    def has_delete_permission(self, request, obj=None):
        """只允许超级管理员删除"""
        return request.user.is_superuser
    
    def get_readonly_fields(self, request, obj=None):
        """根据用户权限设置只读字段"""
        readonly_fields = list(self.readonly_fields)
        
        # 非超级用户只能编辑排序字段
        if not request.user.is_superuser and obj:
            readonly_fields.extend(['sort_order'])
        
        return readonly_fields


# 自定义管理界面标题
admin.site.site_header = 'NPC系统管理后台'
admin.site.site_title = 'NPC管理'
admin.site.index_title = '履职管理系统'
