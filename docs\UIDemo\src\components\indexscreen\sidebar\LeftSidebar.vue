<template>
  <aside class="left-sidebar">
    <LeftBlock1 />
    <LeftBlock2 />
    <LeftBlock3 />
  </aside>
</template>

<script setup>
// 导入独立的区块组件
import LeftBlock1 from '../blocks/LeftBlock1.vue'
import LeftBlock2 from '../blocks/LeftBlock2.vue'
import LeftBlock3 from '../blocks/LeftBlock3.vue'
</script>

<style scoped>
/* 左侧边栏样式 */
.left-sidebar {
  display: flex;
  flex-direction: column;
  width: 28vw;
  gap: 8px;
}

</style> 