<template>
  <div class="ai-report-renderer">
    <!-- 报告头部 -->
    <el-card class="report-header" shadow="never">
      <div class="header-content">
        <div class="user-avatar">
          <el-avatar
            :size="80"
            :src="representativeAvatar"
            style="background-color: #c62d2d; font-size: 32px; color: white;"
          >
            {{ representativeName?.[0] || '代' }}
          </el-avatar>
        </div>
        <div class="header-info">
          <h1>{{ representativeName }} 年度履职AI分析报告</h1>
          <p class="subtitle">智能分析履职表现，洞察发展趋势</p>
          <div class="representative-details">
            <div class="detail-item">
              <span class="label">代表姓名：</span>
              <span class="value">{{ representativeName }}</span>
            </div>
            <div class="detail-item">
              <span class="label">代表层级：</span>
              <span class="value">{{ representativeLevel }}</span>
            </div>
            <div class="detail-item">
              <span class="label">现任职务：</span>
              <span class="value">{{ representativePosition }}</span>
            </div>
            <div class="detail-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ representativePhone }}</span>
            </div>
          </div>
          <p class="generate-time">生成时间：{{ currentTime }}</p>
        </div>
      </div>
    </el-card>

    <!-- 核心指标 -->
    <el-card class="metrics-section" shadow="hover" v-if="data.核心指标">
      <template #header>
        <h3><el-icon><TrendCharts /></el-icon> 核心指标</h3>
      </template>
      <div class="metrics-grid">
        <div 
          v-for="(metric, index) in data.核心指标" 
          :key="index"
          class="metric-card"
        >
          <div class="metric-icon" :style="{ backgroundColor: getMetricColor(index) + '20' }">
            <el-icon :style="{ color: getMetricColor(index) }" :size="24">
              <component :is="getMetricIcon(index)" />
            </el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value" :style="{ color: getMetricColor(index) }">
              {{ getMetricValue(metric) }}
            </div>
            <div class="metric-title">{{ getMetricTitle(metric) }}</div>
            <div class="metric-description">{{ getMetricDescription(metric) }}</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 履职活动分析 -->
    <el-card class="activity-section" shadow="hover" v-if="data.履职活动分析">
      <template #header>
        <h3><el-icon><DataLine /></el-icon> 履职活动分析</h3>
      </template>
      <div class="activity-content">
        <div class="activity-chart" ref="activityChart"></div>
        <div class="activity-list">
          <div 
            v-for="(activity, index) in data.履职活动分析" 
            :key="index"
            class="activity-item"
          >
            <div class="activity-label">{{ getActivityLabel(activity) }}</div>
            <div class="activity-bar">
              <div 
                class="activity-progress" 
                :style="{ 
                  width: getActivityPercentage(activity) + '%',
                  backgroundColor: getActivityColor(index)
                }"
              ></div>
              <span class="activity-value">{{ getActivityValue(activity) }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 履职月度统计 -->
    <el-card class="monthly-section" shadow="hover" v-if="data.履职月度统计">
      <template #header>
        <h3><el-icon><Calendar /></el-icon> 履职月度统计</h3>
      </template>
      <div class="monthly-chart" ref="monthlyChart"></div>
    </el-card>

    <!-- 关注领域 -->
    <el-card class="focus-section" shadow="hover" v-if="data.关注领域">
      <template #header>
        <h3><el-icon><Collection /></el-icon> 关注领域</h3>
      </template>
      <div class="focus-areas">
        <span 
          v-for="(area, index) in data.关注领域" 
          :key="index"
          class="focus-tag"
          :style="{ 
            backgroundColor: getFocusColor(index),
            fontSize: getFocusSize(index) + 'px'
          }"
        >
          {{ area }}
        </span>
      </div>
    </el-card>

    <!-- 履职亮点 -->
    <el-card class="highlights-section" shadow="hover" v-if="data.履职亮点">
      <template #header>
        <h3><el-icon><Trophy /></el-icon> 履职亮点</h3>
      </template>
      <div class="highlights-grid">
        <div 
          v-for="(highlight, index) in data.履职亮点" 
          :key="index"
          class="highlight-card"
        >
          <div class="highlight-content">
            <div class="highlight-icon">
              <el-icon :size="32" style="color: #c62d2d;">
                <component :is="getHighlightIcon(index)" />
              </el-icon>
            </div>
            <div class="highlight-text">
              <h4>{{ getHighlightTitle(highlight) }}</h4>
              <p>{{ getHighlightContent(highlight) }}</p>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- AI智能总结 -->
    <el-card class="summary-section" shadow="hover" v-if="data.ai智能总结">
      <template #header>
        <h3><el-icon><Monitor /></el-icon> AI智能总结</h3>
      </template>
      <div class="summary-content">
        <div class="summary-text">
          <div class="summary-item" v-if="data.ai智能总结.年度表现评价">
            <h4>年度表现评价</h4>
            <p>{{ data.ai智能总结.年度表现评价 }}</p>
          </div>
          
          <div class="summary-item" v-if="data.ai智能总结.突出成就">
            <h4>突出成就</h4>
            <ul class="achievement-list">
              <li v-for="(achievement, index) in data.ai智能总结.突出成就" :key="index">
                <el-icon><Star /></el-icon>
                {{ achievement }}
              </li>
            </ul>
          </div>
          
          <div class="summary-item" v-if="data.ai智能总结.改进建议">
            <h4>改进建议</h4>
            <ul class="suggestion-list">
              <li v-for="(suggestion, index) in data.ai智能总结.改进建议" :key="index">
                <el-icon><Operation /></el-icon>
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </div>
        
        <div class="summary-decoration">
          <el-icon :size="100" style="color: #f0f0f0;">
            <Monitor />
          </el-icon>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { 
  TrendCharts, 
  Trophy, 
  DataLine, 
  Collection, 
  Monitor,
  Calendar,
  Star,
  Operation,
  User,
  ChatDotSquare,
  Edit,
  Document
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  representativeInfo: {
    type: Object,
    default: () => ({})
  },
  generatedTime: {
    type: String,
    default: null
  }
})

const userStore = useUserStore()
const activityChart = ref(null)
const monthlyChart = ref(null)

// 生成时间 - 优先使用传入的时间，否则使用当前时间
const currentTime = computed(() => {
  if (props.generatedTime) {
    return props.generatedTime
  }
  return new Date().toLocaleString('zh-CN')
})

// 代表信息 - 优先使用props中的代表信息，如果没有则使用当前用户信息
const representativeName = computed(() => {
  return props.representativeInfo.representative_name || 
         props.representativeInfo.name || 
         userStore.userInfo.representative_info?.name ||
         userStore.userName || 
         '代表'
})

const representativeLevel = computed(() => {
  return props.representativeInfo.level || 
         userStore.userInfo.representative_info?.level || 
         '区级人大代表'
})



const representativePhone = computed(() => {
  return props.representativeInfo.mobile_phone || 
         props.representativeInfo.phone || 
         userStore.userInfo.representative_info?.mobile_phone || 
         '未设置'
})

const representativePosition = computed(() => {
  return props.representativeInfo.current_position ||
         props.representativeInfo.position ||
         userStore.userInfo.representative_info?.current_position ||
         '未设置'
})

// 代表头像
const representativeAvatar = computed(() => {
  return props.representativeInfo.avatar ||
         userStore.userInfo.representative_info?.avatar ||
         null
})

// 中国红主题颜色配置
const colors = ['#c62d2d', '#d73527', '#e04142', '#8b1e1e', '#b02626', '#dc143c', '#a0282f', '#cd5c5c']
const focusColors = ['#c62d2d', '#d73527', '#e04142', '#8b1e1e', '#b02626', '#dc143c']

// 饼图专用颜色配置 - 增加对比度便于区分
const pieChartColors = [
  '#c62d2d',  // 深红
  '#f56565',  // 浅红
  '#8b1e1e',  // 更深红
  '#ff7875',  // 粉红
  '#b02626',  // 中红
  '#ffa39e',  // 浅粉红
  '#722f37',  // 深棕红
  '#ffccc7'   // 极浅粉红
]

// 获取指标相关信息
const getMetricColor = (index) => colors[index] || '#c62d2d'

const getMetricIcon = (index) => {
  const icons = [User, ChatDotSquare, TrendCharts, DataLine]
  return icons[index] || TrendCharts
}

const getMetricValue = (metric) => {
  const key = Object.keys(metric)[0]
  return metric[key]
}

const getMetricTitle = (metric) => {
  const key = Object.keys(metric)[0]
  return key
}

const getMetricDescription = (metric) => {
  const key = Object.keys(metric)[0]
  const value = metric[key]
  const descriptions = {
    '履职活动': `本年度参与履职活动${value}次，体现了积极的参政议政态度`,
    '意见建议': `共提交意见建议${value}条，为民生发展建言献策`
  }
  return descriptions[key] || `数量: ${value}`
}

// 获取活动相关信息
const getActivityLabel = (activity) => {
  return Object.keys(activity)[0]
}

const getActivityValue = (activity) => {
  return Object.values(activity)[0]
}

const getActivityPercentage = (activity) => {
  const value = Object.values(activity)[0]
  const maxValue = Math.max(...props.data.履职活动分析.map(item => Object.values(item)[0]))
  return maxValue > 0 ? (value / maxValue * 100) : 0
}

const getActivityColor = (index) => colors[index % colors.length]

// 获取关注领域相关信息
const getFocusColor = (index) => focusColors[index % focusColors.length]

const getFocusSize = (index) => {
  // 根据位置调整字体大小，模拟词云效果
  const sizes = [18, 16, 20, 14, 17, 15]
  return sizes[index % sizes.length]
}

// 获取履职亮点相关信息
const getHighlightIcon = (index) => {
  const icons = [Edit, Trophy, Document, Star]
  return icons[index % icons.length]
}

const getHighlightTitle = (highlight) => {
  const key = Object.keys(highlight)[0]
  const titleMap = {
    '走访记录': '群众走访',
    '重点提案': '重点提案',
    '高频建议': '热点关注'
  }
  return titleMap[key] || key
}

const getHighlightContent = (highlight) => {
  return Object.values(highlight)[0]
}

// 初始化图表
onMounted(() => {
  nextTick(() => {
    initActivityChart()
    initMonthlyChart()
  })
})

const initActivityChart = () => {
  if (!activityChart.value || !props.data.履职活动分析) return
  
  const chart = echarts.init(activityChart.value)
  
  const data = props.data.履职活动分析.map(item => ({
    name: Object.keys(item)[0],
    value: Object.values(item)[0]
  }))
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12
      }
    },
    series: [{
      name: '履职活动',
      type: 'pie',
      radius: ['50%', '70%'],
      center: ['60%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '16',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: data,
      color: pieChartColors
    }]
  }
  
  chart.setOption(option)
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

const initMonthlyChart = () => {
  if (!monthlyChart.value || !props.data.履职月度统计) return
  
  const chart = echarts.init(monthlyChart.value)
  
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  const data = months.map((month, index) => {
    const monthKey = (index + 1).toString()
    return props.data.履职月度统计[monthKey] || 0
  })
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months,
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '履职数量',
      type: 'bar',
      data: data,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#c62d2d' },
          { offset: 1, color: '#8b1e1e' }
        ])
      }
    }]
  }
  
  chart.setOption(option)
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}
</script>

<style scoped>
.ai-report-renderer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 100%);
  border-radius: 20px;
}

/* 报告头部 */
.report-header {
  margin-bottom: 30px;
  background: white;
  color: #c62d2d !important;
  border-radius: 16px;
  border: 2px solid #c62d2d;
}

/* 强制确保header-content内的所有文本都是中国红色 */
.report-header .header-content,
.report-header .header-content *,
.report-header .header-info,
.report-header .header-info * {
  color: #c62d2d !important;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 30px;
  padding: 20px 0;
}

.header-info h1 {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 10px 0;
  color: #c62d2d !important;
}

.subtitle {
  font-size: 16px;
  margin: 0 0 5px 0;
  opacity: 0.9;
  color: #c62d2d !important;
}

.representative-details {
  margin: 15px 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 20px;
  font-size: 14px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item .label {
  color: rgba(198, 45, 45, 0.8) !important;
  margin-right: 8px;
  font-weight: 500;
}

.detail-item .value {
  color: #c62d2d !important;
  font-weight: bold;
}

.generate-time {
  font-size: 14px;
  margin: 0;
  opacity: 0.8;
  color: #c62d2d !important;
}

/* 核心指标 */
.metrics-section {
  margin-bottom: 30px;
  border-radius: 16px;
  border: 2px solid #fee2e2;
}

.metrics-section .el-card__header {
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
  border-bottom: 2px solid #fee2e2;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border: 2px solid #fee2e2;
  border-radius: 12px;
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 32px rgba(198, 45, 45, 0.15);
  border-color: #c62d2d;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.metric-value {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 5px;
}

.metric-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.metric-description {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 履职活动分析 */
.activity-section {
  margin-bottom: 30px;
  border-radius: 16px;
  border: 2px solid #fee2e2;
}

.activity-section .el-card__header {
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
  border-bottom: 2px solid #fee2e2;
}

.activity-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  align-items: center;
}

.activity-chart {
  height: 300px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.activity-label {
  min-width: 100px;
  font-size: 14px;
  color: #333;
}

.activity-bar {
  flex: 1;
  height: 20px;
  background-color: #fee2e2;
  border-radius: 10px;
  position: relative;
  display: flex;
  align-items: center;
}

.activity-progress {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.activity-value {
  position: absolute;
  right: 8px;
  font-size: 12px;
  color: #666;
  font-weight: bold;
}

/* 月度统计 */
.monthly-section {
  margin-bottom: 30px;
  border-radius: 16px;
  border: 2px solid #fee2e2;
}

.monthly-section .el-card__header {
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
  border-bottom: 2px solid #fee2e2;
}

.monthly-chart {
  height: 300px;
}

/* 关注领域 */
.focus-section {
  margin-bottom: 30px;
  border-radius: 16px;
  border: 2px solid #fee2e2;
}

.focus-section .el-card__header {
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
  border-bottom: 2px solid #fee2e2;
}

.focus-areas {
  padding: 30px;
  text-align: center;
  line-height: 2.5;
}

.focus-tag {
  display: inline-block;
  margin: 8px 12px;
  padding: 10px 20px;
  border-radius: 25px;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: default;
}

.focus-tag:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* 履职亮点 */
.highlights-section {
  margin-bottom: 30px;
  border-radius: 16px;
  border: 2px solid #fee2e2;
}

.highlights-section .el-card__header {
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
  border-bottom: 2px solid #fee2e2;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.highlight-card {
  border-left: 4px solid #c62d2d;
  padding: 20px;
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
  border-radius: 12px;
  border: 2px solid #fee2e2;
  border-left: 4px solid #c62d2d;
  transition: all 0.3s ease;
}

.highlight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(198, 45, 45, 0.15);
  border-color: #c62d2d;
  border-left-color: #c62d2d;
}

.highlight-content {
  display: flex;
  gap: 20px;
}

.highlight-icon {
  flex-shrink: 0;
}

.highlight-text h4 {
  color: #c62d2d;
  font-size: 18px;
  margin: 0 0 10px 0;
}

.highlight-text p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* AI智能总结 */
.summary-section {
  margin-bottom: 30px;
  border-radius: 16px;
  border: 2px solid #fee2e2;
}

.summary-section .el-card__header {
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
  border-bottom: 2px solid #fee2e2;
}

.summary-content {
  display: flex;
  gap: 30px;
  position: relative;
}

.summary-text {
  flex: 1;
}

.summary-item {
  margin-bottom: 30px;
}

.summary-item h4 {
  color: #c62d2d;
  font-size: 18px;
  margin: 0 0 15px 0;
  font-weight: bold;
}

.summary-item p {
  color: #333;
  line-height: 1.8;
  margin-bottom: 15px;
  font-size: 15px;
}

.achievement-list,
.suggestion-list {
  padding-left: 0;
  margin: 0;
  list-style: none;
}

.achievement-list li,
.suggestion-list li {
  color: #555;
  line-height: 1.8;
  margin-bottom: 10px;
  font-size: 14px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.achievement-list li .el-icon {
  color: #c62d2d;
  margin-top: 2px;
}

.suggestion-list li .el-icon {
  color: #d73527;
  margin-top: 2px;
}

.summary-decoration {
  position: absolute;
  right: 20px;
  top: 20px;
}

/* 统一的卡片标题样式 */
:deep(.el-card__header) {
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
  border-bottom: 2px solid #fee2e2;
  border-radius: 16px 16px 0 0;
}

:deep(.el-card__header h3) {
  color: #c62d2d;
  font-weight: bold;
  font-size: 18px;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-card__body) {
  background: white;
  border-radius: 0 0 16px 16px;
}

/* PDF导出优化 */
@media print {
  .ai-report-renderer {
    background: white !important;
    padding: 10px !important;
    margin: 0 !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    max-width: none !important;
    width: 100% !important;
  }
  
  .el-card {
    box-shadow: none !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 8px !important;
    page-break-inside: avoid;
    margin-bottom: 15px !important;
  }
  
  .el-card__body {
    padding: 15px !important;
  }
  
  .activity-chart,
  .monthly-chart {
    page-break-inside: avoid;
    width: 100% !important;
    min-height: 200px !important;
  }
  
  .highlight-card {
    page-break-inside: avoid;
  }
  
  .summary-item {
    page-break-inside: avoid;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 15px !important;
  }
  
  .activity-content {
    display: block !important;
  }
  
  .activity-chart {
    margin-bottom: 15px !important;
  }
  
  .highlights-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-report-renderer {
    padding: 15px;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .representative-details {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .activity-content {
    grid-template-columns: 1fr;
  }
  
  .highlights-grid {
    grid-template-columns: 1fr;
  }
  
  .summary-content {
    flex-direction: column;
  }
  
  .summary-decoration {
    position: static;
    text-align: center;
    margin-top: 20px;
  }
}
</style> 