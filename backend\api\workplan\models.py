"""
工作计划管理数据模型

包含以下模型：
1. WorkPlan - 工作计划主表

设计原则：
- 基于简化的6个核心字段：标题、开始时间、结束时间、内容、状态、提醒天数
- 支持登录时弹窗提醒功能
- 确保数据完整性和一致性
"""

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()


class WorkPlan(models.Model):
    """
    工作计划模型
    对应数据库设计中的work_plans表
    """
    
    # 计划状态选择
    STATUS_CHOICES = [
        ('planned', '计划中'),
        ('in_progress', '进行中'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
    ]
    
    # 主键ID（BigInt自增）
    id = models.BigAutoField(
        primary_key=True,
        verbose_name='工作计划ID'
    )
    
    # 关联工作人员（外键）
    staff_member = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name='负责工作人员',
        help_text='计划负责的工作人员',
        limit_choices_to={'is_staff': True}  # 只允许选择工作人员
    )
    
    # 计划标题
    title = models.CharField(
        max_length=200,
        verbose_name='计划标题',
        help_text='工作计划的标题，最多200字符'
    )
    
    # 开始时间
    start_date = models.DateField(
        verbose_name='开始时间',
        help_text='计划开始日期'
    )
    
    # 结束时间
    end_date = models.DateField(
        verbose_name='结束时间',
        help_text='计划结束日期'
    )
    
    # 计划内容
    content = models.TextField(
        verbose_name='计划内容',
        help_text='详细的计划内容描述'
    )
    
    # 计划状态
    status = models.CharField(
        max_length=50,
        choices=STATUS_CHOICES,
        default='planned',
        verbose_name='计划状态',
        help_text='当前计划的执行状态'
    )
    
    # 提醒天数
    reminder_days = models.IntegerField(
        default=3,
        validators=[MinValueValidator(0), MaxValueValidator(30)],
        verbose_name='提醒天数',
        help_text='到期前多少天提醒，用于登录弹窗提醒'
    )
    
    # 创建时间
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )
    
    # 更新时间
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    class Meta:
        db_table = 'work_plans'
        verbose_name = '工作计划'
        verbose_name_plural = '工作计划'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['staff_member']),
            models.Index(fields=['title']),
            models.Index(fields=['start_date']),
            models.Index(fields=['end_date']),
            models.Index(fields=['status']),
            models.Index(fields=['end_date', 'status']),  # 用于提醒查询优化
        ]
    
    def __str__(self):
        return f"{self.title} ({self.get_status_display()})"
    
    def clean(self):
        """模型验证"""
        from django.core.exceptions import ValidationError
        
        if self.start_date and self.end_date:
            if self.end_date < self.start_date:
                raise ValidationError('结束时间不能早于开始时间')
    
    def save(self, *args, **kwargs):
        """保存前验证"""
        self.clean()
        super().save(*args, **kwargs)
    
    @property
    def days_until_end(self):
        """距离结束还有多少天"""
        if self.end_date:
            delta = self.end_date - timezone.now().date()
            return delta.days
        return None
    
    @property
    def should_remind(self):
        """是否应该提醒"""
        if self.status in ['planned', 'in_progress']:
            days_left = self.days_until_end
            if days_left is not None and days_left <= self.reminder_days and days_left >= 0:
                return True
        return False
    
    @property
    def is_overdue(self):
        """是否已逾期"""
        if self.status in ['planned', 'in_progress']:
            return self.days_until_end is not None and self.days_until_end < 0
        return False
    
    @classmethod
    def get_reminder_plans(cls, staff_member=None):
        """获取需要提醒的计划"""
        from django.db.models import Q
        from datetime import date, timedelta
        
        today = date.today()
        
        # 基础查询条件：状态为计划中或进行中
        base_query = Q(status__in=['planned', 'in_progress'])
        
        # 如果指定了工作人员，添加过滤条件
        if staff_member:
            base_query &= Q(staff_member=staff_member)
        
        # 构造提醒条件：当前日期距离结束日期小于等于提醒天数
        reminder_conditions = []
        for i in range(31):  # 支持最多30天提醒
            reminder_date = today + timedelta(days=i)
            reminder_conditions.append(
                Q(end_date=reminder_date, reminder_days__gte=i)
            )
        
        if reminder_conditions:
            reminder_query = reminder_conditions[0]
            for condition in reminder_conditions[1:]:
                reminder_query |= condition
            base_query &= reminder_query
        
        return cls.objects.filter(base_query).select_related('staff_member__staffmember')
    
    @classmethod
    def get_overdue_plans(cls, staff_member=None):
        """获取逾期的计划"""
        from django.db.models import Q
        from datetime import date
        
        today = date.today()
        
        # 基础查询条件
        base_query = Q(
            status__in=['planned', 'in_progress'],
            end_date__lt=today
        )
        
        # 如果指定了工作人员，添加过滤条件
        if staff_member:
            base_query &= Q(staff_member=staff_member)
        
        return cls.objects.filter(base_query).select_related('staff_member__staffmember')
