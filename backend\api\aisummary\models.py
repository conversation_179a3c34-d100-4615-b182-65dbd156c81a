"""
代表AI总结数据模型

包含以下模型：
1. RepresentativeAISummary - 代表AI总结模型

设计原则：
- 遵循现有应用的代码风格和结构
- 支持JSON格式存储AI分析结果
- 完整的状态管理和错误处理
- 考虑性能优化和数据完整性
"""

import json
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from api.users.models import Representative


class RepresentativeAISummary(models.Model):
    """
    代表AI总结模型
    对应数据库设计中的representative_ai_summaries表
    """
    
    # 生成状态选择
    STATUS_CHOICES = [
        ('pending', '待生成'),
        ('generating', '生成中'),
        ('completed', '已完成'),
        ('failed', '生成失败'),
    ]
    
    # 主键ID
    id = models.BigAutoField(
        primary_key=True,
        verbose_name='AI总结ID'
    )
    
    # 关联代表ID（外键关联）
    representative = models.ForeignKey(
        Representative,
        on_delete=models.CASCADE,
        verbose_name='关联代表',
        help_text='生成AI总结的人大代表'
    )
    
    # 分析年份
    analysis_year = models.PositiveIntegerField(
        validators=[
            MinValueValidator(2020),
            MaxValueValidator(2050)
        ],
        verbose_name='分析年份',
        help_text='生成AI总结的年份，范围：2020-2050'
    )
    
    # 数据来源概要（JSON格式）
    source_data_summary = models.JSONField(
        default=dict,
        verbose_name='数据来源概要',
        help_text='记录用于AI分析的数据来源统计信息，JSON格式'
    )
    
    # AI分析结果（JSON格式）
    ai_result = models.JSONField(
        default=dict,
        verbose_name='AI分析结果',
        help_text='AI生成的分析结果，JSON格式，匹配前端需要的数据结构'
    )
    
    # 生成状态
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='生成状态',
        help_text='AI总结的生成状态'
    )
    
    # 生成耗时（秒）
    generation_duration = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='生成耗时',
        help_text='AI生成总结的耗时，单位：秒'
    )
    
    # 错误信息
    error_message = models.TextField(
        null=True,
        blank=True,
        verbose_name='错误信息',
        help_text='生成失败时的错误信息'
    )
    
    # 时间戳字段
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='完成时间',
        help_text='AI总结生成完成的时间'
    )
    
    class Meta:
        db_table = 'representative_ai_summaries'
        verbose_name = '代表AI总结'
        verbose_name_plural = '代表AI总结'
        
        # 复合唯一约束：每个代表每年只能有一条记录
        unique_together = ['representative', 'analysis_year']
        
        # 索引优化
        indexes = [
            models.Index(fields=['representative', 'analysis_year']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]
        
        # 默认排序
        ordering = ['-analysis_year', '-created_at']
    
    def __str__(self):
        return f"{self.representative.name} - {self.analysis_year}年AI总结"
    
    @property
    def status_display(self):
        """获取状态显示名称"""
        return self.get_status_display()
    
    @property
    def representative_name(self):
        """获取代表姓名"""
        return self.representative.name if self.representative else ''
    
    @property
    def is_completed(self):
        """检查是否已完成"""
        return self.status == 'completed'
    
    @property
    def is_failed(self):
        """检查是否生成失败"""
        return self.status == 'failed'
    
    @property
    def is_generating(self):
        """检查是否正在生成"""
        return self.status == 'generating'
    
    @property
    def has_ai_result(self):
        """检查是否有AI结果"""
        return bool(self.ai_result and isinstance(self.ai_result, dict) and self.ai_result)
    
    def get_source_data_stats(self):
        """获取数据来源统计信息"""
        if not self.source_data_summary:
            return {}
        
        try:
            return self.source_data_summary
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def get_ai_result_data(self):
        """获取AI分析结果数据"""
        if not self.ai_result:
            return {}
        
        try:
            return self.ai_result
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def mark_as_generating(self):
        """标记为生成中"""
        self.status = 'generating'
        self.error_message = None
        self.save(update_fields=['status', 'error_message', 'updated_at'])
    
    def mark_as_completed(self, ai_result_data, generation_duration=None):
        """标记为已完成"""
        completion_time = timezone.now()
        
        self.status = 'completed'
        self.ai_result = ai_result_data
        self.completed_at = completion_time
        self.updated_at = completion_time  # 确保时间一致
        self.error_message = None
        
        if generation_duration is not None:
            self.generation_duration = generation_duration
        
        self.save(update_fields=[
            'status', 'ai_result', 'completed_at', 'error_message',
            'generation_duration', 'updated_at'
        ])
    
    def mark_as_failed(self, error_message):
        """标记为生成失败"""
        self.status = 'failed'
        self.error_message = error_message
        self.save(update_fields=['status', 'error_message', 'updated_at'])
    
    def update_source_data_summary(self, source_data):
        """更新数据来源概要"""
        self.source_data_summary = source_data
        self.save(update_fields=['source_data_summary', 'updated_at'])
