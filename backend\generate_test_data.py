#!/usr/bin/env python
"""
群众意见测试数据生成脚本

使用方法：
1. 在backend目录下运行：
   python generate_test_data.py

2. 或者使用Django管理命令：
   python manage.py generate_test_opinions --count 50

这个脚本会生成50条测试群众意见数据，用于测试分页功能
"""

import os
import sys
import django
import random
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'npcsite.settings')
django.setup()

from django.utils import timezone
from api.peopleopnion.models import PeopleOpinion


def generate_test_data(count=50, clear_existing=False):
    """生成测试数据"""
    
    if clear_existing:
        print('🗑️ 清空现有群众意见数据...')
        deleted_count = PeopleOpinion.objects.all().delete()[0]
        print(f'✅ 已清空 {deleted_count} 条现有数据')

    print(f'📝 开始生成 {count} 条群众意见测试数据...')

    # 测试数据模板
    titles = [
        "建议改善社区绿化环境",
        "关于完善公共交通设施的建议",
        "希望增设便民服务窗口",
        "建议优化政务服务流程",
        "关于加强食品安全监管的意见",
        "希望改善学校周边交通状况",
        "建议增加公共停车位",
        "关于完善医疗保障制度的建议",
        "希望加强社区治安管理",
        "建议改善老旧小区基础设施",
        "关于提高环卫服务质量的意见",
        "希望增设文化娱乐设施",
        "建议完善养老服务体系",
        "关于规范市场经营秩序的建议",
        "希望改善农村道路状况",
        "建议加强噪音污染治理",
        "关于优化教育资源配置的意见",
        "希望完善残疾人无障碍设施",
        "建议加强水环境保护",
        "关于改善就业服务的建议",
        "希望增强基层医疗服务能力",
        "建议完善垃圾分类处理",
        "关于加强网络安全管理的意见",
        "希望改善农贸市场环境",
        "建议优化城市规划布局",
        "关于提升政府服务效率的建议",
        "希望加强青少年教育管理",
        "建议完善社会保障制度",
        "关于改善空气质量的意见",
        "希望增设体育健身设施"
    ]

    content_templates = [
        "作为一名普通市民，我认为这个问题需要得到重视和解决。具体来说，我建议相关部门能够",
        "通过日常生活的观察，我发现这个方面确实存在一些不足。希望政府能够采取措施",
        "作为社区居民，我对这个问题深有感触。建议相关部门能够从以下几个方面着手",
        "经过长期关注，我认为这个领域的问题比较突出。希望有关部门能够认真考虑",
        "基于实际需求，我觉得这个建议很有必要。建议政府部门能够统筹规划",
        "从民生角度出发，这个问题确实影响了大家的日常生活。希望能够尽快",
        "作为关心社会发展的公民，我认为这个方面需要进一步完善。建议",
        "通过实地调研，我发现这个问题比较普遍。希望相关部门能够重视并",
        "站在群众立场，我觉得这个建议很有价值。希望政府能够考虑",
        "基于长期观察，我认为这个问题需要系统性解决。建议有关部门"
    ]

    content_endings = [
        "加大投入力度，完善相关配套设施，提高服务质量，让广大市民受益。",
        "制定详细的实施方案，明确时间节点，确保各项措施落到实处。",
        "统筹协调各方资源，形成工作合力，推动问题得到有效解决。",
        "加强监督管理，建立长效机制，确保工作取得实实在在的效果。",
        "深入调研论证，广泛征求意见，制定科学合理的解决方案。",
        "优化工作流程，提高办事效率，为群众提供更加便民的服务。",
        "加强宣传引导，提高公众意识，营造良好的社会氛围。",
        "完善制度建设，规范管理行为，确保各项工作有序开展。",
        "加大执法力度，严格落实责任，维护良好的秩序环境。",
        "创新工作方式，运用现代技术，不断提升管理和服务水平。"
    ]

    names = [
        "张伟", "李娜", "王强", "刘敏", "陈杰", "杨丽", "赵勇", "孙静",
        "周涛", "吴红", "徐斌", "朱华", "胡军", "郭燕", "林峰", "何琳",
        "罗宁", "梁超", "谢明", "唐丽", "韩磊", "冯洁", "于海", "董娟",
        "薛强", "段红", "雷鸣", "方丽", "石磊", "龚静", "邱勇", "史娜",
        "汤杰", "黎华", "熊伟", "秦丽", "顾强", "毛敏", "邹勇", "滕红"
    ]

    contact_prefixes = [
        "138", "139", "136", "137", "135", "134", "150", "151",
        "152", "158", "159", "157", "188", "187", "186", "185",
        "183", "182", "180", "177", "176", "175", "173", "170"
    ]

    created_opinions = []

    for i in range(count):
        # 随机选择标题，避免重复
        title_base = random.choice(titles)
        title = f"{title_base}（编号：{i+1:03d}）"
        
        # 生成内容
        content_start = random.choice(content_templates)
        content_end = random.choice(content_endings)
        content = content_start + content_end + f" 这是第{i+1}条测试意见，用于测试分页和搜索功能。"
        
        # 随机选择姓名
        name = random.choice(names)
        
        # 生成随机手机号
        prefix = random.choice(contact_prefixes)
        suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
        contact_info = prefix + suffix
        
        # 生成随机创建时间（最近30天内）
        days_ago = random.randint(0, 30)
        hours_ago = random.randint(0, 23)
        minutes_ago = random.randint(0, 59)
        created_at = timezone.now() - timedelta(
            days=days_ago,
            hours=hours_ago,
            minutes=minutes_ago
        )
        
        # 创建群众意见
        opinion = PeopleOpinion.objects.create(
            title=title,
            content=content,
            name=name,
            contact_info=contact_info,
            created_at=created_at
        )
        
        created_opinions.append(opinion)
        
        # 显示进度
        if (i + 1) % 10 == 0:
            print(f'✅ 已生成 {i + 1} 条数据...')

    print(f'🎉 成功生成 {count} 条群众意见测试数据！')
    
    # 显示数据概览
    total_opinions = PeopleOpinion.objects.count()
    today_opinions = PeopleOpinion.objects.filter(
        created_at__date=timezone.now().date()
    ).count()
    
    print('\n📊 数据概览：')
    print(f'  总意见数：{total_opinions}')
    print(f'  今日意见：{today_opinions}')
    print(f'  最新意见：{created_opinions[-1].title}')
    print(f'  最早意见：{created_opinions[0].title}')
    
    print('\n💡 测试建议：')
    print('  1. 访问 http://localhost:3000/staff/people-opinion-list')
    print('  2. 测试搜索功能（搜索"交通"、"环境"、"编号"等关键词）')
    print('  3. 测试分页功能（调整每页显示数量：10、20、50）')
    print('  4. 测试编辑和删除功能')
    print('  5. 测试批量操作性能')


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='生成群众意见测试数据')
    parser.add_argument('--count', type=int, default=50, help='生成数据数量（默认50）')
    parser.add_argument('--clear', action='store_true', help='清空现有数据')
    
    args = parser.parse_args()
    
    try:
        generate_test_data(count=args.count, clear_existing=args.clear)
    except Exception as e:
        print(f'❌ 生成测试数据失败：{e}')
        sys.exit(1) 