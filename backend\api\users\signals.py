"""
用户管理应用信号处理器

处理用户相关的自动化操作：
1. 用户创建后自动创建对应的代表或工作人员记录
2. 记录用户操作日志
"""

import logging
import sys
from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver
from django.contrib.auth.signals import user_logged_in, user_logged_out

from .models import User, Representative, StaffMember

logger = logging.getLogger(__name__)


def safe_log_info(message):
    """安全的日志记录函数，避免编码问题"""
    try:
        logger.info(message)
    except UnicodeEncodeError:
        # 如果遇到编码错误，使用print作为备选方案
        print(f"[INFO] {message}", file=sys.stdout, flush=True)


def safe_log_warning(message):
    """安全的警告日志记录函数，避免编码问题"""
    try:
        logger.warning(message)
    except UnicodeEncodeError:
        # 如果遇到编码错误，使用print作为备选方案
        print(f"[WARNING] {message}", file=sys.stdout, flush=True)


@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """记录用户登录日志"""
    ip_address = request.META.get('REMOTE_ADDR', '未知')
    user_agent = request.META.get('HTTP_USER_AGENT', '未知')
    
    message = (
        f"用户登录 - 用户名: {user.username}, "
        f"角色: {user.get_role_display()}, "
        f"IP: {ip_address}, "
        f"User-Agent: {user_agent}"
    )
    safe_log_info(message)


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """记录用户登出日志"""
    if user:
        message = f"用户登出 - 用户名: {user.username}, 角色: {user.get_role_display()}"
        safe_log_info(message)


@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """
    用户创建后的信号处理
    
    注意：这里不自动创建Representative或StaffMember记录，
    因为这些记录需要额外的信息，应该由管理员手动创建或通过专门的API创建
    """
    if created:
        message = (
            f"新用户创建 - ID: {instance.id}, "
            f"用户名: {instance.username}, "
            f"角色: {instance.get_role_display()}"
        )
        safe_log_info(message)


@receiver(pre_delete, sender=User)
def log_user_deletion(sender, instance, **kwargs):
    """记录用户删除日志"""
    message = (
        f"用户即将删除 - ID: {instance.id}, "
        f"用户名: {instance.username}, "
        f"角色: {instance.get_role_display()}"
    )
    safe_log_warning(message)


@receiver(post_save, sender=Representative)
def log_representative_creation(sender, instance, created, **kwargs):
    """记录人大代表信息创建日志"""
    if created:
        message = (
            f"人大代表信息创建 - 姓名: {instance.name}, "
            f"层级: {instance.level}, "
            f"关联用户: {instance.user.username}"
        )
        safe_log_info(message)


@receiver(post_save, sender=StaffMember)
def log_staff_creation(sender, instance, created, **kwargs):
    """记录工作人员信息创建日志"""
    if created:
        message = (
            f"工作人员信息创建 - 姓名: {instance.name}, "
            f"职位: {instance.position}, "
            f"站点: {instance.station_name}, "
            f"关联用户: {instance.user.username}"
        )
        safe_log_info(message) 