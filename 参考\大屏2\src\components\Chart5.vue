<template>
  <div ref="myChart" style="width: 100%; height: 100%"></div>
  <div v-if="activeRegion" class="popup" :style="popupStyle">
    <div class="popup-title">{{ activeRegion }}</div>
    <div class="popup-row popup-circle-row">
      <div class="popup-circle">
        <div class="popup-circle-num">{{ regions[activeRegion].city }}</div>
        <div class="popup-circle-label">市代表</div>
      </div>
      <div class="popup-circle">
        <div class="popup-circle-num">{{ regions[activeRegion].town }}</div>
        <div class="popup-circle-label">镇代表</div>
      </div>
    </div>
    <div class="popup-row popup-gender-label">男女比例</div>
    <div class="popup-row popup-gender">
      <span
        v-for="n in regions[activeRegion].male"
        :key="'m' + n"
        class="iconfont icon-mti-nan male"
      ></span>
      <span
        v-for="n in regions[activeRegion].female"
        :key="'f' + n"
        class="iconfont icon-mti-nv female"
      ></span>
    </div>
    <div class="popup-row popup-bar-row">
      <div class="popup-bar-label">党员占比</div>
      <div class="popup-bar">
        <div
          class="popup-bar-inner party"
          :style="{ width: regions[activeRegion].party + '%' }"
        ></div>
      </div>
      <div class="popup-bar-value">{{ regions[activeRegion].party }}%</div>
    </div>
    <div class="popup-row popup-bar-row">
      <div class="popup-bar-label">建议落实率</div>
      <div class="popup-bar">
        <div
          class="popup-bar-inner advice"
          :style="{ width: regions[activeRegion].advice + '%' }"
        ></div>
      </div>
      <div class="popup-bar-value">{{ regions[activeRegion].advice }}%</div>
    </div>
    <div class="popup-row popup-bar-row">
      <div class="popup-bar-label">履职平台参与率</div>
      <div class="popup-bar">
        <div
          class="popup-bar-inner platform"
          :style="{ width: regions[activeRegion].platform + '%' }"
        ></div>
      </div>
      <div class="popup-bar-value">{{ regions[activeRegion].platform }}%</div>
    </div>
    <span class="popup-close" @click="activeRegion = null">×</span>
  </div>
</template>

<script>
import jiangnan from '../assets/jiangnan.json'

export default {
  props: {
    regions: {
      type: Object,
      default: () => ({})
    }
  },
  mounted() {
    this.initChart()
  },
  watch: {
    regions: {
      handler() {
        this.initChart()
      },
      deep: true
    }
  },
  data() {
    return {
      activeRegion: null,
      popupPosition: { left: 0, top: 0 },
    }
  },
  computed: {
    popupStyle() {
      // 居中于地图区域
      return {
        position: 'absolute',
        left: '60%',
        top: '40%',
        transform: 'translate(-50%, -50%)',
        zIndex: 10,
      }
    },
  },
  methods: {
    async initChart() {
      const myChart = this.$echarts.init(this.$refs.myChart)
      this.$echarts.registerMap('jiangnan', jiangnan)
      let option = {
        geo: {
          map: 'jiangnan',
          roam: true,
          // left: '30%',
          zoom: 1.2,
          label: {
            show: true,
            color: '#fff',
          },
          itemStyle: {
            areaColor: '#013163 ',
            borderColor: '#3a769e',
            borderWidth: 1,
          },
          emphasis: {
            itemStyle: {
              areaColor: '#3498db',
            },
          },
        },
      }
      await myChart.setOption(option)
      // 监听区域点击
      myChart.on('click', (params) => {
        if (params.name && this.regions[params.name]) {
          this.activeRegion = params.name
        }
      })
    },
  },
}
</script>

<style scoped>
.popup {
  z-index: 999;
  background: rgba(10, 32, 64, 0.68);
  color: #fff;
  border: 1.5px solid #3a7bd5;
  border-radius: 14px;
  padding: 24px 15px 18px 15px;
  min-width: 260px;
  min-height: 320px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.45);
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.popup-title {
  color: #ffd700;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 18px;
  letter-spacing: 2px;
}
.popup-row {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.popup-circle-row {
  justify-content: space-around;
  margin-bottom: 18px;
}
.popup-circle {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(145deg, #1e3c72 60%, #2a5298 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(58, 123, 213, 0.18);
}
.popup-circle-num {
  font-size: 22px;
  font-weight: bold;
  color: #fff;
}
.popup-circle-label {
  font-size: 13px;
  color: #b0cfff;
  margin-top: 2px;
}
.popup-gender-label {
  font-size: 15px;
  color: #b0cfff;
  margin-bottom: 2px;
  justify-content: flex-start;
}
.popup-gender {
  justify-content: flex-start;
  margin-bottom: 14px;
}
.iconfont {
  font-size: 20px;
  margin-right: 2px;
}
.male {
  color: #4fc3f7;
}
.female {
  color: #f06292;
}
.popup-bar-row {
  justify-content: flex-start;
  margin-bottom: 8px;
}
.popup-bar-label {
  width: 100px;
  font-size: 13px;
  color: #b0cfff;
}
.popup-bar {
  flex: 1;
  height: 10px;
  background: #183a5a;
  border-radius: 5px;
  margin: 0 8px;
  overflow: hidden;
}
.popup-bar-inner {
  height: 100%;
  border-radius: 5px;
}
.popup-bar-inner.party {
  background: linear-gradient(90deg, #3a7bd5, #00d2ff);
}
.popup-bar-inner.advice {
  background: linear-gradient(90deg, #f7971e, #ffd200);
}
.popup-bar-inner.platform {
  background: linear-gradient(90deg, #43e97b, #38f9d7);
}
.popup-bar-value {
  width: 38px;
  text-align: right;
  font-size: 13px;
  color: #fff;
}
.popup-close {
  position: absolute;
  top: 10px;
  right: 16px;
  font-size: 22px;
  color: #b0cfff;
  cursor: pointer;
  font-weight: bold;
  transition: color 0.2s;
}
.popup-close:hover {
  color: #fff;
}
</style>
