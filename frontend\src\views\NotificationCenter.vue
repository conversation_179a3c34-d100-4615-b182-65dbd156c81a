<template>
  <div class="notification-center">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>
          <el-icon><Bell /></el-icon>
          通知中心
        </h2>
        <span class="total-count">共 {{ total }} 条通知</span>
      </div>
      <div class="header-right">
        <el-button 
          type="primary" 
          :disabled="!hasUnread"
          @click="handleMarkAllAsRead"
        >
          全部标记为已读
        </el-button>
      </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-bar">
      <el-row :gutter="16" align="middle">
        <el-col :span="6">
          <el-select 
            v-model="currentFilters.type" 
            placeholder="选择通知类型"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="全部类型" value="" />
            <el-option 
              v-for="(config, type) in notificationTypes" 
              :key="type"
              :label="config.title"
              :value="type"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-checkbox 
            v-model="currentFilters.unreadOnly"
            @change="handleFilterChange"
          >
            只看未读
          </el-checkbox>
        </el-col>
        <el-col :span="6">
          <span class="unread-info" v-if="hasUnread">
            <el-badge :value="unreadCount" type="danger">
              <el-icon><Message /></el-icon>
            </el-badge>
            <span class="unread-text">{{ unreadCount }} 条未读</span>
          </span>
        </el-col>
        <el-col :span="8" class="text-right">
          <el-button @click="handleRefresh" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 通知列表 -->
    <div class="notification-list">
      <el-card v-if="loading" class="loading-card">
        <div class="loading-content">
          <el-icon class="is-loading"><Loading /></el-icon>
          正在加载通知...
        </div>
      </el-card>

      <div v-else-if="notifications.length === 0" class="empty-state">
        <el-empty description="暂无通知" />
      </div>

      <div v-else>
        <!-- 批量操作工具栏 -->
        <div class="batch-actions" v-if="selectedNotifications.length > 0">
          <el-alert 
            :title="`已选择 ${selectedNotifications.length} 条通知`" 
            type="info" 
            show-icon 
            :closable="false"
          >
            <template #default>
              <el-button 
                size="small" 
                @click="handleBatchMarkAsRead"
                :disabled="!hasSelectedUnread"
              >
                批量标记已读
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click="handleBatchDelete"
              >
                批量删除
              </el-button>
              <el-button size="small" @click="clearSelection">
                取消选择
              </el-button>
            </template>
          </el-alert>
        </div>

        <!-- 通知项列表 -->
        <div class="notification-items">
          <div 
            v-for="notification in notifications" 
            :key="notification.id"
            class="notification-item"
            :class="{ 
              'unread': !notification.isRead,
              'selected': selectedNotifications.includes(notification.id)
            }"
          >
            <div class="item-checkbox">
              <el-checkbox 
                :model-value="selectedNotifications.includes(notification.id)"
                @change="handleSelectNotification(notification.id, $event)"
              />
            </div>

            <div class="item-icon">
              <el-icon 
                :style="{ color: getNotificationTypeConfig(notification.type).color }"
                :size="20"
              >
                <component :is="getNotificationTypeConfig(notification.type).icon" />
              </el-icon>
            </div>

            <div class="item-content" @click="handleNotificationClick(notification)">
              <div class="content-header">
                <div class="title-row">
                  <span class="notification-title">{{ notification.title }}</span>
                  <el-tag 
                    v-if="!notification.isRead" 
                    type="danger" 
                    size="small"
                    round
                  >
                    未读
                  </el-tag>
                </div>
                <div class="meta-info">
                  <span class="sender">来自：{{ notification.sender }}</span>
                  <span class="time">{{ formatTime(notification.createdAt) }}</span>
                </div>
              </div>
              <div class="content-body">
                <p class="notification-content">{{ notification.content }}</p>
              </div>
            </div>

            <div class="item-actions">
              <el-dropdown 
                trigger="click"
                @command="(command) => handleAction(command, notification)"
              >
                <el-button text>
                  <el-icon><More /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item 
                      command="markAsRead" 
                      :disabled="notification.isRead"
                    >
                      标记为已读
                    </el-dropdown-item>
                    <el-dropdown-item 
                      command="viewDetail" 
                      v-if="notification.relatedId"
                    >
                      查看详情
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      删除通知
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Bell, Message, Refresh, Loading, More,
  Document, Check, Finished, Setting, DataAnalysis
} from '@element-plus/icons-vue'
import { useNotificationStore } from '@/stores/notification'
import { useUserStore } from '@/stores/user'
import { getNotificationTypes } from '@/api/notification'
import { useRouter } from 'vue-router'

const router = useRouter()
const notificationStore = useNotificationStore()
const userStore = useUserStore()

// 解构响应式状态
const {
  notifications,
  unreadCount,
  loading,
  currentPage,
  pageSize,
  total,
  filters,
  hasUnread,
  totalPages
} = notificationStore

// 本地状态
const selectedNotifications = ref([])
const currentFilters = ref({ ...filters })
const notificationTypes = getNotificationTypes()

// 计算属性
const hasSelectedUnread = computed(() => {
  return selectedNotifications.value.some(id => {
    const notification = notifications.find(n => n.id === id)
    return notification && !notification.isRead
  })
})

// 获取通知类型配置
const getNotificationTypeConfig = (type) => {
  return notificationTypes[type] || {
    title: '未知类型',
    icon: 'Bell',
    color: '#909399'
  }
}

// 格式化时间
const formatTime = (time) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  
  // 1分钟内
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  // 1小时内
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`
  }
  // 24小时内
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
  }
  // 超过24小时
  if (date.getFullYear() === now.getFullYear()) {
    return date.toLocaleDateString('zh-CN', { 
      month: 'numeric', 
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    })
  }
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric'
  })
}

// 事件处理
const handleFilterChange = () => {
  notificationStore.setFilters(currentFilters.value)
  loadNotifications(true)
}

const handleRefresh = () => {
  loadNotifications(true)
  notificationStore.fetchUnreadCount(userStore.userInfo.id)
}

const handleMarkAllAsRead = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要将所有通知标记为已读吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await notificationStore.markAllNotificationsAsRead(userStore.userInfo.id)
  } catch (error) {
    // 用户取消操作
  }
}

const handleSelectNotification = (notificationId, checked) => {
  if (checked) {
    selectedNotifications.value.push(notificationId)
  } else {
    const index = selectedNotifications.value.indexOf(notificationId)
    if (index > -1) {
      selectedNotifications.value.splice(index, 1)
    }
  }
}

const clearSelection = () => {
  selectedNotifications.value = []
}

const handleBatchMarkAsRead = async () => {
  const unreadIds = selectedNotifications.value.filter(id => {
    const notification = notifications.find(n => n.id === id)
    return notification && !notification.isRead
  })
  
  if (unreadIds.length === 0) {
    ElMessage.info('所选通知均已读')
    return
  }
  
  await notificationStore.markBatchAsRead(unreadIds)
  clearSelection()
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedNotifications.value.length} 条通知吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 逐个删除通知
    for (const id of selectedNotifications.value) {
      await notificationStore.removeNotification(id)
    }
    
    clearSelection()
    // 如果当前页没有数据了，回到上一页
    if (notifications.length === 0 && currentPage > 1) {
      notificationStore.setPage(currentPage - 1)
      loadNotifications()
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleNotificationClick = async (notification) => {
  // 标记为已读
  if (!notification.isRead) {
    await notificationStore.markNotificationAsRead(notification.id)
  }
  
  // 根据通知类型跳转到相关页面
  if (notification.relatedId) {
    switch (notification.type) {
      case 'opinion_submit':
      case 'opinion_audit':
      case 'opinion_result':
        // 跳转到意见详情
        if (userStore.isRepresentative) {
          router.push('/representative/opinions')
        } else {
          router.push('/staff/review')
        }
        break
      case 'mediation_submit':
      case 'mediation_analysis':
        // 跳转到调解案件
        if (userStore.isRepresentative) {
          router.push('/representative/mediation-cases')
        } else {
          router.push('/staff/mediation-cases')
        }
        break
      default:
        // 其他类型暂不跳转
        break
    }
  }
}

const handleAction = async (command, notification) => {
  switch (command) {
    case 'markAsRead':
      await notificationStore.markNotificationAsRead(notification.id)
      break
    case 'viewDetail':
      handleNotificationClick(notification)
      break
    case 'delete':
      try {
        await ElMessageBox.confirm(
          '确定要删除这条通知吗？',
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await notificationStore.removeNotification(notification.id)
      } catch (error) {
        // 用户取消操作
      }
      break
  }
}

const handleSizeChange = (size) => {
  notificationStore.setPageSize(size)
  loadNotifications()
}

const handleCurrentChange = (page) => {
  notificationStore.setPage(page)
  loadNotifications()
}

// 加载通知列表
const loadNotifications = (refresh = false) => {
  notificationStore.fetchNotifications(userStore.userInfo.id, refresh)
}

// 监听筛选条件变化
watch(filters, () => {
  currentFilters.value = { ...filters }
}, { deep: true })

// 初始化
onMounted(() => {
  loadNotifications()
  notificationStore.fetchUnreadCount(userStore.userInfo.id)
})
</script>

<style scoped>
.notification-center {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 0 20px 0;
  border-bottom: 1px solid #e4e7ed;
}

.header-left h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
  font-size: 20px;
}

.total-count {
  color: #909399;
  font-size: 14px;
  margin-left: 16px;
}

.filter-bar {
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
}

.unread-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #f56c6c;
}

.unread-text {
  font-size: 14px;
  font-weight: 500;
}

.text-right {
  text-align: right;
}

.notification-list {
  min-height: 400px;
}

.loading-card {
  text-align: center;
  padding: 40px;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #909399;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.batch-actions {
  margin-bottom: 16px;
}

.notification-items {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  transition: all 0.3s;
  cursor: pointer;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #fdf6ec;
  border-left: 4px solid #e6a23c;
}

.notification-item.selected {
  background-color: #e8f4fd;
}

.item-checkbox {
  margin-right: 12px;
  margin-top: 2px;
}

.item-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.content-header {
  margin-bottom: 8px;
}

.title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.notification-title {
  font-weight: 500;
  font-size: 15px;
  color: #303133;
}

.meta-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.content-body {
  line-height: 1.5;
}

.notification-content {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  word-break: break-word;
}

.item-actions {
  margin-left: 12px;
  margin-top: 2px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-center {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .filter-bar .el-col {
    margin-bottom: 8px;
  }
  
  .notification-item {
    padding: 12px;
  }
  
  .title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .meta-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style> 