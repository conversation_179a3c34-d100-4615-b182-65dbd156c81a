<template>
  <div class="account-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>账号管理</h2>
      <p>管理系统中所有用户账号，包括人大代表和站点工作人员</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showAddDialog">
        <el-icon><Plus /></el-icon>
        新增账号
      </el-button>
      <el-button @click="refreshData">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
      
      <!-- 搜索框 -->
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索用户名或姓名"
          clearable
          @input="handleSearch"
          style="width: 300px;"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 账号列表 -->
    <el-card class="account-list-card">
      <el-table
        :data="filteredAccountList"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column prop="realName" label="真实姓名" width="120" />
        <el-table-column prop="role" label="角色" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.role === 'representative' ? 'success' : 'primary'">
              {{ scope.row.roleText }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="department" label="所属部门" width="150" />
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.statusText }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginTime" label="最后登录" width="160" />
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="editAccount(scope.row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button size="small" type="warning" @click="resetPassword(scope.row)">
              <el-icon><Key /></el-icon>
              重置密码
            </el-button>
            <el-button 
              size="small" 
              :type="scope.row.status === 'active' ? 'danger' : 'success'"
              @click="toggleAccountStatus(scope.row)"
            >
              <el-icon><Switch /></el-icon>
              {{ scope.row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑账号对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="accountFormRef"
        :model="accountForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="accountForm.username" 
            placeholder="请输入用户名"
            :disabled="isEdit"
          />
          <div class="form-tip" v-if="!isEdit">用户名创建后不可修改</div>
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="accountForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="accountForm.role" placeholder="请选择角色" style="width: 100%">
            <el-option label="人大代表" value="representative" />
            <el-option label="站点工作人员" value="staff" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="所属部门" prop="department">
          <el-input v-model="accountForm.department" placeholder="请输入所属部门" />
        </el-form-item>
        
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="accountForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="accountForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>
        
        <el-form-item label="初始密码" prop="password" v-if="!isEdit">
          <el-input 
            v-model="accountForm.password" 
            type="password" 
            placeholder="请输入初始密码"
            show-password
          />
          <div class="form-tip">密码长度至少6位，建议包含字母和数字</div>
        </el-form-item>
        
        <el-form-item label="账号状态" prop="status">
          <el-radio-group v-model="accountForm.status">
            <el-radio value="active">启用</el-radio>
            <el-radio value="disabled">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input 
            v-model="accountForm.remark" 
            type="textarea" 
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAccount" :loading="saving">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="resetPasswordVisible"
      title="重置密码"
      width="400px"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="用户">
          <el-input :value="selectedAccount?.realName + ' (' + selectedAccount?.username + ')'" disabled />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="passwordForm.newPassword" 
            type="password" 
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetPasswordVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmResetPassword" :loading="resetting">
            确认重置
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Search, Edit, Key, Switch } from '@element-plus/icons-vue'
import { accountManagementAPI } from '@/api/accountManagement'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const resetting = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)
const accountList = ref([])

// 对话框相关
const dialogVisible = ref(false)
const resetPasswordVisible = ref(false)
const isEdit = ref(false)
const selectedAccount = ref(null)

// 表单引用
const accountFormRef = ref()
const passwordFormRef = ref()

// 表单数据
const accountForm = reactive({
  id: null,
  username: '',
  realName: '',
  role: '',
  department: '',
  phone: '',
  email: '',
  password: '',
  status: 'active',
  remark: ''
})

const passwordForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑账号' : '新增账号')

const filteredAccountList = computed(() => {
  if (!searchKeyword.value) {
    return accountList.value
  }
  return accountList.value.filter(account => 
    account.username.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    account.realName.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '姓名长度在 2 到 10 个字符', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  department: [
    { required: true, message: '请输入所属部门', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入初始密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择账号状态', trigger: 'change' }
  ]
}

const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 方法
const loadAccountList = async () => {
  try {
    loading.value = true
    const response = await accountManagementAPI.getAccountList({
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value
    })
    
    accountList.value = response.data.list
    totalCount.value = response.data.total
  } catch (error) {
    ElMessage.error('加载账号列表失败：' + error.message)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadAccountList()
}

const handleSearch = () => {
  currentPage.value = 1
  loadAccountList()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadAccountList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadAccountList()
}

const showAddDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

const editAccount = (account) => {
  isEdit.value = true
  selectedAccount.value = account
  
  // 填充表单数据
  Object.keys(accountForm).forEach(key => {
    if (account[key] !== undefined) {
      accountForm[key] = account[key]
    }
  })
  
  dialogVisible.value = true
}

const resetForm = () => {
  if (accountFormRef.value) {
    accountFormRef.value.resetFields()
  }
  
  Object.keys(accountForm).forEach(key => {
    if (key === 'status') {
      accountForm[key] = 'active'
    } else {
      accountForm[key] = key === 'id' ? null : ''
    }
  })
}

const saveAccount = async () => {
  try {
    await accountFormRef.value.validate()
    
    saving.value = true
    
    if (isEdit.value) {
      await accountManagementAPI.updateAccount(accountForm.id, accountForm)
      ElMessage.success('账号更新成功')
    } else {
      await accountManagementAPI.createAccount(accountForm)
      ElMessage.success('账号创建成功')
    }
    
    dialogVisible.value = false
    loadAccountList()
  } catch (error) {
    if (error.message) {
      ElMessage.error('操作失败：' + error.message)
    }
  } finally {
    saving.value = false
  }
}

const resetPassword = (account) => {
  selectedAccount.value = account
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  resetPasswordVisible.value = true
}

const confirmResetPassword = async () => {
  try {
    await passwordFormRef.value.validate()
    
    resetting.value = true
    
    await accountManagementAPI.resetPassword(selectedAccount.value.id, {
      newPassword: passwordForm.newPassword
    })
    
    ElMessage.success('密码重置成功')
    resetPasswordVisible.value = false
  } catch (error) {
    ElMessage.error('密码重置失败：' + error.message)
  } finally {
    resetting.value = false
  }
}

const toggleAccountStatus = async (account) => {
  const action = account.status === 'active' ? '禁用' : '启用'
  const newStatus = account.status === 'active' ? 'disabled' : 'active'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}账号 "${account.realName}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await accountManagementAPI.updateAccountStatus(account.id, newStatus)
    
    ElMessage.success(`账号${action}成功`)
    loadAccountList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`账号${action}失败：` + error.message)
    }
  }
}

// 生命周期
onMounted(() => {
  loadAccountList()
})
</script>

<style scoped>
.account-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: var(--china-red);
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.search-box {
  flex-shrink: 0;
}

.account-list-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .account-management {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    width: 100%;
  }
  
  .search-box .el-input {
    width: 100% !important;
  }
}
</style> 