<template>
  <section class="bottom-content-block">
    <!-- 标题区域 -->
    <div class="chart-header">
      <div class="header-left">
        <!-- <div class="chart-icon">📋</div> -->
        <div class="chart-title">{{ chartTitle }}</div>
      </div>
      <div class="header-right">
        <!-- 月份选择器 -->
        <div class="month-selector">
          <select v-model="selectedMonth" @change="handleMonthChange" class="month-select">
            <option v-for="month in monthOptions" :key="month.value" :value="month.value">
              {{ month.label }}
            </option>
          </select>
        </div>
        <!-- 履职总数显示 -->
        <div class="total-count">
          履职总数：{{ totalCount }}
        </div>
      </div>
    </div>
    
    <!-- 图表容器 -->
    <div class="chart-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">数据加载中...</div>
      </div>
      
      <!-- 图表内容 -->
      <div v-else ref="chartRef" class="chart-content"></div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import { getRepresentativeDuties } from '@/api/indexscreen/representativeDuties.js'

// 响应式数据
const chartTitle = ref('履职明细')
const chartRef = ref(null)
const loading = ref(false)
const chartData = ref([])
const totalCount = ref(0)
const selectedMonth = ref('')
let chartInstance = null

// 月份选项
const monthOptions = ref([
  { value: '01', label: '1月' },
  { value: '02', label: '2月' },
  { value: '03', label: '3月' },
  { value: '04', label: '4月' },
  { value: '05', label: '5月' },
  { value: '06', label: '6月' },
  { value: '07', label: '7月' },
  { value: '08', label: '8月' },
  { value: '09', label: '9月' },
  { value: '10', label: '10月' },
  { value: '11', label: '11月' },
  { value: '12', label: '12月' }
])

// 获取图表数据
const fetchChartData = async () => {
  try {
    loading.value = true
    const response = await getRepresentativeDuties({
      year: '2024',
      month: selectedMonth.value
    })
    
    if (response.code === 200) {
      chartData.value = response.data
      totalCount.value = response.totalCount || 0
      console.log('📋 代表履职统计数据加载成功:', chartData.value)
      console.log('📊 履职总数:', totalCount.value)
    } else {
      console.error('❌ 获取数据失败:', response.message)
    }
  } catch (error) {
    console.error('❌ 数据请求异常:', error)
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value || chartData.value.length === 0) return
  
  chartInstance = echarts.init(chartRef.value)
  
  // 定义统一的主题色系 - 蓝色科技渐变
  const themeColors = [
    '#02a6b5', // 青蓝色
    '#1a5490', // 中蓝色
    '#337ab7', // 蓝色
    '#49bcf7', // 亮蓝色
    '#5bc0de', // 天蓝色
    '#4682b4', // 钢蓝色
    '#000d4a', // 深蓝色
    '#2e8b57', // 海绿色
    '#20b2aa', // 浅海绿色
    '#4169e1', // 皇家蓝
    '#1e90ff', // 道奇蓝
    '#00bfff', // 深天蓝色
    '#87ceeb', // 天蓝色
    '#87cefa', // 浅天蓝色
    '#6495ed', // 矢车菊蓝
    '#4169e1', // 皇家蓝
    '#0000cd'  // 中蓝色
  ]
  
  const option = {
    backgroundColor: 'transparent',
    grid: {
      top: '8%',
      left: '8%',
      right: '8%',
      bottom: '20%', // 进一步增加底部空间给X轴标签
      containLabel: false // 手动控制标签空间
    },
    xAxis: {
      type: 'category',
      data: chartData.value.map(item => item.name),
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: true, // 确保显示标签
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10,
        interval: 0, // 显示所有标签
        rotate: 45, // 旋转标签以适应更多文字
        margin: 10, // 增加标签与轴的距离
        fontWeight: 'normal',
        overflow: 'none' // 不截断文字
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 10
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'bar',
        data: chartData.value.map((item, index) => {
          // 使用主题色系，循环使用
          const themeColor = themeColors[index % themeColors.length]
          return {
            value: item.value,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: themeColor },
                { offset: 1, color: themeColor + '40' }
              ]),
              borderRadius: [3, 3, 0, 0],
              shadowColor: themeColor,
              shadowBlur: 6,
              shadowOffsetY: 2
            }
          }
        }),
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          color: '#ffffff',
          fontSize: 9,
          fontWeight: 'bold',
          distance: 3
        },
        animationDelay: (idx) => idx * 80,
        animationDuration: 1000,
        animationEasing: 'elasticOut'
      }
    ],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'rgba(255, 255, 255, 0.2)',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff',
        fontSize: 11
      },
      formatter: function(params) {
        const data = params[0]
        const item = chartData.value[data.dataIndex]
        const themeColor = themeColors[data.dataIndex % themeColors.length]
        return `
          <div style="padding: 5px;">
            <div style="color: ${themeColor}; font-weight: bold; margin-bottom: 5px;">
              ${data.name}
            </div>
            <div style="margin-bottom: 3px;">
              数量: <span style="color: #49bcf7; font-weight: bold;">${data.value}</span>
            </div>
            <div>
              占比: <span style="color: #02a6b5; font-weight: bold;">${item.percentage}%</span>
            </div>
          </div>
        `
      }
    }
  }
  
  chartInstance.setOption(option)
  
  // 添加点击事件
  chartInstance.on('click', (params) => {
    console.log('点击了履职类型:', params.name, '数值:', params.value)
  })
}

// 处理月份变化
const handleMonthChange = async () => {
  console.log('📅 切换月份:', selectedMonth.value)
  await fetchChartData()
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  await nextTick()
  initChart()
}

// 响应式调整
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 初始化当前月份
const initCurrentMonth = () => {
  const currentMonth = new Date().getMonth() + 1
  selectedMonth.value = currentMonth.toString().padStart(2, '0')
}

// 组件挂载
onMounted(async () => {
  await nextTick()
  // 初始化当前月份
  initCurrentMonth()
  // 先获取数据，再初始化图表
  await fetchChartData()
  initChart()
  window.addEventListener('resize', resizeChart)
})

// 组件卸载
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
/* 主容器 - 使用flex布局 */
.bottom-content-block {
  /* background: rgba(0, 13, 74, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(2, 166, 181, 0.15); */
  border-radius: 12px;
  padding: 16px;
  color: #ffffff;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  height: 30vh;
}

.bottom-content-block:hover {
  background: rgba(26, 84, 144, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 13, 74, 0.2);
  border-color: rgba(2, 166, 181, 0.3);
}

/* 图表头部 - flex布局 */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  position: relative;
  flex-shrink: 0; /* 防止头部被压缩 */
}

/* 美观的分割线设计 */
.chart-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(73, 188, 247, 0.3) 10%,
    rgba(73, 188, 247, 0.8) 30%,
    rgba(73, 188, 247, 1) 50%,
    rgba(73, 188, 247, 0.8) 70%,
    rgba(73, 188, 247, 0.3) 90%,
    transparent 100%
  );
  border-radius: 2px;
  box-shadow: 
    0 0 8px rgba(73, 188, 247, 0.4),
    0 2px 4px rgba(73, 188, 247, 0.2);
  animation: glow-pulse 3s ease-in-out infinite alternate;
}

/* 添加一个细的上层装饰线 */
.chart-header::before {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  border-radius: 1px;
  animation: shimmer 2s ease-in-out infinite alternate;
}

/* 分割线发光动画 */
@keyframes glow-pulse {
  0% {
    box-shadow: 
      0 0 8px rgba(73, 188, 247, 0.4),
      0 2px 4px rgba(73, 188, 247, 0.2);
  }
  100% {
    box-shadow: 
      0 0 12px rgba(73, 188, 247, 0.6),
      0 2px 6px rgba(73, 188, 247, 0.3);
  }
}

/* 上层装饰线闪烁动画 */
@keyframes shimmer {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-icon {
  font-size: 1.1rem;
  filter: drop-shadow(0 0 4px rgba(73, 188, 247, 0.8));
}

.chart-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 月份选择器 */
.month-selector {
  position: relative;
}

.month-select {
  background: rgba(255, 255, 255, 0.1);
  /* border: 1px solid rgba(255, 255, 255, 0.2); */
  border: 1px solid #49bcf7;
  border-radius: 6px;
  padding: 4px 8px;
  color: #ffffff;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}

.month-select:hover {
  background: rgba(255, 255, 255, 0.18);
  border-color: rgba(2, 166, 181, 0.4);
}

.month-select:focus {
  border-color: rgba(2, 166, 181, 0.6);
  box-shadow: 0 0 8px rgba(2, 166, 181, 0.3);
}

.month-select option {
  background: rgba(0, 0, 0, 0.9);
  color: #ffffff;
  padding: 4px;
}

/* 履职总数显示 */
.total-count {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.9);
  /* background: rgba(2, 166, 181, 0.2); */
  padding: 3px 8px;
  border-radius: 10px;
  border: 1px solid #49bcf7;
  white-space: nowrap;
}

/* 图表容器 - flex自适应 */
.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许容器收缩 */
  position: relative;
}

.chart-content {
  flex: 1;
  min-height: 100px; /* 最小高度确保图表可见 */
  width: 100%;
  position: relative;
}

/* 加载状态样式 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  min-height: 100px;
}

.loading-spinner {
  width: 28px;
  height: 28px;
  border: 2px solid rgba(2, 166, 181, 0.2);
  border-top: 2px solid rgba(2, 166, 181, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}


/* 加载动画效果 */
.bottom-content-block::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 212, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.bottom-content-block:hover::after {
  left: 100%;
}

/* 确保图表在容器变化时正确响应 */
.chart-content > div {
  width: 100% !important;
  height: 100% !important;
}
</style> 