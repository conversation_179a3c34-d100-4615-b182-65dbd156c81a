# Generated by Django 5.2.3 on 2025-06-17 11:44

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='用户ID')),
                ('username', models.Char<PERSON>ield(help_text='用于登录的唯一标识符，最多50个字符', max_length=50, unique=True, verbose_name='用户名')),
                ('role', models.CharField(choices=[('representative', '人大代表'), ('staff', '站点工作人员')], help_text='用户在系统中的角色类型', max_length=20, verbose_name='用户角色')),
                ('is_active', models.BooleanField(default=True, help_text='指定用户是否应被视为活跃。取消选择此项而不是删除账户。', verbose_name='账号激活状态')),
                ('is_staff', models.BooleanField(default=False, help_text='指定用户是否可以登录管理站点。', verbose_name='管理员状态')),
                ('last_login_at', models.DateTimeField(blank=True, null=True, verbose_name='最后登录时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
                'db_table': 'users',
            },
        ),
        migrations.CreateModel(
            name='Representative',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='代表ID')),
                ('level', models.CharField(help_text='如：区级、市级、省级等', max_length=50, verbose_name='代表层级')),
                ('name', models.CharField(max_length=50, verbose_name='姓名')),
                ('gender', models.CharField(choices=[('male', '男'), ('female', '女')], max_length=10, verbose_name='性别')),
                ('nationality', models.CharField(max_length=50, verbose_name='民族')),
                ('birth_date', models.DateField(verbose_name='出生日期')),
                ('birthplace', models.CharField(max_length=100, verbose_name='籍贯')),
                ('party', models.CharField(max_length=50, verbose_name='党派')),
                ('current_position', models.CharField(max_length=100, verbose_name='现任职务')),
                ('mobile_phone', models.CharField(max_length=11, validators=[django.core.validators.RegexValidator(message='请输入有效的手机号码格式', regex='^1[3-9]\\d{9}$')], verbose_name='移动电话号码')),
                ('education', models.CharField(max_length=50, verbose_name='学历')),
                ('graduated_school', models.CharField(blank=True, max_length=100, null=True, verbose_name='毕业院校')),
                ('major', models.CharField(blank=True, max_length=100, null=True, verbose_name='所学专业')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(help_text='与用户表的一对一关联', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='关联用户')),
            ],
            options={
                'verbose_name': '人大代表',
                'verbose_name_plural': '人大代表',
                'db_table': 'representatives',
            },
        ),
        migrations.CreateModel(
            name='StaffMember',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='工作人员ID')),
                ('name', models.CharField(max_length=50, verbose_name='姓名')),
                ('position', models.CharField(max_length=100, verbose_name='职位')),
                ('mobile_phone', models.CharField(max_length=11, validators=[django.core.validators.RegexValidator(message='请输入有效的手机号码格式', regex='^1[3-9]\\d{9}$')], verbose_name='移动电话号码')),
                ('email', models.EmailField(blank=True, max_length=100, null=True, verbose_name='邮箱地址')),
                ('station_name', models.CharField(max_length=100, verbose_name='所属站点名称')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(help_text='与用户表的一对一关联', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='关联用户')),
            ],
            options={
                'verbose_name': '站点工作人员',
                'verbose_name_plural': '站点工作人员',
                'db_table': 'staff_members',
            },
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['username'], name='users_usernam_baeb4b_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['role', 'is_active'], name='users_role_a8f2ba_idx'),
        ),
        migrations.AddIndex(
            model_name='representative',
            index=models.Index(fields=['name'], name='representat_name_e3bb3c_idx'),
        ),
        migrations.AddIndex(
            model_name='representative',
            index=models.Index(fields=['level'], name='representat_level_a9c736_idx'),
        ),
        migrations.AddIndex(
            model_name='staffmember',
            index=models.Index(fields=['name'], name='staff_membe_name_b7a7a4_idx'),
        ),
        migrations.AddIndex(
            model_name='staffmember',
            index=models.Index(fields=['station_name'], name='staff_membe_station_f4b213_idx'),
        ),
    ]
