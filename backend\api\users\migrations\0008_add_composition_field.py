# Generated by Django 5.2.3 on 2025-07-28 13:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0007_update_level_field_for_multiple_levels'),
    ]

    operations = [
        migrations.AddField(
            model_name='representative',
            name='composition',
            field=models.CharField(blank=True, help_text='代表的构成类型：一线工人、农民、专业技术人员等（多个构成用逗号分隔）', max_length=300, null=True, verbose_name='代表构成'),
        ),
        migrations.AddIndex(
            model_name='representative',
            index=models.Index(fields=['composition'], name='representat_composi_5c8307_idx'),
        ),
    ]
