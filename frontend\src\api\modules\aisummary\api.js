/**
 * AI总结API请求方法
 * 包含年度履职AI分析和成果展示相关接口
 */
import request from '../../http/client'
import { AI_SUMMARY_CONFIG } from './config'

/**
 * AI总结相关API端点
 */
const AI_SUMMARY_ENDPOINTS = {
  GENERATE: '/ai-summaries/generate/',
  DETAIL: '/ai-summaries/{year}/',
  CHECK_DATA: '/ai-summaries/{year}/check/',
  LIST: '/ai-summaries/'
}

/**
 * AI总结相关API
 */
export const aiSummaryAPI = {
  /**
   * 生成AI总结（支持首次生成和重新生成）
   * @param {Object} data - 生成参数
   * @param {number} data.analysis_year - 分析年份
   * @param {boolean} data.force_regenerate - 是否强制重新生成（默认false）
   * @returns {Promise} 生成结果
   */
  generate(data) {
    return request({
      url: AI_SUMMARY_ENDPOINTS.GENERATE,
      method: 'post',
      data,
      timeout: AI_SUMMARY_CONFIG.REQUEST_TIMEOUT, // 使用配置的超时时间
      showLoading: false, // 关闭自动loading，使用页面自定义loading组件
      loadingText: data.force_regenerate ? 'AI正在重新生成履职分析...' : 'AI正在生成履职分析...'
    })
  },

  /**
   * 获取AI总结详情
   * @param {number} year - 分析年份
   * @param {Object} params - 查询参数
   * @param {number} params.representative_id - 代表ID（工作人员查询时使用）
   * @returns {Promise} 总结详情
   */
  getDetail(year, params = {}) {
    return request({
      url: AI_SUMMARY_ENDPOINTS.DETAIL.replace('{year}', year),
      method: 'get',
      params,
      showLoading: false
    })
  },

  /**
   * 检查数据可用性
   * @param {number} year - 分析年份
   * @returns {Promise} 数据检查结果
   */
  checkData(year) {
    return request({
      url: AI_SUMMARY_ENDPOINTS.CHECK_DATA.replace('{year}', year),
      method: 'get',
      showLoading: false
    })
  },

  /**
   * 获取AI总结列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.page_size - 每页数量
   * @param {number} params.year - 筛选年份
   * @param {string} params.status - 筛选状态
   * @param {number} params.representative_id - 筛选代表ID（工作人员使用）
   * @returns {Promise} 总结列表
   */
  getList(params = {}) {
    return request({
      url: AI_SUMMARY_ENDPOINTS.LIST,
      method: 'get',
      params,
      showLoading: false
    })
  },

  /**
   * 轮询检查生成状态
   * @param {number} year - 分析年份
   * @param {number} maxAttempts - 最大尝试次数
   * @param {number} interval - 轮询间隔（毫秒）
   * @returns {Promise} 最终状态
   */
  async pollGenerationStatus(year, maxAttempts = AI_SUMMARY_CONFIG.POLLING.MAX_ATTEMPTS, interval = AI_SUMMARY_CONFIG.POLLING.INTERVAL) {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const response = await this.getDetail(year)
        const status = response.data.status
        
        console.log(`[AI总结轮询] 第${attempt + 1}次检查，状态: ${status}`)
        
        // 如果已完成或失败，返回结果
        if (status === 'completed' || status === 'failed') {
          return response.data
        }
        
        // 等待下次轮询
        if (attempt < maxAttempts - 1) {
          await new Promise(resolve => setTimeout(resolve, interval))
        }
      } catch (error) {
        console.error(`[AI总结轮询] 第${attempt + 1}次检查失败:`, error)
        
        // 如果是最后一次尝试，抛出错误
        if (attempt === maxAttempts - 1) {
          throw error
        }
        
        // 等待下次轮询
        await new Promise(resolve => setTimeout(resolve, interval))
      }
    }
    
    throw new Error(AI_SUMMARY_CONFIG.ERROR_MESSAGES.POLLING_TIMEOUT)
  }
}

/**
 * AI总结工具函数
 */
export const aiSummaryUtils = {
  /**
   * 格式化状态显示
   * @param {string} status - 状态值
   * @returns {Object} 格式化的状态信息
   */
  formatStatus(status) {
    const statusMap = {
      'pending': { text: '待生成', color: 'info', icon: 'Clock' },
      'generating': { text: '生成中', color: 'warning', icon: 'Loading' },
      'completed': { text: '已完成', color: 'success', icon: 'Check' },
      'failed': { text: '生成失败', color: 'danger', icon: 'Close' }
    }
    
    return statusMap[status] || { text: '未知状态', color: 'info', icon: 'Question' }
  },

  /**
   * 获取可用的分析年份列表
   * @param {number} yearRange - 年份范围（向前几年）
   * @returns {Array} 年份列表
   */
  getAvailableYears(yearRange = 5) {
    const currentYear = new Date().getFullYear()
    const years = []
    
    for (let i = 0; i < yearRange; i++) {
      years.push(currentYear - i)
    }
    
    return years
  },

  /**
   * 验证年份有效性
   * @param {number} year - 年份
   * @returns {boolean} 是否有效
   */
  isValidYear(year) {
    const currentYear = new Date().getFullYear()
    return year >= 2020 && year <= currentYear
  },

  /**
   * 生成AI总结摘要文本
   * @param {Object} summary - AI总结数据
   * @returns {string} 摘要文本
   */
  generateSummaryText(summary) {
    if (!summary || !summary.ai_result_data) {
      return '暂无AI分析结果'
    }
    
    const data = summary.ai_result_data
    const overview = data.overview || {}
    const coreMetrics = data.coreMetrics || []
    
    let text = overview.subtitle || '年度履职总结'
    
    if (coreMetrics.length > 0) {
      const activities = coreMetrics.find(m => m.label === '履职活动')
      const suggestions = coreMetrics.find(m => m.label === '建议提案')
      
      if (activities || suggestions) {
        text += '，'
        if (activities) text += `完成履职活动${activities.value}`
        if (activities && suggestions) text += '，'
        if (suggestions) text += `提交建议提案${suggestions.value}`
      }
    }
    
    return text
  }
}

/**
 * 导出默认对象（兼容旧的导入方式）
 */
export default {
  aiSummaryAPI,
  aiSummaryUtils
}
