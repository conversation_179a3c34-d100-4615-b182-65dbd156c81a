<template>
  <div class="notification-icon">
    <el-dropdown 
      trigger="click" 
      placement="bottom-end"
      :hide-on-click="false"
      @visible-change="handleDropdownVisibleChange"
    >
      <div class="notification-trigger">
        <el-badge 
          :value="unreadCount" 
          :hidden="!hasUnread"
          :max="99"
          type="danger"
        >
          <el-icon :size="20" class="notification-bell">
            <Bell />
          </el-icon>
        </el-badge>
      </div>
      
      <template #dropdown>
        <div class="notification-dropdown">
          <!-- 头部 -->
          <div class="dropdown-header">
            <div class="header-title">
              <span>通知消息</span>
              <el-tag v-if="hasUnread" type="danger" size="small" round>
                {{ unreadCount }}条未读
              </el-tag>
            </div>
            <div class="header-actions">
              <el-button 
                text 
                size="small" 
                @click="handleMarkAllAsRead"
                :disabled="!hasUnread"
              >
                全部已读
              </el-button>
              <el-button 
                text 
                size="small" 
                @click="goToNotificationCenter"
              >
                查看全部
              </el-button>
            </div>
          </div>

          <!-- 通知列表 -->
          <div class="dropdown-content">
            <div v-if="loading" class="loading-state">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>正在加载...</span>
            </div>

            <div v-else-if="recentNotifications.length === 0" class="empty-state">
              <el-empty 
                :image-size="60" 
                description="暂无通知"
              />
            </div>

            <div v-else class="notification-list">
              <div 
                v-for="notification in recentNotifications" 
                :key="notification.id"
                class="notification-item"
                :class="{ 'unread': !notification.isRead }"
                @click="handleNotificationClick(notification)"
              >
                <div class="item-icon">
                  <el-icon 
                    :style="{ color: getNotificationTypeConfig(notification.type).color }"
                    :size="16"
                  >
                    <component :is="getNotificationTypeConfig(notification.type).icon" />
                  </el-icon>
                </div>
                
                <div class="item-content">
                  <div class="item-title">
                    {{ notification.title }}
                    <el-tag 
                      v-if="!notification.isRead" 
                      type="danger" 
                      size="small"
                      round
                    >
                      新
                    </el-tag>
                  </div>
                  <div class="item-summary">
                    {{ truncateContent(notification.content, 50) }}
                  </div>
                  <div class="item-time">
                    {{ formatTime(notification.createdAt) }}
                  </div>
                </div>

                <div class="item-actions">
                  <el-button 
                    text 
                    size="small"
                    @click.stop="handleMarkAsRead(notification)"
                    v-if="!notification.isRead"
                  >
                    <el-icon><Check /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部 -->
          <div class="dropdown-footer" v-if="recentNotifications.length > 0">
            <el-button 
              text 
              type="primary" 
              @click="goToNotificationCenter"
              class="view-all-btn"
            >
              查看全部通知
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Bell, Loading, Check, ArrowRight,
  Document, Finished, Setting, DataAnalysis
} from '@element-plus/icons-vue'
import { useNotificationStore } from '@/stores/notification'
import { useUserStore } from '@/stores/user'
import { getNotificationTypes } from '@/api/notification'
import { useRouter } from 'vue-router'

const router = useRouter()
const notificationStore = useNotificationStore()
const userStore = useUserStore()

// 本地状态
const loading = ref(false)
const recentNotifications = ref([])
const dropdownVisible = ref(false)
const notificationTypes = getNotificationTypes()

// 计算属性
const unreadCount = computed(() => notificationStore.unreadCount)
const hasUnread = computed(() => notificationStore.hasUnread)

// 定时刷新间隔（5分钟）
let refreshTimer = null

// 获取通知类型配置
const getNotificationTypeConfig = (type) => {
  return notificationTypes[type] || {
    title: '未知类型',
    icon: 'Bell',
    color: '#909399'
  }
}

// 截断内容
const truncateContent = (content, maxLength) => {
  if (!content) return ''
  return content.length > maxLength 
    ? content.substring(0, maxLength) + '...' 
    : content
}

// 格式化时间
const formatTime = (time) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  
  // 1分钟内
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  // 1小时内
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`
  }
  // 24小时内
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
  }
  // 7天内
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
  }
  
  return date.toLocaleDateString('zh-CN', {
    month: 'numeric',
    day: 'numeric'
  })
}

// 获取最近通知
const fetchRecentNotifications = async () => {
  loading.value = true
  try {
    const response = await notificationStore.fetchNotifications(userStore.userInfo.id, true)
    // 只取前5条最新通知
    recentNotifications.value = notificationStore.notifications.slice(0, 5)
  } catch (error) {
    console.error('获取通知失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取未读数量
const fetchUnreadCount = async () => {
  try {
    await notificationStore.fetchUnreadCount(userStore.userInfo.id)
  } catch (error) {
    console.error('获取未读数量失败:', error)
  }
}

// 下拉面板显示/隐藏事件
const handleDropdownVisibleChange = (visible) => {
  dropdownVisible.value = visible
  if (visible) {
    // 打开时获取最新通知
    fetchRecentNotifications()
  }
}

// 标记单个通知为已读
const handleMarkAsRead = async (notification) => {
  const success = await notificationStore.markNotificationAsRead(notification.id)
  if (success) {
    // 更新本地数据
    notification.isRead = true
    // 刷新未读数量
    fetchUnreadCount()
  }
}

// 全部标记为已读
const handleMarkAllAsRead = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要将所有通知标记为已读吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = await notificationStore.markAllNotificationsAsRead(userStore.userInfo.id)
    if (success) {
      // 更新本地数据
      recentNotifications.value.forEach(n => n.isRead = true)
      // 刷新未读数量
      fetchUnreadCount()
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 点击通知项
const handleNotificationClick = async (notification) => {
  // 标记为已读
  if (!notification.isRead) {
    await handleMarkAsRead(notification)
  }
  
  // 关闭下拉面板
  dropdownVisible.value = false
  
  // 根据通知类型跳转到相关页面
  if (notification.relatedId) {
    switch (notification.type) {
      case 'opinion_submit':
      case 'opinion_audit':
      case 'opinion_result':
        // 跳转到意见详情
        if (userStore.isRepresentative) {
          router.push('/representative/opinions')
        } else {
          router.push('/staff/review')
        }
        break
      case 'mediation_submit':
      case 'mediation_analysis':
        // 跳转到调解案件
        if (userStore.isRepresentative) {
          router.push('/representative/mediation-cases')
        } else {
          router.push('/staff/mediation-cases')
        }
        break
      default:
        // 其他类型跳转到通知中心
        goToNotificationCenter()
        break
    }
  } else {
    // 无关联业务，跳转到通知中心
    goToNotificationCenter()
  }
}

// 跳转到通知中心
const goToNotificationCenter = () => {
  dropdownVisible.value = false
  router.push('/notifications')
}

// 启动定时刷新
const startRefreshTimer = () => {
  refreshTimer = setInterval(() => {
    fetchUnreadCount()
    // 如果下拉面板是打开的，也刷新最近通知
    if (dropdownVisible.value) {
      fetchRecentNotifications()
    }
  }, 5 * 60 * 1000) // 5分钟
}

// 停止定时刷新
const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 页面可见性变化处理
const handleVisibilityChange = () => {
  if (document.hidden) {
    stopRefreshTimer()
  } else {
    fetchUnreadCount()
    startRefreshTimer()
  }
}

// 初始化
onMounted(() => {
  fetchUnreadCount()
  startRefreshTimer()
  
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

// 清理
onUnmounted(() => {
  stopRefreshTimer()
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})

// 暴露方法给父组件
defineExpose({
  refresh: () => {
    fetchUnreadCount()
    if (dropdownVisible.value) {
      fetchRecentNotifications()
    }
  }
})
</script>

<style scoped>
.notification-icon {
  position: relative;
}

.notification-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.notification-trigger:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.notification-bell {
  color: #ffffff;
  transition: color 0.3s;
}

.notification-bell:hover {
  color: #ffd04b;
}

.notification-dropdown {
  width: 360px;
  max-height: 480px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.dropdown-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.header-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  font-weight: 500;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.dropdown-content {
  max-height: 320px;
  overflow-y: auto;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px 20px;
  color: #909399;
}

.empty-state {
  padding: 20px;
}

.notification-list {
  padding: 8px 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  border-bottom: 1px solid #f5f7fa;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #fef7e6;
  border-left: 3px solid #e6a23c;
}

.item-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.4;
}

.item-summary {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 4px;
  word-break: break-word;
}

.item-time {
  font-size: 11px;
  color: #909399;
}

.item-actions {
  margin-left: 8px;
  margin-top: 2px;
}

.dropdown-footer {
  padding: 12px 16px;
  border-top: 1px solid #e4e7ed;
  background: #f8f9fa;
  text-align: center;
}

.view-all-btn {
  width: 100%;
  justify-content: center;
}

/* 滚动条样式 */
.dropdown-content::-webkit-scrollbar {
  width: 4px;
}

.dropdown-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dropdown-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.dropdown-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-dropdown {
    width: 320px;
    max-height: 400px;
  }
  
  .dropdown-content {
    max-height: 260px;
  }
  
  .notification-item {
    padding: 10px 12px;
  }
  
  .item-title {
    font-size: 13px;
  }
  
  .item-summary {
    font-size: 11px;
  }
}
</style> 