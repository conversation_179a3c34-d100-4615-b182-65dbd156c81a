<template>
  <div class="review-container">
    <div class="page-header">
      <h2>意见建议审核</h2>
      <p>审核人大代表提交的群众意见建议</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-container">
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.submitted }}</div>
              <div class="stat-label">待审核</div>
            </div>
            <el-icon class="stat-icon" style="color: #e6a23c;"><Clock /></el-icon>
          </el-card>
        </div>
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.approved }}</div>
              <div class="stat-label">已通过待转交</div>
            </div>
            <el-icon class="stat-icon" style="color: #67c23a;"><CircleCheck /></el-icon>
          </el-card>
        </div>
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.transferred }}</div>
              <div class="stat-label">已转交</div>
            </div>
            <el-icon class="stat-icon" style="color: #409eff;"><Share /></el-icon>
          </el-card>
        </div>
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.inProgress }}</div>
              <div class="stat-label">处理中</div>
            </div>
            <el-icon class="stat-icon" style="color: #f56c6c;"><Loading /></el-icon>
          </el-card>
        </div>
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.completed }}</div>
              <div class="stat-label">已办结</div>
            </div>
            <el-icon class="stat-icon" style="color: #67c23a;"><CircleCheck /></el-icon>
          </el-card>
        </div>

      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-card>
        <el-form :model="filterForm" inline>
          <el-form-item label="状态筛选">
            <el-select v-model="filterForm.status" placeholder="全部状态" style="width: 150px;" @change="loadOpinions">
              <el-option label="全部状态" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="待审核" value="submitted" />
              <el-option label="审核通过" value="approved" />
              <el-option label="需要修改" value="rejected" />
              <el-option label="已转交" value="transferred" />
              <el-option label="处理中" value="in_progress" />
              <el-option label="已办结" value="completed" />
            </el-select>
          </el-form-item>
          <el-form-item label="分类筛选">
            <el-select v-model="filterForm.category" placeholder="全部分类" style="width: 150px;" @change="loadOpinions">
              <el-option label="全部分类" value="" />
              <el-option label="城建环保" value="urban_construction" />
              <el-option label="交通出行" value="transportation" />
              <el-option label="教育文化" value="education" />
              <el-option label="医疗卫生" value="healthcare" />
              <el-option label="社会保障" value="social_security" />
              <el-option label="经济发展" value="economic" />
              <el-option label="政务服务" value="government_service" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="提交人">
            <el-input 
              v-model="filterForm.submitter" 
              placeholder="输入代表姓名" 
              style="width: 150px;"
              @input="loadOpinions"
              clearable
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 意见列表 -->
    <el-card>
      <el-table :data="opinionsList" v-loading="loading" @row-click="viewOpinion">
        <el-table-column prop="title" label="意见建议标题" min-width="220" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            <el-tag :type="getCategoryColor(row.category)">{{ row.category }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="submitterName" label="提交代表" width="130" />
        <el-table-column prop="submitDate" label="提交时间" width="170" />
        <el-table-column prop="lastUpdateDate" label="最后更新" width="170">
          <template #default="{ row }">
            <span>{{ row.lastUpdateDate || row.submitDate }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">{{ getStatusText(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <div class="table-action-buttons">
              <el-button size="small" @click.stop="viewOpinion(row)">
                查看
              </el-button>
              <el-button 
                v-if="row.status === 'submitted'" 
                size="small" 
                type="primary" 
                @click.stop="reviewOpinion(row)"
              >
                审核
              </el-button>
              <el-button 
                v-if="row.status === 'approved'" 
                size="small" 
                type="success" 
                @click.stop="transferOpinion(row)"
              >
                转交
              </el-button>
              <el-button 
                v-if="row.status === 'transferred' || row.status === 'in_progress'" 
                size="small" 
                type="warning" 
                @click.stop="updateProgress(row)"
              >
                更新进度
              </el-button>
              <el-button 
                v-if="row.status === 'transferred' || row.status === 'in_progress'" 
                size="small" 
                type="success" 
                @click.stop="closeOpinion(row)"
              >
                办结
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="意见详情"
      width="900px"
    >
      <div v-if="currentOpinion" class="opinion-detail">
        <el-row :gutter="20">
          <el-col :span="16">
            <div class="detail-section">
              <h3>基本信息</h3>
              <div class="detail-grid">
                <div class="detail-item">
                  <span class="label">意见标题：</span>
                  <span class="value">{{ currentOpinion.title }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">意见分类：</span>
                  <el-tag :type="getCategoryColor(currentOpinion.category)">{{ currentOpinion.category_display || currentOpinion.category }}</el-tag>
                </div>
                <div class="detail-item">
                  <span class="label">提交代表：</span>
                  <span class="value">{{ currentOpinion.representative_info?.name || '未知' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">反映人：</span>
                  <span class="value">{{ currentOpinion.reporter_name }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">提交时间：</span>
                  <span class="value">{{ formatDateTime(currentOpinion.created_at) }}</span>
                </div>
                <div v-if="currentOpinion.latest_review?.transferred_department" class="detail-item">
                  <span class="label">转交部门：</span>
                  <span class="value">{{ currentOpinion.latest_review.transferred_department }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">最后更新：</span>
                  <span class="value">{{ formatDateTime(currentOpinion.last_updated_time) }}</span>
                </div>
              </div>
            </div>

            <div class="detail-section">
              <h3>意见建议内容</h3>
              <div class="content-box">{{ currentOpinion.original_content }}</div>
            </div>

            <div v-if="currentOpinion.ai_generated_content" class="detail-section">
              <h3>AI生成内容</h3>
              <div class="ai-suggestion">{{ currentOpinion.ai_generated_content }}</div>
            </div>

            <div v-if="currentOpinion.final_suggestion && currentOpinion.final_suggestion !== currentOpinion.original_content" class="detail-section">
              <h3>代表最终意见建议</h3>
              <div class="content-box">{{ currentOpinion.final_suggestion }}</div>
            </div>
            
            <!-- 处理流程 -->
            <div class="detail-section">
              <h3>处理流程</h3>
              <el-timeline>
                <el-timeline-item
                  v-for="step in (currentOpinion.review_history || [])"
                  :key="step.id"
                  :timestamp="formatDateTime(step.action_time)"
                  :type="getTimelineType(step.status)"
                >
                  <div class="timeline-content">
                    <div class="timeline-title">{{ step.action_display }}</div>
                    <div v-if="step.operator_name" class="reviewer-info">
                      操作人：{{ step.operator_name }}
                    </div>
                    <div v-if="step.review_comment" class="review-comment">
                      <strong>{{ getCommentLabel(step.action) }}：</strong>{{ step.review_comment }}
                    </div>
                    <div v-if="step.processing_result" class="review-comment">
                      <strong>处理结果：</strong>{{ step.processing_result }}
                    </div>
                    <div v-if="step.transferred_department" class="review-comment">
                      <strong>转交部门：</strong>{{ step.transferred_department }}
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="action-panel">
              <h3>操作面板</h3>
              <div v-if="currentOpinion.current_status === 'submitted'" class="action-buttons">
                <el-button type="success" @click="approveOpinion" block>
                  <el-icon><Check /></el-icon>
                  审核通过
                </el-button>
                <el-button type="warning" @click="showRejectDialog" block>
                  <el-icon><Close /></el-icon>
                  需要修改
                </el-button>
              </div>
              
              <div v-if="currentOpinion.current_status === 'approved'" class="action-buttons">
                <el-button type="primary" @click="showTransferDialog" block>
                  <el-icon><Share /></el-icon>
                  转交部门
                </el-button>
              </div>

              <div class="status-info">
                <h4>当前状态</h4>
                <el-tag :type="getStatusColor(currentOpinion.current_status)" size="large">
                  {{ currentOpinion.current_status_display || getStatusText(currentOpinion.current_status) }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="reviewDialogVisible"
      title="意见建议审核"
      width="600px"
    >
      <el-form
        ref="reviewFormRef"
        :model="reviewForm"
        :rules="reviewRules"
        label-width="120px"
      >
        <el-form-item label="审核结果" prop="result">
          <el-radio-group v-model="reviewForm.result">
            <el-radio value="approved">审核通过</el-radio>
            <el-radio value="rejected">需要修改</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="审核意见" prop="reviewComment">
          <el-input
            v-model="reviewForm.reviewComment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见或修改建议"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="reviewDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitReview">提交审核</el-button>
      </template>
    </el-dialog>

    <!-- 转交对话框 -->
    <el-dialog
      v-model="transferDialogVisible"
      title="转交部门"
      width="500px"
    >
      <el-form
        ref="transferFormRef"
        :model="transferForm"
        :rules="transferRules"
        label-width="100px"
      >
        <el-form-item label="转交部门" prop="department">
          <el-select v-model="transferForm.department" placeholder="请选择转交部门" style="width: 100%">
            <el-option label="城管局" value="城管局" />
            <el-option label="交通局" value="交通局" />
            <el-option label="环保局" value="环保局" />
            <el-option label="教育局" value="教育局" />
            <el-option label="卫健委" value="卫健委" />
            <el-option label="住建局" value="住建局" />
            <el-option label="民政局" value="民政局" />
            <el-option label="公安局" value="公安局" />
            <el-option label="其他部门" value="其他部门" />
          </el-select>
        </el-form-item>

        <el-form-item label="转交说明" prop="transferNote">
          <el-input
            v-model="transferForm.transferNote"
            type="textarea"
            :rows="3"
            placeholder="请输入转交说明"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>


      </el-form>

      <template #footer>
        <el-button @click="transferDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitTransfer">确认转交</el-button>
      </template>
    </el-dialog>

    <!-- 进度更新对话框 -->
    <el-dialog
      v-model="progressDialogVisible"
      title="更新处理进度"
      width="600px"
    >
      <el-form
        ref="progressFormRef"
        :model="progressForm"
        :rules="progressRules"
        label-width="100px"
      >
        <el-form-item label="处理进度" prop="progress">
          <el-input
            v-model="progressForm.progress"
            type="textarea"
            :rows="4"
            placeholder="请输入最新的处理进度"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="progressDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitProgress">确认更新</el-button>
      </template>
    </el-dialog>

    <!-- 办结对话框 -->
    <el-dialog
      v-model="closeDialogVisible"
      title="办结意见建议"
      width="600px"
    >
      <el-form
        ref="closeFormRef"
        :model="closeForm"
        :rules="closeRules"
        label-width="100px"
      >
        <el-form-item label="办结结果" prop="result">
          <el-input
            v-model="closeForm.result"
            type="textarea"
            :rows="4"
            placeholder="请输入办结结果说明"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="closeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitClose">确认办结</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Clock, CircleCheck, Share, Check, Close, Warning, Loading } from '@element-plus/icons-vue'
import { opinionAPI } from '@/api/modules/opinion'

const route = useRoute()

// 数据状态
const loading = ref(false)
const opinionsList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 统计数据
const stats = ref({
  submitted: 0,
  approved: 0,
  transferred: 0,
  inProgress: 0,
  completed: 0
})

// 筛选条件
const filterForm = reactive({
  status: '',
  category: '',
  submitter: ''
})

// 对话框状态
const viewDialogVisible = ref(false)
const reviewDialogVisible = ref(false)
const transferDialogVisible = ref(false)
const progressDialogVisible = ref(false)
const closeDialogVisible = ref(false)

// 表单引用
const reviewFormRef = ref()
const transferFormRef = ref()
const progressFormRef = ref()
const closeFormRef = ref()

// 当前意见
const currentOpinion = ref(null)

// 审核表单
const reviewForm = reactive({
  result: 'approved',
  reviewComment: ''
})

// 转交表单
const transferForm = reactive({
  department: '',
  transferNote: ''
})

// 进度更新表单
const progressForm = reactive({
  progress: ''
})

// 办结表单
const closeForm = reactive({
  result: ''
})

// 表单验证规则
const reviewRules = {
  result: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  reviewComment: [
    { required: true, message: '请输入审核意见', trigger: 'blur' }
  ]
}

const transferRules = {
  department: [
    { required: true, message: '请选择转交部门', trigger: 'change' }
  ],
  transferNote: [
    { required: true, message: '请输入转交说明', trigger: 'blur' }
  ]
}

const progressRules = {
  progress: [
    { required: true, message: '请输入处理进度', trigger: 'blur' }
  ]
}

const closeRules = {
  result: [
    { required: true, message: '请输入办结结果', trigger: 'blur' }
  ]
}

// 获取分类颜色
const getCategoryColor = (category) => {
  const colorMap = {
    'urban_construction': 'success',
    'transportation': 'warning',
    'education': 'primary',
    'healthcare': 'danger',
    'social_security': 'info',
    'economic': 'success',
    'government_service': 'primary',
    'other': 'info',
    // 兼容显示名称
    '城建环保': 'success',
    '交通出行': 'warning',
    '教育文化': 'primary',
    '医疗卫生': 'danger',
    '社会保障': 'info',
    '经济发展': 'success',
    '政务服务': 'primary',
    '其他': 'info'
  }
  return colorMap[category] || ''
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'draft': 'info',
    'submitted': 'warning',
    'under_review': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'transferred': 'primary',
    'in_progress': 'info',
    'completed': 'success'
  }
  return colorMap[status] || ''
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'draft': '草稿',
    'submitted': '待审核',
    'under_review': '待审核',
    'approved': '审核通过',
    'rejected': '需要修改',
    'transferred': '已转交',
    'in_progress': '处理中',
    'completed': '已办结'
  }
  return textMap[status] || status
}

// 获取处理流程步骤
const getProcessSteps = (opinion) => {
  const steps = []
  
  // 如果有完整的审核历史，使用审核历史
  if (opinion.review_history && opinion.review_history.length > 0) {
    opinion.review_history.forEach((review) => {
      steps.push({
        id: `review-${review.id}`,
        timestamp: formatDateTime(review.action_time),
        description: getActionDescription(review.action, review.status, review),
        type: getStepType(review.status),
        reviewer: review.operator_name || (review.reviewer_info ? review.reviewer_info.name : '系统'),
        comment: getReviewComment(review),
        action: review.action
      })
    })
  } else {
    // 构建基础步骤
    steps.push({
      id: 'create',
      timestamp: formatDateTime(opinion.created_at || opinion.submitDate),
      description: '创建意见草稿',
      type: 'primary',
      action: 'create'
    })
    
    if (opinion.latest_review && opinion.latest_review.id) {
      const review = opinion.latest_review
      steps.push({
        id: `latest-${review.id}`,
        timestamp: formatDateTime(review.action_time),
        description: getActionDescription(review.action, review.status, review),
        type: getStepType(review.status),
        reviewer: review.operator_name || (review.reviewer_info ? review.reviewer_info.name : '系统'),
        comment: getReviewComment(review),
        action: review.action
      })
    }
  }
  
  return steps
}

// 格式化时间
const formatDateTime = (timeStr) => {
  if (!timeStr) return ''
  try {
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return timeStr
  }
}

// 获取操作描述
const getActionDescription = (action, status, review) => {
  const actionMap = {
    'create': '创建意见草稿',
    'submit': '代表提交意见建议',
    'approve': '工作人员审核通过',
    'reject': '工作人员审核驳回',
    'transfer': `转交至${review.transferred_department || '相关部门'}`,
    'update_progress': '更新处理进展',
    'close': '标记办结'
  }
  return actionMap[action] || `${action} - ${status}`
}

// 获取步骤类型
const getStepType = (status) => {
  const typeMap = {
    'draft': 'info',
    'submitted': 'primary',
    'under_review': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'transferred': 'primary',
    'in_progress': 'info',
    'completed': 'success'
  }
  return typeMap[status] || 'info'
}

// 获取审核备注
const getReviewComment = (review) => {
  if (review.review_comment) return review.review_comment
  if (review.processing_result) return review.processing_result
  return ''
}

// 获取时间线类型
const getTimelineType = (status) => {
  const typeMap = {
    'draft': 'info',
    'submitted': 'primary',
    'approved': 'success',
    'rejected': 'danger',
    'transferred': 'warning',
    'in_progress': 'primary',
    'completed': 'success'
  }
  return typeMap[status] || 'info'
}

// 获取备注标签
const getCommentLabel = (action) => {
  const labelMap = {
    'create': '创建说明',
    'submit': '提交说明',
    'approve': '审核意见',
    'reject': '驳回理由',
    'transfer': '转交说明',
    'update_progress': '处理进展',
    'close': '办结结果'
  }
  return labelMap[action] || '说明'
}

// 查看意见详情
const viewOpinion = async (row) => {
  try {
    console.log('查看意见详情，ID:', row.id)
    
    // 调用详情API获取完整数据，包括审核历史
    const response = await opinionAPI.getOpinionDetail(row.id)
    
    if (response.data && response.data.success) {
      currentOpinion.value = response.data.data
      console.log('获取意见详情成功:', currentOpinion.value)
    } else {
      console.error('获取意见详情失败:', response.data?.message)
      currentOpinion.value = row // 降级使用行数据
      ElMessage.warning('获取详细信息失败，使用基础信息显示')
    }
  } catch (error) {
    console.error('获取意见详情异常:', error)
    currentOpinion.value = row // 降级使用行数据
    ElMessage.warning('获取详细信息失败，使用基础信息显示')
  }
  
  viewDialogVisible.value = true
}

// 审核意见
const reviewOpinion = (row) => {
  currentOpinion.value = row
  Object.assign(reviewForm, {
    result: 'approved',
    reviewComment: ''
  })
  reviewDialogVisible.value = true
}

// 批准意见
const approveOpinion = () => {
  Object.assign(reviewForm, {
    result: 'approved',
    reviewComment: ''
  })
  reviewDialogVisible.value = true
}

// 显示驳回对话框
const showRejectDialog = () => {
  Object.assign(reviewForm, {
    result: 'rejected',
    reviewComment: ''
  })
  reviewDialogVisible.value = true
}

// 显示转交对话框
const showTransferDialog = () => {
  Object.assign(transferForm, {
    department: '',
    transferNote: ''
  })
  transferDialogVisible.value = true
}

// 转交意见
const transferOpinion = (row) => {
  currentOpinion.value = row
  showTransferDialog()
}

// 更新进度
const updateProgress = (row) => {
  currentOpinion.value = row
  Object.assign(progressForm, {
    progress: ''
  })
  progressDialogVisible.value = true
}

// 办结意见
const closeOpinion = (row) => {
  currentOpinion.value = row
  Object.assign(closeForm, {
    result: ''
  })
  closeDialogVisible.value = true
}

// 提交审核
const submitReview = async () => {
  if (!reviewFormRef.value) return
  
  const valid = await reviewFormRef.value.validate().catch(() => false)
  if (!valid) return

  try {
    // 执行审核操作
    const reviewData = {
      action: reviewForm.result === 'approved' ? 'approve' : 'reject',
      review_comment: reviewForm.reviewComment
    }
    
    console.log('发送审核请求:', reviewData)
    const response = await opinionAPI.reviewOpinion(currentOpinion.value.id, reviewData)
    console.log('审核响应:', response)
    
    if (response.data && response.data.success) {
      ElMessage.success('审核完成')
      
      reviewDialogVisible.value = false
      viewDialogVisible.value = false
      
      // 重新加载数据
      console.log('重新加载意见列表...')
      await loadOpinions()
      await loadStatistics()
      console.log('数据重新加载完成')
    } else {
      const errorMsg = response.data?.message || '审核失败'
      console.error('审核失败:', errorMsg, response)
      ElMessage.error(errorMsg)
    }
  } catch (error) {
    console.error('审核失败:', error)
    ElMessage.error('审核失败，请重试')
  }
}

// 提交转交
const submitTransfer = async () => {
  if (!transferFormRef.value) return
  
  const valid = await transferFormRef.value.validate().catch(() => false)
  if (!valid) return

  try {
    // 构建转交数据
    const transferData = {
      action: 'transfer',
      transferred_department: transferForm.department,
      review_comment: transferForm.transferNote
    }
    
    // 调用审核API进行转交
    const response = await opinionAPI.reviewOpinion(currentOpinion.value.id, transferData)
    
    if (response.data.success) {
      ElMessage.success('转交成功')
      transferDialogVisible.value = false
      viewDialogVisible.value = false
      
      // 重新加载数据
      await loadOpinions()
      await loadStatistics()
    } else {
      ElMessage.error(response.data.message || '转交失败')
    }
  } catch (error) {
    console.error('转交失败:', error)
    ElMessage.error('转交失败，请重试')
  }
}

// 提交进度更新
const submitProgress = async () => {
  if (!progressFormRef.value) return
  
  const valid = await progressFormRef.value.validate().catch(() => false)
  if (!valid) return

  try {
    // 构建进度更新数据
    const progressData = {
      action: 'update_progress',
      processing_result: progressForm.progress
    }
    
    // 调用审核API进行进度更新
    const response = await opinionAPI.reviewOpinion(currentOpinion.value.id, progressData)
    
    if (response.data.success) {
      ElMessage.success('进度更新成功')
      progressDialogVisible.value = false
      viewDialogVisible.value = false
      
      // 重新加载数据
      await loadOpinions()
      await loadStatistics()
    } else {
      ElMessage.error(response.data.message || '进度更新失败')
    }
  } catch (error) {
    console.error('进度更新失败:', error)
    ElMessage.error('进度更新失败，请重试')
  }
}

// 提交办结
const submitClose = async () => {
  if (!closeFormRef.value) return
  
  const valid = await closeFormRef.value.validate().catch(() => false)
  if (!valid) return

  try {
    // 构建办结数据
    const closeData = {
      action: 'close',
      processing_result: closeForm.result
    }
    
    // 调用审核API进行办结
    const response = await opinionAPI.reviewOpinion(currentOpinion.value.id, closeData)
    
    if (response.data.success) {
      ElMessage.success('办结成功')
      closeDialogVisible.value = false
      viewDialogVisible.value = false
      
      // 重新加载数据
      await loadOpinions()
      await loadStatistics()
    } else {
      ElMessage.error(response.data.message || '办结失败')
    }
  } catch (error) {
    console.error('办结失败:', error)
    ElMessage.error('办结失败，请重试')
  }
}



// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  loadOpinions()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadOpinions()
}

// 加载意见列表
const loadOpinions = async () => {
  loading.value = true
  
  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ordering: '-created_at'
    }
    
    // 添加筛选条件
    if (filterForm.status) {
      params.status = filterForm.status
    }
    if (filterForm.category) {
      params.category = filterForm.category
    }
    if (filterForm.submitter) {
      params.search = filterForm.submitter
    }
    
    console.log('加载意见列表，查询参数:', params)
    
    // 调用API
    const response = await opinionAPI.getReviewList(params)
    console.log('意见列表响应:', response)
    
    if (response.data.success) {
      const data = response.data.data
      
      // 转换数据格式以适配现有UI
      opinionsList.value = data.results.map(item => ({
        id: item.id,
        title: item.title,
        category: item.category_display || item.category, // 显示用
        categoryValue: item.category, // 原始值用于颜色映射
        submitterName: item.representative_info?.name || '未知代表',
        reflectorInfo: item.reporter_name,
        content: item.original_content,
        aiSuggestion: item.ai_generated_content,
        suggestion: item.final_suggestion,
        status: item.current_status,
        submitDate: formatDateTime(item.created_at),
        reviewDate: item.latest_review?.action_time ? formatDateTime(item.latest_review.action_time) : null,
        reviewer: item.latest_review?.reviewer_info?.name,
        reviewComment: item.latest_review?.review_comment,
        transferDepartment: item.latest_review?.transferred_department,
        transferNote: item.latest_review?.review_comment,
        lastUpdateDate: formatDateTime(item.last_updated_time)
      }))
      
      total.value = data.count
    } else {
      ElMessage.error(response.data.message || '加载失败')
    }
  } catch (error) {
    console.error('加载意见列表失败:', error)
    ElMessage.error('加载失败，请重试')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    // 获取后端统计API数据
    const response = await opinionAPI.getOpinionStatistics()
    
    if (response.data.success) {
      const data = response.data.data
      
      // 根据后端API字段映射统计数据（基于opinion_reviews最新状态）
      stats.value = {
        submitted: data.pending_review_count || 0,        // 待审核数量（最新状态=submitted）
        approved: data.approved_count || 0,               // 审核通过数量（最新状态=approved）
        transferred: data.transferred_count || 0,         // 已转交数量（最新状态=transferred）
        inProgress: data.in_progress_count || 0,          // 处理中数量（最新状态=in_progress）
        completed: data.completed_opinions || 0           // 已办结数量（最新状态=completed）
      }
      
      console.log('从后端API获取的统计数据:', stats.value)
    } else {
      // 后端API失败时，通过分析列表数据计算统计
      await calculateStatsFromList()
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // API失败时，通过分析列表数据计算统计
    await calculateStatsFromList()
  }
}

// 通过分析列表数据计算统计信息
const calculateStatsFromList = async () => {
  try {
    // 获取所有数据来计算统计
    const response = await opinionAPI.getReviewList({ page_size: 1000 })
    
    if (response.data.success) {
      const allOpinions = response.data.data.results || []
      
      // 按状态统计
      const submitted = allOpinions.filter(item => 
        item.status === 'submitted' || item.current_status === 'submitted'
      ).length
      
      const transferred = allOpinions.filter(item => 
        item.status === 'transferred' || item.current_status === 'transferred'
      ).length
      
      const inProgress = allOpinions.filter(item => 
        item.status === 'in_progress' || item.current_status === 'in_progress'
      ).length
      
      const completed = allOpinions.filter(item => 
        item.status === 'completed' || item.current_status === 'completed'
      ).length
      
      const approved = allOpinions.filter(item => 
        item.status === 'approved' || item.current_status === 'approved'
      ).length
      
      stats.value = {
        submitted,
        approved,
        transferred,
        inProgress,
        completed
      }
      
      console.log('从列表数据计算的统计:', stats.value)
    }
  } catch (error) {
    console.error('通过列表计算统计失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 初始化
onMounted(async () => {
  // 如果URL中有id参数，直接打开对应意见
  if (route.query.id) {
    // 这里可以根据id加载特定意见
  }
  
  // 并行加载数据和统计信息
  await Promise.all([
    loadOpinions(),
    loadStatistics()
  ])
})
</script>

<style scoped>
.review-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: var(--china-red);
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  margin: 0;
}

.stats-section {
  margin-bottom: 20px;
}

.stats-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  flex: 1;
  min-width: 200px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 16px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(200, 16, 46, 0.15);
}

.stat-card .el-card__body {
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.stat-content {
  text-align: center;
  margin-bottom: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: var(--china-red);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  white-space: nowrap;
}

.stat-icon {
  font-size: 28px;
  opacity: 0.8;
}

/* 响应式调整 */
@media (min-width: 1200px) {
  .stat-item {
    min-width: 220px;
  }
  
  .stat-card .el-card__body {
    padding: 20px;
  }
  
  .stat-content {
    margin-bottom: 12px;
  }
  
  .stat-number {
    font-size: 28px;
  }
  
  .stat-label {
    font-size: 14px;
    margin-top: 5px;
  }
  
  .stat-icon {
    font-size: 32px;
  }
}

@media (max-width: 992px) {
  .stat-item {
    min-width: 180px;
  }
}

@media (max-width: 768px) {
  .stats-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .stat-item {
    flex: none;
    min-width: auto;
  }
  
  .stat-card {
    margin-bottom: 0;
  }
  
  .stat-card .el-card__body {
    padding: 12px;
  }
  
  .stat-content {
    margin-bottom: 6px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .stat-icon {
    font-size: 24px;
  }
}

@media (max-width: 576px) {
  .stats-container {
    gap: 8px;
  }
  
  .stat-card .el-card__body {
    padding: 10px;
  }
  
  .stat-content {
    margin-bottom: 4px;
  }
}

.filter-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.opinion-detail {
  line-height: 1.6;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  color: var(--china-red);
  margin-bottom: 12px;
  font-size: 16px;
  border-bottom: 2px solid var(--china-red);
  padding-bottom: 4px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  color: var(--text-color);
  min-width: 80px;
  margin-right: 10px;
}

.value {
  flex: 1;
  color: var(--text-color);
}

.content-box {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid var(--china-red);
  line-height: 1.6;
  white-space: pre-wrap;
}

.ai-suggestion {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 15px;
  color: #0369a1;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-wrap; /* 保留换行和空格 */
  word-wrap: break-word; /* 长单词自动换行 */
  overflow-wrap: break-word; /* 现代浏览器的换行处理 */
  max-height: 300px; /* 限制最大高度 */
  overflow-y: auto; /* 内容过多时显示滚动条 */
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
}

/* AI生成内容的滚动条样式 */
.ai-suggestion::-webkit-scrollbar {
  width: 6px;
}

.ai-suggestion::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 3px;
}

.ai-suggestion::-webkit-scrollbar-thumb {
  background: rgba(3, 105, 161, 0.3);
  border-radius: 3px;
}

.ai-suggestion::-webkit-scrollbar-thumb:hover {
  background: rgba(3, 105, 161, 0.5);
}

/* AI生成内容前添加小图标标识 */
.ai-suggestion::before {
  content: "🤖";
  position: absolute;
  top: 8px;
  right: 12px;
  font-size: 16px;
  opacity: 0.6;
}

.action-panel {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  height: fit-content;
}

.action-panel h3 {
  color: var(--china-red);
  margin-bottom: 15px;
  font-size: 16px;
}

.action-buttons {
  margin-bottom: 20px;
}

.action-buttons .el-button {
  margin-bottom: 10px;
}

/* 表格操作按钮对齐 */
.table-action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.table-action-buttons .el-button {
  margin: 0;
}

/* 处理流程时间线样式 */
.timeline-content {
  padding: 4px 0;
}

.timeline-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.reviewer-info {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.review-comment {
  font-size: 13px;
  color: var(--el-text-color-regular);
  background: var(--el-fill-color-extra-light);
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 6px;
  line-height: 1.5;
}

.review-comment strong {
  color: var(--el-text-color-primary);
}

/* 时间线图标颜色优化 */
:deep(.el-timeline-item__node--primary) {
  background-color: var(--el-color-primary);
}

:deep(.el-timeline-item__node--success) {
  background-color: var(--el-color-success);
}

:deep(.el-timeline-item__node--warning) {
  background-color: var(--el-color-warning);
}

:deep(.el-timeline-item__node--danger) {
  background-color: var(--el-color-danger);
}

:deep(.el-timeline-item__node--info) {
  background-color: var(--el-color-info);
}

.status-info {
  text-align: center;
}

.status-info h4 {
  margin-bottom: 10px;
  color: var(--text-color);
}

/* 表格行可点击样式 */
:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: rgba(200, 16, 46, 0.05);
}

/* 防止表格内容换行 */
:deep(.el-table .el-table__cell) {
  white-space: nowrap;
}

/* 标题列特殊处理 - 允许鼠标悬浮查看完整内容 */
:deep(.el-table .el-table__cell:first-child .cell) {
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 时间列确保能够完整显示 */
:deep(.el-table .el-table__cell:nth-child(4) .cell),
:deep(.el-table .el-table__cell:nth-child(5) .cell),
:deep(.el-table .el-table__cell:nth-child(6) .cell) {
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .review-container {
    padding: 10px;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  /* 移动端隐藏部分列 */
  :deep(.el-table .el-table__cell:nth-child(3)),
  :deep(.el-table .el-table__cell:nth-child(4)),
  :deep(.el-table .el-table__cell:nth-child(6)) {
    display: none;
  }
}
</style> 