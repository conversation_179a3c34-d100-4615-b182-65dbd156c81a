/**
 * API统一导出文件
 * 统一管理所有API接口，方便组件导入使用
 */

// 导出请求工具
export { 
  mockRequest, 
  get, 
  post, 
  put, 
  del, 
  API_CONFIG 
} from './request.js'

// 导出人大代表统计数据相关API
export {
  getRepresentativeStatistics,
  getRealtimeMonitoring,
  getSystemStatus,
  getUserInfo,
  getNotifications
} from './representativeStatistics.js'

// 导出代表组成数据相关API
export {
  getRepresentativeComposition,
  getRepresentativeCompositionDetail
} from './representativeComposition.js'

// 导出代表意见列表数据相关API
export {
  getRepresentativeOpinions,
  getOpinionDetail,
  getOpinionStatistics
} from './representativeOpinions.js'

// 导出履职统计数据相关API
export {
  getYearlyDutyStatistics,
  getMonthlyDutyDetails
} from './dutyStatistics.js'

// 导出AI知识库法律条文相关API
export {
  getLegalKnowledgeList,
  getAIQuestionLink
} from './legalKnowledge.js'

// 导出地图数据相关API
export {
  getMapBoundary,
  getStreetRepresentativeCount,
  getStreetDetail
} from './mapData.js'

/**
 * API接口地址常量
 * 便于统一管理和修改
 */
export const API_URLS = {
  // 统计相关
  ARTICLE_STATISTICS: '/statistics/articles',
  REALTIME_MONITORING: '/monitoring/realtime',
  SYSTEM_STATUS: '/system/status',
  
  // 用户相关
  USER_INFO: '/users/info',
  USER_LIST: '/users/list',
  
  // 通知相关
  NOTIFICATIONS: '/notifications',
  MARK_READ: '/notifications/read',
  
  // 系统相关
  SYSTEM_CONFIG: '/system/config',
  SYSTEM_LOGS: '/system/logs'
}

/**
 * API响应状态码
 */
export const API_STATUS = {
  SUCCESS: 200,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
}

/**
 * 通用API错误处理
 * @param {Error} error - 错误对象
 * @param {string} context - 错误上下文
 */
export const handleApiError = (error, context = '') => {
  console.error(`API错误 ${context}:`, error)
  
  // 这里可以添加全局错误处理逻辑
  // 比如显示错误提示、跳转登录页等
  
  return {
    code: error.code || 500,
    message: error.message || '网络请求失败',
    data: null
  }
} 