from django.db import models
from django.utils import timezone


class PeopleOpinion(models.Model):
    """群众意见模型"""
    
    title = models.CharField(
        max_length=200, 
        verbose_name="意见标题",
        help_text="群众反映问题的标题"
    )
    
    content = models.TextField(
        verbose_name="意见内容",
        help_text="群众反映问题的详细内容"
    )
    
    contact_info = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="联系方式",
        help_text="群众的联系方式（电话、邮箱等）"
    )
    
    name = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name="姓名",
        help_text="群众的真实姓名或昵称"
    )
    
    created_at = models.DateTimeField(
        default=timezone.now,
        verbose_name="提交时间",
        help_text="意见提交的时间"
    )
    
    class Meta:
        db_table = 'people_opinion'
        verbose_name = "群众意见"
        verbose_name_plural = "群众意见"
        ordering = ['-created_at']  # 按提交时间倒序排列
        
    def __str__(self):
        return f"{self.title} - {self.name}"
    
    @property
    def short_content(self):
        """返回内容的简短版本，用于列表显示"""
        if len(self.content) > 100:
            return self.content[:100] + "..."
        return self.content
