"""
大屏展示视图

提供大屏展示所需的数据接口，无需鉴权，支持匿名访问
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
import base64
import mimetypes

from .services import BigScreenDataService
from .serializers import BigScreenDataSerializer
from api.users.models import Representative


class BigScreenDataView(APIView):
    """
    大屏数据接口

    GET /api/v1/bigscreen/
    返回大屏展示所需的所有数据，无需鉴权
    """
    permission_classes = [AllowAny]  # 允许匿名访问

    def get(self, request):
        """获取大屏数据"""
        try:
            # 获取所有大屏数据
            data = BigScreenDataService.get_all_data()

            # 直接返回数据，保持与data.json相同的结构
            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取大屏数据失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DistrictMembersView(APIView):
    """
    片区人员列表接口

    GET /api/v1/bigscreen/district-members/?district=片区名称
    返回指定片区的人员列表，无需鉴权
    """
    permission_classes = [AllowAny]  # 允许匿名访问

    def get(self, request):
        """获取片区人员列表"""
        try:
            district_name = request.query_params.get('district')
            if not district_name:
                return Response({
                    'code': 400,
                    'message': '请提供片区名称参数',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取片区人员数据
            data = BigScreenDataService.get_district_members(district_name)

            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取片区人员数据失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RepresentativeAvatarView(APIView):
    """
    代表头像接口

    GET /api/v1/bigscreen/avatar/{representative_id}/
    返回指定代表的头像图片，无需鉴权
    """
    permission_classes = [AllowAny]  # 允许匿名访问

    def get(self, request, representative_id):
        """获取代表头像"""
        try:
            # 获取代表信息
            representative = get_object_or_404(Representative, id=representative_id)

            # 检查是否有头像数据
            if not representative.avatar:
                return Response({
                    'code': 404,
                    'message': '该代表暂无头像',
                    'data': None
                }, status=status.HTTP_404_NOT_FOUND)

            # 解析base64头像数据
            avatar_data = representative.avatar

            # 检查是否是data URL格式
            if avatar_data.startswith('data:'):
                # 解析data URL: data:image/jpeg;base64,/9j/4AAQ...
                try:
                    header, encoded = avatar_data.split(',', 1)
                    mime_type = header.split(':')[1].split(';')[0]
                    image_data = base64.b64decode(encoded)
                except (ValueError, IndexError) as e:
                    return Response({
                        'code': 400,
                        'message': '头像数据格式错误',
                        'data': None
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                # 如果不是data URL格式，假设是纯base64数据
                try:
                    image_data = base64.b64decode(avatar_data)
                    mime_type = 'image/jpeg'  # 默认JPEG格式
                except Exception as e:
                    return Response({
                        'code': 400,
                        'message': '头像数据解码失败',
                        'data': None
                    }, status=status.HTTP_400_BAD_REQUEST)

            # 创建HTTP响应
            response = HttpResponse(image_data, content_type=mime_type)

            # 设置缓存头，缓存1小时
            response['Cache-Control'] = 'public, max-age=3600'
            response['ETag'] = f'"{representative_id}-{hash(avatar_data[:100])}"'

            return response

        except Representative.DoesNotExist:
            return Response({
                'code': 404,
                'message': '代表不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'code': 500,
                'message': f'获取头像失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BigScreenHealthView(APIView):
    """
    大屏健康检查接口

    GET /api/v1/bigscreen/health/
    用于检查大屏数据服务是否正常
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """健康检查"""
        return Response({
            'code': 200,
            'message': '大屏数据服务正常',
            'data': {
                'status': 'healthy',
                'timestamp': request.META.get('HTTP_X_FORWARDED_FOR',
                                            request.META.get('REMOTE_ADDR', 'unknown'))
            }
        }, status=status.HTTP_200_OK)
