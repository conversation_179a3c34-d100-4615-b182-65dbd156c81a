<template>
  <div style="position: relative; width: 100%; height: 95%">
    <!-- 右上角信息栏 -->
    <div
      style="
        position: absolute;
        right: 20px;
        top: 10px;
        z-index: 10;
        display: flex;
        align-items: center;
      "
    >
      <span
        style="
          color: #fff;
          font-size: 14px;
          margin-right: 10px;
          border-bottom: 1px skyblue solid;
        "
      >
        当月累计履职
        <span style="color: #ffd700; font-size: 20px">{{ total }}</span> 条
      </span>
      <select
        v-model="selectedMonth"
        @change="onMonthChange"
        style="
          background: #0a2740;
          color: #ffd700;
          border: 1px solid #1e90ff;
          border-radius: 4px;
          padding: 2px 8px;
        "
      >
        <option v-for="month in months" :key="month" :value="month">
          {{ month }}
        </option>
      </select>
    </div>
    <!-- 图表 -->
    <div ref="myChart" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
export default {
  props: {
    months: {
      type: Array,
      default: () => [],
    },
    chartData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      total: 0,
      myChart: null,
      selectedMonth: '2020年9月',
    }
  },
  mounted() {
    this.initChart()
  },
  watch: {
    chartData: {
      handler() {
        this.initChart()
      },
      deep: true,
    },
    selectedMonth() {
      this.initChart()
    },
  },
  methods: {
    initChart() {
      if (!this.myChart) {
        this.myChart = this.$echarts.init(this.$refs.myChart)
      }
      const { categories, values } = this.chartData[this.selectedMonth] || {
        categories: [],
        values: [],
      }
      this.total = values.reduce((a, b) => a + b, 0)
      const option = {
        tooltip: { trigger: 'axis' },
        grid: { left: 40, right: 30, bottom: 5, top: 70, containLabel: true },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { color: '#fff', rotate: 45, fontSize: 10 },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#ced6e0', fontSize: 12 },
          splitLine: { lineStyle: { color: '#0c4787', type: 'dashed' } },
          axisLine: { show: 'true' },
        },
        series: [
          {
            data: values,
            type: 'bar',
            barWidth: 15,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#69b2ff' },
                  { offset: 1, color: '#205afd' },
                ],
              },
            },
          },
        ],
      }
      this.myChart.setOption(option)
    },
    onMonthChange() {
      this.initChart()
    },
  },
}
</script>

<style scoped></style>
