// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    username: 'representative1',
    password: '123456',
    name: '张三',
    role: 'representative',
    phone: '13812345678',
    email: '<PERSON><PERSON><PERSON>@example.com',
    district: '某某街道',
    certificateNo: 'NPC2024001',
    term: '第十四届'
  },
  {
    id: 2,
    username: 'staff1',
    password: '123456',
    name: '李四',
    role: 'staff',
    phone: '13987654321',
    email: '<EMAIL>',
    department: '某某站点',
    position: '工作人员'
  },
  {
    id: 3,
    username: 'representative2',
    password: '123456',
    name: '王五',
    role: 'representative',
    phone: '13511112222',
    email: '<EMAIL>',
    district: '某某街道',
    certificateNo: 'NPC2024002',
    term: '第十四届'
  }
]

// 模拟登录API
export const mockLogin = (loginForm) => {
  return new Promise((resolve) => {
    // 模拟网络延迟
    setTimeout(() => {
      const { username, password } = loginForm
      
      // 查找用户
      const user = mockUsers.find(u => u.username === username && u.password === password)
      
      if (user) {
        // 生成模拟token
        const token = `mock_token_${user.id}_${Date.now()}`
        
        // 返回用户信息（不包含密码）
        const { password: pwd, ...userInfo } = user
        
        resolve({
          success: true,
          data: {
            token,
            userInfo
          },
          message: '登录成功'
        })
      } else {
        resolve({
          success: false,
          message: '用户名或密码错误'
        })
      }
    }, 500) // 模拟500ms网络延迟
  })
}

// 模拟获取用户信息API
export const mockGetUserInfo = (token) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 从token中提取用户ID（简单模拟）
      const userId = token.split('_')[2]
      const user = mockUsers.find(u => u.id === parseInt(userId))
      
      if (user) {
        const { password, ...userInfo } = user
        resolve({
          success: true,
          data: userInfo
        })
      } else {
        resolve({
          success: false,
          message: 'Token无效'
        })
      }
    }, 200)
  })
}

// 获取测试账号信息
export const getTestAccounts = () => {
  return mockUsers.map(user => ({
    username: user.username,
    password: '123456',
    name: user.name,
    role: user.role === 'representative' ? '人大代表' : '站点工作人员'
  }))
} 