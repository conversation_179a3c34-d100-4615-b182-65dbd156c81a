<template>
  <div class="site-summary-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>站点工作总结</h2>
      <p>通过AI智能分析，生成站点年度工作综合分析报告</p>
    </div>

    <!-- 年度选择区域 -->
    <el-card class="year-selector-card">
      <div class="year-selector">
        <label>选择分析年度：</label>
        <el-date-picker
          v-model="selectedYear"
          type="year"
          placeholder="选择年度"
          value-format="YYYY"
          style="margin-left: 10px; margin-right: 20px;"
        />
        <el-button 
          type="primary" 
          :loading="isAnalyzing"
          @click="generateAnalysis"
          :disabled="!selectedYear"
        >
          <el-icon><Document /></el-icon>
          {{ isAnalyzing ? '正在分析...' : '生成分析报告' }}
        </el-button>
      </div>
    </el-card>

    <!-- AI分析过程展示 -->
    <el-card v-if="isAnalyzing" class="analysis-progress-card">
      <div class="analysis-progress">
        <el-steps :active="currentStep" direction="horizontal" simple>
          <el-step title="收集数据" />
          <el-step title="AI智能分析" />
          <el-step title="生成报告" />
        </el-steps>
        <div class="progress-text">
          <el-icon class="is-loading"><Loading /></el-icon>
          {{ stepTexts[currentStep] }}
        </div>
      </div>
    </el-card>

    <!-- 分析结果展示 -->
    <div v-if="analysisResult && !isAnalyzing" class="analysis-result">
      <!-- 报告头部 -->
      <el-card class="report-header-card">
        <div class="report-header">
          <div class="header-content">
            <h2>{{ selectedYear }}年度站点工作分析报告</h2>
            <div class="report-meta">
              <span>生成时间：{{ analysisResult.generateTime }}</span>
              <span>分析范围：{{ analysisResult.scope }}</span>
            </div>
          </div>
          <div class="header-actions">
            <el-button type="primary" @click="exportReport">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
            <el-button @click="shareReport">
              <el-icon><Share /></el-icon>
              分享报告
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 核心数据概览 -->
      <el-card class="data-overview-card">
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>核心数据概览</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="6" v-for="item in analysisResult.keyMetrics" :key="item.label">
            <div class="metric-item">
              <div class="metric-number" :style="{color: item.color}">{{ item.value }}</div>
              <div class="metric-label">{{ item.label }}</div>
              <div class="metric-trend" :class="item.trend">
                <el-icon v-if="item.trend === 'up'"><TrendCharts /></el-icon>
                <el-icon v-else-if="item.trend === 'down'"><Bottom /></el-icon>
                <span>{{ item.trendText }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 工作亮点 -->
      <el-card class="highlights-card">
        <template #header>
          <div class="card-header">
            <el-icon><Star /></el-icon>
            <span>工作亮点</span>
          </div>
        </template>
        <div class="highlights-content">
          <div v-for="(highlight, index) in analysisResult.highlights" :key="index" class="highlight-item">
            <div class="highlight-icon">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="highlight-content">
              <h4>{{ highlight.title }}</h4>
              <p>{{ highlight.description }}</p>
              <div class="highlight-tags">
                <el-tag v-for="tag in highlight.tags" :key="tag" size="small">{{ tag }}</el-tag>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 数据可视化分析 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <el-icon><PieChart /></el-icon>
                <span>意见处理情况分布</span>
              </div>
            </template>
            <div class="chart-container" ref="opinionChart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <el-icon><DataLine /></el-icon>
                <span>月度工作量趋势</span>
              </div>
            </template>
            <div class="chart-container" ref="trendChart"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 问题分析 -->
      <el-card class="issues-card">
        <template #header>
          <div class="card-header">
            <el-icon><Warning /></el-icon>
            <span>问题分析</span>
          </div>
        </template>
        <div class="issues-content">
          <div v-for="(issue, index) in analysisResult.issues" :key="index" class="issue-item">
            <div class="issue-header">
              <el-tag type="warning" size="small">{{ issue.level }}</el-tag>
              <h4>{{ issue.title }}</h4>
            </div>
            <p class="issue-description">{{ issue.description }}</p>
            <div class="issue-impact">
              <strong>影响程度：</strong>{{ issue.impact }}
            </div>
          </div>
        </div>
      </el-card>

      <!-- 改进建议 -->
      <el-card class="suggestions-card">
        <template #header>
          <div class="card-header">
            <el-icon><Promotion /></el-icon>
            <span>改进建议</span>
          </div>
        </template>
        <div class="suggestions-content">
          <div v-for="(suggestion, index) in analysisResult.suggestions" :key="index" class="suggestion-item">
            <div class="suggestion-number">{{ index + 1 }}</div>
            <div class="suggestion-content">
              <h4>{{ suggestion.title }}</h4>
              <p>{{ suggestion.description }}</p>
              <div class="suggestion-meta">
                <el-tag size="small" type="info">优先级：{{ suggestion.priority }}</el-tag>
                <el-tag size="small" type="success">预期效果：{{ suggestion.expectedEffect }}</el-tag>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- AI智能总结 -->
      <el-card class="ai-summary-card">
        <template #header>
          <div class="card-header">
            <el-icon><ChatLineSquare /></el-icon>
            <span>AI智能总结</span>
          </div>
        </template>
        <div class="ai-summary-content">
          <div class="summary-section">
            <h4>总体评价</h4>
            <p>{{ analysisResult.aiSummary.overall }}</p>
          </div>
          <div class="summary-section">
            <h4>关键成就</h4>
            <p>{{ analysisResult.aiSummary.achievements }}</p>
          </div>
          <div class="summary-section">
            <h4>发展展望</h4>
            <p>{{ analysisResult.aiSummary.outlook }}</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 空状态 -->
    <el-empty v-if="!analysisResult && !isAnalyzing" description="请选择年度并生成分析报告" />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Document, Download, Share, DataAnalysis, Star, Trophy, 
  PieChart, DataLine, Warning, Promotion, ChatLineSquare, Loading,
  TrendCharts, Bottom
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { generateSiteAnalysis, exportAnalysisReport, shareAnalysisReport } from '@/api/workAnalysis'

// 响应式数据
const selectedYear = ref(new Date().getFullYear().toString())
const isAnalyzing = ref(false)
const currentStep = ref(0)
const analysisResult = ref(null)
const opinionChart = ref(null)
const trendChart = ref(null)

// 分析步骤文本
const stepTexts = ref([
  '正在收集站点工作数据...',
  '正在进行AI智能分析...',
  '正在生成分析报告...'
])

// 模拟分析过程
const simulateAnalysis = async () => {
  for (let i = 0; i <= 2; i++) {
    currentStep.value = i
    await new Promise(resolve => setTimeout(resolve, 1500))
  }
}

// 生成分析报告
const generateAnalysis = async () => {
  if (!selectedYear.value) {
    ElMessage.warning('请选择分析年度')
    return
  }

  isAnalyzing.value = true
  currentStep.value = 0

  try {
    // 模拟AI分析过程
    await simulateAnalysis()

    // 调用API生成分析报告
    const response = await generateSiteAnalysis(selectedYear.value)
    
    if (response.success) {
      analysisResult.value = response.data
      ElMessage.success('分析报告生成成功！')

      // 生成图表
      await nextTick()
      generateCharts()
    } else {
      throw new Error(response.message || '分析报告生成失败')
    }

  } catch (error) {
    console.error('生成分析报告失败：', error)
    ElMessage.error('分析报告生成失败，请稍后重试')
  } finally {
    isAnalyzing.value = false
  }
}

// 生成图表
const generateCharts = () => {
  // 意见处理情况分布图
  if (opinionChart.value) {
    const chart1 = echarts.init(opinionChart.value)
    chart1.setOption({
      tooltip: {
        trigger: 'item'
      },
      legend: {
        bottom: '5%',
        left: 'center'
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: 144, name: '已办结', itemStyle: { color: '#67C23A' } },
            { value: 8, name: '处理中', itemStyle: { color: '#E6A23C' } },
            { value: 4, name: '待审核', itemStyle: { color: '#409EFF' } }
          ]
        }
      ]
    })
  }

  // 月度工作量趋势图
  if (trendChart.value) {
    const chart2 = echarts.init(trendChart.value)
    chart2.setOption({
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['意见数量', '办结数量']
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '意见数量',
          type: 'line',
          smooth: true,
          data: [12, 8, 15, 18, 14, 16, 13, 19, 11, 15, 17, 9],
          itemStyle: { color: '#409EFF' }
        },
        {
          name: '办结数量',
          type: 'line',
          smooth: true,
          data: [11, 8, 14, 17, 13, 15, 12, 18, 10, 14, 16, 8],
          itemStyle: { color: '#67C23A' }
        }
      ]
    })
  }
}

// 导出报告
const exportReport = async () => {
  if (!analysisResult.value) {
    ElMessage.warning('请先生成分析报告')
    return
  }

  try {
    const response = await exportAnalysisReport('site', {
      year: selectedYear.value
    })
    
    if (response.success) {
      ElMessage.success(response.message || '报告导出成功')
      // 这里可以添加实际的文件下载逻辑
    } else {
      throw new Error(response.message || '导出失败')
    }
  } catch (error) {
    console.error('导出报告失败：', error)
    ElMessage.error('导出报告失败，请稍后重试')
  }
}

// 分享报告
const shareReport = async () => {
  if (!analysisResult.value) {
    ElMessage.warning('请先生成分析报告')
    return
  }

  try {
    const response = await shareAnalysisReport('site', {
      year: selectedYear.value
    })
    
    if (response.success) {
      ElMessage.success(response.message || '分享链接生成成功')
      // 这里可以显示分享链接或二维码
    } else {
      throw new Error(response.message || '分享失败')
    }
  } catch (error) {
    console.error('分享报告失败：', error)
    ElMessage.error('分享报告失败，请稍后重试')
  }
}

onMounted(() => {
  // 组件挂载后的初始化操作
})
</script>

<style scoped>
.site-summary-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  text-align: center;
}

.page-header h2 {
  color: #c62d2d;
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: bold;
}

.page-header p {
  color: #666;
  margin: 0;
  font-size: 16px;
}

.year-selector-card {
  margin-bottom: 20px;
}

.year-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.year-selector label {
  font-weight: bold;
  color: #333;
}

.analysis-progress-card {
  margin-bottom: 20px;
}

.analysis-progress {
  padding: 30px;
  text-align: center;
}

.progress-text {
  margin-top: 20px;
  font-size: 16px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.analysis-result {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.report-header-card {
  margin-bottom: 20px;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.header-content h2 {
  color: #c62d2d;
  margin: 0 0 10px 0;
  font-size: 24px;
}

.report-meta {
  display: flex;
  gap: 20px;
  color: #666;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.data-overview-card,
.highlights-card,
.chart-card,
.issues-card,
.suggestions-card,
.ai-summary-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  color: #c62d2d;
  font-size: 18px;
}

.metric-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  border: 2px solid #f5f5f5;
  transition: all 0.3s ease;
}

.metric-item:hover {
  border-color: #c62d2d;
  transform: translateY(-5px);
}

.metric-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #c62d2d;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.metric-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: bold;
}

.metric-trend.up {
  background: #e6f7e6;
  color: #52c41a;
}

.metric-trend.down {
  background: #ffe6e6;
  color: #ff4d4f;
}

.highlights-content {
  padding: 20px;
}

.highlight-item {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #c62d2d;
  background: #fff2f2;
  transition: transform 0.3s ease;
}

.highlight-item:hover {
  transform: translateY(-3px);
}

.highlight-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #c62d2d;
  box-shadow: 0 2px 8px rgba(198, 45, 45, 0.2);
}

.highlight-content h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #c62d2d;
  font-weight: bold;
}

.highlight-content p {
  margin: 0 0 10px 0;
  line-height: 1.6;
  color: #666;
}

.highlight-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.issues-content,
.suggestions-content {
  padding: 20px;
}

.issue-item,
.suggestion-item {
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #c62d2d;
  background: #fff2f2;
  transition: transform 0.3s ease;
}

.issue-item:hover,
.suggestion-item:hover {
  transform: translateY(-3px);
}

.suggestion-item {
  border-left-color: #c62d2d;
  background: #fff2f2;
  display: flex;
  gap: 15px;
}

.suggestion-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #c62d2d;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.issue-header h4,
.suggestion-content h4 {
  margin: 0;
  color: #c62d2d;
  font-weight: bold;
}

.issue-description,
.suggestion-content p {
  color: #666;
  line-height: 1.6;
  margin: 10px 0;
}

.issue-impact {
  color: #c62d2d;
  font-size: 14px;
  font-weight: 500;
}

.suggestion-meta {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.ai-summary-content {
  padding: 20px;
}

.summary-section {
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  border: 2px solid #f5f5f5;
}

.summary-section h4 {
  margin: 0 0 15px 0;
  font-size: 18px;
  color: #c62d2d;
  font-weight: bold;
}

.summary-section p {
  margin: 0;
  line-height: 1.8;
  color: #333;
  font-size: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .site-summary-container {
    padding: 10px;
  }

  .report-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .highlight-item,
  .suggestion-item {
    flex-direction: column;
  }

  .metric-item {
    margin-bottom: 15px;
  }
}
</style> 