# Generated manually for adding district field

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_add_avatar_field'),
    ]

    operations = [
        migrations.AddField(
            model_name='representative',
            name='district',
            field=models.CharField(
                choices=[
                    ('那洪片区', '那洪片区'),
                    ('那历片区', '那历片区'),
                    ('沛鸿片区', '沛鸿片区')
                ],
                default='那洪片区',
                help_text='代表所属的片区，必填字段',
                max_length=20,
                verbose_name='所属片区'
            ),
            preserve_default=False,
        ),
    ]
