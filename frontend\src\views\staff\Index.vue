<template>
  <div class="staff-layout">
    <!-- 头部 -->
    <el-header class="layout-header">
      <div class="header-left">
        <el-icon size="24" style="color: var(--china-red); margin-right: 10px;">
          <OfficeBuilding />
        </el-icon>
        <span class="system-title">人大代表履职服务与管理平台</span>
      </div>
      <div class="header-right">
        <!-- 通知图标 -->
        <!-- <NotificationIcon /> -->
        
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-avatar
              :size="32"
              :src="userAvatar"
              style="background-color: var(--china-red);"
            >
              {{ userStore.userName.charAt(0) }}
            </el-avatar>
            <span class="user-name">{{ userStore.userName }}</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <!-- <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人信息
              </el-dropdown-item> -->
              <el-dropdown-item command="logout" divided>
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主体布局 -->
    <el-container class="main-container">
      <!-- 侧边栏 -->
      <el-aside class="layout-aside">
        <el-menu
          :default-active="$route.path"
          router
          class="nav-menu"
        >
          <el-menu-item index="/staff">
            <el-icon><House /></el-icon>
            <span>工作台概览</span>
          </el-menu-item>
          <el-menu-item index="/staff/review">
            <el-icon><Select /></el-icon>
            <span>意见建议审核</span>
          </el-menu-item>
          <el-menu-item index="/staff/work-plan">
            <el-icon><Calendar /></el-icon>
            <span>工作计划管理</span>
          </el-menu-item>
          <el-sub-menu index="work-analysis">
            <template #title>
              <el-icon><DataAnalysis /></el-icon>
              <span>工作分析</span>
            </template>
            <!-- <el-menu-item index="/staff/work-analysis/site-summary">
              <el-icon><Document /></el-icon>
              <span>站点工作总结</span>
            </el-menu-item> -->
            <el-menu-item index="/staff/work-analysis/representative-summary">
              <el-icon><User /></el-icon>
              <span>代表工作总结</span>
            </el-menu-item>
          </el-sub-menu>
          <el-menu-item index="/staff/knowledge-qa">
            <el-icon><DocumentCopy /></el-icon>
            <span>法律政策互动AI问答</span>
          </el-menu-item>
          <el-menu-item index="/staff/people-opinion-list">
            <el-icon><ChatDotRound /></el-icon>
            <span>群众意见管理</span>
          </el-menu-item>
          <el-menu-item index="/staff/account-management">
            <el-icon><UserFilled /></el-icon>
            <span>账号管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 内容区域 -->
      <el-main class="layout-main">
        <!-- 路由出口：如果是根路径显示概览，否则显示子页面 -->
        <div v-if="$route.path === '/staff'" class="dashboard-container">
          <!-- 欢迎卡片 -->
          <div class="welcome-card">
            <h2>欢迎回来，{{ staffInfo.name || userStore.userName }} 同志</h2>
            <p>{{ currentDate }} | {{ userStore.roleText }}</p>
            <div class="role-info">
              <div class="info-item">
                <el-icon class="info-icon"><User /></el-icon>
                <div class="info-content">
                  <span class="label">姓名</span>
                  <span class="value">{{ staffInfo.name || '未设置' }}</span>
                </div>
              </div>
              <div class="info-item">
                <el-icon class="info-icon"><Medal /></el-icon>
                <div class="info-content">
                  <span class="label">职务</span>
                  <span class="value">{{ staffInfo.position || '工作人员' }}</span>
                </div>
              </div>
              <div class="info-item">
                <el-icon class="info-icon"><LocationInformation /></el-icon>
                <div class="info-content">
                  <span class="label">工作站点</span>
                  <span class="value">{{ staffInfo.station_name || '未设置' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 统计数据 -->
          <div class="stats-section">
            <div class="stats-container">
              <div class="stat-item">
                <el-card class="stat-card" v-loading="loadingStats">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.submitted }}</div>
                    <div class="stat-label">待审核</div>
                  </div>
                  <el-icon class="stat-icon" style="color: #e6a23c;"><Clock /></el-icon>
                </el-card>
              </div>
              <div class="stat-item">
                <el-card class="stat-card" v-loading="loadingStats">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.approved }}</div>
                    <div class="stat-label">已通过待转交</div>
                  </div>
                  <el-icon class="stat-icon" style="color: #67c23a;"><CircleCheck /></el-icon>
                </el-card>
              </div>
              <div class="stat-item">
                <el-card class="stat-card" v-loading="loadingStats">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.transferred }}</div>
                    <div class="stat-label">已转交</div>
                  </div>
                  <el-icon class="stat-icon" style="color: #409eff;"><Share /></el-icon>
                </el-card>
              </div>
              <div class="stat-item">
                <el-card class="stat-card" v-loading="loadingStats">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.inProgress }}</div>
                    <div class="stat-label">处理中</div>
                  </div>
                  <el-icon class="stat-icon" style="color: #f56c6c;"><Loading /></el-icon>
                </el-card>
              </div>
              <div class="stat-item">
                <el-card class="stat-card" v-loading="loadingStats">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.completed }}</div>
                    <div class="stat-label">已办结</div>
                  </div>
                  <el-icon class="stat-icon" style="color: #67c23a;"><CircleCheck /></el-icon>
                </el-card>
              </div>
            </div>
          </div>

          <!-- 工作概览 -->
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <el-card header="部分待审核意见列表" v-loading="loadingPendingList">
                <el-empty v-if="!pendingReviewList.length && !loadingPendingList" description="暂无待审核意见" />
                <div v-else>
                  <div
                    v-for="opinion in pendingReviewList"
                    :key="opinion.id"
                    class="review-item"
                  >
                    <div class="review-header">
                      <span class="review-title" @click="reviewOpinion(opinion.id)" :title="opinion.title">
                        {{ truncateTitle(opinion.title, 18) }}
                      </span>
                      <el-button size="small" type="primary" @click="reviewOpinion(opinion.id)">
                        审核
                      </el-button>
                    </div>
                    <div class="review-meta">
                      <span class="submitter">{{ opinion.submitterName }}</span>
                      <span class="submit-date">{{ opinion.submitDate }}</span>
                      <el-tag size="small" :type="getCategoryColor(opinion.category)">{{ opinion.categoryText }}</el-tag>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card header="最近更新意见" v-loading="loadingRecentActivities">
                <el-timeline>
                  <el-timeline-item
                    v-for="record in recentActivities"
                    :key="record.id"
                    :timestamp="record.date"
                    :type="record.type"
                  >
                    <div class="activity-content">
                      <div class="activity-title" :title="record.title">
                        {{ truncateTitle(record.title, 25) }}
                      </div>
                      <div class="activity-meta" v-if="record.action">
                        <el-tag size="small" :type="record.type">
                          {{ record.action }}
                        </el-tag>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </el-card>
            </el-col>
          </el-row>

          <!-- 快捷操作 -->
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card header="快捷操作">
                <el-row :gutter="15">
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="$router.push('/staff/review')">
                      <el-icon size="24" style="color: var(--china-red);">
                        <Select />
                      </el-icon>
                      <span>意见建议审核</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="$router.push('/staff/work-plan')">
                      <el-icon size="24" style="color: var(--china-red);">
                        <Calendar />
                      </el-icon>
                      <span>工作计划管理</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="$router.push('/staff/work-analysis/site-summary')">
                      <el-icon size="24" style="color: var(--china-red);">
                        <DataAnalysis />
                      </el-icon>
                      <span>站点工作总结</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="$router.push('/staff/work-analysis/representative-summary')">
                      <el-icon size="24" style="color: var(--china-red);">
                        <User />
                      </el-icon>
                      <span>代表工作总结</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="showNotification">
                      <el-icon size="24" style="color: var(--china-red);">
                        <Bell />
                      </el-icon>
                      <span>通知公告</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="exportData">
                      <el-icon size="24" style="color: var(--china-red);">
                        <Download />
                      </el-icon>
                      <span>数据导出</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="$router.push('/staff/knowledge-qa')">
                      <el-icon size="24" style="color: var(--china-red);">
                        <DocumentCopy />
                      </el-icon>
                      <span>法律政策互动AI问答</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="$router.push('/staff/account-management')">
                      <el-icon size="24" style="color: var(--china-red);">
                        <UserFilled />
                      </el-icon>
                      <span>账号管理</span>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-col>
          </el-row>
        </div>
        
        <!-- 子路由内容 -->
        <router-view v-else />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import NotificationIcon from '@/components/NotificationIcon.vue'
import { opinionAPI, opinionUtils } from '@/api/modules/opinion'
import {
  User,
  ArrowDown,
  SwitchButton,
  House,
  Select,
  Management,
  Bell,
  Download,
  DocumentCopy,
  Calendar,
  DataAnalysis,
  Document,
  UserFilled,
  OfficeBuilding,
  Clock,
  CircleCheck,
  Share,
  Loading,
  Medal,
  LocationInformation,
  ChatDotRound
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 用户头像计算属性（工作人员暂时不支持头像，但保持一致性）
const userAvatar = computed(() => {
  // 工作人员暂时不支持头像功能，返回null使用默认头像
  return null
})

// 加载状态
const loadingStats = ref(false)
const loadingPendingList = ref(false)
const loadingRecentActivities = ref(false)

// 当前日期
const currentDate = computed(() => {
  const now = new Date()
  return now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

// 工作人员信息
const staffInfo = computed(() => {
  return userStore.userInfo.staff_info || {}
})

// 统计数据
const stats = ref({
  submitted: 0,
  approved: 0,
  transferred: 0,
  inProgress: 0,
  completed: 0
})

// 待审核意见列表
const pendingReviewList = ref([])

// 最近活动记录
const recentActivities = ref([])

// 获取分类颜色
const getCategoryColor = (category) => {
  return opinionUtils.getCategoryColor(category)
}

// 加载所有工作台数据（统计数据+待审核列表+最近活动记录）
const loadStatistics = async () => {
  loadingStats.value = true
  try {
    const response = await opinionAPI.getOpinionStatistics()
    
    if (response.data.success) {
      const data = response.data.data
      
      // 映射后端统计数据到前端显示
      stats.value = {
        submitted: data.pending_review_count || 0,        // 待审核数量（最新状态=submitted）
        approved: data.approved_count || 0,               // 审核通过数量（最新状态=approved）
        transferred: data.transferred_count || 0,         // 已转交数量（最新状态=transferred）
        inProgress: data.in_progress_count || 0,          // 处理中数量（最新状态=in_progress）
        completed: data.completed_opinions || 0           // 已办结数量（最新状态=completed）
      }
      
      // 同时处理待审核列表和最近活动记录
      loadPendingReviewList(data)
      loadRecentActivities(data)
      
      console.log('工作人员工作台统计数据:', stats.value)
    } else {
      ElMessage.error(response.data.message || '加载统计数据失败')
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败，请重试')
  } finally {
    loadingStats.value = false
  }
}

// 从统计API获取待审核列表（不再使用分页请求）
const loadPendingReviewList = (statisticsData) => {
  loadingPendingList.value = true
  try {
    const pendingReviews = statisticsData.pending_reviews || []
    
    // 转换数据格式用于显示
    pendingReviewList.value = pendingReviews.map(item => ({
      id: item.id,
      title: item.title,
      submitterName: item.representative_name || '未知代表',
      statusText: '待审核',
      submitDate: formatDate(item.submitted_time),
      category: item.category,
      categoryText: opinionUtils.getCategoryLabel(item.category)
    }))
    
    console.log('待审核意见列表:', pendingReviewList.value)
  } catch (error) {
    console.error('处理待审核列表数据失败:', error)
    pendingReviewList.value = []
  } finally {
    loadingPendingList.value = false
  }
}

// 从统计API获取最近活动记录（不再使用分页请求）
const loadRecentActivities = (statisticsData) => {
  loadingRecentActivities.value = true
  try {
    const recentOpinions = statisticsData.recent_opinions || []
    
    // 转换为活动记录格式
    recentActivities.value = recentOpinions.map(item => {
      let actionText = ''
      let type = 'info'
      
      // 根据状态确定操作描述
      switch (item.current_status) {
        case 'approved':
          actionText = '审核通过'
          type = 'success'
          break
        case 'transferred':
          actionText = '转交至相关部门'
          type = 'primary'
          break
        case 'in_progress':
          actionText = '处理中'
          type = 'warning'
          break
        case 'completed':
          actionText = '办结'
          type = 'success'
          break
        case 'rejected':
          actionText = '驳回需要修改'
          type = 'danger'
          break
        default:
          actionText = '处理'
          type = 'info'
      }
      
      return {
        id: item.id,
        title: item.title,
        action: actionText,
        date: formatDateTime(item.last_updated_time),
        type
      }
    })
    
    // 如果没有任何记录，显示提示
    if (recentActivities.value.length === 0) {
      recentActivities.value = [
        {
          id: 'empty',
          title: '暂无处理记录',
          action: '',
          date: formatDateTime(new Date()),
          type: 'info'
        }
      ]
    }
    
    console.log('最近活动记录:', recentActivities.value)
  } catch (error) {
    console.error('处理最近活动记录数据失败:', error)
    // 加载失败时显示默认记录
    recentActivities.value = [
      {
        id: 'error',
        title: '加载处理记录失败',
        action: '',
        date: formatDateTime(new Date()),
        type: 'danger'
      }
    ]
  } finally {
    loadingRecentActivities.value = false
  }
}

// 截断标题用于Dashboard展示
const truncateTitle = (title, maxLength = 25) => {
  if (!title) return '无标题'
  return title.length > maxLength ? title.substring(0, maxLength) + '...' : title
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return opinionUtils.formatDate(dateString)
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return ''
  return opinionUtils.formatDateTime(dateString)
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 等待登出完成后再跳转
    await userStore.logout()
    router.push('/login')
  } catch {
    // 用户取消
  }
}

// 审核意见
const reviewOpinion = (opinionId) => {
  router.push(`/staff/review?id=${opinionId}`)
}

// 显示通知
const showNotification = () => {
  ElMessage.info('通知公告功能开发中')
}

// 导出数据
const exportData = () => {
  ElMessage.info('数据导出功能开发中')
}

// 组件挂载时的初始化
onMounted(async () => {
  // 获取最新的用户信息
  await userStore.fetchUserInfo()
  
  // 一次API调用获取所有工作台数据（统计API已包含列表数据）
  await loadStatistics()
})
</script>

<style scoped>
.staff-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: white;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
}

.system-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--china-red);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: var(--bg-color);
}

.user-name {
  font-weight: 500;
  color: var(--text-color);
}

.main-container {
  flex: 1;
  height: calc(100vh - 60px);
}

.layout-aside {
  width: 200px;
  background: white;
  border-right: 1px solid var(--border-color);
}

.nav-menu {
  border-right: none;
  height: 100%;
}

.layout-main {
  background: var(--bg-color);
  padding: 0;
  overflow-y: auto;
}

.dashboard-container {
  padding: 20px;
}

.welcome-card {
  background: linear-gradient(135deg, var(--china-red) 0%, #d32f2f 100%);
  color: white;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(200, 16, 46, 0.3);
}

.welcome-card h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.welcome-card p {
  margin: 0 0 16px 0;
  opacity: 0.9;
  font-size: 14px;
}

.role-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.stats-section {
  margin-bottom: 20px;
}

.stats-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  flex: 1;
  min-width: 200px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 16px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(200, 16, 46, 0.15);
}

.stat-card .el-card__body {
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.stat-content {
  text-align: center;
  margin-bottom: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: var(--china-red);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  white-space: nowrap;
}

.stat-icon {
  font-size: 28px;
  opacity: 0.8;
}

/* 响应式调整 */
@media (min-width: 1200px) {
  .stat-item {
    min-width: 220px;
  }
  
  .stat-card .el-card__body {
    padding: 20px;
  }
  
  .stat-content {
    margin-bottom: 12px;
  }
  
  .stat-number {
    font-size: 28px;
  }
  
  .stat-label {
    font-size: 14px;
    margin-top: 5px;
  }
  
  .stat-icon {
    font-size: 32px;
  }
}

@media (max-width: 992px) {
  .stat-item {
    min-width: 180px;
  }
}

@media (max-width: 768px) {
  .stats-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .stat-item {
    flex: none;
    min-width: auto;
  }
  
  .stat-card {
    margin-bottom: 0;
  }
  
  .stat-card .el-card__body {
    padding: 12px;
  }
  
  .stat-content {
    margin-bottom: 6px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .stat-icon {
    font-size: 24px;
  }
}

@media (max-width: 576px) {
  .stats-container {
    gap: 8px;
  }
  
  .stat-card .el-card__body {
    padding: 10px;
  }
  
  .stat-content {
    margin-bottom: 4px;
  }
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 180px;
}

.info-icon {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  flex-shrink: 0;
}

.info-content {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  white-space: nowrap;
}

.value {
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.review-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.review-item:hover {
  background-color: #fafbfc;
  border-radius: 4px;
  margin: 0 -8px;
  padding: 12px 8px;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.review-title {
  font-weight: 500;
  color: var(--text-color);
  flex: 1;
  cursor: pointer;
  margin-right: 8px;
  line-height: 1.4;
}

.review-title:hover {
  color: var(--china-red);
}

.review-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #666;
  gap: 8px;
}

.submitter {
  color: #409eff;
  font-weight: 500;
}

.submit-date {
  color: #909399;
}

.activity-content {
  font-size: 13px;
  color: var(--text-color);
  line-height: 1.4;
}

.activity-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-color);
  cursor: pointer;
}

.activity-title:hover {
  color: var(--china-red);
}

.activity-meta {
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.quick-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.quick-action:hover {
  border-color: var(--china-red);
  box-shadow: 0 2px 8px rgba(200, 16, 46, 0.2);
  transform: translateY(-2px);
}

.quick-action span {
  font-size: 14px;
  color: var(--text-color);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-aside {
    width: 160px;
  }
  
  .system-title {
    display: none;
  }
  
  .stats-section {
    margin: 10px 0;
  }
  
  .review-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .role-info {
    flex-direction: column;
    gap: 12px;
  }
  
  .welcome-card {
    padding: 20px;
  }
  
  .welcome-card h2 {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .layout-header {
    padding: 0 10px;
  }
  
  .layout-aside {
    width: 120px;
  }
  
  .user-name {
    display: none;
  }
  
  .quick-action {
    padding: 15px 10px;
  }
  
  .dashboard-container {
    padding: 10px;
  }
  
  .welcome-card {
    padding: 15px;
    margin-bottom: 15px;
  }
  
  .welcome-card h2 {
    font-size: 18px;
  }
  
  .info-item {
    min-width: auto;
  }
}
</style> 