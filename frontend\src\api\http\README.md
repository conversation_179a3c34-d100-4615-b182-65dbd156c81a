# API配置管理说明

## 概述

本项目使用统一的API配置管理方案，所有URL相关配置都在 `src/api/http/config.js` 中管理。

## 配置结构

### 基础配置

```javascript
export const API_CONFIG = {
  // API基础URL
  BASE_URL: 'http://localhost:8000/api/v1',
  
  // 后端服务基础URL
  BACKEND_BASE_URL: 'http://localhost:8000',
  
  // 媒体文件URL前缀
  MEDIA_URL: '/media',
  
  // 静态文件URL前缀
  STATIC_URL: '/static'
}
```

### 环境变量支持

- `VITE_API_BASE_URL`: API服务地址
- `VITE_BACKEND_BASE_URL`: 后端服务地址

### URL构建方法

- `API_CONFIG.buildMediaUrl(path)`: 构建媒体文件URL
- `API_CONFIG.buildStaticUrl(path)`: 构建静态文件URL

## 开发环境配置

### Vite代理配置 (vite.config.js)

```javascript
proxy: {
  '/media': {
    target: 'http://localhost:8000',
    changeOrigin: true,
    secure: false
  },
  '/api': {
    target: 'http://localhost:8000',
    changeOrigin: true,
    secure: false
  }
}
```

### 工作原理

1. 前端请求 `/media/xxx` 
2. Vite代理转发到 `http://localhost:8000/media/xxx`
3. Django返回实际文件

## 生产环境部署

### 方案一：Nginx代理

```nginx
location /media/ {
    alias /path/to/media/;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location /api/ {
    proxy_pass http://django_backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

### 方案二：CDN + 云存储

修改 `API_CONFIG.BACKEND_BASE_URL` 指向CDN域名：

```javascript
BACKEND_BASE_URL: 'https://cdn.yourdomain.com'
```

### 方案三：同域部署

前端构建后放入Django的static目录，统一通过一个域名访问。

## 使用示例

### 在组件中使用

```javascript
import { API_CONFIG } from '@/api/http/config'

// 构建媒体文件URL
const imageUrl = API_CONFIG.buildMediaUrl('/performance/2025/01/20/image.jpg')

// 在开发环境返回: /media/performance/2025/01/20/image.jpg
// 在生产环境返回: https://yourdomain.com/media/performance/2025/01/20/image.jpg
```

### 缩略图处理

AttachmentUploader组件自动处理缩略图：

- 列表显示：使用缩略图URL（加载快）
- 点击预览：使用原始图片URL（高清晰度）

## 维护说明

### 修改服务器地址

只需要修改 `src/api/http/config.js` 中的配置，或设置对应的环境变量。

### 添加新的URL类型

在 `API_CONFIG` 中添加新的URL配置和构建方法。

### 环境切换

通过环境变量自动适配不同环境，无需修改代码。 