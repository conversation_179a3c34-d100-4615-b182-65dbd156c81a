<template>
  <div ref="myChart" style="width: 100%; height: 90%"></div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    this.initChart()
  },
  watch: {
    data: {
      handler() {
        this.initChart()
      },
      deep: true
    }
  },
  methods: {
    async initChart() {
      const myChart = this.$echarts.init(this.$refs.myChart)
      const option = {
        xAxis: {
          type: 'category',
          name: '',
          axisLabel: {
            color: '#ced6e0',
            fontSize: 10,
          },
          data: this.data.map((item) => item.month),
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 1000,
          axisLine: {
            show: 'true',
          },
          axisLabel: {
            color: '#ced6e0',
            fontSize: 10,
          },
          splitNumber: 5,
          splitLine: {
            lineStyle: {
              color: '#0c4787',
            },
          },
        },
        grid: {
          top: 25,
          bottom: 20,
          borderWidth: 1,
        },
        legend: {
          data: ['记录数（条）'],
          textStyle: {
            color: '#fff',
            fontSize: 11,
          },
          itemWidth: 20,
          itemHeight: 13,
          top: '0%',
          right: 20,
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          textStyle: {
            color: '#34495e',
          },
          axisPointer: {
            type: 'shadow',
          },
        },
        series: [
          {
            type: 'line',
            name: '记录数（条）',
            data: this.data.map((item) => item.value),
            smooth: true,
            lineStyle: {
              color: '#5effff',
            },
            itemStyle: {
              color: '#5effff',
            },
            areaStyle: {
              color: 'rgba(94, 255, 255, 0.2)',
            },
            zlevel: 5,
          },
        ],
      }
      await myChart.setOption(option)
    },
  },
}
</script>

<style scoped></style>
