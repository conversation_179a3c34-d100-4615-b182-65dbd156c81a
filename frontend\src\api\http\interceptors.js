/**
 * Axios拦截器配置
 * 处理请求和响应的统一逻辑
 */
import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'
import { useUserStore } from '@/stores/user'
import router from '@/router'
import { API_CONFIG, HTTP_STATUS, STORAGE_KEYS } from './config'

// Loading状态管理
let loadingInstance = null
let loadingCount = 0

/**
 * 显示全局Loading
 */
const showLoading = () => {
  if (loadingCount === 0) {
    loadingInstance = ElLoading.service({
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
  }
  loadingCount++
}

/**
 * 隐藏全局Loading
 */
const hideLoading = () => {
  loadingCount--
  if (loadingCount <= 0) {
    loadingCount = 0
    loadingInstance?.close()
    loadingInstance = null
  }
}

// Token刷新相关状态
let isRefreshing = false
let failedQueue = []

/**
 * 处理失败队列
 */
const processQueue = (error, token = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })
  failedQueue = []
}

/**
 * 检查refresh token是否存在且有效
 */
const isRefreshTokenValid = () => {
  const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
  if (!refreshToken) {
    return false
  }

  try {
    // 简单检查token格式（JWT通常有三个部分用.分隔）
    const parts = refreshToken.split('.')
    if (parts.length !== 3) {
      return false
    }

    // 解析payload检查过期时间
    const payload = JSON.parse(atob(parts[1]))
    const currentTime = Math.floor(Date.now() / 1000)

    // 检查token是否已过期
    if (payload.exp && payload.exp < currentTime) {
      console.log('Refresh token已过期')
      return false
    }

    return true
  } catch (error) {
    console.error('Refresh token格式检查失败:', error)
    return false
  }
}

/**
 * 清理认证状态并跳转登录页
 */
const clearAuthAndRedirect = (message = '登录已过期，请重新登录') => {
  const userStore = useUserStore()
  
  // 清理本地存储
  localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN)
  localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
  localStorage.removeItem(STORAGE_KEYS.USER_INFO)
  
  // 清理store状态
  userStore.token = ''
  userStore.userInfo = {}
  
  // 显示提示消息
  ElMessage.warning(message)
  
  // 跳转到登录页（避免重复跳转）
  if (router.currentRoute.value.path !== '/login') {
    router.push('/login')
  }
}

/**
 * 刷新访问令牌
 */
const refreshAccessToken = async () => {
  if (isRefreshing) {
    // 如果正在刷新，将请求加入队列
    return new Promise((resolve, reject) => {
      failedQueue.push({ resolve, reject })
    })
  }

  isRefreshing = true

  try {
    const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
    
    if (!refreshToken || !isRefreshTokenValid()) {
      throw new Error('No valid refresh token available')
    }

    console.log('🔄 尝试刷新access token...')

    // 直接使用axios避免循环调用
    const response = await axios.post(`${API_CONFIG.BASE_URL}/users/auth/refresh/`, {
      refresh: refreshToken
    })

    const newAccessToken = response.data.access
    const newRefreshToken = response.data.refresh // 如果启用了ROTATE_REFRESH_TOKENS
    
    // 更新本地存储
    localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, newAccessToken)
    if (newRefreshToken) {
      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, newRefreshToken)
    }
    
    // 更新用户store
    const userStore = useUserStore()
    userStore.token = newAccessToken

    console.log('✅ Token刷新成功')
    processQueue(null, newAccessToken)
    return newAccessToken
  } catch (error) {
    console.error('❌ Token刷新失败:', error)
    
    // 分析刷新失败的原因
    if (error.response?.status === 401) {
      console.log('Refresh token已过期或无效')
      clearAuthAndRedirect('登录已过期，请重新登录')
    } else if (error.code === 'ECONNABORTED' || !error.response) {
      console.log('刷新请求网络错误')
      // 网络错误时也需要清理认证状态，避免无限循环
      clearAuthAndRedirect('网络连接失败，请重新登录')
    } else {
      console.log('其他刷新错误:', error.message)
      clearAuthAndRedirect('认证状态异常，请重新登录')
    }
    
    processQueue(error, null)
    throw error
  } finally {
    isRefreshing = false
  }
}

/**
 * 检查是否为网络连接错误
 */
const isNetworkError = (error) => {
  return !error.response && (
    error.code === 'ECONNREFUSED' ||
    error.code === 'ENOTFOUND' ||
    error.code === 'ECONNABORTED' ||
    error.message === 'Network Error'
  )
}

/**
 * 处理网络连接错误
 */
const handleNetworkError = (error) => {
  console.error('网络连接错误:', error)
  
  // 显示错误消息
  ElMessage.error('网络连接失败，后端服务可能不可用')
  
  // 获取当前路由路径，传递给错误页面
  const currentPath = window.location.pathname
  
  // 跳转到专门的网络错误页面，并传递来源页面信息
  router.push({
    path: '/network-error',
    query: { from: currentPath }
  })
}

/**
 * 处理HTTP错误响应
 */
const handleErrorResponse = (error) => {
  const { response } = error

  // 特殊处理：Token刷新失败的错误不再处理，避免重复跳转
  if (error._isTokenRefreshError) {
    console.log('🚫 Token刷新失败错误，已处理，跳过')
    return
  }

  // 特殊处理：AI生成请求的超时错误不跳转页面
  if (error.code === 'ECONNABORTED' && error.message?.includes('timeout')) {
    const isOpinionGenerate = error.config?.url?.includes('/aiknowledge/opinion/generate/')
    const isAISummaryGenerate = error.config?.url?.includes('/ai-summaries/generate/') || 
                               error.config?.url?.includes('/ai-summaries/') && error.config?.method === 'post'
    
    if (isOpinionGenerate) {
      console.error('AI意见建议生成请求超时:', error)
      ElMessage.error('AI生成请求超时，请稍后重试')
      return
    }
    
    if (isAISummaryGenerate) {
      console.error('AI履职总结生成请求超时:', error)
      ElMessage.error('AI分析生成超时，Dify响应时间较长，请稍后查看结果或重试')
      return
    }
  }
  
  // 网络连接错误，跳转到错误页面
  if (isNetworkError(error)) {
    handleNetworkError(error)
    return
  }

  const { status, data } = response
  const message = data?.message || data?.detail

  switch (status) {
    case HTTP_STATUS.BAD_REQUEST:
      ElMessage.error(message || '请求参数错误')
      break
    case HTTP_STATUS.UNAUTHORIZED:
      // 401错误在响应拦截器中已处理，这里不重复提示
      console.log('401未授权错误已在拦截器中处理')
      break
    case HTTP_STATUS.FORBIDDEN:
      ElMessage.error(API_CONFIG.ERROR_MESSAGES.FORBIDDEN)
      break
    case HTTP_STATUS.NOT_FOUND:
      // AI总结相关的404错误不显示错误消息，避免误导用户
      const isAISummaryRequest = error.config?.url?.includes('/ai-summaries/')
      if (!isAISummaryRequest) {
        ElMessage.error(API_CONFIG.ERROR_MESSAGES.NOT_FOUND)
      } else {
        console.log('AI总结数据不存在，这是正常情况')
      }
      break
    case HTTP_STATUS.VALIDATION_ERROR:
      // 表单验证错误
      if (data?.errors) {
        const messages = Object.values(data.errors).flat()
        ElMessage.error(messages.join('; '))
      } else {
        ElMessage.error(message || API_CONFIG.ERROR_MESSAGES.VALIDATION_ERROR)
      }
      break
    case HTTP_STATUS.INTERNAL_SERVER_ERROR:
      ElMessage.error(API_CONFIG.ERROR_MESSAGES.SERVER_ERROR)
      break
    default:
      ElMessage.error(message || `请求失败 (${status})`)
  }
}

/**
 * 生成请求ID
 */
const generateRequestId = () => {
  return Math.random().toString(36).substr(2, 9)
}

/**
 * 设置axios拦截器
 * @param {Object} axiosInstance - axios实例
 */
export const setupInterceptors = (axiosInstance) => {
  // 请求拦截器
  axiosInstance.interceptors.request.use(
    (config) => {
      // 添加认证token
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN)
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      
      // 处理FormData请求，删除默认的Content-Type让浏览器自动设置
      if (config.data instanceof FormData) {
        delete config.headers['Content-Type']
      }
      
      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = generateRequestId()
      
      // 显示loading（可通过config.showLoading控制）
      if (config.showLoading !== false) {
        showLoading()
      }
      
      // 开发环境下打印请求信息
      if (import.meta.env.DEV) {
        console.log('📤 API请求:', {
          method: config.method?.toUpperCase(),
          url: config.url,
          data: config.data,
          params: config.params,
          timeout: config.timeout, // 显示超时配置
          isFormData: config.data instanceof FormData
        })
      }
      
      return config
    },
    (error) => {
      hideLoading()
      console.error('请求拦截器错误:', error)
      
      // 检查请求阶段的网络错误
      if (isNetworkError(error)) {
        handleNetworkError(error)
      }
      
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  axiosInstance.interceptors.response.use(
    (response) => {
      hideLoading()
      
      // 开发环境下打印响应信息
      if (import.meta.env.DEV) {
        console.log('📥 API响应:', {
          status: response.status,
          url: response.config.url,
          data: response.data
        })
      }
      
      return response
    },
    async (error) => {
      hideLoading()
      
      const originalRequest = error.config
      
      // 检查是否为网络连接错误，如果是则直接处理
      if (isNetworkError(error)) {
        handleErrorResponse(error)
        return Promise.reject(error)
      }
      
      // Token过期，尝试刷新
      if (error.response?.status === HTTP_STATUS.UNAUTHORIZED && !originalRequest._retry) {
        originalRequest._retry = true
        
        // 特殊处理：如果是刷新token的请求失败，直接清理状态
        if (originalRequest.url?.includes('/auth/refresh/')) {
          console.log('🚫 刷新token请求失败，清理认证状态')
          clearAuthAndRedirect('登录已过期，请重新登录')
          return Promise.reject(error)
        }
        
        try {
          const newToken = await refreshAccessToken()
          
          // 重新设置Authorization头
          originalRequest.headers.Authorization = `Bearer ${newToken}`
          
          // 重试原始请求
          console.log('🔄 使用新token重试请求:', originalRequest.url)
          return axiosInstance(originalRequest)
        } catch (refreshError) {
          // 刷新失败，已在refreshAccessToken中处理跳转
          console.log('🚫 Token刷新失败，终止重试')
          // 标记这个错误是Token刷新失败导致的，避免再次触发网络错误处理
          refreshError._isTokenRefreshError = true
          return Promise.reject(refreshError)
        }
      }
      
      // 处理其他错误
      handleErrorResponse(error)
      
      // 开发环境下打印错误详情
      if (import.meta.env.DEV) {
        console.error('❌ API错误:', {
          message: error.message,
          status: error.response?.status,
          url: error.config?.url,
          data: error.response?.data
        })
      }
      
      return Promise.reject(error)
    }
  )
}

/**
 * 错误处理工具函数
 */
export const errorHandler = {
  /**
   * 处理网络错误
   */
  handleNetworkError: (error) => {
    if (isNetworkError(error)) {
      handleNetworkError(error)
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error(API_CONFIG.ERROR_MESSAGES.TIMEOUT_ERROR)
    } else {
      ElMessage.error(API_CONFIG.ERROR_MESSAGES.NETWORK_ERROR)
    }
  },
  
  /**
   * 处理业务错误
   */
  handleBusinessError: (errorCode, errorMessage) => {
    ElMessage.error(errorMessage || '业务处理失败')
  },
  
  /**
   * 清理认证状态
   */
  clearAuthAndRedirect
} 