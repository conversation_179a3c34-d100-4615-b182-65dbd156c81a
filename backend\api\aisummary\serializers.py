"""
代表AI总结序列化器

包含以下序列化器：
1. RepresentativeAISummarySerializer - AI总结完整序列化器
2. AISummaryGenerateRequestSerializer - AI总结生成请求序列化器
3. AISummaryGenerateResponseSerializer - AI总结生成响应序列化器
4. AISummaryListSerializer - AI总结列表序列化器

设计原则：
- 遵循现有应用的序列化器风格
- 严格的数据验证和权限控制
- 友好的错误提示信息
- 完整的字段映射和计算属性
"""

from rest_framework import serializers
from django.utils import timezone
from django.core.exceptions import ValidationError
from api.users.serializers import RepresentativeSerializer
from .models import RepresentativeAISummary


class RepresentativeAISummarySerializer(serializers.ModelSerializer):
    """
    代表AI总结完整序列化器
    用于详情展示和完整信息
    """
    
    # 代表信息（嵌套序列化）
    representative_info = RepresentativeSerializer(
        source='representative',
        read_only=True,
        help_text='代表详细信息'
    )
    
    # 状态显示名称
    status_display = serializers.CharField(
        source='get_status_display',
        read_only=True,
        help_text='状态显示名称'
    )
    
    # 代表姓名（简化字段）
    representative_name = serializers.ReadOnlyField(
        help_text='代表姓名'
    )
    
    # 生成状态标识
    is_completed = serializers.ReadOnlyField(
        help_text='是否已完成'
    )
    
    is_failed = serializers.ReadOnlyField(
        help_text='是否生成失败'
    )
    
    is_generating = serializers.ReadOnlyField(
        help_text='是否正在生成'
    )
    
    has_ai_result = serializers.ReadOnlyField(
        help_text='是否有AI结果'
    )
    
    # 数据来源统计（方法字段）
    source_data_stats = serializers.SerializerMethodField(
        help_text='数据来源统计信息'
    )
    
    # AI结果数据（方法字段）
    ai_result_data = serializers.SerializerMethodField(
        help_text='AI分析结果数据'
    )
    
    # 生成耗时格式化显示
    generation_duration_display = serializers.SerializerMethodField(
        help_text='生成耗时格式化显示'
    )
    
    class Meta:
        model = RepresentativeAISummary
        fields = [
            'id', 'analysis_year', 'status', 'status_display',
            'generation_duration', 'generation_duration_display',
            'error_message', 'created_at', 'updated_at', 'completed_at',
            'representative_info', 'representative_name',
            'is_completed', 'is_failed', 'is_generating', 'has_ai_result',
            'source_data_stats', 'ai_result_data'
        ]
        read_only_fields = [
            'id', 'status', 'generation_duration', 'error_message',
            'created_at', 'updated_at', 'completed_at',
            'representative_info', 'representative_name',
            'is_completed', 'is_failed', 'is_generating', 'has_ai_result'
        ]
    
    def get_source_data_stats(self, obj):
        """获取数据来源统计信息"""
        return obj.get_source_data_stats()
    
    def get_ai_result_data(self, obj):
        """获取AI分析结果数据"""
        return obj.get_ai_result_data()
    
    def get_generation_duration_display(self, obj):
        """获取生成耗时格式化显示"""
        if obj.generation_duration is None:
            return None
        
        duration = obj.generation_duration
        if duration < 60:
            return f"{duration}秒"
        elif duration < 3600:
            minutes = duration // 60
            seconds = duration % 60
            return f"{minutes}分{seconds}秒"
        else:
            hours = duration // 3600
            minutes = (duration % 3600) // 60
            return f"{hours}小时{minutes}分钟"


class AISummaryListSerializer(serializers.ModelSerializer):
    """
    AI总结列表序列化器
    用于列表展示，包含简化信息
    """
    
    # 状态显示名称
    status_display = serializers.CharField(
        source='get_status_display',
        read_only=True,
        help_text='状态显示名称'
    )
    
    # 代表姓名
    representative_name = serializers.ReadOnlyField(
        help_text='代表姓名'
    )
    
    # 生成状态标识
    is_completed = serializers.ReadOnlyField(
        help_text='是否已完成'
    )
    
    has_ai_result = serializers.ReadOnlyField(
        help_text='是否有AI结果'
    )
    
    # 简化的数据统计
    record_count = serializers.SerializerMethodField(
        help_text='履职记录数量'
    )
    
    opinion_count = serializers.SerializerMethodField(
        help_text='意见建议数量'
    )
    
    class Meta:
        model = RepresentativeAISummary
        fields = [
            'id', 'analysis_year', 'status', 'status_display',
            'created_at', 'completed_at', 'representative_name',
            'is_completed', 'has_ai_result', 'record_count', 'opinion_count'
        ]
        read_only_fields = [
            'id', 'status', 'created_at', 'completed_at',
            'representative_name', 'is_completed', 'has_ai_result'
        ]
    
    def get_record_count(self, obj):
        """获取履职记录数量"""
        source_data = obj.get_source_data_stats()
        return source_data.get('performance_records', {}).get('total_count', 0)
    
    def get_opinion_count(self, obj):
        """获取意见建议数量"""
        source_data = obj.get_source_data_stats()
        return source_data.get('opinion_suggestions', {}).get('total_count', 0)


class AISummaryGenerateRequestSerializer(serializers.Serializer):
    """
    AI总结生成请求序列化器
    用于验证生成请求参数
    """
    
    analysis_year = serializers.IntegerField(
        min_value=2020,
        max_value=2050,
        required=True,
        help_text='分析年份，范围：2020-2050',
        error_messages={
            'required': '分析年份不能为空',
            'min_value': '分析年份不能早于2020年',
            'max_value': '分析年份不能晚于2050年',
            'invalid': '请输入有效的年份'
        }
    )
    
    force_regenerate = serializers.BooleanField(
        default=False,
        required=False,
        help_text='是否强制重新生成（覆盖已有结果）'
    )
    
    representative_id = serializers.IntegerField(
        required=False,
        help_text='代表ID（工作人员使用，为指定代表生成AI总结）'
    )
    
    def validate_analysis_year(self, value):
        """验证分析年份"""
        current_year = timezone.now().year
        
        # 不能超过当前年份
        if value > current_year:
            raise serializers.ValidationError(f'分析年份不能超过当前年份({current_year})')
        
        # 不能太久远（避免无意义的分析）
        if value < current_year - 10:
            raise serializers.ValidationError(f'分析年份不能早于{current_year - 10}年')
        
        return value
    
    def validate(self, attrs):
        """整体验证"""
        analysis_year = attrs.get('analysis_year')
        force_regenerate = attrs.get('force_regenerate', False)
        representative_id = attrs.get('representative_id')
        
        # 检查用户权限和目标代表
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            representative = None
            
            if hasattr(request.user, 'representative'):
                # 代表用户，只能为自己生成
                if representative_id and representative_id != request.user.representative.id:
                    raise serializers.ValidationError({
                        'representative_id': '代表用户只能为自己生成AI总结'
                    })
                representative = request.user.representative
                
            elif request.user.role == 'staff':
                # 工作人员用户（使用role检查而不是hasattr）
                if not representative_id:
                    raise serializers.ValidationError({
                        'representative_id': '工作人员生成AI总结必须指定代表ID'
                    })
                
                # 验证代表是否存在
                try:
                    from api.users.models import Representative
                    representative = Representative.objects.get(id=representative_id)
                except Representative.DoesNotExist:
                    raise serializers.ValidationError({
                        'representative_id': '指定的代表不存在'
                    })
            else:
                # 未知用户角色
                raise serializers.ValidationError({
                    'non_field_errors': ['用户角色不正确，无法生成AI总结']
                })
            
            # 检查是否已有记录
            if representative:
                existing = RepresentativeAISummary.objects.filter(
                    representative=representative,
                    analysis_year=analysis_year
                ).first()
                
                if existing and not force_regenerate:
                    if existing.is_completed:
                        raise serializers.ValidationError({
                            'analysis_year': f'{analysis_year}年的AI总结已存在，如需重新生成请设置force_regenerate=True'
                        })
                    elif existing.is_generating:
                        raise serializers.ValidationError({
                            'analysis_year': f'{analysis_year}年的AI总结正在生成中，请稍后再试'
                        })
        
        return attrs


class AISummaryGenerateResponseSerializer(serializers.Serializer):
    """
    AI总结生成响应序列化器
    用于返回生成请求的响应信息
    """
    
    id = serializers.IntegerField(
        read_only=True,
        help_text='AI总结记录ID'
    )
    
    analysis_year = serializers.IntegerField(
        read_only=True,
        help_text='分析年份'
    )
    
    status = serializers.CharField(
        read_only=True,
        help_text='生成状态'
    )
    
    status_display = serializers.CharField(
        read_only=True,
        help_text='状态显示名称'
    )
    
    message = serializers.CharField(
        read_only=True,
        help_text='响应消息'
    )
    
    estimated_duration = serializers.IntegerField(
        read_only=True,
        required=False,
        help_text='预估生成时间（秒）'
    )
    
    created_at = serializers.DateTimeField(
        read_only=True,
        help_text='创建时间'
    )


class AISummaryCheckDataSerializer(serializers.Serializer):
    """
    AI总结数据检查序列化器
    用于检查指定年份的数据情况
    """
    
    analysis_year = serializers.IntegerField(
        min_value=2020,
        max_value=2050,
        required=True,
        help_text='分析年份'
    )
    
    has_performance_data = serializers.BooleanField(
        read_only=True,
        help_text='是否有履职记录数据'
    )
    
    has_opinion_data = serializers.BooleanField(
        read_only=True,
        help_text='是否有意见建议数据'
    )
    
    performance_record_count = serializers.IntegerField(
        read_only=True,
        help_text='履职记录数量'
    )
    
    opinion_suggestion_count = serializers.IntegerField(
        read_only=True,
        help_text='意见建议数量'
    )
    
    can_generate = serializers.BooleanField(
        read_only=True,
        help_text='是否可以生成AI总结'
    )
    
    message = serializers.CharField(
        read_only=True,
        help_text='检查结果说明'
    ) 