"""
代表AI总结模块测试用例

包含以下测试：
1. 模型测试
2. 序列化器测试
3. API测试
4. 服务类测试

设计原则：
- 遵循现有应用的测试风格
- 完整的单元测试覆盖
- 模拟AI服务调用
- 严格的权限测试

uv run manage.py test api.aisummary.tests -v 2
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from unittest.mock import patch, MagicMock
from rest_framework.test import APITestCase
from rest_framework import status
from datetime import datetime, timedelta

from .models import RepresentativeAISummary
from .services import AISummaryService, DataCollectionService
from .serializers import (
    RepresentativeAISummarySerializer,
    AISummaryGenerateRequestSerializer
)
from api.users.models import Representative
from api.performance.models import PerformanceRecord
from api.opinion.models import OpinionSuggestion

User = get_user_model()


class RepresentativeAISummaryModelTest(TestCase):
    """RepresentativeAISummary模型测试"""
    
    def setUp(self):
        """测试数据准备"""
        # 获取或创建用户和代表
        self.user, created = User.objects.get_or_create(
            username='rep001',
            defaults={
                'password': 'test123456',
                'role': 'representative'
            }
        )
        if created:
            self.user.set_password('test123456')
            self.user.save()
        
        self.representative, created = Representative.objects.get_or_create(
            user=self.user,
            defaults={
                'level': '区级',
                'name': '测试代表',
                'gender': 'male',
                'nationality': '汉族',
                'birth_date': '1980-01-01',
                'birthplace': '测试地区',
                'party': '中国共产党',
                'current_position': '测试职位',
                'mobile_phone': '13800138000',
                'education': '本科'
            }
        )
    
    def test_create_ai_summary(self):
        """测试创建AI总结"""
        ai_summary = RepresentativeAISummary.objects.create(
            representative=self.representative,
            analysis_year=2024
        )
        
        self.assertEqual(ai_summary.representative, self.representative)
        self.assertEqual(ai_summary.analysis_year, 2024)
        self.assertEqual(ai_summary.status, 'pending')
        self.assertFalse(ai_summary.is_completed)
        self.assertFalse(ai_summary.has_ai_result)
    
    def test_ai_summary_status_methods(self):
        """测试AI总结状态方法"""
        ai_summary = RepresentativeAISummary.objects.create(
            representative=self.representative,
            analysis_year=2024
        )
        
        # 测试待生成状态
        self.assertFalse(ai_summary.is_completed)
        self.assertFalse(ai_summary.is_failed)
        self.assertFalse(ai_summary.is_generating)
        
        # 测试生成中状态
        ai_summary.mark_as_generating()
        ai_summary.refresh_from_db()
        self.assertTrue(ai_summary.is_generating)
        self.assertEqual(ai_summary.status, 'generating')
        
        # 测试完成状态
        test_result = {'test': 'data'}
        ai_summary.mark_as_completed(test_result, 30)
        ai_summary.refresh_from_db()
        self.assertTrue(ai_summary.is_completed)
        self.assertEqual(ai_summary.ai_result, test_result)
        self.assertEqual(ai_summary.generation_duration, 30)
        self.assertIsNotNone(ai_summary.completed_at)
        
        # 测试失败状态
        ai_summary.mark_as_failed('测试错误')
        ai_summary.refresh_from_db()
        self.assertTrue(ai_summary.is_failed)
        self.assertEqual(ai_summary.error_message, '测试错误')
    
    def test_unique_constraint(self):
        """测试唯一约束"""
        # 创建第一个AI总结
        RepresentativeAISummary.objects.create(
            representative=self.representative,
            analysis_year=2024
        )
        
        # 尝试创建相同代表和年份的记录应该失败
        with self.assertRaises(Exception):
            RepresentativeAISummary.objects.create(
                representative=self.representative,
                analysis_year=2024
            )


class DataCollectionServiceTest(TestCase):
    """数据收集服务测试"""
    
    def setUp(self):
        """测试数据准备"""
        # 获取或创建用户和代表
        self.user, created = User.objects.get_or_create(
            username='rep001',
            defaults={
                'password': 'test123456',
                'role': 'representative'
            }
        )
        if created:
            self.user.set_password('test123456')
            self.user.save()
        
        self.representative, created = Representative.objects.get_or_create(
            user=self.user,
            defaults={
                'level': '区级',
                'name': '测试代表',
                'gender': 'male',
                'nationality': '汉族',
                'birth_date': '1980-01-01',
                'birthplace': '测试地区',
                'party': '中国共产党',
                'current_position': '测试职位',
                'mobile_phone': '13800138000',
                'education': '本科'
            }
        )
        
        # 创建测试履职记录
        self.performance_record = PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date='2024-01-15',
            performance_type='会议参与',
            performance_content='参加人大会议',
            activity_location='市政府会议室',
            detailed_description='认真参与讨论，提出建设性意见',
            performance_status='已完成'
        )
        
        # 创建测试意见建议
        from django.utils import timezone
        import datetime
        test_datetime = timezone.make_aware(datetime.datetime(2024, 1, 20, 10, 0, 0))
        self.opinion = OpinionSuggestion.objects.create(
            representative=self.representative,
            title='关于改善交通的建议',
            category='transportation',
            reporter_name='张三',
            original_content='建议改善市区交通状况',
            final_suggestion='建议增加公交线路'
        )
        # 手动设置创建时间（因为auto_now_add=True不能在create时覆盖）
        self.opinion.created_at = test_datetime
        self.opinion.save(update_fields=['created_at'])
    
    def test_collect_representative_data(self):
        """测试收集代表数据"""
        data = DataCollectionService.collect_representative_data(
            self.representative, 2024
        )
        
        self.assertEqual(data['analysis_year'], 2024)
        self.assertEqual(data['representative_id'], self.representative.id)
        self.assertEqual(data['representative_name'], self.representative.name)
        
        # 检查履职记录数据
        performance_data = data['performance_records']
        self.assertEqual(performance_data['total_count'], 1)
        self.assertEqual(len(performance_data['record_details']), 1)
        
        # 检查意见建议数据
        opinion_data = data['opinion_suggestions']
        self.assertEqual(opinion_data['total_count'], 1)
        self.assertEqual(len(opinion_data['opinion_details']), 1)
        
        # 检查总活动数
        self.assertEqual(data['total_activities'], 2)


class AISummaryAPITest(APITestCase):
    """AI总结API测试"""
    
    def setUp(self):
        """测试数据准备"""
        # 获取或创建代表用户
        self.representative_user, created = User.objects.get_or_create(
            username='rep001',
            defaults={
                'password': 'test123456',
                'role': 'representative'
            }
        )
        if created:
            self.representative_user.set_password('test123456')
            self.representative_user.save()
        
        self.representative, created = Representative.objects.get_or_create(
            user=self.representative_user,
            defaults={
                'level': '区级',
                'name': '测试代表',
                'gender': 'male',
                'nationality': '汉族',
                'birth_date': '1980-01-01',
                'birthplace': '测试地区',
                'party': '中国共产党',
                'current_position': '测试职位',
                'mobile_phone': '13800138000',
                'education': '本科'
            }
        )
        
        # 获取或创建工作人员用户
        self.staff_user, created = User.objects.get_or_create(
            username='staff001',
            defaults={
                'password': 'test123456',
                'role': 'staff'
            }
        )
        if created:
            self.staff_user.set_password('test123456')
            self.staff_user.save()
    
    def test_generate_ai_summary_permission(self):
        """测试生成AI总结权限"""
        # 未登录用户
        response = self.client.post('/api/v1/ai-summaries/generate/', {
            'analysis_year': 2024
        })
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # 工作人员用户（无权限）
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.post('/api/v1/ai-summaries/generate/', {
            'analysis_year': 2024
        })
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # 代表用户（有权限）
        self.client.force_authenticate(user=self.representative_user)
        with patch.object(AISummaryService, 'generate_summary') as mock_generate:
            # 模拟服务返回
            mock_ai_summary = MagicMock()
            mock_ai_summary.id = 1
            mock_ai_summary.analysis_year = 2024
            mock_ai_summary.status = 'generating'
            mock_ai_summary.status_display = '生成中'
            mock_ai_summary.is_generating = True
            mock_ai_summary.is_completed = False
            mock_ai_summary.is_failed = False
            mock_ai_summary.created_at = timezone.now()
            mock_generate.return_value = mock_ai_summary
            
            response = self.client.post('/api/v1/ai-summaries/generate/', {
                'analysis_year': 2024
            })
            self.assertEqual(response.status_code, status.HTTP_202_ACCEPTED)
    
    def test_generate_ai_summary_validation(self):
        """测试生成AI总结参数验证"""
        self.client.force_authenticate(user=self.representative_user)
        
        # 无效年份
        response = self.client.post('/api/v1/ai-summaries/generate/', {
            'analysis_year': 2019
        })
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # 超出当前年份
        next_year = timezone.now().year + 1
        response = self.client.post('/api/v1/ai-summaries/generate/', {
            'analysis_year': next_year
        })
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_get_ai_summary_detail(self):
        """测试获取AI总结详情"""
        # 创建AI总结记录
        ai_summary = RepresentativeAISummary.objects.create(
            representative=self.representative,
            analysis_year=2024,
            status='completed',
            ai_result={'test': 'data'}
        )
        
        # 代表查看自己的AI总结
        self.client.force_authenticate(user=self.representative_user)
        response = self.client.get(f'/api/v1/ai-summaries/{2024}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], ai_summary.id)
        
        # 不存在的年份
        response = self.client.get(f'/api/v1/ai-summaries/{2023}/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_check_data_availability(self):
        """测试检查数据可用性"""
        self.client.force_authenticate(user=self.representative_user)
        
        # 创建测试数据
        PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date='2024-01-15',
            performance_type='会议参与',
            performance_content='参加人大会议',
            activity_location='市政府会议室',
            detailed_description='认真参与讨论',
            performance_status='已完成'
        )
        
        response = self.client.get(f'/api/v1/ai-summaries/{2024}/check/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['can_generate'])
        self.assertTrue(response.data['has_performance_data'])
        self.assertEqual(response.data['performance_record_count'], 1)


class AISummaryServiceTest(TestCase):
    """AI总结服务测试"""
    
    def setUp(self):
        """测试数据准备"""
        # 获取或创建用户和代表
        self.user, created = User.objects.get_or_create(
            username='rep001',
            defaults={
                'password': 'test123456',
                'role': 'representative'
            }
        )
        if created:
            self.user.set_password('test123456')
            self.user.save()
        
        self.representative, created = Representative.objects.get_or_create(
            user=self.user,
            defaults={
                'level': '区级',
                'name': '测试代表',
                'gender': 'male',
                'nationality': '汉族',
                'birth_date': '1980-01-01',
                'birthplace': '测试地区',
                'party': '中国共产党',
                'current_position': '测试职位',
                'mobile_phone': '13800138000',
                'education': '本科'
            }
        )
    
    @patch('api.aisummary.services.should_use_real_ai')
    @patch('api.aisummary.services.AIProviderService.generate_ai_summary')
    def test_generate_summary_success(self, mock_ai_generate, mock_should_use_real_ai):
        """测试成功生成AI总结"""
        # 模拟使用真实AI服务
        mock_should_use_real_ai.return_value = True
        
        # 模拟AI服务返回
        mock_ai_generate.return_value = {
            'overview': {'subtitle': '年度履职总结'},
            'coreMetrics': [],
            'aiSummary': {'conclusion': '表现优秀'}
        }
        
        # 创建测试数据
        PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date='2024-01-15',
            performance_type='会议参与',
            performance_content='参加人大会议',
            activity_location='市政府会议室',
            detailed_description='认真参与讨论',
            performance_status='已完成'
        )
        
        # 生成AI总结
        ai_summary = AISummaryService.generate_summary(
            representative=self.representative,
            analysis_year=2024
        )
        
        self.assertEqual(ai_summary.representative, self.representative)
        self.assertEqual(ai_summary.analysis_year, 2024)
        self.assertTrue(ai_summary.is_completed)
        self.assertTrue(ai_summary.has_ai_result)
        self.assertIsNotNone(ai_summary.generation_duration)
    
    def test_check_data_availability(self):
        """测试检查数据可用性"""
        # 无数据情况
        result = AISummaryService.check_data_availability(
            representative=self.representative,
            analysis_year=2024
        )
        self.assertFalse(result['can_generate'])
        self.assertEqual(result['performance_record_count'], 0)
        self.assertEqual(result['opinion_suggestion_count'], 0)
        
        # 有数据情况
        PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date='2024-01-15',
            performance_type='会议参与',
            performance_content='参加人大会议',
            activity_location='市政府会议室',
            detailed_description='认真参与讨论',
            performance_status='已完成'
        )
        
        result = AISummaryService.check_data_availability(
            representative=self.representative,
            analysis_year=2024
        )
        self.assertTrue(result['can_generate'])
        self.assertEqual(result['performance_record_count'], 1)
        self.assertTrue(result['has_performance_data'])
