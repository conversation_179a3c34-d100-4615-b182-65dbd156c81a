<template>
  <div class="opinions-container">
    <div class="page-header">
      <h2>意见建议</h2>
      <p>收集、处理和跟踪群众意见建议</p>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showAddDialog">
        <el-icon><Plus /></el-icon>
        录入新意见建议
      </el-button>
      <div class="filter-group">
        <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;" @change="loadOpinions">
          <el-option label="全部" value="" />
          <el-option label="草稿" value="draft" />
          <el-option label="已提交" value="submitted" />
          <el-option label="审核中" value="reviewing" />
          <el-option label="已转交" value="transferred" />
          <el-option label="处理中" value="processing" />
          <el-option label="已办结" value="completed" />
        </el-select>
      </div>
    </div>

    <!-- 意见列表 -->
    <el-card>
      <el-table :data="opinionsList" v-loading="loading">
        <el-table-column prop="title" label="意见建议标题" min-width="200" />
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            <el-tag :type="getCategoryColor(row.category)">{{ row.category }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">{{ getStatusText(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="submitDate" label="提交时间" width="120" />
        <el-table-column prop="updateDate" label="更新时间" width="120" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewOpinion(row)">
              查看
            </el-button>
            <el-button 
              v-if="row.status === 'draft'" 
              size="small" 
              type="primary" 
              @click="editOpinion(row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="row.status === 'draft'" 
              size="small" 
              type="success" 
              @click="submitOpinion(row)"
            >
              提交
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '录入意见建议' : '编辑意见建议'"
      width="800px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="opinionForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="意见建议标题" prop="title">
          <el-input
            v-model="opinionForm.title"
            placeholder="请输入意见建议标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="意见建议分类" prop="category">
          <el-select v-model="opinionForm.category" placeholder="请选择意见建议分类" style="width: 100%">
            <el-option label="民生保障" value="民生保障" />
            <el-option label="城市建设" value="城市建设" />
            <el-option label="交通出行" value="交通出行" />
            <el-option label="环境保护" value="环境保护" />
            <el-option label="教育医疗" value="教育医疗" />
            <el-option label="社会治安" value="社会治安" />
            <el-option label="经济发展" value="经济发展" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>

        <el-form-item label="反映人" prop="reflectorInfo">
          <el-input
            v-model="opinionForm.reflectorInfo"
            placeholder="请输入反映人姓名和联系方式（如：张三 13812345678）"
          />
        </el-form-item>

        <el-form-item label="意见建议内容" prop="content">
          <el-input
            v-model="opinionForm.content"
            type="textarea"
            :rows="6"
            placeholder="请详细描述群众反映的意见建议或问题"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="AI辅助生成">
          <el-button type="info" @click="generateAISuggestion" :loading="aiLoading">
            <el-icon><Magic /></el-icon>
            AI辅助生成高质量意见建议
          </el-button>
        </el-form-item>

        <el-form-item v-if="opinionForm.aiSuggestion" label="AI生成内容">
          <div class="ai-suggestion">
            {{ opinionForm.aiSuggestion }}
          </div>
        </el-form-item>

        <el-form-item label="最终意见建议" prop="suggestion">
          <el-input
            v-model="opinionForm.suggestion"
            type="textarea"
            :rows="4"
            placeholder="基于AI生成内容或您的判断，完善最终的意见建议"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button @click="saveOpinion">保存草稿</el-button>
        <el-button type="primary" @click="saveAndSubmitOpinion">保存并提交</el-button>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="意见详情"
      width="800px"
    >
      <div v-if="currentOpinion" class="opinion-detail">
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="detail-item">
            <span class="label">意见建议标题：</span>
            <span class="value">{{ currentOpinion.title }}</span>
          </div>
          <div class="detail-item">
            <span class="label">意见建议分类：</span>
            <el-tag :type="getCategoryColor(currentOpinion.category)">{{ currentOpinion.category }}</el-tag>
          </div>
          <div class="detail-item">
            <span class="label">当前状态：</span>
            <el-tag :type="getStatusColor(currentOpinion.status)">{{ getStatusText(currentOpinion.status) }}</el-tag>
          </div>
          <div class="detail-item">
            <span class="label">反映人：</span>
            <span class="value">{{ currentOpinion.reflectorInfo }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h3>意见建议内容</h3>
          <div class="content-box">{{ currentOpinion.content }}</div>
        </div>

        <div v-if="currentOpinion.aiSuggestion" class="detail-section">
          <h3>AI生成内容</h3>
          <div class="ai-suggestion">{{ currentOpinion.aiSuggestion }}</div>
        </div>

        <div v-if="currentOpinion.suggestion" class="detail-section">
          <h3>最终意见建议</h3>
          <div class="content-box">{{ currentOpinion.suggestion }}</div>
        </div>

        <div v-if="currentOpinion.feedback" class="detail-section">
          <h3>处理反馈</h3>
          <div class="content-box">{{ currentOpinion.feedback }}</div>
        </div>

        <!-- 处理流程 -->
        <div class="detail-section">
          <h3>处理流程</h3>
          <el-timeline>
            <el-timeline-item
              v-for="step in getProcessSteps(currentOpinion)"
              :key="step.timestamp"
              :timestamp="step.timestamp"
              :type="step.type"
            >
              {{ step.description }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 数据状态
const loading = ref(false)
const aiLoading = ref(false)
const opinionsList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const statusFilter = ref('')

// 对话框状态
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit'

// 表单引用
const formRef = ref()

// 当前意见
const currentOpinion = ref(null)

// 表单数据
const opinionForm = reactive({
  id: null,
  title: '',
  category: '',
  reflectorInfo: '',
  content: '',
  aiSuggestion: '',
  suggestion: '',
  status: 'draft'
})

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入意见建议标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择意见建议分类', trigger: 'change' }
  ],
  reflectorInfo: [
    { required: true, message: '请输入反映人信息', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入意见建议内容', trigger: 'blur' },
    { min: 10, max: 1000, message: '内容长度在 10 到 1000 个字符', trigger: 'blur' }
  ]
}

// 获取分类颜色
const getCategoryColor = (category) => {
  const colorMap = {
    '民生保障': 'primary',
    '城市建设': 'success',
    '交通出行': 'warning',
    '环境保护': 'info',
    '教育医疗': 'danger',
    '社会治安': '',
    '经济发展': 'success',
    '其他': ''
  }
  return colorMap[category] || ''
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'draft': 'info',
    'submitted': 'primary',
    'reviewing': 'warning',
    'transferred': 'success',
    'processing': 'warning',
    'completed': 'success'
  }
  return colorMap[status] || ''
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'draft': '草稿',
    'submitted': '已提交',
    'reviewing': '审核中',
    'transferred': '已转交',
    'processing': '处理中',
    'completed': '已办结'
  }
  return textMap[status] || status
}

// 获取处理步骤
const getProcessSteps = (opinion) => {
  const steps = [
    {
      timestamp: opinion.createDate,
      description: '创建意见草稿',
      type: 'primary'
    }
  ]
  
  if (opinion.submitDate) {
    steps.push({
      timestamp: opinion.submitDate,
      description: '提交给站点工作人员',
      type: 'success'
    })
  }
  
  if (opinion.status === 'reviewing') {
    steps.push({
      timestamp: opinion.updateDate,
      description: '站点工作人员审核中',
      type: 'warning'
    })
  }
  
  if (opinion.status === 'transferred') {
    steps.push({
      timestamp: opinion.updateDate,
      description: '已转交相关职能部门',
      type: 'success'
    })
  }
  
  if (opinion.status === 'completed') {
    steps.push({
      timestamp: opinion.updateDate,
      description: '意见已办结',
      type: 'success'
    })
  }
  
  return steps
}

// 显示新增对话框
const showAddDialog = () => {
  dialogMode.value = 'add'
  resetForm()
  dialogVisible.value = true
}

// 编辑意见
const editOpinion = (row) => {
  dialogMode.value = 'edit'
  Object.assign(opinionForm, row)
  dialogVisible.value = true
}

// 查看意见
const viewOpinion = (row) => {
  currentOpinion.value = row
  viewDialogVisible.value = true
}

// 提交意见
const submitOpinion = async (row) => {
  try {
    await ElMessageBox.confirm('确定要提交该意见吗？提交后将无法修改。', '确认提交', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 模拟提交操作
    const index = opinionsList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      opinionsList.value[index].status = 'submitted'
      opinionsList.value[index].submitDate = new Date().toLocaleDateString()
      opinionsList.value[index].updateDate = new Date().toLocaleDateString()
    }
    
    ElMessage.success('意见提交成功')
  } catch {
    // 用户取消
  }
}

// AI辅助生成高质量意见建议
const generateAISuggestion = async () => {
  if (!opinionForm.content.trim()) {
    ElMessage.warning('请先输入意见建议内容')
    return
  }
  
  aiLoading.value = true
  
  // 模拟AI处理
  setTimeout(() => {
    const suggestions = [
      '关于加强小区停车管理的建议：建议相关部门制定停车位配置标准，优化停车位设计；建立智能停车管理系统，实现车位信息共享；加强停车管理执法，规范停车秩序；探索错时停车、共享停车等创新模式，缓解停车难题。',
      '关于完善社区儿童游乐设施的建议：建议在社区公园增设适龄儿童游乐设备，设施应符合国家安全标准；建立定期维护检查机制，确保设施安全；合理规划布局，避免噪音扰民；建议政府加大投入，完善相关配套设施。',
      '关于优化交通信号灯配时的建议：建议交通部门根据实际车流量调整信号灯配时方案；引入智能交通管理系统，实现动态调节；在重要路口增设倒计时显示器；建立交通流量监测机制，为配时优化提供数据支撑。',
      '关于改善社区环境质量的建议：建议加强环境监测，定期公布数据；完善垃圾分类管理制度，提高居民参与度；增加绿化面积，改善空气质量；建立环境问题投诉处理机制，及时回应群众关切。'
    ]
    
    opinionForm.aiSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)]
    aiLoading.value = false
    ElMessage.success('AI高质量意见建议生成成功')
  }, 1500)
}

// 重置表单
const resetForm = () => {
  Object.assign(opinionForm, {
    id: null,
    title: '',
    category: '',
    reflectorInfo: '',
    content: '',
    aiSuggestion: '',
    suggestion: '',
    status: 'draft'
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 保存意见
const saveOpinion = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  await saveOpinionData('draft')
}

// 保存并提交意见
const saveAndSubmitOpinion = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  await saveOpinionData('submitted')
}

// 保存意见数据
const saveOpinionData = async (status) => {
  try {
    loading.value = true
    
    const opinionData = {
      ...opinionForm,
      status,
      updateDate: new Date().toLocaleDateString()
    }

    if (dialogMode.value === 'add') {
      opinionData.id = Date.now()
      opinionData.createDate = new Date().toLocaleDateString()
      if (status === 'submitted') {
        opinionData.submitDate = new Date().toLocaleDateString()
      }
      opinionsList.value.unshift(opinionData)
      total.value++
      ElMessage.success(status === 'submitted' ? '意见提交成功' : '草稿保存成功')
    } else {
      const index = opinionsList.value.findIndex(item => item.id === opinionData.id)
      if (index !== -1) {
        if (status === 'submitted' && opinionsList.value[index].status === 'draft') {
          opinionData.submitDate = new Date().toLocaleDateString()
        }
        opinionsList.value[index] = opinionData
      }
      ElMessage.success(status === 'submitted' ? '意见提交成功' : '意见更新成功')
    }

    dialogVisible.value = false
    resetForm()
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  loadOpinions()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadOpinions()
}

// 加载意见列表
const loadOpinions = () => {
  loading.value = true
  
  // 模拟数据
  setTimeout(() => {
    let mockData = [
      {
        id: 1,
        title: '关于小区停车难问题',
        category: '城市建设',
        reflectorInfo: '张三 13812345678',
        content: '我们小区停车位严重不足，经常出现车辆乱停乱放的情况，希望能够增设停车位或规范停车管理。',
        aiSuggestion: '建议联系城管部门和物业公司进行实地调研，评估增设停车位的可行性。',
        suggestion: '建议协调相关部门，制定停车位增设计划，同时加强停车管理。',
        status: 'reviewing',
        createDate: '2024-01-16',
        submitDate: '2024-01-16',
        updateDate: '2024-01-16'
      },
      {
        id: 2,
        title: '建议增设儿童游乐设施',
        category: '民生保障',
        reflectorInfo: '李四 13987654321',
        content: '社区内缺少儿童游乐设施，希望能够在公园内增设一些适合儿童的娱乐设备。',
        status: 'draft',
        createDate: '2024-01-15',
        updateDate: '2024-01-15'
      },
      {
        id: 3,
        title: '交通信号灯时间调整建议',
        category: '交通出行',
        reflectorInfo: '王五 13511112222',
        content: '某路口的交通信号灯绿灯时间过短，经常造成交通拥堵，建议适当延长绿灯时间。',
        status: 'completed',
        createDate: '2024-01-10',
        submitDate: '2024-01-10',
        updateDate: '2024-01-14',
        feedback: '已联系交通部门，信号灯时间已调整优化。'
      }
    ]
    
    // 状态筛选
    if (statusFilter.value) {
      mockData = mockData.filter(item => item.status === statusFilter.value)
    }
    
    opinionsList.value = mockData
    total.value = mockData.length
    loading.value = false
  }, 500)
}

// 初始化
onMounted(() => {
  loadOpinions()
})
</script>

<style scoped>
.opinions-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: var(--china-red);
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  margin: 0;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.ai-suggestion {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 15px;
  color: #0369a1;
  line-height: 1.6;
  font-size: 14px;
}

.opinion-detail {
  line-height: 1.6;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  color: var(--china-red);
  margin-bottom: 12px;
  font-size: 16px;
  border-bottom: 2px solid var(--china-red);
  padding-bottom: 4px;
}

.detail-item {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.label {
  font-weight: bold;
  color: var(--text-color);
  min-width: 100px;
  margin-right: 10px;
}

.value {
  flex: 1;
  color: var(--text-color);
}

.content-box {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid var(--china-red);
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .opinions-container {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .filter-group {
    justify-content: flex-end;
  }
  
  /* 移动端隐藏部分列 */
  :deep(.el-table .el-table__cell:nth-child(3)),
  :deep(.el-table .el-table__cell:nth-child(4)),
  :deep(.el-table .el-table__cell:nth-child(5)) {
    display: none;
  }
}
</style> 