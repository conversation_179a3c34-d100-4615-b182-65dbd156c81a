import { mockRequest } from './request.js'

/**
 * 代表履职类型统计API
 * 提供人大代表各种履职活动的统计数据
 */

/**
 * 获取代表履职类型统计数据（真实API接口，暂时注释）
 * @param {Object} params - 查询参数
 * @param {string} params.month - 月份 (格式: YYYY-MM)
 * @param {string} params.year - 年份 (格式: YYYY)
 * @returns {Promise} 返回履职统计数据
 */
// export const getRepresentativeDutiesReal = (params = {}) => {
//   return request({
//     url: '/api/representative/duties',
//     method: 'GET',
//     params
//   })
// }

/**
 * 获取履职类型详情
 * @param {string} dutyType - 履职类型
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回履职详情数据
 */
export const getDutyTypeDetail = (dutyType, params = {}) => {
  return mockRequest(`/api/representative/duties/${dutyType}`, 'GET', params)
}

/**
 * 获取履职趋势数据
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回履职趋势数据
 */
export const getDutyTrend = (params = {}) => {
  return mockRequest('/api/representative/duties/trend', 'GET', params)
}

// ==================== Mock 数据 ====================

/**
 * 模拟代表履职类型统计数据
 * 包含11种履职类型（根据最新要求调整）
 */
const mockDutiesData = {
  // 2024年各月份数据
  '2024': {
    '01': [
      { name: '视察调研', value: 156, color: '#DC143C', percentage: 18.5 },
      { name: '学习培训', value: 142, color: '#B22222', percentage: 16.8 },
      { name: '接待走访', value: 128, color: '#CD5C5C', percentage: 15.2 },
      { name: '执法检查', value: 89, color: '#FFD700', percentage: 10.6 },
      { name: '主题活动', value: 76, color: '#FFA500', percentage: 9.0 },
      { name: '述职', value: 68, color: '#FF4500', percentage: 8.1 },
      { name: '法规征询意见', value: 54, color: '#8B0000', percentage: 6.4 },
      { name: '政策法规宣传', value: 47, color: '#A0522D', percentage: 5.6 },
      { name: '议案建议办理督办', value: 42, color: '#DAA520', percentage: 5.0 },
      { name: '会议', value: 35, color: '#4B0082', percentage: 4.1 },
      { name: '其它', value: 29, color: '#FF8C00', percentage: 3.4 }
    ],
    '02': [
      { name: '视察调研', value: 134, color: '#00D4FF', percentage: 17.8 },
      { name: '学习培训', value: 125, color: '#FF6B6B', percentage: 16.6 },
      { name: '接待走访', value: 118, color: '#4ECDC4', percentage: 15.7 },
      { name: '执法检查', value: 92, color: '#45B7D1', percentage: 12.2 },
      { name: '主题活动', value: 78, color: '#96CEB4', percentage: 10.4 },
      { name: '述职', value: 65, color: '#FFEAA7', percentage: 8.6 },
      { name: '法规征询意见', value: 48, color: '#DDA0DD', percentage: 6.4 },
      { name: '政策法规宣传', value: 41, color: '#98D8C8', percentage: 5.4 },
      { name: '议案建议办理督办', value: 35, color: '#F7DC6F', percentage: 4.7 },
      { name: '会议', value: 28, color: '#BB8FCE', percentage: 3.7 },
      { name: '其它', value: 22, color: '#85C1E9', percentage: 2.9 }
    ],
    '03': [
      { name: '视察调研', value: 168, color: '#00D4FF', percentage: 19.2 },
      { name: '学习培训', value: 155, color: '#FF6B6B', percentage: 17.7 },
      { name: '接待走访', value: 138, color: '#4ECDC4', percentage: 15.8 },
      { name: '执法检查', value: 95, color: '#45B7D1', percentage: 10.9 },
      { name: '主题活动', value: 82, color: '#96CEB4', percentage: 9.4 },
      { name: '述职', value: 71, color: '#FFEAA7', percentage: 8.1 },
      { name: '法规征询意见', value: 58, color: '#DDA0DD', percentage: 6.6 },
      { name: '政策法规宣传', value: 49, color: '#98D8C8', percentage: 5.6 },
      { name: '议案建议办理督办', value: 41, color: '#F7DC6F', percentage: 4.7 },
      { name: '会议', value: 33, color: '#BB8FCE', percentage: 3.8 },
      { name: '其它', value: 27, color: '#85C1E9', percentage: 3.1 }
    ],
    '04': [
      { name: '视察调研', value: 145, color: '#00D4FF', percentage: 18.1 },
      { name: '学习培训', value: 132, color: '#FF6B6B', percentage: 16.5 },
      { name: '接待走访', value: 124, color: '#4ECDC4', percentage: 15.5 },
      { name: '执法检查', value: 88, color: '#45B7D1', percentage: 11.0 },
      { name: '主题活动', value: 75, color: '#96CEB4', percentage: 9.4 },
      { name: '述职', value: 67, color: '#FFEAA7', percentage: 8.4 },
      { name: '法规征询意见', value: 52, color: '#DDA0DD', percentage: 6.5 },
      { name: '政策法规宣传', value: 45, color: '#98D8C8', percentage: 5.6 },
      { name: '议案建议办理督办', value: 38, color: '#F7DC6F', percentage: 4.7 },
      { name: '会议', value: 30, color: '#BB8FCE', percentage: 3.7 },
      { name: '其它', value: 24, color: '#85C1E9', percentage: 3.0 }
    ],
    '05': [
      { name: '视察调研', value: 172, color: '#00D4FF', percentage: 19.5 },
      { name: '学习培训', value: 158, color: '#FF6B6B', percentage: 17.9 },
      { name: '接待走访', value: 141, color: '#4ECDC4', percentage: 16.0 },
      { name: '执法检查', value: 98, color: '#45B7D1', percentage: 11.1 },
      { name: '主题活动', value: 84, color: '#96CEB4', percentage: 9.5 },
      { name: '述职', value: 73, color: '#FFEAA7', percentage: 8.3 },
      { name: '法规征询意见', value: 59, color: '#DDA0DD', percentage: 6.7 },
      { name: '政策法规宣传', value: 51, color: '#98D8C8', percentage: 5.8 },
      { name: '议案建议办理督办', value: 43, color: '#F7DC6F', percentage: 4.9 },
      { name: '会议', value: 35, color: '#BB8FCE', percentage: 4.0 },
      { name: '其它', value: 29, color: '#85C1E9', percentage: 3.3 }
    ],
    '06': [
      { name: '视察调研', value: 163, color: '#00D4FF', percentage: 18.8 },
      { name: '学习培训', value: 149, color: '#FF6B6B', percentage: 17.2 },
      { name: '接待走访', value: 135, color: '#4ECDC4', percentage: 15.6 },
      { name: '执法检查', value: 91, color: '#45B7D1', percentage: 10.5 },
      { name: '主题活动', value: 79, color: '#96CEB4', percentage: 9.1 },
      { name: '述职', value: 69, color: '#FFEAA7', percentage: 8.0 },
      { name: '法规征询意见', value: 56, color: '#DDA0DD', percentage: 6.5 },
      { name: '政策法规宣传', value: 48, color: '#98D8C8', percentage: 5.5 },
      { name: '议案建议办理督办', value: 40, color: '#F7DC6F', percentage: 4.6 },
      { name: '会议', value: 32, color: '#BB8FCE', percentage: 3.7 },
      { name: '其它', value: 26, color: '#85C1E9', percentage: 3.0 }
    ]
  }
}

/**
 * 获取代表履职统计数据
 * @param {Object} params - 查询参数
 * @param {string} params.month - 月份 (格式: YYYY-MM)
 * @param {string} params.year - 年份 (格式: YYYY)
 * @returns {Promise} 返回履职统计数据
 */
export const getRepresentativeDuties = async (params = {}) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const { month, year = '2024' } = params
  
  try {
    if (month) {
      // 返回指定月份的数据
      const monthKey = month.split('-')[1] // 从 YYYY-MM 格式中提取月份
      const monthData = mockDutiesData[year]?.[monthKey] || []
      
      return {
        success: true,
        data: {
          month: month,
          year: year,
          duties: monthData,
          total: monthData.reduce((sum, item) => sum + item.value, 0),
          updateTime: new Date().toISOString()
        }
      }
    } else {
      // 返回全年汇总数据
      const yearData = mockDutiesData[year] || {}
      const allMonthsData = Object.values(yearData).flat()
      
      // 按履职类型汇总
      const dutyTypeMap = new Map()
      allMonthsData.forEach(item => {
        if (dutyTypeMap.has(item.name)) {
          dutyTypeMap.get(item.name).value += item.value
        } else {
          dutyTypeMap.set(item.name, { ...item })
        }
      })
      
      const yearSummary = Array.from(dutyTypeMap.values())
      const totalValue = yearSummary.reduce((sum, item) => sum + item.value, 0)
      
      // 重新计算百分比
      yearSummary.forEach(item => {
        item.percentage = ((item.value / totalValue) * 100).toFixed(1)
      })
      
      // 按数值排序
      yearSummary.sort((a, b) => b.value - a.value)
      
      return {
        success: true,
        data: {
          year: year,
          duties: yearSummary,
          total: totalValue,
          updateTime: new Date().toISOString()
        }
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '获取履职统计数据失败',
      error: error.message
    }
  }
}

export default {
  getRepresentativeDuties,
  getDutyTypeDetail,
  getDutyTrend
}
