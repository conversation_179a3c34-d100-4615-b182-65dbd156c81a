# Generated by Django 5.2.3 on 2025-07-28 13:44

from django.db import migrations


def set_default_composition(apps, schema_editor):
    """将现有代表的构成字段设置为'其他'"""
    Representative = apps.get_model('users', 'Representative')

    # 更新所有现有代表的构成字段为'其他'
    Representative.objects.filter(composition__isnull=True).update(composition='其他')
    Representative.objects.filter(composition='').update(composition='其他')

    print(f"已将 {Representative.objects.filter(composition='其他').count()} 条代表记录的构成设置为'其他'")


def reverse_set_default_composition(apps, schema_editor):
    """回滚操作：将构成字段设置为空"""
    Representative = apps.get_model('users', 'Representative')

    # 将所有'其他'构成的记录设置为空
    Representative.objects.filter(composition='其他').update(composition=None)


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0008_add_composition_field'),
    ]

    operations = [
        migrations.RunPython(set_default_composition, reverse_set_default_composition),
    ]
