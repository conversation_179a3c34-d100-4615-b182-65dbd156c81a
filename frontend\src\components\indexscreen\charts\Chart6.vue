<template>
  <div style="position: relative; width: 100%; height: 95%">
    <!-- 右上角信息栏 -->
    <div
      style="
        position: absolute;
        right: 20px;
        top: 10px;
        z-index: 10;
        display: flex;
        align-items: center;
      "
    >
      <span
        style="
          color: #fff;
          font-size: 14px;
          margin-right: 10px;
          border-bottom: 1px skyblue solid;
        "
      >
        {{ isYearlyData ? '当年累计履职' : '当月累计履职' }}
        <span style="color: #ffd700; font-size: 20px">{{ total }}</span> 条
      </span>
      <select
        v-model="selectedMonth"
        @change="onMonthChange"
        style="
          background: #0a2740;
          color: #ffd700;
          border: 1px solid #1e90ff;
          border-radius: 4px;
          padding: 2px 8px;
          max-width: 120px;
        "
      >
        <optgroup label="年度汇总">
          <option v-for="month in yearOptions" :key="month" :value="month">
            {{ month }}
          </option>
        </optgroup>
        <optgroup label="月度明细">
          <option v-for="month in monthOptions" :key="month" :value="month">
            {{ month }}
          </option>
        </optgroup>
      </select>
    </div>
    <!-- 图表 -->
    <div ref="myChart" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
export default {
  props: {
    months: {
      type: Array,
      default: () => [],
    },
    chartData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      total: 0,
      myChart: null,
      selectedMonth: '2025年7月',
    }
  },
  computed: {
    // 判断当前选择的是否为年度数据
    isYearlyData() {
      return this.selectedMonth && this.selectedMonth.endsWith('年') && !this.selectedMonth.includes('月')
    },
    // 年度选项
    yearOptions() {
      return this.months.filter(month => month.endsWith('年') && !month.includes('月'))
    },
    // 月度选项
    monthOptions() {
      return this.months.filter(month => month.includes('月'))
    }
  },
  mounted() {
    // 使用nextTick确保DOM已经渲染
    this.$nextTick(() => {
      // 如果有月份数据，使用第一个月份作为默认选中
      if (this.months && this.months.length > 0) {
        this.selectedMonth = this.months[0]
      }
      this.initChart()
    })
  },
  watch: {
    months: {
      handler(newMonths) {
        this.$nextTick(() => {
          if (newMonths && newMonths.length > 0) {
            this.selectedMonth = newMonths[0]
          }
          this.initChart()
        })
      },
      immediate: true,
    },
    chartData: {
      handler() {
        this.initChart()
      },
      deep: true,
    },
    selectedMonth() {
      this.initChart()
    },
  },
  methods: {
    initChart() {
      // 检查DOM元素是否存在
      if (!this.$refs.myChart) {
        console.warn('Chart6: DOM element not ready')
        return
      }

      // 检查数据是否存在
      if (!this.chartData || !this.selectedMonth) {
        console.warn('Chart6: Data not ready')
        return
      }

      if (!this.myChart) {
        this.myChart = this.$echarts.init(this.$refs.myChart)
      }
      const { categories, values } = this.chartData[this.selectedMonth] || {
        categories: [],
        values: [],
      }
      this.total = values.reduce((a, b) => a + b, 0)
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            const item = params[0]
            const period = this.isYearlyData ? '年度' : '月度'
            return `${item.name}<br/>${period}履职: ${item.value} 条`
          }
        },
        grid: { left: 40, right: 30, bottom: 5, top: 70, containLabel: true },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            color: '#fff',
            rotate: 45,
            fontSize: this.isYearlyData ? 9 : 10,  // 年度数据字体稍小
            interval: this.isYearlyData ? 0 : 'auto'  // 年度数据显示所有标签
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#ffffff', fontSize: 12 },
          splitLine: { lineStyle: { color: '#0c4787', type: 'dashed' } },
          axisLine: { show: 'true' },
        },
        series: [
          {
            data: values,
            type: 'bar',
            barWidth: this.isYearlyData ? 12 : 15,  // 年度数据柱子稍细
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: this.isYearlyData ? [
                  { offset: 0, color: '#ff9500' },  // 年度数据使用橙色渐变
                  { offset: 1, color: '#ff6b00' },
                ] : [
                  { offset: 0, color: '#69b2ff' },  // 月度数据使用蓝色渐变
                  { offset: 1, color: '#205afd' },
                ],
              },
            },
          },
        ],
      }
      this.myChart.setOption(option)
    },
    onMonthChange() {
      this.initChart()
    },
  },
}
</script>

<style scoped></style>
