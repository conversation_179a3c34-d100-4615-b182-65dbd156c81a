"""
履职管理数据模型

包含以下模型：
1. PerformanceRecord - 履职记录主表
2. PerformanceAttachment - 履职记录附件表

设计原则：
- 严格按照数据库设计文档实现
- 确保数据完整性和一致性
- 支持多媒体附件管理
- 考虑老年用户使用场景
"""

import os
import hashlib
from django.db import models
from django.core.validators import RegexValidator
from django.utils import timezone
from django.conf import settings
from api.users.models import Representative
from django.urls import reverse


class PerformanceRecord(models.Model):
    """
    履职记录模型
    对应数据库设计中的performance_records表
    """
    
    # 履职类型选择（根据最新要求调整为11个类型）
    PERFORMANCE_TYPE_CHOICES = [
        ('视察调研', '视察调研'),
        ('学习培训', '学习培训'),
        ('接待走访', '接待走访'),
        ('执法检查', '执法检查'),
        ('主题活动', '主题活动'),
        ('述职', '述职'),
        ('法规征询意见', '法规征询意见'),
        ('政策法规宣传', '政策法规宣传'),
        ('议案建议办理督办', '议案建议办理督办'),
        ('会议', '会议'),
        ('其它', '其它'),
    ]
    
    # 履职状态选择
    PERFORMANCE_STATUS_CHOICES = [
        ('进行中', '进行中'),
        ('已完成', '已完成'),
        ('已暂停', '已暂停'),
    ]
    
    # 主键ID（BigInt自增）
    id = models.BigAutoField(
        primary_key=True,
        verbose_name='履职记录ID'
    )
    
    # 关联代表表（外键关系）
    representative = models.ForeignKey(
        Representative,
        on_delete=models.CASCADE,
        related_name='performance_records',
        verbose_name='关联代表',
        help_text='与代表表的外键关联，删除代表时级联删除履职记录'
    )
    
    # 履职日期
    performance_date = models.DateField(
        verbose_name='履职日期',
        help_text='履职活动发生的日期，不能超过今天'
    )
    
    # 履职类型
    performance_type = models.CharField(
        max_length=50,
        choices=PERFORMANCE_TYPE_CHOICES,
        verbose_name='履职类型',
        help_text='履职活动的类型分类，预设17种类型'
    )
    
    # 履职内容（主要内容，文本域，限制字数）
    performance_content = models.TextField(
        verbose_name='履职内容',
        help_text='履职活动的主要内容描述，建议100-200字'
    )
    
    # 活动地点
    activity_location = models.CharField(
        max_length=200,
        verbose_name='活动地点',
        help_text='履职活动发生的具体地点'
    )
    
    # 详细描述（可选，更长的文本）
    detailed_description = models.TextField(
        null=True,
        blank=True,
        verbose_name='详细描述',
        help_text='履职活动的详细情况描述，可选填写，建议300-500字'
    )
    
    # 履职状态
    performance_status = models.CharField(
        max_length=50,
        choices=PERFORMANCE_STATUS_CHOICES,
        default='已完成',
        verbose_name='履职状态',
        help_text='履职活动的当前状态'
    )
    
    # 是否有附件（布尔字段，用于快速查询）
    has_attachments = models.BooleanField(
        default=False,
        verbose_name='是否有附件',
        help_text='标识该履职记录是否包含多媒体附件，自动维护'
    )
    
    # 创建时间
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )
    
    # 更新时间
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    class Meta:
        db_table = 'performance_records'
        verbose_name = '履职记录'
        verbose_name_plural = '履职记录'
        ordering = ['-performance_date', '-created_at']
        indexes = [
            # 单字段索引
            models.Index(fields=['representative']),
            models.Index(fields=['performance_date']),
            models.Index(fields=['performance_type']),
            models.Index(fields=['performance_status']),
            models.Index(fields=['has_attachments']),
            # 复合索引（查询优化）
            models.Index(fields=['representative', 'performance_date']),
            models.Index(fields=['representative', 'performance_type']),
        ]
    
    def __str__(self):
        return f"{self.representative.name} - {self.performance_type} - {self.performance_date}"
    
    def save(self, *args, **kwargs):
        """保存时自动更新has_attachments字段"""
        super().save(*args, **kwargs)
        # 保存后检查是否有附件
        has_attachments = self.attachments.exists()
        if self.has_attachments != has_attachments:
            PerformanceRecord.objects.filter(id=self.id).update(
                has_attachments=has_attachments
            )
    
    def get_attachment_count(self):
        """获取附件总数"""
        return self.attachments.count()
    
    def get_attachment_stats(self):
        """获取附件统计信息"""
        attachments = self.attachments.all()
        stats = {
            'total': attachments.count(),
            'images': attachments.filter(file_type='image').count(),
            'audios': attachments.filter(file_type='audio').count(),
            'videos': attachments.filter(file_type='video').count(),
            'documents': attachments.filter(file_type='document').count(),
        }
        return stats
    
    def can_add_attachment(self, file_type):
        """检查是否可以添加指定类型的附件"""
        # 根据设计文档的数量限制
        limits = {
            'image': 9,
            'audio': 3,
            'video': 2,
            'document': 5
        }
        current_count = self.attachments.filter(file_type=file_type).count()
        return current_count < limits.get(file_type, 0)
    
    def update_attachment_status(self):
        """更新附件状态"""
        has_attachments = self.attachments.exists()
        if self.has_attachments != has_attachments:
            self.has_attachments = has_attachments
            self.save(update_fields=['has_attachments'])


class PerformanceAttachment(models.Model):
    """
    履职记录附件模型
    对应数据库设计中的performance_attachments表
    支持图片、音频、视频、文档四种类型
    """
    
    # 文件类型选择
    FILE_TYPE_CHOICES = [
        ('image', '图片'),
        ('audio', '音频'),
        ('video', '视频'),
        ('document', '文档'),
    ]
    
    # 上传状态选择
    UPLOAD_STATUS_CHOICES = [
        ('uploading', '上传中'),
        ('uploaded', '已上传'),
        ('failed', '上传失败'),
        ('processing', '处理中'),
    ]
    
    # 主键ID
    id = models.BigAutoField(
        primary_key=True,
        verbose_name='附件ID'
    )
    
    # 关联履职记录表（外键关系）
    performance_record = models.ForeignKey(
        PerformanceRecord,
        on_delete=models.CASCADE,
        related_name='attachments',
        verbose_name='关联履职记录',
        help_text='与履职记录表的外键关联，删除记录时级联删除附件'
    )
    
    # 文件类型
    file_type = models.CharField(
        max_length=20,
        choices=FILE_TYPE_CHOICES,
        verbose_name='文件类型',
        help_text='附件的文件类型分类：图片/音频/视频/文档'
    )
    
    # 原始文件名
    original_filename = models.CharField(
        max_length=255,
        verbose_name='原始文件名',
        help_text='用户上传时的原始文件名'
    )
    
    # 存储文件名（UUID格式：{uuid}_{timestamp}_{original_name}）
    stored_filename = models.CharField(
        max_length=255,
        verbose_name='存储文件名',
        help_text='系统生成的唯一存储文件名，格式：uuid_timestamp_原始名'
    )
    
    # 文件存储路径
    file_path = models.CharField(
        max_length=500,
        verbose_name='文件存储路径',
        help_text='文件在服务器上的相对存储路径'
    )
    
    # 文件大小（字节）
    file_size = models.BigIntegerField(
        verbose_name='文件大小',
        help_text='文件大小，单位为字节'
    )
    
    # MIME类型
    mime_type = models.CharField(
        max_length=100,
        verbose_name='MIME类型',
        help_text='文件的MIME类型，用于文件验证和处理'
    )
    
    # 文件MD5哈希值（用于去重和完整性校验）
    file_hash = models.CharField(
        max_length=64,
        verbose_name='文件哈希值',
        help_text='文件的MD5哈希值，用于去重和完整性校验'
    )
    
    # 缩略图路径（图片/视频专用）
    thumbnail_path = models.CharField(
        max_length=500,
        null=True,
        blank=True,
        verbose_name='缩略图路径',
        help_text='图片和视频文件的缩略图存储路径'
    )
    
    # 媒体时长（秒，音视频文件专用）
    duration = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='媒体时长',
        help_text='音频和视频文件的时长，单位为秒'
    )
    
    # 图片/视频宽度
    width = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='宽度',
        help_text='图片和视频的宽度像素'
    )
    
    # 图片/视频高度
    height = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='高度',
        help_text='图片和视频的高度像素'
    )
    
    # 上传状态
    upload_status = models.CharField(
        max_length=20,
        choices=UPLOAD_STATUS_CHOICES,
        default='uploaded',
        verbose_name='上传状态',
        help_text='文件的上传处理状态'
    )
    
    # 排序序号（用于界面显示排序）
    sort_order = models.IntegerField(
        default=0,
        verbose_name='排序序号',
        help_text='附件在界面上的显示顺序'
    )
    
    # 创建时间
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )
    
    # 更新时间
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    class Meta:
        db_table = 'performance_attachments'
        verbose_name = '履职记录附件'
        verbose_name_plural = '履职记录附件'
        ordering = ['sort_order', 'created_at']
        indexes = [
            # 单字段索引
            models.Index(fields=['performance_record']),
            models.Index(fields=['file_type']),
            models.Index(fields=['upload_status']),
            models.Index(fields=['file_hash']),
            models.Index(fields=['created_at']),
            # 复合索引
            models.Index(fields=['performance_record', 'sort_order']),
            models.Index(fields=['performance_record', 'file_type']),
        ]
        constraints = [
            # 检查约束
            models.CheckConstraint(
                check=models.Q(file_type__in=['image', 'audio', 'video', 'document']),
                name='chk_performance_attachment_file_type'
            ),
            models.CheckConstraint(
                check=models.Q(upload_status__in=['uploading', 'uploaded', 'failed', 'processing']),
                name='chk_performance_attachment_upload_status'
            ),
            models.CheckConstraint(
                check=models.Q(file_size__gt=0) & models.Q(file_size__lte=104857600),  # 最大100MB
                name='chk_performance_attachment_file_size'
            ),
        ]
    
    def __str__(self):
        return f"{self.performance_record} - {self.original_filename}"
    
    def save(self, *args, **kwargs):
        """保存时自动更新关联记录的has_attachments字段"""
        super().save(*args, **kwargs)
        # 触发父记录的保存，更新has_attachments字段
        self.performance_record.save()
    
    def delete(self, *args, **kwargs):
        """删除时清理文件和更新父记录"""
        # 删除物理文件
        self.delete_files()
        # 删除数据库记录
        result = super().delete(*args, **kwargs)
        # 更新父记录的has_attachments字段
        try:
            self.performance_record.save()
        except PerformanceRecord.DoesNotExist:
            pass  # 父记录已被删除
        return result
    
    def delete_files(self):
        """删除关联的物理文件"""
        files_to_delete = []
        
        # 主文件
        if self.file_path:
            main_file_path = os.path.join(settings.MEDIA_ROOT, self.file_path)
            files_to_delete.append(main_file_path)
        
        # 缩略图文件
        if self.thumbnail_path:
            thumbnail_file_path = os.path.join(settings.MEDIA_ROOT, self.thumbnail_path)
            files_to_delete.append(thumbnail_file_path)
        
        # 删除文件
        for file_path in files_to_delete:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception:
                pass  # 忽略文件删除错误
    
    def get_file_url(self, request=None):
        """获取文件访问URL - 返回安全的API URL"""
        if self.file_path:
            try:
                # 返回安全文件访问API的URL
                return reverse('performance:secure-file-access', kwargs={'attachment_id': self.id})
            except:
                # 如果URL反向解析失败，返回相对路径
                return f"/api/v1/performance/secure-file/{self.id}/"
        return None

    def get_thumbnail_url(self, request=None):
        """获取缩略图访问URL - 返回安全的API URL"""
        if self.thumbnail_path:
            try:
                # 对于缩略图，同样使用安全文件访问API
                return reverse('performance:secure-file-access', kwargs={'attachment_id': self.id})
            except:
                # 如果URL反向解析失败，返回相对路径
                return f"/api/v1/performance/secure-file/{self.id}/"
        elif self.file_type == 'image' and self.file_path:
            # 如果是图片文件但没有缩略图，返回原文件的安全URL
            return self.get_file_url(request)
        return None
    
    def get_file_size_display(self):
        """获取人类可读的文件大小"""
        if not self.file_size:
            return '0 B'
        
        size = float(self.file_size)
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    def get_duration_display(self):
        """获取人类可读的时长"""
        if not self.duration:
            return None
        
        hours = self.duration // 3600
        minutes = (self.duration % 3600) // 60
        seconds = self.duration % 60
        
        if hours > 0:
            return f"{hours}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes}:{seconds:02d}"
    
    @staticmethod
    def calculate_file_hash(file_obj):
        """计算文件的MD5哈希值"""
        hash_md5 = hashlib.md5()
        file_obj.seek(0)  # 确保从文件开头读取
        for chunk in iter(lambda: file_obj.read(4096), b""):
            hash_md5.update(chunk)
        file_obj.seek(0)  # 重置文件指针
        return hash_md5.hexdigest()
    
    @classmethod
    def get_file_type_limits(cls):
        """获取各文件类型的数量限制"""
        return {
            'image': 9,    # 图片最多9张
            'audio': 3,    # 音频最多3个
            'video': 2,    # 视频最多2个
            'document': 5  # 文档最多5个
        }
    
    @classmethod
    def get_file_size_limits(cls):
        """获取各文件类型的大小限制（字节）"""
        return {
            'image': 10 * 1024 * 1024,   # 图片最大10MB
            'audio': 50 * 1024 * 1024,   # 音频最大50MB
            'video': 100 * 1024 * 1024,  # 视频最大100MB
            'document': 20 * 1024 * 1024 # 文档最大20MB
        }
    
    @classmethod
    def get_allowed_extensions(cls):
        """获取各文件类型允许的扩展名"""
        return {
            'image': ['jpg', 'jpeg', 'png', 'gif'],
            'audio': ['mp3', 'wav', 'aac', 'm4a'],
            'video': ['mp4', 'avi', 'mov', 'wmv'],
            'document': ['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx']
        }
