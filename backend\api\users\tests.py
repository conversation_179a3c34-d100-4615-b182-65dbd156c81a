"""
用户管理应用测试文件

包含以下测试类：
1. UserModelTest - 用户模型测试
2. RepresentativeModelTest - 人大代表模型测试
3. StaffMemberModelTest - 工作人员模型测试
4. UserLoginAPITest - 用户登录API测试
5. UserProfileAPITest - 用户个人信息API测试
6. PasswordChangeAPITest - 密码修改API测试
7. UserManagementAPITest - 用户管理API测试
8. PermissionTest - 权限测试

uv run manage.py test api.users.tests -v 2
"""

from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from django.core.exceptions import ValidationError
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from datetime import date, timedelta

from .models import User, Representative, StaffMember
from .permissions import IsOwnerOrStaff, <PERSON><PERSON><PERSON><PERSON>, CanManageUsers, CanChangePassword

User = get_user_model()


class UserModelTest(TestCase):
    """用户模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user_data = {
            'username': 'test_user',
            'password': 'test_password123',
            'role': 'representative'
        }
    
    def test_create_user(self):
        """测试创建普通用户"""
        user = User.objects.create_user(**self.user_data)
        
        self.assertEqual(user.username, 'test_user')
        self.assertEqual(user.role, 'representative')
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
        self.assertTrue(user.check_password('test_password123'))
    
    def test_create_superuser(self):
        """测试创建超级用户"""
        superuser = User.objects.create_superuser(
            username='admin',
            password='admin123',
            role='staff'
        )
        
        self.assertEqual(superuser.username, 'admin')
        self.assertTrue(superuser.is_active)
        self.assertTrue(superuser.is_staff)
        self.assertTrue(superuser.is_superuser)
    
    def test_username_unique(self):
        """测试用户名唯一性"""
        User.objects.create_user(**self.user_data)
        
        with self.assertRaises(Exception):
            User.objects.create_user(**self.user_data)
    
    def test_username_required(self):
        """测试用户名必填"""
        with self.assertRaises(ValueError):
            User.objects.create_user(username='', password='123')
    
    def test_user_string_representation(self):
        """测试用户字符串表示"""
        user = User.objects.create_user(**self.user_data)
        expected = f"{user.username} ({user.get_role_display()})"
        self.assertEqual(str(user), expected)
    
    def test_last_login_update(self):
        """测试最后登录时间更新"""
        user = User.objects.create_user(**self.user_data)
        test_time = timezone.now()
        user.last_login = test_time
        user.save()
        
        user.refresh_from_db()
        self.assertEqual(user.last_login_at, test_time)


class RepresentativeModelTest(TestCase):
    """人大代表模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            username='rep_user',
            password='password123',
            role='representative'
        )
        
        self.representative_data = {
            'user': self.user,
            'level': '区级',
            'name': '张三',
            'gender': 'male',
            'nationality': '汉族',
            'birth_date': date(1980, 1, 1),
            'birthplace': '江苏南京',
            'party': '中国共产党',
            'current_position': '工程师',
            'mobile_phone': '13888888888',
            'education': '本科',
            'graduated_school': '南京大学',
            'major': '计算机科学'
        }
    
    def test_create_representative(self):
        """测试创建人大代表"""
        representative = Representative.objects.create(**self.representative_data)
        
        self.assertEqual(representative.name, '张三')
        self.assertEqual(representative.user, self.user)
        self.assertEqual(representative.level, '区级')
        self.assertEqual(representative.mobile_phone, '13888888888')
    
    def test_representative_age_property(self):
        """测试年龄计算属性"""
        representative = Representative.objects.create(**self.representative_data)
        expected_age = timezone.now().year - 1980
        
        # 考虑生日是否已过，年龄可能相差1岁
        self.assertIn(representative.age, [expected_age - 1, expected_age])
    
    def test_mobile_phone_validation(self):
        """测试手机号码验证"""
        # 无效的手机号
        self.representative_data['mobile_phone'] = '12345678901'
        representative = Representative(**self.representative_data)
        
        with self.assertRaises(ValidationError):
            representative.full_clean()
    
    def test_representative_string_representation(self):
        """测试人大代表字符串表示"""
        representative = Representative.objects.create(**self.representative_data)
        expected = f"{representative.name} ({representative.level})"
        self.assertEqual(str(representative), expected)
    
    def test_one_to_one_relationship_with_user(self):
        """测试与用户的一对一关系"""
        representative = Representative.objects.create(**self.representative_data)
        
        # 测试正向关系
        self.assertEqual(representative.user, self.user)
        
        # 测试反向关系
        self.assertEqual(self.user.representative, representative)


class StaffMemberModelTest(TestCase):
    """工作人员模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            username='staff_user',
            password='password123',
            role='staff'
        )
        
        self.staff_data = {
            'user': self.user,
            'name': '李四',
            'position': '站长',
            'mobile_phone': '13999999999',
            'email': '<EMAIL>',
            'station_name': '那洪联络站'
        }
    
    def test_create_staff_member(self):
        """测试创建工作人员"""
        staff = StaffMember.objects.create(**self.staff_data)
        
        self.assertEqual(staff.name, '李四')
        self.assertEqual(staff.position, '站长')
        self.assertEqual(staff.station_name, '那洪联络站')
        self.assertEqual(staff.user, self.user)
    
    def test_staff_email_optional(self):
        """测试邮箱字段可选"""
        self.staff_data.pop('email')
        staff = StaffMember.objects.create(**self.staff_data)
        
        self.assertIsNone(staff.email)
    
    def test_staff_string_representation(self):
        """测试工作人员字符串表示"""
        staff = StaffMember.objects.create(**self.staff_data)
        expected = f"{staff.name} ({staff.station_name})"
        self.assertEqual(str(staff), expected)


class UserLoginAPITest(APITestCase):
    """用户登录API测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
        self.login_url = reverse('users:login')
        self.logout_url = reverse('users:logout')
        
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            role='representative'
        )
    
    def test_successful_login(self):
        """测试成功登录"""
        data = {
            'username': 'testuser',
            'password': 'testpass123'
        }
        
        response = self.client.post(self.login_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('access_token', response.data['data'])
        self.assertIn('refresh_token', response.data['data'])
        self.assertIn('user', response.data['data'])
    
    def test_invalid_credentials(self):
        """测试无效凭据"""
        data = {
            'username': 'testuser',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(self.login_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
    
    def test_inactive_user_login(self):
        """测试非活跃用户登录"""
        self.user.is_active = False
        self.user.save()
        
        data = {
            'username': 'testuser',
            'password': 'testpass123'
        }
        
        response = self.client.post(self.login_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_successful_logout(self):
        """测试成功登出"""
        # 先登录获取token
        login_data = {
            'username': 'testuser',
            'password': 'testpass123'
        }
        login_response = self.client.post(self.login_url, login_data)
        refresh_token = login_response.data['data']['refresh_token']
        access_token = login_response.data['data']['access_token']
        
        # 设置认证头
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # 登出（不传refresh_token，测试基本登出功能）
        logout_data = {}
        response = self.client.post(self.logout_url, logout_data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])


class UserProfileAPITest(APITestCase):
    """用户个人信息API测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
        self.profile_url = reverse('users:profile')
        
        # 创建代表用户
        self.rep_user = User.objects.create_user(
            username='representative',
            password='password123',
            role='representative'
        )
        self.representative = Representative.objects.create(
            user=self.rep_user,
            level='区级',
            name='张代表',
            gender='male',
            nationality='汉族',
            birth_date=date(1980, 1, 1),
            birthplace='江苏南京',
            party='中国共产党',
            current_position='工程师',
            mobile_phone='13888888888',
            education='本科'
        )
        
        # 创建工作人员用户
        self.staff_user = User.objects.create_user(
            username='staff_member',
            password='password123',
            role='staff'
        )
        self.staff = StaffMember.objects.create(
            user=self.staff_user,
            name='李工作人员',
            position='站长',
            mobile_phone='13999999999',
            station_name='测试站'
        )
    
    def _authenticate_user(self, user):
        """辅助方法：为用户设置认证"""
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    def test_get_representative_profile(self):
        """测试获取代表个人信息"""
        self._authenticate_user(self.rep_user)
        
        response = self.client.get(self.profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['username'], 'representative')
        self.assertEqual(response.data['data']['role'], 'representative')
        self.assertIn('representative_info', response.data['data'])
    
    def test_get_staff_profile(self):
        """测试获取工作人员个人信息"""
        self._authenticate_user(self.staff_user)
        
        response = self.client.get(self.profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['username'], 'staff_member')
        self.assertEqual(response.data['data']['role'], 'staff')
        self.assertIn('staff_info', response.data['data'])
    
    def test_unauthorized_access(self):
        """测试未认证访问"""
        response = self.client.get(self.profile_url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_update_representative_profile(self):
        """测试更新代表个人信息"""
        self._authenticate_user(self.rep_user)
        
        # 测试基本的用户信息更新（不涉及嵌套的representative_info）
        update_data = {
            'is_active': True  # 测试一个简单的更新
        }
        
        response = self.client.put(self.profile_url, update_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # 验证用户仍然是活跃的
        self.rep_user.refresh_from_db()
        self.assertTrue(self.rep_user.is_active)


class PasswordChangeAPITest(APITestCase):
    """密码修改API测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
        self.password_change_url = reverse('users:password_change')
        
        self.user = User.objects.create_user(
            username='testuser',
            password='oldpassword123',
            role='representative'
        )
        
        # 认证用户
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    def test_successful_password_change(self):
        """测试成功修改密码"""
        data = {
            'old_password': 'oldpassword123',
            'new_password': 'newpassword456',
            'confirm_password': 'newpassword456'
        }
        
        response = self.client.post(self.password_change_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # 验证密码已更改
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('newpassword456'))
    
    def test_incorrect_old_password(self):
        """测试错误的旧密码"""
        data = {
            'old_password': 'wrongpassword',
            'new_password': 'newpassword456',
            'confirm_password': 'newpassword456'
        }
        
        response = self.client.post(self.password_change_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
    
    def test_weak_new_password(self):
        """测试弱新密码"""
        data = {
            'old_password': 'oldpassword123',
            'new_password': '123',  # 太短
            'confirm_password': '123'
        }
        
        response = self.client.post(self.password_change_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class UserManagementAPITest(APITestCase):
    """用户管理API测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
        self.user_list_url = reverse('users:user_list')
        
        # 创建工作人员（有管理权限）
        self.staff_user = User.objects.create_user(
            username='staff_admin',
            password='password123',
            role='staff'
        )
        StaffMember.objects.create(
            user=self.staff_user,
            name='管理员',
            position='站长',
            mobile_phone='13888888888',
            station_name='测试站'
        )
        
        # 创建普通代表（无管理权限）
        self.rep_user = User.objects.create_user(
            username='representative',
            password='password123',
            role='representative'
        )
        Representative.objects.create(
            user=self.rep_user,
            level='区级',
            name='张代表',
            gender='male',
            nationality='汉族',
            birth_date=date(1980, 1, 1),
            birthplace='江苏南京',
            party='中国共产党',
            current_position='工程师',
            mobile_phone='13999999999',
            education='本科'
        )
    
    def test_staff_can_access_user_list(self):
        """测试工作人员可以访问用户列表"""
        refresh = RefreshToken.for_user(self.staff_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        response = self.client.get(self.user_list_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)
    
    def test_representative_cannot_access_user_list(self):
        """测试代表不能访问用户列表"""
        refresh = RefreshToken.for_user(self.rep_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        response = self.client.get(self.user_list_url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_create_user_by_staff(self):
        """测试工作人员创建用户"""
        refresh = RefreshToken.for_user(self.staff_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        data = {
            'username': 'newuser',
            'password': 'newpassword123',
            'confirm_password': 'newpassword123',
            'role': 'representative'
        }
        
        response = self.client.post(self.user_list_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(User.objects.filter(username='newuser').exists())
    
    def test_user_detail_access(self):
        """测试用户详情访问"""
        refresh = RefreshToken.for_user(self.staff_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        user_detail_url = reverse('users:user_detail', kwargs={'user_id': self.rep_user.id})
        response = self.client.get(user_detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['username'], 'representative')


class PermissionTest(TestCase):
    """权限测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试用户
        self.rep_user = User.objects.create_user(
            username='representative',
            password='password123',
            role='representative'
        )
        
        self.staff_user = User.objects.create_user(
            username='staff',
            password='password123',
            role='staff'
        )
        
        # 创建关联信息
        StaffMember.objects.create(
            user=self.staff_user,
            name='工作人员',
            position='站长',
            mobile_phone='13888888888',
            station_name='测试站'
        )
    
    def test_is_staff_permission(self):
        """测试IsStaff权限"""
        from .permissions import IsStaff
        from rest_framework.test import APIRequestFactory
        from unittest.mock import Mock
        
        factory = APIRequestFactory()
        request = factory.get('/')
        view = Mock()
        
        permission = IsStaff()
        
        # 测试工作人员有权限
        request.user = self.staff_user
        self.assertTrue(permission.has_permission(request, view))
        
        # 测试代表无权限
        request.user = self.rep_user
        self.assertFalse(permission.has_permission(request, view))
    
    def test_can_manage_users_permission(self):
        """测试CanManageUsers权限"""
        from .permissions import CanManageUsers
        from rest_framework.test import APIRequestFactory
        from unittest.mock import Mock
        
        factory = APIRequestFactory()
        request = factory.get('/')
        view = Mock()
        
        permission = CanManageUsers()
        
        # 测试工作人员有权限
        request.user = self.staff_user
        self.assertTrue(permission.has_permission(request, view))
        
        # 测试代表无权限
        request.user = self.rep_user
        self.assertFalse(permission.has_permission(request, view))


class SignalTest(TransactionTestCase):
    """信号测试"""
    
    def test_user_creation_signal(self):
        """测试用户创建信号"""
        # 测试信号是否正确连接和触发
        from django.db.models.signals import post_save
        from .signals import create_user_profile
        
        # 检查信号是否已连接
        receivers = post_save._live_receivers(sender=User)
        signal_functions = []
        for receiver_list in receivers:
            if isinstance(receiver_list, list):
                signal_functions.extend(receiver_list)
            else:
                signal_functions.append(receiver_list)
        
        # 验证我们的信号处理函数在接收器列表中
        self.assertIn(create_user_profile, signal_functions)
        
        # 创建用户触发信号
        user = User.objects.create_user(
            username='signal_test',
            password='password123',
            role='representative'
        )
        
        # 验证用户创建成功（信号没有阻止创建）
        self.assertTrue(User.objects.filter(username='signal_test').exists())


class IntegrationTest(APITestCase):
    """集成测试"""
    
    def test_complete_user_workflow(self):
        """测试完整的用户工作流"""
        # 1. 创建用户
        user = User.objects.create_user(
            username='workflow_test',
            password='password123',
            role='representative'
        )
        
        representative = Representative.objects.create(
            user=user,
            level='区级',
            name='工作流测试',
            gender='male',
            nationality='汉族',
            birth_date=date(1980, 1, 1),
            birthplace='测试地',
            party='中国共产党',
            current_position='测试职位',
            mobile_phone='13888888888',
            education='本科'
        )
        
        # 2. 登录
        login_url = reverse('users:login')
        login_data = {
            'username': 'workflow_test',
            'password': 'password123'
        }
        login_response = self.client.post(login_url, login_data)
        self.assertEqual(login_response.status_code, status.HTTP_200_OK)
        
        access_token = login_response.data['data']['access_token']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # 3. 获取个人信息
        profile_url = reverse('users:profile')
        profile_response = self.client.get(profile_url)
        self.assertEqual(profile_response.status_code, status.HTTP_200_OK)
        
        # 4. 修改密码
        password_change_url = reverse('users:password_change')
        password_data = {
            'old_password': 'password123',
            'new_password': 'newpassword456',
            'confirm_password': 'newpassword456'
        }
        password_response = self.client.post(password_change_url, password_data)
        self.assertEqual(password_response.status_code, status.HTTP_200_OK)
        
        # 5. 验证新密码有效
        user.refresh_from_db()
        self.assertTrue(user.check_password('newpassword456'))


class RepresentativeListAPITest(APITestCase):
    """人大代表列表API权限测试 - 修复安全漏洞的测试用例"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
        self.representative_list_url = reverse('users:representative_list')
        
        # 创建工作人员
        self.staff_user = User.objects.create_user(
            username='staff_user',
            password='password123',
            role='staff'
        )
        StaffMember.objects.create(
            user=self.staff_user,
            name='工作人员',
            position='站长',
            mobile_phone='13888888888',
            station_name='测试站'
        )
        
        # 创建代表用户1
        self.rep_user1 = User.objects.create_user(
            username='rep001',
            password='password123',
            role='representative'
        )
        Representative.objects.create(
            user=self.rep_user1,
            level='区级',
            name='张代表',
            gender='male',
            nationality='汉族',
            birth_date=date(1980, 1, 1),
            birthplace='江苏南京',
            party='中国共产党',
            current_position='工程师',
            mobile_phone='13999999999',
            education='本科'
        )
        
        # 创建代表用户2
        self.rep_user2 = User.objects.create_user(
            username='rep002',
            password='password123',
            role='representative'
        )
        Representative.objects.create(
            user=self.rep_user2,
            level='市级',
            name='李代表',
            gender='female',
            nationality='汉族',
            birth_date=date(1975, 5, 15),
            birthplace='江苏苏州',
            party='中国共产党',
            current_position='医生',
            mobile_phone='13777777777',
            education='硕士'
        )
    
    def test_staff_can_access_representative_list(self):
        """测试工作人员可以访问代表列表"""
        refresh = RefreshToken.for_user(self.staff_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        response = self.client.get(self.representative_list_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 2)  # 应该返回2个代表
        
        # 验证返回的数据包含代表信息
        representative_names = [rep['name'] for rep in response.data['data']]
        self.assertIn('张代表', representative_names)
        self.assertIn('李代表', representative_names)
    
    def test_representative_cannot_access_representative_list(self):
        """测试代表不能访问其他代表列表（重要的安全测试）"""
        refresh = RefreshToken.for_user(self.rep_user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        response = self.client.get(self.representative_list_url)
        
        # 应该返回403禁止访问
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_another_representative_cannot_access_representative_list(self):
        """测试另一个代表也不能访问代表列表"""
        refresh = RefreshToken.for_user(self.rep_user2)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        response = self.client.get(self.representative_list_url)
        
        # 应该返回403禁止访问
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_unauthenticated_cannot_access_representative_list(self):
        """测试未认证用户不能访问代表列表"""
        response = self.client.get(self.representative_list_url)
        
        # 应该返回401未认证
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_representative_list_filtering(self):
        """测试工作人员访问代表列表时的筛选功能"""
        refresh = RefreshToken.for_user(self.staff_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        # 测试按层级筛选
        response = self.client.get(self.representative_list_url, {'level': '区级'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']), 1)
        self.assertEqual(response.data['data'][0]['name'], '张代表')
        
        # 测试按姓名搜索
        response = self.client.get(self.representative_list_url, {'search': '李'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']), 1)
        self.assertEqual(response.data['data'][0]['name'], '李代表')