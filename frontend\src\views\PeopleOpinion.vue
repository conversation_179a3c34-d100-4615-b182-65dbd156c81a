<template>
  <div class="people-opinion-container">
    <!-- 头部标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <el-icon size="32" style="color: var(--china-red); margin-right: 12px;">
            <MessageBox />
          </el-icon>
          <div>
            <h1 class="main-title">群众意见反馈</h1>
            <p class="subtitle">您的声音，我们用心倾听</p>
          </div>
        </div>
        <div class="description">
          <p>亲爱的市民朋友，我们真诚欢迎您对人大工作提出宝贵意见和建议。</p>
          <p>您的每一条反馈都是我们改进工作、更好服务人民的重要依据。</p>
        </div>
      </div>
    </div>

    <!-- 表单区域 -->
    <div class="form-section">
      <el-card class="opinion-form-card" shadow="hover">
        <template #header>
          <div class="form-header">
            <el-icon style="color: var(--china-red); margin-right: 8px;"><Edit /></el-icon>
            <span>请填写您的意见反馈</span>
          </div>
        </template>

        <el-form
          ref="opinionFormRef"
          :model="opinionForm"
          :rules="opinionRules"
          label-width="120px"
          label-position="top"
        >
          <el-form-item label="意见标题" prop="title" required>
            <el-input
              v-model="opinionForm.title"
              placeholder="请简要概括您要反映的问题（至少5个字符）"
              maxlength="200"
              show-word-limit
              clearable
            />
          </el-form-item>

          <el-form-item label="详细内容" prop="content" required>
            <el-input
              v-model="opinionForm.content"
              type="textarea"
              placeholder="请详细描述您的意见或建议，包括具体情况、建议措施等（至少10个字符）"
              :rows="8"
              maxlength="2000"
              show-word-limit
              clearable
            />
          </el-form-item>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="您的姓名" prop="name" required>
                <el-input
                  v-model="opinionForm.name"
                  placeholder="请输入您的真实姓名或昵称"
                  maxlength="50"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系方式" prop="contact_info" required>
                <el-input
                  v-model="opinionForm.contact_info"
                  placeholder="请输入手机号码或邮箱地址"
                  maxlength="100"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 提交按钮区域 -->
          <div class="submit-section">
            <el-button
              type="primary"
              size="large"
              :loading="submitting"
              @click="handleSubmit"
              class="submit-btn"
            >
              <el-icon v-if="!submitting"><MessageBox /></el-icon>
              {{ submitting ? '正在提交...' : '提交意见反馈' }}
            </el-button>
            
            <el-button
              size="large"
              @click="handleReset"
              :disabled="submitting"
            >
              重新填写
            </el-button>
          </div>

          <!-- 温馨提示 -->
          <div class="tips-section">
            <el-alert
              title="温馨提示"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <ul>
                  <li>您的个人信息仅用于必要时的沟通联系，我们将严格保护您的隐私</li>
                  <li>我们将认真对待每一条意见建议，并及时研究处理</li>
                  <li>对于重要问题，我们会及时回复并告知处理结果</li>
                  <li>请确保提供真实有效的联系方式，以便我们与您沟通</li>
                </ul>
              </template>
            </el-alert>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 成功提交弹窗 -->
    <el-dialog
      v-model="showSuccessDialog"
      title="提交成功"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="success-content">
        <el-icon size="64" color="#67c23a">
          <CircleCheck />
        </el-icon>
        <h3>感谢您的宝贵意见！</h3>
        <p>我们已收到您的反馈，将认真处理。</p>
        <div class="submission-info">
          <p><strong>提交信息：</strong></p>
          <p>标题：{{ submissionInfo.title }}</p>
          <p>提交时间：{{ formatTime(submissionInfo.created_at) }}</p>
          <p>意见编号：{{ submissionInfo.id }}</p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleContinue">
            继续提交意见
          </el-button>
          <el-button @click="handleBackToHome">
            返回首页
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { MessageBox, Edit, CircleCheck } from '@element-plus/icons-vue'
import { submitPeopleOpinion } from '@/api/modules/people-opinion/api'

console.log('📋 群众意见反馈页面加载完成')

// 响应式数据
const opinionFormRef = ref()
const submitting = ref(false)
const showSuccessDialog = ref(false)
const submissionInfo = ref({})

// 表单数据
const opinionForm = reactive({
  title: '',
  content: '',
  name: '',
  contact_info: ''
})

// 表单验证规则
const opinionRules = {
  title: [
    { required: true, message: '请输入意见标题', trigger: 'blur' },
    { min: 5, message: '标题至少需要5个字符', trigger: 'blur' },
    { max: 200, message: '标题不能超过200个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入详细内容', trigger: 'blur' },
    { min: 10, message: '内容至少需要10个字符', trigger: 'blur' },
    { max: 2000, message: '内容不能超过2000个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入您的姓名', trigger: 'blur' },
    { max: 50, message: '姓名不能超过50个字符', trigger: 'blur' }
  ],
  contact_info: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { max: 100, message: '联系方式不能超过100个字符', trigger: 'blur' }
  ]
}

// 提交意见
const handleSubmit = async () => {
  try {
    // 表单验证
    await opinionFormRef.value?.validate()
    
    submitting.value = true
    console.log('📤 开始提交群众意见:', opinionForm)
    
    // 调用API提交意见
    const response = await submitPeopleOpinion(opinionForm)
    
    if (response.success) {
      submissionInfo.value = response.data
      console.log('✅ 意见提交成功')
      showSuccessDialog.value = true
      ElMessage.success(response.message || '意见提交成功！')
    } else {
      console.error('❌ 意见提交失败:', response.message)
      ElMessage.error(response.message || '提交失败，请重试')
    }
    
  } catch (error) {
    console.error('❌ 提交意见失败:', error)
    ElMessage.error('提交失败，请检查输入信息')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const handleReset = () => {
  opinionFormRef.value?.resetFields()
  Object.assign(opinionForm, {
    title: '',
    content: '',
    name: '',
    contact_info: ''
  })
}

// 继续提交
const handleContinue = () => {
  showSuccessDialog.value = false
  handleReset()
}

// 返回首页
const handleBackToHome = () => {
  showSuccessDialog.value = false
  // 这里可以跳转到首页或关闭页面
  window.location.href = '/'
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''
  const date = new Date(timeString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped>
/* 主容器 */
.people-opinion-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

/* 头部区域 */
.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.title-section {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.title-section h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.title-section p {
  margin: 4px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
}

.description {
  color: #595959;
  line-height: 1.6;
}

.description p {
  margin: 8px 0;
}

/* 表单区域 */
.form-section {
  max-width: 1200px;
  margin: 0 auto;
}

.opinion-form-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
}

.form-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: #262626;
}

/* 提交按钮区域 */
.submit-section {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.submit-btn {
  background: linear-gradient(135deg, var(--china-red) 0%, #8b1538 100%);
  border-color: var(--china-red);
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 6px;
}

.submit-btn:hover {
  background: linear-gradient(135deg, #d32f2f 0%, #9c1b3f 100%);
  border-color: #d32f2f;
}

/* 提示区域 */
.tips-section {
  margin-top: 24px;
}

.tips-section ul {
  margin: 0;
  padding-left: 20px;
}

.tips-section li {
  margin-bottom: 8px;
  line-height: 1.6;
  color: #666;
}

/* 成功弹窗 */
.success-content {
  text-align: center;
  padding: 20px;
}

.success-content h3 {
  color: #67c23a;
  margin: 20px 0 16px 0;
  font-size: 20px;
}

.success-content p {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
}

.submission-info {
  background: #f0f9ff;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
  font-size: 14px;
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .people-opinion-container {
    padding: 10px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .title-section h1 {
    font-size: 20px;
  }
  
  .submit-section {
    flex-direction: column;
    align-items: center;
  }
  
  .submit-btn {
    width: 100%;
    max-width: 300px;
  }
}

/* 中国红主题色彩变量 */
:root {
  --china-red: #c41e3a;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--china-red) inset;
}

:deep(.el-form-item.is-error .el-input__wrapper) {
  box-shadow: 0 0 0 1px #f56c6c inset;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, var(--china-red) 0%, #8b1538 100%);
  border-color: var(--china-red);
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #d32f2f 0%, #9c1b3f 100%);
  border-color: #d32f2f;
}
</style> 