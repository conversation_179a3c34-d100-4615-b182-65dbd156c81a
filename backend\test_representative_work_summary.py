#!/usr/bin/env python
"""
代表工作总结功能测试脚本

测试工作人员查看代表工作总结和生成AI分析的功能
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# 设置 Django 环境
sys.path.append('/c%3A/code/NPC/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'npcsite.settings')
django.setup()

# API 基础配置
BASE_URL = 'http://127.0.0.1:8000'
HEADERS = {'Content-Type': 'application/json'}

def test_staff_login():
    """测试工作人员登录"""
    print("=== 测试工作人员登录 ===")
    
    login_data = {
        'username': 'staff1',  # 假设存在的工作人员账号
        'password': 'password123'
    }
    
    response = requests.post(f'{BASE_URL}/api/v1/auth/login/', 
                           json=login_data, headers=HEADERS)
    
    if response.status_code == 200:
        token = response.json().get('token')
        print(f"✅ 工作人员登录成功，Token: {token[:20]}...")
        return token
    else:
        print(f"❌ 工作人员登录失败: {response.status_code} - {response.text}")
        return None

def test_get_representatives_list(token):
    """测试获取代表工作总结列表"""
    print("\n=== 测试获取代表工作总结列表 ===")
    
    headers = {**HEADERS, 'Authorization': f'Bearer {token}'}
    year = datetime.now().year
    
    response = requests.get(f'{BASE_URL}/api/v1/ai-summaries/representatives/',
                          params={'year': year}, headers=headers)
    
    print(f"请求URL: {response.url}")
    print(f"响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            representatives = data.get('data', [])
            print(f"✅ 成功获取代表列表，共 {len(representatives)} 位代表:")
            
            for rep in representatives[:3]:  # 只显示前3个
                print(f"  - {rep['name']} ({rep['level']}) - {rep['analysisStatus']}")
            
            return representatives
        else:
            print(f"❌ 获取代表列表失败: {data.get('message')}")
    else:
        print(f"❌ 请求失败: {response.status_code} - {response.text}")
    
    return []

def test_generate_representative_analysis(token, representative_id):
    """测试为代表生成AI分析"""
    print(f"\n=== 测试为代表ID {representative_id} 生成AI分析 ===")
    
    headers = {**HEADERS, 'Authorization': f'Bearer {token}'}
    year = datetime.now().year
    
    generate_data = {
        'analysis_year': year,
        'force_regenerate': True,  # 强制重新生成用于测试
        'representative_id': representative_id
    }
    
    response = requests.post(f'{BASE_URL}/api/v1/ai-summaries/generate/',
                           json=generate_data, headers=headers)
    
    print(f"请求数据: {json.dumps(generate_data, ensure_ascii=False)}")
    print(f"响应状态码: {response.status_code}")
    
    if response.status_code in [201, 202]:
        data = response.json()
        print(f"✅ AI分析生成请求成功:")
        print(f"  - 状态: {data.get('status')}")
        print(f"  - 消息: {data.get('message')}")
        return True
    else:
        print(f"❌ AI分析生成失败: {response.status_code}")
        try:
            error_data = response.json()
            print(f"  错误详情: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
        except:
            print(f"  错误内容: {response.text}")
        return False

def test_get_representative_analysis(token, representative_id):
    """测试获取代表AI分析结果"""
    print(f"\n=== 测试获取代表ID {representative_id} 的AI分析结果 ===")
    
    headers = {**HEADERS, 'Authorization': f'Bearer {token}'}
    year = datetime.now().year
    
    response = requests.get(f'{BASE_URL}/api/v1/ai-summaries/{year}/',
                          params={'representative_id': representative_id}, 
                          headers=headers)
    
    print(f"响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            ai_result = data.get('data', {})
            print(f"✅ 成功获取AI分析结果:")
            print(f"  - 代表: {ai_result.get('representative_name', 'N/A')}")
            print(f"  - 状态: {ai_result.get('status_display', 'N/A')}")
            print(f"  - 完成时间: {ai_result.get('completed_at', 'N/A')}")
            
            # 如果有AI结果，显示概要
            ai_data = ai_result.get('ai_result_data', {})
            if ai_data:
                print(f"  - AI分析概要: {str(ai_data)[:100]}...")
            
            return ai_result
        else:
            print(f"❌ 获取AI分析失败: {data.get('message')}")
    else:
        print(f"❌ 请求失败: {response.status_code} - {response.text}")
    
    return None

def main():
    """主测试流程"""
    print("🚀 开始测试代表工作总结功能")
    print("=" * 50)
    
    # 1. 测试工作人员登录
    token = test_staff_login()
    if not token:
        print("❌ 无法获取认证令牌，测试终止")
        return
    
    # 2. 测试获取代表列表
    representatives = test_get_representatives_list(token)
    if not representatives:
        print("❌ 无法获取代表列表，测试终止")
        return
    
    # 3. 选择第一个代表进行测试
    test_representative = representatives[0]
    representative_id = test_representative['id']
    
    print(f"\n📝 使用代表 '{test_representative['name']}' (ID: {representative_id}) 进行测试")
    
    # 4. 测试生成AI分析
    if test_generate_representative_analysis(token, representative_id):
        print("⏳ 等待3秒后检查生成结果...")
        import time
        time.sleep(3)
        
        # 5. 测试获取AI分析结果
        test_get_representative_analysis(token, representative_id)
    
    print("\n" + "=" * 50)
    print("🏁 代表工作总结功能测试完成")

if __name__ == '__main__':
    main() 