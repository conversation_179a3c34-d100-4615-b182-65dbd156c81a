# 前端配置说明

## 🔧 URL配置管理

本项目采用统一的URL配置管理方案，所有与服务器地址相关的配置都集中在 `src/api/http/config.js` 文件中。

## 📁 配置文件结构

```
frontend/
├── src/api/http/config.js        # 统一配置文件（主要）
├── vite.config.js               # Vite配置（自动导入代理配置）
├── .env.example                 # 环境变量示例（需手动创建）
└── CONFIG.md                    # 本说明文档
```

## ⚙️ 环境配置

### 1. 开发环境（默认）
- 后端地址：`http://localhost:8000`
- 前端地址：`http://localhost:3000`
- 使用代理：是（Vite开发服务器代理）

### 2. 生产环境
- 通过环境变量或修改config.js配置
- 使用代理：否（直接访问后端）

### 3. 测试环境
- 可配置独立的测试服务器地址
- 使用代理：否

## 🚀 部署配置方法

### 方法1：环境变量配置（推荐）

创建环境变量文件：

**开发环境** (`.env.local`)
```bash
VITE_BACKEND_URL=http://localhost:8000
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_FRONTEND_URL=http://localhost:3000
```

**生产环境** (`.env.production`)
```bash
VITE_BACKEND_URL=https://your-domain.com
VITE_API_BASE_URL=https://your-domain.com/api/v1
VITE_FRONTEND_URL=https://your-domain.com
```

**测试环境** (`.env.staging`)
```bash
VITE_BACKEND_URL=https://test.your-domain.com
VITE_API_BASE_URL=https://test.your-domain.com/api/v1
VITE_FRONTEND_URL=https://test.your-domain.com
```

### 方法2：直接修改config.js

修改 `src/api/http/config.js` 中的 `ENVIRONMENT_CONFIG` 对象：

```javascript
production: {
  BACKEND_URL: 'https://your-production-domain.com',
  API_BASE_URL: 'https://your-production-domain.com/api/v1',
  FRONTEND_URL: 'https://your-production-domain.com',
  USE_PROXY: false
}
```

## 📦 构建和部署

### 开发环境启动
```bash
npm run dev
# 或
npm run serve
```

### 生产环境构建
```bash
# 使用生产环境配置构建
npm run build

# 使用自定义环境构建
npm run build -- --mode staging
```

### 预览生产构建
```bash
npm run preview
```

## 🛠️ 配置特性

### 1. 智能URL构建
- `API_CONFIG.buildMediaUrl(path)` - 构建媒体文件URL
- `API_CONFIG.buildStaticUrl(path)` - 构建静态文件URL
- `API_CONFIG.buildBackendUrl(path)` - 构建后端API URL
- `API_CONFIG.buildApiUrl(endpoint)` - 构建完整API端点URL

### 2. 环境自适应
- 开发环境：自动使用代理，避免CORS问题
- 生产环境：直接访问后端，支持CDN等优化

### 3. 代理配置自动化
- Vite代理配置从config.js自动导入
- 修改一处配置，多处生效

## 🚀 常见部署场景

### 1. 同域部署（推荐）
前后端部署在同一域名下：
```
https://your-domain.com/         # 前端
https://your-domain.com/api/     # 后端API
https://your-domain.com/media/   # 媒体文件
```

### 2. 分离部署
前后端分别部署：
```
https://web.your-domain.com/     # 前端
https://api.your-domain.com/     # 后端
```

### 3. CDN + 云存储
```
https://your-domain.com/         # 前端 (CDN)
https://api.your-domain.com/     # 后端API
https://cdn.your-domain.com/     # 媒体文件 (CDN/云存储)
```

## 🔍 故障排查

### 1. 图片无法显示
- 检查 `buildMediaUrl()` 返回的URL是否正确
- 确认代理配置是否生效
- 检查后端媒体文件服务是否正常

### 2. API请求失败
- 检查 `BASE_URL` 配置是否正确
- 确认网络连通性
- 检查CORS配置（生产环境）

### 3. 代理不生效
- 确认是否在开发环境
- 检查 `USE_PROXY` 配置
- 重启开发服务器

## 📝 注意事项

1. **环境变量优先级**：环境变量 > 默认配置
2. **代理仅开发环境**：生产环境不使用代理
3. **路径规范**：所有路径以 `/` 开头
4. **安全性**：生产环境URL不要写死在代码中
5. **缓存**：修改配置后需要重新构建 