import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  searchKnowledge,
  getKnowledgeDetail,
  toggleFavorite,
  getFavoriteKnowledge,
  getQueryHistory,
  getKnowledgeCategories,
  getHotKeywords,
  clearQueryHistory,
  checkIsFavorite,
  getKnowledgeStats
} from '@/api/knowledgeQA'
import { ElMessage } from 'element-plus'

export const useKnowledgeQAStore = defineStore('knowledgeQA', () => {
  // 搜索相关状态
  const searchResults = ref([])
  const searchQuery = ref('')
  const searchCategory = ref('')
  const searchLoading = ref(false)
  const searchTotal = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 知识详情状态
  const currentKnowledge = ref(null)
  const detailLoading = ref(false)

  // 收藏相关状态
  const favoriteList = ref([])
  const favoriteLoading = ref(false)
  const favoriteTotal = ref(0)
  const favoritePage = ref(1)

  // 查询历史状态
  const historyList = ref([])
  const historyLoading = ref(false)
  const historyTotal = ref(0)
  const historyPage = ref(1)

  // 分类和热门关键词
  const categories = ref([])
  const hotKeywords = ref([])

  // 统计信息
  const stats = ref({
    totalKnowledge: 0,
    totalViews: 0,
    totalFavorites: 0,
    userFavoriteCount: 0,
    queryCount: 0
  })

  // 计算属性
  const hasSearchResults = computed(() => searchResults.value.length > 0)
  const totalPages = computed(() => Math.ceil(searchTotal.value / pageSize.value))
  const hasFavorites = computed(() => favoriteList.value.length > 0)
  const favoritePages = computed(() => Math.ceil(favoriteTotal.value / pageSize.value))
  const historyPages = computed(() => Math.ceil(historyTotal.value / pageSize.value))

  // 搜索知识
  const performSearch = async (query, category = '', page = 1, refresh = false) => {
    if (refresh || page === 1) {
      currentPage.value = 1
      searchResults.value = []
    } else {
      currentPage.value = page
    }

    searchLoading.value = true
    
    try {
      const params = {
        query: query || searchQuery.value,
        category: category || searchCategory.value,
        page: currentPage.value,
        size: pageSize.value
      }

      const response = await searchKnowledge(params)
      
      if (response.success) {
        searchResults.value = response.data.list
        searchTotal.value = response.data.total
        searchQuery.value = response.data.query
        
        if (response.data.total === 0) {
          ElMessage.info('未找到相关知识，请尝试其他关键词')
        }
      } else {
        ElMessage.error(response.message || '搜索失败')
        searchResults.value = []
        searchTotal.value = 0
      }
    } catch (error) {
      console.error('搜索知识失败:', error)
      ElMessage.error('搜索失败，请稍后重试')
    } finally {
      searchLoading.value = false
    }
  }

  // 获取知识详情
  const fetchKnowledgeDetail = async (knowledgeId) => {
    detailLoading.value = true
    
    try {
      const response = await getKnowledgeDetail(knowledgeId)
      
      if (response.success) {
        currentKnowledge.value = response.data
        return response.data
      } else {
        ElMessage.error(response.message || '获取知识详情失败')
        return null
      }
    } catch (error) {
      console.error('获取知识详情失败:', error)
      ElMessage.error('获取详情失败，请稍后重试')
      return null
    } finally {
      detailLoading.value = false
    }
  }

  // 切换收藏状态
  const toggleKnowledgeFavorite = async (knowledgeId) => {
    try {
      const isFavorite = checkIsFavorite(knowledgeId)
      const response = await toggleFavorite(knowledgeId, !isFavorite)
      
      if (response.success) {
        ElMessage.success(response.message)
        
        // 更新搜索结果中的收藏状态
        const searchItem = searchResults.value.find(item => item.id === knowledgeId)
        if (searchItem) {
          if (response.data.isFavorite) {
            searchItem.favoriteCount++
          } else {
            searchItem.favoriteCount = Math.max(0, searchItem.favoriteCount - 1)
          }
        }
        
        // 更新详情页面的收藏状态
        if (currentKnowledge.value && currentKnowledge.value.id === knowledgeId) {
          if (response.data.isFavorite) {
            currentKnowledge.value.favoriteCount++
          } else {
            currentKnowledge.value.favoriteCount = Math.max(0, currentKnowledge.value.favoriteCount - 1)
          }
        }

        // 如果是取消收藏，更新收藏列表
        if (!response.data.isFavorite && favoriteList.value.length > 0) {
          fetchFavorites(favoritePage.value)
        }

        return response.data.isFavorite
      } else {
        ElMessage.error(response.message || '操作失败')
        return null
      }
    } catch (error) {
      console.error('切换收藏状态失败:', error)
      ElMessage.error('操作失败，请稍后重试')
      return null
    }
  }

  // 获取收藏列表
  const fetchFavorites = async (page = 1) => {
    favoriteLoading.value = true
    favoritePage.value = page
    
    try {
      const response = await getFavoriteKnowledge({
        page,
        size: pageSize.value
      })
      
      if (response.success) {
        favoriteList.value = response.data.list
        favoriteTotal.value = response.data.total
      } else {
        ElMessage.error('获取收藏列表失败')
      }
    } catch (error) {
      console.error('获取收藏列表失败:', error)
      ElMessage.error('获取收藏列表失败')
    } finally {
      favoriteLoading.value = false
    }
  }

  // 获取查询历史
  const fetchHistory = async (page = 1) => {
    historyLoading.value = true
    historyPage.value = page
    
    try {
      const response = await getQueryHistory({
        page,
        size: pageSize.value
      })
      
      if (response.success) {
        historyList.value = response.data.list
        historyTotal.value = response.data.total
      } else {
        ElMessage.error('获取查询历史失败')
      }
    } catch (error) {
      console.error('获取查询历史失败:', error)
      ElMessage.error('获取查询历史失败')
    } finally {
      historyLoading.value = false
    }
  }

  // 清除查询历史
  const clearHistory = async () => {
    try {
      const response = await clearQueryHistory()
      
      if (response.success) {
        historyList.value = []
        historyTotal.value = 0
        ElMessage.success(response.message)
      } else {
        ElMessage.error('清除历史失败')
      }
    } catch (error) {
      console.error('清除查询历史失败:', error)
      ElMessage.error('清除历史失败')
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await getKnowledgeCategories()
      
      if (response.success) {
        categories.value = response.data
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
    }
  }

  // 获取热门关键词
  const fetchHotKeywords = async () => {
    try {
      const response = await getHotKeywords()
      
      if (response.success) {
        hotKeywords.value = response.data
      }
    } catch (error) {
      console.error('获取热门关键词失败:', error)
    }
  }

  // 获取统计信息
  const fetchStats = async () => {
    try {
      const response = await getKnowledgeStats()
      
      if (response.success) {
        stats.value = response.data
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
    }
  }

  // 设置搜索条件
  const setSearchParams = (query, category = '') => {
    searchQuery.value = query
    searchCategory.value = category
  }

  // 重置搜索状态
  const resetSearch = () => {
    searchResults.value = []
    searchQuery.value = ''
    searchCategory.value = ''
    searchTotal.value = 0
    currentPage.value = 1
  }

  // 重置详情状态
  const resetDetail = () => {
    currentKnowledge.value = null
  }

  // 检查是否已收藏
  const isKnowledgeFavorite = (knowledgeId) => {
    return checkIsFavorite(knowledgeId)
  }

  // 通过历史记录快速搜索
  const searchFromHistory = (historyQuery) => {
    performSearch(historyQuery, '', 1, true)
  }

  // 通过热门关键词搜索
  const searchFromKeyword = (keyword) => {
    performSearch(keyword, '', 1, true)
  }

  // 搜索分页
  const searchNextPage = () => {
    if (currentPage.value < totalPages.value) {
      performSearch(searchQuery.value, searchCategory.value, currentPage.value + 1)
    }
  }

  const searchPrevPage = () => {
    if (currentPage.value > 1) {
      performSearch(searchQuery.value, searchCategory.value, currentPage.value - 1)
    }
  }

  const goToSearchPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      performSearch(searchQuery.value, searchCategory.value, page)
    }
  }

  return {
    // 状态
    searchResults,
    searchQuery,
    searchCategory,
    searchLoading,
    searchTotal,
    currentPage,
    pageSize,
    currentKnowledge,
    detailLoading,
    favoriteList,
    favoriteLoading,
    favoriteTotal,
    favoritePage,
    historyList,
    historyLoading,
    historyTotal,
    historyPage,
    categories,
    hotKeywords,
    stats,

    // 计算属性
    hasSearchResults,
    totalPages,
    hasFavorites,
    favoritePages,
    historyPages,

    // 方法
    performSearch,
    fetchKnowledgeDetail,
    toggleKnowledgeFavorite,
    fetchFavorites,
    fetchHistory,
    clearHistory,
    fetchCategories,
    fetchHotKeywords,
    fetchStats,
    setSearchParams,
    resetSearch,
    resetDetail,
    isKnowledgeFavorite,
    searchFromHistory,
    searchFromKeyword,
    searchNextPage,
    searchPrevPage,
    goToSearchPage
  }
}) 