from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from django.db.models import Q
from datetime import datetime, timedelta

from api.users.permissions import IsStaff
from .models import PeopleOpinion
from .serializers import PeopleOpinionSerializer, PeopleOpinionCreateSerializer


class PeopleOpinionSubmitAPIView(APIView):
    """群众意见提交API（匿名访问）"""
    
    permission_classes = [AllowAny]  # 允许匿名访问
    
    def post(self, request):
        """群众提交意见"""
        try:
            serializer = PeopleOpinionCreateSerializer(data=request.data)
            if serializer.is_valid():
                opinion = serializer.save()
                
                # 返回成功信息，不返回敏感数据
                return Response({
                    'success': True,
                    'message': '感谢您的宝贵意见！我们已收到您的反馈，将认真处理。',
                    'data': {
                        'id': opinion.id,
                        'title': opinion.title,
                        'created_at': opinion.created_at
                    }
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    'success': False,
                    'message': '提交失败，请检查输入信息',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            return Response({
                'success': False,
                'message': '系统错误，请稍后重试',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PeopleOpinionListAPIView(APIView):
    """群众意见列表API（工作人员使用）"""
    
    permission_classes = [IsStaff]
    
    def get(self, request):
        """获取群众意见列表"""
        try:
            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            search = request.GET.get('search', '').strip()
            
            # 构建查询条件
            queryset = PeopleOpinion.objects.all()
            
            # 搜索过滤
            if search:
                queryset = queryset.filter(
                    Q(title__icontains=search) |
                    Q(content__icontains=search) |
                    Q(name__icontains=search)
                )
            
            # 分页
            total_count = queryset.count()
            start = (page - 1) * page_size
            end = start + page_size
            opinions = queryset[start:end]
            
            # 序列化数据
            serializer = PeopleOpinionSerializer(opinions, many=True)
            
            return Response({
                'success': True,
                'data': {
                    'opinions': serializer.data,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total_count': total_count,
                        'total_pages': (total_count + page_size - 1) // page_size
                    }
                }
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': '获取意见列表失败',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PeopleOpinionDetailAPIView(APIView):
    """群众意见详情API（工作人员使用）"""
    
    permission_classes = [IsStaff]
    
    def get(self, request, opinion_id):
        """获取意见详情"""
        try:
            opinion = PeopleOpinion.objects.get(id=opinion_id)
            serializer = PeopleOpinionSerializer(opinion)
            
            return Response({
                'success': True,
                'data': serializer.data
            })
            
        except PeopleOpinion.DoesNotExist:
            return Response({
                'success': False,
                'message': '意见不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': '获取意见详情失败',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def put(self, request, opinion_id):
        """更新意见（工作人员编辑）"""
        try:
            opinion = PeopleOpinion.objects.get(id=opinion_id)
            serializer = PeopleOpinionSerializer(opinion, data=request.data, partial=True)
            
            if serializer.is_valid():
                serializer.save()
                return Response({
                    'success': True,
                    'message': '意见更新成功',
                    'data': serializer.data
                })
            else:
                return Response({
                    'success': False,
                    'message': '更新失败，请检查输入信息',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except PeopleOpinion.DoesNotExist:
            return Response({
                'success': False,
                'message': '意见不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': '更新意见失败',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request, opinion_id):
        """删除意见"""
        try:
            opinion = PeopleOpinion.objects.get(id=opinion_id)
            opinion.delete()
            
            return Response({
                'success': True,
                'message': '意见删除成功'
            }, status=status.HTTP_200_OK)
            
        except PeopleOpinion.DoesNotExist:
            return Response({
                'success': False,
                'message': '意见不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': '删除意见失败',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 删除统计视图
