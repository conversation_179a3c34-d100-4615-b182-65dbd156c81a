from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.renderers import BaseRenderer
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.http import JsonResponse
import json

from .services import AIKnowledgeService, OpinionAIService


class SSERenderer(BaseRenderer):
    """SSE渲染器"""
    media_type = 'text/event-stream'
    format = 'sse'
    charset = 'utf-8'
    
    def render(self, data, accepted_media_type=None, renderer_context=None):
        return data


class ChatSSEAPIView(APIView):
    """AI聊天SSE流式API"""
    permission_classes = [IsAuthenticated]
    renderer_classes = [SSERenderer]  # 使用SSE渲染器
    
    def post(self, request):
        """发送消息给AI，返回SSE流"""
        try:
            # 简单获取请求数据
            data = request.data
            query = data.get('query', '').strip()
            
            if not query:
                return JsonResponse(
                    {'error': '问题不能为空'},
                    status=400
                )
            
            # 获取可选参数
            user_id = data.get('user', str(request.user.id))
            conversation_id = data.get('conversation_id')
            # 如果conversation_id为null或空字符串，则设为None
            if not conversation_id or conversation_id == 'null':
                conversation_id = None
            inputs = data.get('inputs', {})
            
            # 创建SSE流式响应
            return AIKnowledgeService.create_sse_response(
                query=query,
                user_id=user_id,
                conversation_id=conversation_id,
                inputs=inputs
            )
            
        except Exception as e:
            return JsonResponse(
                {'error': 'AI服务暂时不可用', 'details': str(e)},
                status=500
            )


class AudioToTextAPIView(APIView):
    """语音转文字API"""
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="语音转文字",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'file': openapi.Schema(
                    type=openapi.TYPE_FILE,
                    description="音频文件 (支持格式: mp3, mp4, mpeg, mpga, m4a, wav, webm)"
                )
            },
            required=['file']
        ),
        responses={
            200: openapi.Response(
                description="语音转文字成功",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否成功'),
                        'text': openapi.Schema(type=openapi.TYPE_STRING, description='转换后的文字'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='成功消息')
                    }
                )
            ),
            400: openapi.Response(
                description="请求错误",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否成功'),
                        'error': openapi.Schema(type=openapi.TYPE_STRING, description='错误信息'),
                        'details': openapi.Schema(type=openapi.TYPE_STRING, description='详细错误信息')
                    }
                )
            )
        }
    )
    def post(self, request):
        """语音转文字接口"""
        try:
            # 检查是否有文件上传
            if 'file' not in request.FILES:
                return Response({
                    'success': False,
                    'error': '请上传音频文件'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            audio_file = request.FILES['file']
            
            # 检查文件大小 (限制为50MB)
            max_size = 50 * 1024 * 1024  # 50MB
            if audio_file.size > max_size:
                return Response({
                    'success': False,
                    'error': f'文件过大，最大支持50MB，当前文件大小: {audio_file.size / 1024 / 1024:.2f}MB'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查文件类型
            allowed_types = [
                'audio/mp3', 'audio/mpeg', 'audio/mp4', 'audio/mpga', 
                'audio/m4a', 'audio/wav', 'audio/webm'
            ]
            
            if audio_file.content_type not in allowed_types:
                return Response({
                    'success': False,
                    'error': f'不支持的文件类型: {audio_file.content_type}，支持的格式: mp3, mp4, mpeg, mpga, m4a, wav, webm'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 调用语音转文字服务
            result = AIKnowledgeService.audio_to_text(audio_file)
            
            if result['success']:
                return Response(result, status=status.HTTP_200_OK)
            else:
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Exception as e:
            return Response({
                'success': False,
                'error': '语音转文字服务异常',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class OpinionGenerateAPIView(APIView):
    """意见建议AI生成API"""
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="AI生成意见建议",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'original_content': openapi.Schema(
                    type=openapi.TYPE_STRING, 
                    description='原始意见内容',
                    example='关于改善社区环境的建议'
                ),
                'category': openapi.Schema(
                    type=openapi.TYPE_STRING, 
                    description='意见类别',
                    example='社会事务'
                ),
                'context': openapi.Schema(
                    type=openapi.TYPE_STRING, 
                    description='上下文信息（可选）',
                    example='该社区存在垃圾分类不规范的问题'
                ),
                'title': openapi.Schema(
                    type=openapi.TYPE_STRING, 
                    description='意见标题（可选）',
                    example='关于改善社区环境卫生的建议'
                ),
                'structured_text': openapi.Schema(
                    type=openapi.TYPE_STRING, 
                    description='结构化文本（标题-分类-内容，可选）',
                    example='标题：关于改善社区环境卫生的建议\n分类：社会事务\n内容：关于改善社区环境的建议'
                )
            },
            required=['original_content', 'category']
        ),
        responses={
            200: openapi.Response(
                description="生成成功",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否成功'),
                        'generated_content': openapi.Schema(type=openapi.TYPE_STRING, description='生成的意见建议内容'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='成功消息')
                    }
                )
            ),
            400: openapi.Response(
                description="请求错误",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否成功'),
                        'error': openapi.Schema(type=openapi.TYPE_STRING, description='错误信息'),
                        'details': openapi.Schema(type=openapi.TYPE_STRING, description='详细错误信息')
                    }
                )
            )
        }
    )
    def post(self, request):
        """生成意见建议接口"""
        try:
            data = request.data
            original_content = data.get('original_content', '').strip()
            category = data.get('category', '').strip()
            context = data.get('context', '').strip()
            title = data.get('title', '').strip()
            structured_text = data.get('structured_text', '').strip()
            
            # 验证必填参数
            if not original_content:
                return Response({
                    'success': False,
                    'error': '原始意见内容不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if not category:
                return Response({
                    'success': False,
                    'error': '意见类别不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 调用意见建议生成服务
            opinion_service = OpinionAIService()
            result = opinion_service.generate_suggestion(
                original_content=original_content,
                category=category,
                context=context,
                title=title,
                structured_text=structured_text
            )
            
            if result['success']:
                return Response(result, status=status.HTTP_200_OK)
            else:
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Exception as e:
            return Response({
                'success': False,
                'error': '意见建议生成服务异常',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class VoiceToOpinionAPIView(APIView):
    """语音转意见建议API - 整合语音转文字和AI内容解析"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="语音转意见建议（语音转文字 + AI内容解析）",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'file': openapi.Schema(
                    type=openapi.TYPE_FILE,
                    description="音频文件 (支持格式: mp3, mp4, mpeg, mpga, m4a, wav, webm)"
                ),
                'category': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="意见建议分类（可选）",
                    example="urban_construction"
                ),
                'summarize_content': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="是否让AI总结优化内容（可选，默认true）",
                    example="true"
                )
            },
            required=['file']
        ),
        responses={
            200: openapi.Response(
                description="解析成功",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否成功'),
                        'title': openapi.Schema(type=openapi.TYPE_STRING, description='提取的标题'),
                        'content': openapi.Schema(type=openapi.TYPE_STRING, description='提取的内容'),
                        'original_text': openapi.Schema(type=openapi.TYPE_STRING, description='原始语音转文字结果'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='成功消息')
                    }
                )
            ),
            400: openapi.Response(description="请求参数错误"),
            500: openapi.Response(description="服务器内部错误")
        }
    )
    def post(self, request):
        """语音转意见建议接口"""
        try:
            # 检查是否有文件上传
            if 'file' not in request.FILES:
                return Response({
                    'success': False,
                    'error': '请上传音频文件'
                }, status=status.HTTP_400_BAD_REQUEST)

            audio_file = request.FILES['file']
            category = request.data.get('category', '')
            summarize_content = request.data.get('summarize_content', 'true').lower() == 'true'

            # 检查文件大小 (限制为50MB)
            max_size = 50 * 1024 * 1024  # 50MB
            if audio_file.size > max_size:
                return Response({
                    'success': False,
                    'error': f'文件过大，最大支持50MB，当前文件大小: {audio_file.size / 1024 / 1024:.2f}MB'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 检查文件类型
            allowed_types = [
                'audio/mp3', 'audio/mpeg', 'audio/mp4', 'audio/mpga',
                'audio/m4a', 'audio/wav', 'audio/webm'
            ]

            if audio_file.content_type not in allowed_types:
                return Response({
                    'success': False,
                    'error': f'不支持的文件类型: {audio_file.content_type}，支持的格式: mp3, mp4, mpeg, mpga, m4a, wav, webm'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 第一步：调用语音转文字服务
            from .services import AIKnowledgeService, VoiceContentParserService

            audio_result = AIKnowledgeService.audio_to_text(audio_file)

            if not audio_result['success']:
                return Response(audio_result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            voice_text = audio_result['text']

            if not voice_text or not voice_text.strip():
                return Response({
                    'success': False,
                    'error': '语音转文字结果为空，请重新录音'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 第二步：调用AI内容解析服务
            parser_service = VoiceContentParserService()
            parse_result = parser_service.parse_voice_text(voice_text, category, summarize_content)

            if parse_result['success']:
                return Response(parse_result, status=status.HTTP_200_OK)
            else:
                return Response(parse_result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({
                'success': False,
                'error': '语音转意见建议服务异常',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class OpinionGenerateSSEAPIView(APIView):
    """意见建议AI生成SSE流式API"""
    permission_classes = [IsAuthenticated]
    renderer_classes = [SSERenderer]

    @swagger_auto_schema(
        operation_description="AI生成意见建议（流式输出）",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'original_content': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="原始意见内容",
                    example="我们小区停车位不够用"
                ),
                'category': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="意见分类",
                    example="urban_construction"
                ),
                'context': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="补充背景信息（可选）",
                    example="小区有200户居民，但只有50个停车位"
                )
            },
            required=['original_content']
        ),
        responses={
            200: openapi.Response(
                description="SSE流式响应",
                schema=openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="text/event-stream格式的流式数据"
                )
            ),
            400: openapi.Response(description="请求参数错误"),
            500: openapi.Response(description="服务器内部错误")
        }
    )
    def post(self, request):
        """AI生成意见建议流式接口"""
        try:
            original_content = request.data.get('original_content', '').strip()
            category = request.data.get('category', '')
            context = request.data.get('context', '')

            if not original_content:
                return JsonResponse({
                    'error': '原始意见内容不能为空'
                }, status=400)

            # 创建SSE流式响应
            from .services import OpinionAIService
            return OpinionAIService.create_opinion_sse_response(
                original_content=original_content,
                category=category,
                context=context,
                user_id=str(request.user.id)
            )

        except Exception as e:
            return JsonResponse({
                'error': 'AI意见建议生成服务异常',
                'details': str(e)
            }, status=500)
