<template>
  <aside class="right-sidebar">
    <RightBlock1 />
    <RightBlock2 />
    <RightBlock3 />
  </aside>
</template>

<script setup>
// 导入独立的区块组件
import RightBlock1 from '../blocks/RightBlock1.vue'
import RightBlock2 from '../blocks/RightBlock2.vue'
import RightBlock3 from '../blocks/RightBlock3.vue'
</script>

<style scoped>
/* 右侧边栏样式 */
.right-sidebar {
  display: flex;
  flex-direction: column;
  width: 28vw;
  gap: 8px;
}


</style> 