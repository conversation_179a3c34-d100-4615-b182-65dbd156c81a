"""
用户管理应用配置
"""

from django.apps import AppConfig


class UsersConfig(AppConfig):
    """用户管理应用配置类"""
    
    # 默认自动字段类型
    default_auto_field = 'django.db.models.BigAutoField'
    
    # 应用名称（文件路径）
    name = 'api.users'
    
    # 应用标签（用于模型引用，不能包含点号）
    label = 'users'
    
    # 应用的可读名称
    verbose_name = '用户管理'
    
    def ready(self):
        """应用准备就绪时执行的代码"""
        # 导入信号处理器
        try:
            import api.users.signals  # noqa F401
        except ImportError:
            pass 