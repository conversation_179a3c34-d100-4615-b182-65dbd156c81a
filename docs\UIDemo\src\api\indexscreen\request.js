/**
 * API请求工具
 * 用于模拟API请求，后期可以替换为真实的HTTP请求
 */

// 模拟网络延迟
const simulateDelay = (ms = 500) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 模拟API请求
 * @param {string} url - 请求URL
 * @param {string} method - 请求方法
 * @param {Object} data - 请求数据
 * @param {Object} mockResponse - 模拟响应数据
 * @param {Object} options - 请求选项
 * @returns {Promise} 返回Promise对象
 */
export const mockRequest = async (url, method = 'GET', data = null, mockResponse = null, options = {}) => {
  const { delay = 500 } = options
  
  console.log(`🚀 模拟API请求: ${method} ${url}`, data ? { data } : '')
  
  // 模拟网络延迟
  await simulateDelay(delay)
  
  try {
    // 如果提供了mockResponse，使用它；否则使用默认响应
    const response = mockResponse || {
      code: 200,
      message: 'success',
      data: null,
      timestamp: Date.now()
    }
    
    console.log(`✅ API请求成功: ${url}`, response)
    return response
  } catch (error) {
    console.error(`❌ API请求失败: ${url}`, error)
    throw error
  }
}

/**
 * GET请求
 * @param {string} url - 请求URL
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回Promise对象
 */
export const get = (url, params = {}) => {
  const queryString = Object.keys(params).length 
    ? '?' + new URLSearchParams(params).toString() 
    : ''
  return mockRequest(url + queryString, 'GET')
}

/**
 * POST请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @returns {Promise} 返回Promise对象
 */
export const post = (url, data = {}) => {
  return mockRequest(url, 'POST', data)
}

/**
 * PUT请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @returns {Promise} 返回Promise对象
 */
export const put = (url, data = {}) => {
  return mockRequest(url, 'PUT', data)
}

/**
 * DELETE请求
 * @param {string} url - 请求URL
 * @returns {Promise} 返回Promise对象
 */
export const del = (url) => {
  return mockRequest(url, 'DELETE')
}

// API基础配置
export const API_CONFIG = {
  baseURL: '/api', // 基础URL，后期可配置为真实API地址
  timeout: 10000,  // 请求超时时间
  headers: {
    'Content-Type': 'application/json'
  }
} 