# Generated by Django 5.2.3 on 2025-07-15 13:06

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='WorkPlan',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='工作计划ID')),
                ('title', models.CharField(help_text='工作计划的标题，最多200字符', max_length=200, verbose_name='计划标题')),
                ('start_date', models.DateField(help_text='计划开始日期', verbose_name='开始时间')),
                ('end_date', models.DateField(help_text='计划结束日期', verbose_name='结束时间')),
                ('content', models.TextField(help_text='详细的计划内容描述', verbose_name='计划内容')),
                ('status', models.CharField(choices=[('planned', '计划中'), ('in_progress', '进行中'), ('completed', '已完成'), ('cancelled', '已取消')], default='planned', help_text='当前计划的执行状态', max_length=50, verbose_name='计划状态')),
                ('reminder_days', models.IntegerField(default=3, help_text='到期前多少天提醒，用于登录弹窗提醒', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(30)], verbose_name='提醒天数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('staff_member', models.ForeignKey(help_text='计划负责的工作人员', on_delete=django.db.models.deletion.CASCADE, to='users.staffmember', verbose_name='负责工作人员')),
            ],
            options={
                'verbose_name': '工作计划',
                'verbose_name_plural': '工作计划',
                'db_table': 'work_plans',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['staff_member'], name='work_plans_staff_m_fb1d22_idx'), models.Index(fields=['title'], name='work_plans_title_37904f_idx'), models.Index(fields=['start_date'], name='work_plans_start_d_daa4b0_idx'), models.Index(fields=['end_date'], name='work_plans_end_dat_94a538_idx'), models.Index(fields=['status'], name='work_plans_status_3b38d7_idx'), models.Index(fields=['end_date', 'status'], name='work_plans_end_dat_7e9a87_idx')],
            },
        ),
    ]
