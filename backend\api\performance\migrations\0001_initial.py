# Generated by Django 5.2.3 on 2025-06-19 12:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PerformanceRecord',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='履职记录ID')),
                ('performance_date', models.DateField(help_text='履职活动发生的日期，不能超过今天', verbose_name='履职日期')),
                ('performance_type', models.CharField(choices=[('会议参与', '会议参与'), ('实地调研', '实地调研'), ('走访群众', '走访群众'), ('议案提交', '议案提交'), ('建议办理', '建议办理'), ('监督检查', '监督检查'), ('法律宣传', '法律宣传'), ('信访接待', '信访接待'), ('培训学习', '培训学习'), ('联络活动', '联络活动'), ('专题调研', '专题调研'), ('视察活动', '视察活动'), ('座谈交流', '座谈交流'), ('执法检查', '执法检查'), ('民生走访', '民生走访'), ('政策宣讲', '政策宣讲'), ('其他活动', '其他活动')], help_text='履职活动的类型分类，预设17种类型', max_length=50, verbose_name='履职类型')),
                ('performance_content', models.TextField(help_text='履职活动的主要内容描述，建议100-200字', verbose_name='履职内容')),
                ('activity_location', models.CharField(help_text='履职活动发生的具体地点', max_length=200, verbose_name='活动地点')),
                ('detailed_description', models.TextField(blank=True, help_text='履职活动的详细情况描述，可选填写，建议300-500字', null=True, verbose_name='详细描述')),
                ('performance_status', models.CharField(choices=[('进行中', '进行中'), ('已完成', '已完成'), ('已暂停', '已暂停')], default='已完成', help_text='履职活动的当前状态', max_length=50, verbose_name='履职状态')),
                ('has_attachments', models.BooleanField(default=False, help_text='标识该履职记录是否包含多媒体附件，自动维护', verbose_name='是否有附件')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('representative', models.ForeignKey(help_text='与代表表的外键关联，删除代表时级联删除履职记录', on_delete=django.db.models.deletion.CASCADE, related_name='performance_records', to='users.representative', verbose_name='关联代表')),
            ],
            options={
                'verbose_name': '履职记录',
                'verbose_name_plural': '履职记录',
                'db_table': 'performance_records',
                'ordering': ['-performance_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PerformanceAttachment',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='附件ID')),
                ('file_type', models.CharField(choices=[('image', '图片'), ('audio', '音频'), ('video', '视频'), ('document', '文档')], help_text='附件的文件类型分类：图片/音频/视频/文档', max_length=20, verbose_name='文件类型')),
                ('original_filename', models.CharField(help_text='用户上传时的原始文件名', max_length=255, verbose_name='原始文件名')),
                ('stored_filename', models.CharField(help_text='系统生成的唯一存储文件名，格式：uuid_timestamp_原始名', max_length=255, verbose_name='存储文件名')),
                ('file_path', models.CharField(help_text='文件在服务器上的相对存储路径', max_length=500, verbose_name='文件存储路径')),
                ('file_size', models.BigIntegerField(help_text='文件大小，单位为字节', verbose_name='文件大小')),
                ('mime_type', models.CharField(help_text='文件的MIME类型，用于文件验证和处理', max_length=100, verbose_name='MIME类型')),
                ('file_hash', models.CharField(help_text='文件的MD5哈希值，用于去重和完整性校验', max_length=64, verbose_name='文件哈希值')),
                ('thumbnail_path', models.CharField(blank=True, help_text='图片和视频文件的缩略图存储路径', max_length=500, null=True, verbose_name='缩略图路径')),
                ('duration', models.IntegerField(blank=True, help_text='音频和视频文件的时长，单位为秒', null=True, verbose_name='媒体时长')),
                ('width', models.IntegerField(blank=True, help_text='图片和视频的宽度像素', null=True, verbose_name='宽度')),
                ('height', models.IntegerField(blank=True, help_text='图片和视频的高度像素', null=True, verbose_name='高度')),
                ('upload_status', models.CharField(choices=[('uploading', '上传中'), ('uploaded', '已上传'), ('failed', '上传失败'), ('processing', '处理中')], default='uploaded', help_text='文件的上传处理状态', max_length=20, verbose_name='上传状态')),
                ('sort_order', models.IntegerField(default=0, help_text='附件在界面上的显示顺序', verbose_name='排序序号')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('performance_record', models.ForeignKey(help_text='与履职记录表的外键关联，删除记录时级联删除附件', on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='performance.performancerecord', verbose_name='关联履职记录')),
            ],
            options={
                'verbose_name': '履职记录附件',
                'verbose_name_plural': '履职记录附件',
                'db_table': 'performance_attachments',
                'ordering': ['sort_order', 'created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='performancerecord',
            index=models.Index(fields=['representative'], name='performance_represe_d9f0bd_idx'),
        ),
        migrations.AddIndex(
            model_name='performancerecord',
            index=models.Index(fields=['performance_date'], name='performance_perform_96084a_idx'),
        ),
        migrations.AddIndex(
            model_name='performancerecord',
            index=models.Index(fields=['performance_type'], name='performance_perform_7b8fe0_idx'),
        ),
        migrations.AddIndex(
            model_name='performancerecord',
            index=models.Index(fields=['performance_status'], name='performance_perform_628e91_idx'),
        ),
        migrations.AddIndex(
            model_name='performancerecord',
            index=models.Index(fields=['has_attachments'], name='performance_has_att_411c65_idx'),
        ),
        migrations.AddIndex(
            model_name='performancerecord',
            index=models.Index(fields=['representative', 'performance_date'], name='performance_represe_da57a7_idx'),
        ),
        migrations.AddIndex(
            model_name='performancerecord',
            index=models.Index(fields=['representative', 'performance_type'], name='performance_represe_706ba5_idx'),
        ),
        migrations.AddIndex(
            model_name='performanceattachment',
            index=models.Index(fields=['performance_record'], name='performance_perform_c10714_idx'),
        ),
        migrations.AddIndex(
            model_name='performanceattachment',
            index=models.Index(fields=['file_type'], name='performance_file_ty_f04381_idx'),
        ),
        migrations.AddIndex(
            model_name='performanceattachment',
            index=models.Index(fields=['upload_status'], name='performance_upload__10abb4_idx'),
        ),
        migrations.AddIndex(
            model_name='performanceattachment',
            index=models.Index(fields=['file_hash'], name='performance_file_ha_a271ef_idx'),
        ),
        migrations.AddIndex(
            model_name='performanceattachment',
            index=models.Index(fields=['created_at'], name='performance_created_aa6c49_idx'),
        ),
        migrations.AddIndex(
            model_name='performanceattachment',
            index=models.Index(fields=['performance_record', 'sort_order'], name='performance_perform_452c51_idx'),
        ),
        migrations.AddIndex(
            model_name='performanceattachment',
            index=models.Index(fields=['performance_record', 'file_type'], name='performance_perform_4ca2e5_idx'),
        ),
        migrations.AddConstraint(
            model_name='performanceattachment',
            constraint=models.CheckConstraint(condition=models.Q(('file_type__in', ['image', 'audio', 'video', 'document'])), name='chk_performance_attachment_file_type'),
        ),
        migrations.AddConstraint(
            model_name='performanceattachment',
            constraint=models.CheckConstraint(condition=models.Q(('upload_status__in', ['uploading', 'uploaded', 'failed', 'processing'])), name='chk_performance_attachment_upload_status'),
        ),
        migrations.AddConstraint(
            model_name='performanceattachment',
            constraint=models.CheckConstraint(condition=models.Q(('file_size__gt', 0), ('file_size__lte', 104857600)), name='chk_performance_attachment_file_size'),
        ),
    ]
