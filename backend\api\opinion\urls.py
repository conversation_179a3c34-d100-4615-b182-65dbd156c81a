"""
意见建议管理URL配置

定义意见建议管理模块的所有API端点路由
"""

from django.urls import path
from . import views

app_name = 'opinion'

urlpatterns = [
    # 意见建议基础CRUD操作
    path('suggestions/', views.OpinionSuggestionListView.as_view(), name='suggestion-list'),
    path('suggestions/create/', views.OpinionSuggestionCreateView.as_view(), name='suggestion-create'),
    path('suggestions/<int:opinion_id>/', views.OpinionSuggestionDetailView.as_view(), name='suggestion-detail'),
    path('suggestions/<int:opinion_id>/update/', views.OpinionSuggestionUpdateView.as_view(), name='suggestion-update'),
    path('suggestions/<int:opinion_id>/delete/', views.OpinionSuggestionDeleteView.as_view(), name='suggestion-delete'),
    
    # 意见建议状态操作
    path('suggestions/<int:opinion_id>/submit/', views.OpinionSuggestionSubmitView.as_view(), name='suggestion-submit'),
    
    # 审核操作（工作人员专用）
    path('suggestions/<int:opinion_id>/review/', views.OpinionReviewCreateView.as_view(), name='review-create'),
    
    # AI辅助功能
    path('ai/generate/', views.AIGenerateView.as_view(), name='ai-generate'),
    
    # 统计数据
    path('statistics/', views.OpinionStatisticsView.as_view(), name='statistics'),
] 