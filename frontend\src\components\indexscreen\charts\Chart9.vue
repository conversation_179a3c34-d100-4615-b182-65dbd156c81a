<template>
  <div class="chart9-container">
    <div class="advice-container">
      <ul
        class="advice-list"
        ref="adviceList"
        @scroll="handleScroll"
        @wheel="handleWheel"
        @touchstart="handleTouch"
        @touchend="handleTouchEnd"
        @mousedown="handleMouseDown"
        @mouseup="handleMouseUp"
      >
        <li v-for="(item, idx) in data" :key="idx">
          <span class="advice-content"> · {{ item.content }} </span>
          <div class="advice-info">
            <span class="advice-date">{{ item.time }}</span>
            <span class="advice-author">{{ item.author || '' }}</span>
          </div>
        </li>
        <!-- 复制列表项实现无缝滚动 -->
        <li v-for="(item, idx) in data" :key="`duplicate-${idx}`">
          <span class="advice-content"> · {{ item.content }} </span>
          <div class="advice-info">
            <span class="advice-date">{{ item.time }}</span>
            <span class="advice-author">{{ item.author || '' }}</span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isManualScrolling: false,
      scrollInterval: null,
      resumeTimeout: null,
      lastScrollTop: 0,
      isMouseDown: false,
    }
  },
  mounted() {
    // 延迟启动滚动，确保DOM完全渲染
    this.$nextTick(() => {
      setTimeout(() => {
        this.startAutoScroll()
      }, 1000)
    })
  },
  beforeUnmount() {
    this.stopAutoScroll()
    if (this.resumeTimeout) {
      clearTimeout(this.resumeTimeout)
    }
  },
  methods: {
    startAutoScroll() {
      // 清除可能存在的定时器
      this.stopAutoScroll()

      this.scrollInterval = setInterval(() => {
        // 如果正在手动滚动，暂停自动滚动
        if (this.isManualScrolling) {
          return
        }

        const list = this.$refs.adviceList
        if (list && this.data.length > 0) {
          // 确保有足够的内容可以滚动
          if (list.scrollHeight > list.clientHeight) {
            if (list.scrollTop >= list.scrollHeight / 2) {
              // 当滚动到一半时，重置到顶部实现无缝循环
              list.scrollTop = 0
            } else {
              list.scrollTop += 1
            }
          }
        }
      }, 10) // 每50ms滚动1px，控制滚动速度
    },
    stopAutoScroll() {
      if (this.scrollInterval) {
        clearInterval(this.scrollInterval)
        this.scrollInterval = null
      }
    },
    pauseAutoScroll() {
      this.isManualScrolling = true
      this.stopAutoScroll()
    },
    resumeAutoScroll() {
      // 清除之前的恢复定时器
      if (this.resumeTimeout) {
        clearTimeout(this.resumeTimeout)
      }

      // 2秒后恢复自动滚动
      this.resumeTimeout = setTimeout(() => {
        this.isManualScrolling = false
        this.startAutoScroll()
      }, 2000)
    },
    handleScroll() {
      const list = this.$refs.adviceList
      if (!list) return

      // 检测是否为手动滚动（滚动位置发生变化）
      if (Math.abs(list.scrollTop - this.lastScrollTop) > 1) {
        this.pauseAutoScroll()
        this.resumeAutoScroll()
      }

      this.lastScrollTop = list.scrollTop
    },
    handleWheel() {
      // 鼠标滚轮事件
      this.pauseAutoScroll()
      this.resumeAutoScroll()
    },
    handleTouch() {
      // 触摸开始事件
      this.pauseAutoScroll()
    },
    handleTouchEnd() {
      // 触摸结束事件
      this.resumeAutoScroll()
    },
    handleMouseDown() {
      // 鼠标按下事件
      this.isMouseDown = true
      this.pauseAutoScroll()
    },
    handleMouseUp() {
      // 鼠标释放事件
      this.isMouseDown = false
      this.resumeAutoScroll()
    },
  },
}
</script>

<style scoped>
.chart9-container {
  /* background: #0a2343; */
  color: #ecefff;
  padding: 8px;
  border-radius: 10px;
  font-size: 13px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.advice-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.advice-list {
  /* margin-top: 16px; */
  background: rgba(20, 50, 90, 0.8);
  border-radius: 8px;
  padding: 0;
  list-style: none;
  margin: 0;
  height: 100%;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.advice-list::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}

.advice-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  line-height: 18px;
  border-bottom: 1px solid #355992;
  position: relative;
  white-space: nowrap;
}

.advice-list li:last-child {
  border-bottom: none;
}

.advice-list li:hover {
  background: rgba(64, 128, 255, 0.12);
}

.advice-content {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.advice-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 16px;
  flex-shrink: 0;
}

.advice-date {
  color: #ecefff;
  font-size: 12px;
  margin-bottom: 2px;
}

.advice-author {
  color: #5b94de;
  font-size: 12px;
}
</style>
