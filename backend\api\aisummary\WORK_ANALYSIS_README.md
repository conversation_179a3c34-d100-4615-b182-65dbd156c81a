# 代表工作总结功能说明

## 功能概述

代表工作总结功能是为站点工作人员提供的管理界面，允许工作人员查看本站点所有代表的年度履职AI分析状态，并为代表生成AI总结。

## 主要特性

### 🎯 核心功能

1. **代表列表查看** - 工作人员可以查看所有代表的履职分析状态
2. **AI分析生成** - 工作人员可以为任意代表生成AI履职总结
3. **状态同步** - 工作人员生成的AI总结与代表端看到的结果完全一致
4. **批量操作** - 支持批量生成多个代表的AI分析
5. **数据统计** - 显示每个代表的履职记录数量和意见建议数量

### 🔐 权限控制

- **工作人员专用** - 只有工作人员角色可以访问代表工作总结功能
- **跨代表操作** - 工作人员可以为任意代表生成AI分析
- **数据隔离** - 生成的AI分析归属于指定代表，保持数据完整性

## API接口

### 1. 获取代表工作总结列表

```http
GET /api/v1/ai-summaries/representatives/?year=2024
Authorization: Bearer <staff_token>
```

**响应示例:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "张明华",
            "level": "市级人大代表",
            "department": "第一选区",
            "phone": "138****1234",
            "recordCount": 25,
            "opinionCount": 8,
            "analysisStatus": "analyzed",
            "lastAnalysisTime": "2024-12-20 14:30:00"
        }
    ]
}
```

### 2. 生成代表AI分析（工作人员操作）

```http
POST /api/v1/ai-summaries/generate/
Authorization: Bearer <staff_token>
Content-Type: application/json

{
    "analysis_year": 2024,
    "force_regenerate": false,
    "representative_id": 1
}
```

**响应示例:**
```json
{
    "success": true,
    "status": "generating",
    "message": "AI总结正在生成中，请稍后查询结果",
    "id": 123,
    "analysis_year": 2024,
    "created_at": "2024-12-20T14:30:00Z"
}
```

### 3. 查看代表AI分析结果

```http
GET /api/v1/ai-summaries/2024/?representative_id=1
Authorization: Bearer <staff_token>
```

## 前端集成

### 页面路径
```
/staff/representative-summary - 代表工作总结页面（工作人员专用）
```

### 主要功能组件

1. **筛选区域** - 年度选择、状态筛选
2. **代表列表** - 显示所有代表及其AI分析状态
3. **批量操作** - 支持批量生成AI分析
4. **进度监控** - 显示批量生成的进度
5. **分析展示** - 查看AI生成的履职分析报告

### API调用示例

```javascript
import { 
    getRepresentativesList, 
    generateRepresentativeAnalysis,
    getRepresentativeAnalysis 
} from '@/api/workAnalysis'

// 获取代表列表
const representatives = await getRepresentativesList('2024')

// 生成AI分析
const result = await generateRepresentativeAnalysis(1, '2024')

// 查看分析结果
const analysis = await getRepresentativeAnalysis(1, '2024')
```

## 数据流程

### 1. 权限验证流程

```mermaid
graph TD
    A[工作人员登录] --> B[访问代表工作总结页面]
    B --> C{权限验证}
    C -->|通过| D[显示代表列表]
    C -->|失败| E[显示权限错误]
```

### 2. AI分析生成流程

```mermaid
graph TD
    A[工作人员选择代表] --> B[点击生成分析]
    B --> C[后端验证权限和参数]
    C --> D[调用AI总结服务]
    D --> E[收集代表履职数据]
    E --> F[调用AI分析接口]
    F --> G[保存分析结果]
    G --> H[返回生成状态]
```

### 3. 数据一致性保证

```mermaid
graph TD
    A[工作人员生成AI分析] --> B[保存到数据库]
    B --> C[代表查看AI分析]
    C --> D[读取相同数据]
    D --> E[显示一致结果]
```

## 配置说明

### 环境变量

```bash
# AI服务配置（同aisummary模块）
export AI_SUMMARY_DIFY_API_KEY=app-YourRealDifyAppKey123456
export AI_SUMMARY_DIFY_BASE_URL=https://dify.gxaigc.cn/v1
export AI_SUMMARY_TIMEOUT=120
```

### Django配置

```python
# settings.py
INSTALLED_APPS = [
    # ...
    'api.aisummary',  # 包含代表工作总结功能
]
```

### URL配置

```python
# urls.py
urlpatterns = [
    path('api/v1/ai-summaries/', include('api.aisummary.urls')),
]
```

## 测试指南

### 运行测试脚本

```bash
# 进入后端目录
cd backend

# 运行代表工作总结功能测试
python test_representative_work_summary.py
```

### 手动测试步骤

1. **登录工作人员账号**
2. **访问代表工作总结页面**
3. **选择分析年度**
4. **查看代表列表和状态**
5. **为代表生成AI分析**
6. **查看生成的分析结果**
7. **验证数据一致性**

## 注意事项

### 开发注意事项

1. **权限验证** - 确保所有API都有正确的权限控制
2. **数据验证** - 对输入参数进行严格验证
3. **错误处理** - 提供友好的错误信息和异常处理
4. **性能优化** - 批量操作时注意性能和超时控制

### 部署注意事项

1. **AI服务配置** - 确保配置了真实的AI服务提供商
2. **权限设置** - 正确配置工作人员和代表的权限关系
3. **数据备份** - 生成AI分析前建议备份重要数据
4. **监控日志** - 关注AI分析生成的成功率和性能指标

## 扩展规划

1. **定时生成** - 支持定时自动为所有代表生成AI分析
2. **模板定制** - 支持自定义AI分析报告模板
3. **对比分析** - 支持代表间和年度间的对比分析
4. **导出功能** - 支持批量导出代表AI分析报告
5. **通知机制** - AI分析完成后自动通知相关人员

---

## 联系信息

如有问题或建议，请联系开发团队。 