#!/usr/bin/env python
"""
安全测试运行脚本
专门用于运行performance模块的安全测试套件
"""
import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner


def run_security_tests():
    """运行安全测试套件"""
    os.environ['DJANGO_SETTINGS_MODULE'] = 'npcsite.settings'
    django.setup()
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    
    print("=" * 70)
    print("🛡️  NPC系统履职管理模块 - 安全测试套件")
    print("=" * 70)
    
    # 安全测试类列表
    security_test_classes = [
        'api.performance.tests.SQLInjectionTest',
        'api.performance.tests.XSSPreventionTest', 
        'api.performance.tests.MaliciousFileUploadTest',
        'api.performance.tests.PathTraversalTest',
        'api.performance.tests.PrivilegeEscalationTest',
        'api.performance.tests.DataLeakageTest',
        'api.performance.tests.FileIntegrityTest',
        'api.performance.tests.BoundaryValueSecurityTest',
        'api.performance.tests.CSRFAndCORSTest',
        'api.performance.tests.RateLimitingTest'
    ]
    
    print(f"即将运行 {len(security_test_classes)} 个安全测试类...")
    print()
    
    # 运行安全测试
    failures = test_runner.run_tests(security_test_classes)
    
    print("\n" + "=" * 70)
    if failures:
        print(f"❌ 发现 {failures} 个安全测试失败")
        print("请检查上述失败的测试用例，确保系统安全性")
    else:
        print("✅ 所有安全测试通过！")
        print("系统具备良好的安全防护能力")
    print("=" * 70)
    
    return failures


def run_all_tests():
    """运行所有测试（包括功能测试和安全测试）"""
    os.environ['DJANGO_SETTINGS_MODULE'] = 'npcsite.settings'
    django.setup()
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    
    print("=" * 70)
    print("🧪 NPC系统履职管理模块 - 完整测试套件")
    print("=" * 70)
    
    # 运行performance模块的所有测试
    failures = test_runner.run_tests(['api.performance.tests'])
    
    print("\n" + "=" * 70)
    if failures:
        print(f"❌ 发现 {failures} 个测试失败")
    else:
        print("✅ 所有测试通过！")
        print("包括功能测试和安全测试")
    print("=" * 70)
    
    return failures


def show_test_categories():
    """显示测试分类信息"""
    print("📊 测试分类说明:")
    print()
    
    categories = {
        "功能测试": [
            "PerformanceRecordModelTest - 履职记录模型测试",
            "PerformanceAttachmentModelTest - 附件模型测试", 
            "PerformanceRecordAPITest - 履职记录API测试",
            "PerformanceAttachmentAPITest - 附件API测试",
            "StatisticsAPITest - 统计API测试",
            "ChoicesAPITest - 选择项API测试",
            "FileUploadAPITest - 文件上传API测试",
            "PermissionTest - 权限控制测试"
        ],
        "安全测试": [
            "SQLInjectionTest - SQL注入防护测试",
            "XSSPreventionTest - XSS攻击防护测试",
            "MaliciousFileUploadTest - 恶意文件上传防护测试",
            "PathTraversalTest - 路径遍历攻击防护测试", 
            "PrivilegeEscalationTest - 权限提升防护测试",
            "DataLeakageTest - 敏感数据泄露防护测试",
            "FileIntegrityTest - 文件完整性测试",
            "BoundaryValueSecurityTest - 边界值安全测试",
            "CSRFAndCORSTest - CSRF和CORS安全测试",
            "RateLimitingTest - 速率限制测试"
        ]
    }
    
    for category, tests in categories.items():
        print(f"\n{category}:")
        for test in tests:
            print(f"  • {test}")
    
    print(f"\n总计: {sum(len(tests) for tests in categories.values())} 个测试类")


if __name__ == '__main__':
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'security':
            exit(run_security_tests())
        elif command == 'all':
            exit(run_all_tests())
        elif command == 'info':
            show_test_categories()
        else:
            print("❌ 未知命令")
            print()
            print("用法:")
            print("  python run_security_tests.py security  # 只运行安全测试")
            print("  python run_security_tests.py all       # 运行所有测试") 
            print("  python run_security_tests.py info      # 显示测试分类信息")
            exit(1)
    else:
        print("🛡️  NPC系统安全测试脚本")
        print()
        print("用法:")
        print("  python run_security_tests.py security  # 只运行安全测试")
        print("  python run_security_tests.py all       # 运行所有测试")
        print("  python run_security_tests.py info      # 显示测试分类信息")
        print()
        show_test_categories() 