# AI知识库应用 (aiknowledge)

## 概述

aiknowledge应用提供AI相关服务，包括：
1. 法律政策知识库问答（已有功能）
2. 语音转文字服务（已有功能）
3. **意见建议AI生成服务（新增功能）**

## 意见建议AI生成功能

### 功能说明

该功能通过调用Dify AI平台，为人大代表提供高质量的意见建议生成服务。系统会根据代表输入的原始意见内容、分类和背景信息，生成符合规范的意见建议。

### 配置说明

#### 1. Dify应用配置

在 `services.py` 中配置新的Dify应用：

```python
# 意见建议生成应用配置
OPINION_DIFY_CONFIG = {
    'api_key': 'app-YOUR_NEW_OPINION_APP_KEY',  # 替换为实际的API密钥
    'base_url': 'https://dify.gxaigc.cn/v1',
}
```

**重要：** 需要在Dify平台创建专门的意见建议生成应用，并获取API密钥。

#### 2. 环境变量配置（可选）

可以通过环境变量配置API密钥：

```bash
export OPINION_DIFY_API_KEY=app-YOUR_NEW_OPINION_APP_KEY
```

### API接口

#### 生成意见建议

**端点：** `POST /api/v1/aiknowledge/opinion/generate/`

**权限：** 需要登录认证

**请求参数：**

```json
{
    "original_content": "关于改善社区环境的建议",
    "category": "城建环保",
    "context": "该社区存在垃圾分类不规范的问题"
}
```

**参数说明：**
- `original_content` (必填): 原始意见内容
- `category` (必填): 意见类别
- `context` (可选): 补充背景信息

**成功响应：**

```json
{
    "success": true,
    "generated_content": "经过调研发现，该社区垃圾分类不规范主要原因是...",
    "message": "意见建议生成成功"
}
```

**错误响应：**

```json
{
    "success": false,
    "error": "原始意见内容不能为空",
    "details": "详细错误信息"
}
```

### 前端集成

前端通过 `opinionAIAPI.generateSuggestion()` 方法调用：

```javascript
import { opinionAIAPI } from '@/api/modules/opinion/api'

// 调用AI生成
const result = await opinionAIAPI.generateSuggestion({
    original_content: '关于改善社区环境的建议',
    category: '城建环保',
    context: '该社区存在垃圾分类不规范的问题'
})

if (result.success) {
    console.log('生成的内容:', result.generated_content)
}
```

### 数据传输方式

**新的实现方式（当前）：**

系统不再在后端生成提示词，而是直接将用户的原始数据发送给Dify应用：

**发送给Dify的数据结构：**
```json
{
  "inputs": {
    "original_content": "用户输入的原始意见内容",
    "category": "意见分类",
    "context": "补充背景信息（可选）",
    "title": "意见标题（可选）",
    "structured_text": "结构化文本（标题-分类-内容格式，可选）"
  },
  "query": "结构化文本（如果有）或原始意见内容",
  "response_mode": "blocking",
  "user": "opinion_system"
}
```

**结构化文本格式示例：**
```
标题：关于改善社区环境卫生的建议
分类：城建环保
内容：我们社区目前存在垃圾分类不规范、清理不及时等问题，建议加强管理...
```

**优势：**
- 提示词完全由Dify应用内部管理，便于调整和优化
- 后端代码更简洁，减少业务逻辑耦合
- 支持Dify应用的高级功能，如变量替换、条件判断等
- 便于A/B测试不同的提示词策略
- **新增结构化文本支持**：可发送"标题-分类-内容"的结构化信息给AI，提高生成质量

### 超时配置

为了适应AI生成需要较长时间的特点，系统配置了较长的超时时间：

- **Vite代理超时**: 150秒（2.5分钟）- 开发环境代理服务器超时
- **前端HTTP超时**: 120秒（2分钟）- axios请求超时
- **后端HTTP超时**: 90秒（1.5分钟）- Django到Dify API超时
- **配置原则**: 代理 > 前端 > 后端，确保不会在错误的层级先超时

#### 配置文件位置

1. **Vite代理超时**: `frontend/src/api/http/config.js` 中的 `getProxyConfig()`
2. **前端HTTP超时**: `frontend/src/api/http/config.js` 中的 `AI_TIMEOUT`
3. **后端HTTP超时**: `backend/api/aiknowledge/services.py` 中的 `timeout=90`

#### 问题排查

如果还是遇到15秒超时，请检查：
1. 前端是否正确使用了 `AI_TIMEOUT` 配置
2. Vite开发服务器是否已重启
3. 浏览器开发者工具中的Network面板，查看实际请求超时时间

**~~废弃的提示词模板方式：~~**

~~系统使用以下提示词模板（已废弃）：~~

```
# 此方式已废弃，提示词现在在Dify应用内部配置
```

### 测试

运行测试用例：

```bash
cd backend
python manage.py test api.aiknowledge.tests.OpinionGenerateAPITest
```

### 部署注意事项

1. **API密钥安全**：生产环境中务必通过环境变量或安全配置管理API密钥
2. **网络访问**：确保服务器能访问Dify API地址
3. **超时设置**：根据实际网络情况调整请求超时时间
4. **日志监控**：关注AI服务的调用日志和错误率

### 与现有功能的关系

- **独立性**：新功能完全独立，不影响现有的法律知识库问答功能
- **代码复用**：复用了现有的HTTP客户端、日志记录等基础设施
- **一致性**：保持与现有API的响应格式和错误处理一致

### 扩展建议

1. **缓存机制**：对相似的请求进行缓存，提高响应速度
2. **批量生成**：支持批量生成多个意见建议
3. **模板管理**：支持自定义提示词模板
4. **质量评估**：添加生成内容的质量评估机制 