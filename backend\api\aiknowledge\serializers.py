from rest_framework import serializers


class ChatRequestSerializer(serializers.Serializer):
    """聊天请求序列化器"""
    query = serializers.CharField(max_length=2000, help_text='用户问题')
    conversation_id = serializers.CharField(required=False, help_text='对话ID，可选')
    inputs = serializers.DictField(required=False, default=dict, help_text='变量输入')
    user = serializers.CharField(required=False, help_text='用户标识')

 