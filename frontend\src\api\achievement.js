/**
 * 年度履职成果AI展示相关API接口
 */

// 请求工具函数（模拟）
const request = {
  async get(url, params) {
    console.log('GET请求:', url, params)
    return Promise.resolve({})
  },
  
  async post(url, data) {
    console.log('POST请求:', url, data)
    return Promise.resolve({})
  }
}

export const achievementAPI = {
  /**
   * 检查指定年度是否有AI分析数据
   * @param {Object} params 查询参数
   * @param {number} params.year 年度
   * @returns {Promise<boolean>} 是否有分析数据
   */
  async checkAnalysisData(params) {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 模拟检查逻辑：当前年度和前一年有数据，其他年度50%概率有数据
    const currentYear = new Date().getFullYear()
    if (params.year === currentYear || params.year === currentYear - 1) {
      return true
    }
    
    return Math.random() > 0.5
  },

  /**
   * 生成年度履职成果AI展示
   * @param {Object} params 生成参数
   * @param {number} params.year 年度
   * @param {string} params.userId 用户ID
   * @returns {Promise} AI成果展示结果
   */
  async generateAchievements(params) {
    // 模拟AI生成延迟
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000))
    
    // 模拟随机失败情况（5%概率）
    if (Math.random() < 0.05) {
      const errorTypes = [
        { code: 'AI_ACHIEVEMENT_SERVICE_UNAVAILABLE', message: 'AI成果展示服务暂不可用' },
        { code: 'AI_ACHIEVEMENT_TIMEOUT', message: 'AI成果展示生成超时' },
        { code: 'AI_ACHIEVEMENT_INVALID', message: 'AI返回无效的成果展示' }
      ]
      const error = errorTypes[Math.floor(Math.random() * errorTypes.length)]
      throw error
    }
    
    // 模拟AI生成的成果展示数据
    const mockAchievements = {
      // 概览信息
      overview: {
        subtitle: `履职年度总结：${Math.floor(50 + Math.random() * 30)}项重要成就，${Math.floor(200 + Math.random() * 100)}天积极履职`
      },
      
      // 核心成就指标
      coreMetrics: [
        {
          label: '履职活动',
          value: Math.floor(45 + Math.random() * 20) + '次',
          icon: 'Trophy',
          color: '#c62d2d',
          trend: (Math.random() > 0.5 ? '+' : '') + (Math.random() * 20 - 5).toFixed(1) + '%',
          trendType: Math.random() > 0.3 ? 'positive' : 'negative'
        },
        {
          label: '建议提案',
          value: Math.floor(12 + Math.random() * 8) + '份',
          icon: 'DataLine',
          color: '#52c41a',
          trend: (Math.random() > 0.5 ? '+' : '') + (Math.random() * 30 - 10).toFixed(1) + '%',
          trendType: Math.random() > 0.4 ? 'positive' : 'negative'
        },
        {
          label: '采纳率',
          value: (75 + Math.random() * 20).toFixed(1) + '%',
          icon: 'Star',
          color: '#fa8c16',
          trend: (Math.random() > 0.5 ? '+' : '') + (Math.random() * 15 - 5).toFixed(1) + '%',
          trendType: Math.random() > 0.6 ? 'positive' : 'negative'
        },
        {
          label: '投入时间',
          value: Math.floor(180 + Math.random() * 80) + '天',
          icon: 'Timer',
          color: '#1890ff',
          trend: (Math.random() > 0.5 ? '+' : '') + (Math.random() * 25 - 10).toFixed(1) + '%',
          trendType: Math.random() > 0.5 ? 'positive' : 'negative'
        }
      ],
      
      // 活动类型分布
      activityDistribution: [
        {
          type: '会议参与',
          count: Math.floor(15 + Math.random() * 15),
          percentage: Math.floor(25 + Math.random() * 15),
          color: '#c62d2d'
        },
        {
          type: '建议提交',
          count: Math.floor(8 + Math.random() * 10),
          percentage: Math.floor(15 + Math.random() * 10),
          color: '#52c41a'
        },
        {
          type: '调研活动',
          count: Math.floor(10 + Math.random() * 12),
          percentage: Math.floor(20 + Math.random() * 10),
          color: '#fa8c16'
        },
        {
          type: '联系群众',
          count: Math.floor(12 + Math.random() * 15),
          percentage: Math.floor(25 + Math.random() * 15),
          color: '#1890ff'
        },
        {
          type: '其他履职',
          count: Math.floor(5 + Math.random() * 8),
          percentage: Math.floor(10 + Math.random() * 10),
          color: '#722ed1'
        }
      ],
      
      // 时间投入分析
      timeInvestment: {
        total: Math.floor(180 + Math.random() * 80),
        monthly: Math.floor(15 + Math.random() * 10),
        activity: Math.floor(85 + Math.random() * 10),
        monthlyData: Array.from({ length: 12 }, (_, i) => ({
          month: i + 1,
          percentage: Math.floor(50 + Math.random() * 50)
        }))
      },
      
      // 履职亮点
      highlights: [
        {
          title: '重点民生提案',
          description: `围绕教育、医疗、住房等民生热点，提交高质量提案${Math.floor(5 + Math.random() * 3)}份，其中${Math.floor(3 + Math.random() * 2)}份被政府部门采纳实施。`,
          metrics: [
            { label: '提案数量', value: Math.floor(5 + Math.random() * 3) + '份' },
            { label: '采纳率', value: (80 + Math.random() * 15).toFixed(1) + '%' }
          ]
        },
        {
          title: '基层调研深入',
          description: `深入社区、企业、学校等基层一线，开展实地调研${Math.floor(12 + Math.random() * 8)}次，收集第一手资料，为履职提供扎实基础。`,
          metrics: [
            { label: '调研次数', value: Math.floor(12 + Math.random() * 8) + '次' },
            { label: '覆盖区域', value: Math.floor(8 + Math.random() * 5) + '个' }
          ]
        },
        {
          title: '监督执法有力',
          description: `参与人大执法检查和专题询问${Math.floor(6 + Math.random() * 4)}次，推动相关部门解决突出问题，维护法律权威。`,
          metrics: [
            { label: '参与次数', value: Math.floor(6 + Math.random() * 4) + '次' },
            { label: '问题推动', value: Math.floor(8 + Math.random() * 5) + '个' }
          ]
        },
        {
          title: '代表联络积极',
          description: `通过代表接待日、走访群众等方式，广泛联系选民${Math.floor(200 + Math.random() * 100)}人次，及时回应群众关切。`,
          metrics: [
            { label: '联系选民', value: Math.floor(200 + Math.random() * 100) + '人次' },
            { label: '办理实事', value: Math.floor(15 + Math.random() * 10) + '件' }
          ]
        }
      ],
      
      // 关键词云
      keywords: [
        { word: '民生保障', weight: 0.95 },
        { word: '教育公平', weight: 0.88 },
        { word: '医疗改革', weight: 0.82 },
        { word: '环境治理', weight: 0.78 },
        { word: '经济发展', weight: 0.75 },
        { word: '乡村振兴', weight: 0.72 },
        { word: '科技创新', weight: 0.68 },
        { word: '法治建设', weight: 0.65 },
        { word: '文化建设', weight: 0.62 },
        { word: '社会治理', weight: 0.58 },
        { word: '数字化转型', weight: 0.55 },
        { word: '绿色发展', weight: 0.52 },
        { word: '民主监督', weight: 0.48 },
        { word: '基础设施', weight: 0.45 },
        { word: '人才引进', weight: 0.42 }
      ],
      
      // AI智能总结
      aiSummary: {
        evaluation: `${params.year}年度，您在人大代表履职方面表现卓越，充分体现了新时代人大代表的使命担当。您始终坚持以人民为中心，积极参与各类履职活动，认真履行代表职责，在推动经济社会发展、维护人民群众利益、促进社会和谐稳定等方面发挥了重要作用。特别是在民生保障、教育公平、环境治理等重点领域贡献突出，得到了人民群众的广泛认可。`,
        
        achievements: [
          `积极参与人大会议${Math.floor(18 + Math.random() * 10)}次，出席率达到${(95 + Math.random() * 5).toFixed(1)}%，认真审议各项议案`,
          `提交高质量建议提案${Math.floor(12 + Math.random() * 8)}份，其中${Math.floor(8 + Math.random() * 5)}份被相关部门采纳`,
          `深入基层调研${Math.floor(15 + Math.random() * 10)}次，足迹遍及城乡，了解民情民意`,
          `通过代表接待日等方式联系群众${Math.floor(300 + Math.random() * 200)}人次，及时回应群众关切`,
          `参与执法检查和专题询问${Math.floor(8 + Math.random() * 5)}次，推动法律法规贯彻实施`
        ],
        
        suggestions: [
          '继续加强对新兴产业发展的关注，提升相关建议的前瞻性和指导性',
          '进一步深化与其他代表的协作，形成更有影响力的联名建议',
          '探索运用新媒体等现代化手段，扩大履职影响力和覆盖面',
          '加强对国际先进经验的学习借鉴，提升履职的国际视野',
          '建议建立履职效果跟踪评估机制，确保建议落地见效'
        ]
      }
    }
    
    return mockAchievements
  },

  /**
   * 获取历史成果展示记录
   * @param {Object} params 查询参数
   * @param {number} params.year 年度
   * @param {string} params.userId 用户ID
   * @returns {Promise} 历史记录
   */
  async getHistoryAchievements(params) {
    // 模拟获取历史记录
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟30%概率没有历史记录
    if (Math.random() < 0.3) {
      return null
    }
    
    return {
      year: params.year,
      userId: params.userId,
      generatedAt: new Date().toISOString(),
      // 其他历史数据...
    }
  },

  /**
   * 导出成果展示为PDF
   * @param {Object} params 导出参数
   * @param {number} params.year 年度
   * @param {string} params.userId 用户ID
   * @returns {Promise} 导出结果
   */
  async exportAchievementsPDF(params) {
    // 模拟导出处理
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    return {
      success: true,
      downloadUrl: '/api/download/achievements_' + params.year + '_' + Date.now() + '.pdf',
      message: 'PDF导出成功'
    }
  }
}

export default achievementAPI 