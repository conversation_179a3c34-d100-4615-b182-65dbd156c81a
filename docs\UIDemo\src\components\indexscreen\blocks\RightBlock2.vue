<template>
  <div class="right-block-2">
    <!-- 标题区域 -->
    <div class="chart-header">
      <div class="header-left">
        <!-- <div class="chart-icon">📈</div> -->
        <div class="chart-title">{{ chartTitle }}</div>
      </div>
      <div class="chart-subtitle">{{ chartSubtitle }}</div>
    </div>
    
    <!-- 图表容器 -->
    <div class="chart-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">数据加载中...</div>
      </div>
      
      <!-- 图表内容 -->
      <div v-else ref="chartRef" class="chart-content"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import { getYearlyDutyStatistics } from '@/api/indexscreen/dutyStatistics.js'

// 响应式数据
const chartTitle = ref('履职统计')
const chartRef = ref(null)
const loading = ref(false)
const dutyData = ref({})
const totalCount = ref(0)
let chartInstance = null

// 计算属性：动态显示总履职数
const chartSubtitle = computed(() => {
  return `总履职数：${totalCount.value}`
})

// 获取图表数据
const fetchChartData = async () => {
  try {
    loading.value = true
    const response = await getYearlyDutyStatistics()
    
    if (response.code === 200) {
      dutyData.value = response.data || {}
      totalCount.value = response.totalCount || 0
      console.log('📈 年度履职统计数据加载成功:', dutyData.value)
    } else {
      console.error('❌ 获取数据失败:', response.message)
      dutyData.value = {} // 确保数据有默认值
    }
  } catch (error) {
    console.error('❌ 数据请求异常:', error)
    dutyData.value = {} // 确保数据有默认值
  } finally {
    loading.value = false
  }
}

// 初始化折线图
const initChart = async () => {
  // 等待DOM更新完成
  await nextTick()
  
  // 确保DOM元素存在且数据已加载
  if (!chartRef.value) {
    console.warn('图表初始化失败: DOM元素未准备好')
    return false
  }
  
  if (Object.keys(dutyData.value).length === 0) {
    console.warn('图表初始化失败: 数据未加载')
    return false
  }
  
  try {
    chartInstance = echarts.init(chartRef.value)
  
    // 提取月份和数据
    const months = Object.keys(dutyData.value)
    const values = Object.values(dutyData.value)
    
    const option = {
      backgroundColor: 'transparent',
      grid: {
        top: '15%',
        left: '5%', /* 减少左边距，比如改为 '5%' */
        right: '5%', /* 减少右边距，比如改为 '5%' ，修改折现图左右两点距离 */ 
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: months,
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10,
          interval: 0,
          rotate: 0,
          margin: 8
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.6)',
          fontSize: 10
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          type: 'line',
          data: values,
          smooth: true, // 平滑曲线
          symbol: 'circle',
          symbolSize: 6,
                  lineStyle: {
          color: 'rgba(2, 166, 181, 0.8)',
          width: 3,
          shadowColor: 'rgba(2, 166, 181, 0.3)',
          shadowBlur: 6,
          shadowOffsetY: 2
        },
        itemStyle: {
          color: 'rgba(2, 166, 181, 0.8)',
          borderColor: '#ffffff',
          borderWidth: 2,
          shadowColor: 'rgba(2, 166, 181, 0.5)',
          shadowBlur: 6,
          shadowOffsetY: 2
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(2, 166, 181, 0.2)' },
            { offset: 1, color: 'rgba(2, 166, 181, 0.03)' }
          ])
        },
                  emphasis: {
          itemStyle: {
            color: 'rgba(2, 166, 181, 0.9)',
            borderColor: '#ffffff',
            borderWidth: 3,
            shadowBlur: 12,
            scale: 1.2
          }
        },
          label: {
            show: true,
            position: 'top',
            color: '#ffffff',
            fontSize: 9,
            fontWeight: 'bold',
            distance: 8,
            formatter: '{c}'
          },
          animationDelay: (idx) => idx * 100,
          animationDuration: 1500,
          animationEasing: 'elasticOut'
        }
      ],
      tooltip: {
              trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'rgba(2, 166, 181, 0.3)',
      borderWidth: 1,
        textStyle: {
          color: '#ffffff',
          fontSize: 12
        },
        formatter: function(params) {
          const data = params[0]
                  return `
          <div style="padding: 8px;">
            <div style="color: rgba(2, 166, 181, 0.9); font-weight: bold; margin-bottom: 4px;">
              ${data.name}
            </div>
            <div>
              履职数量: <span style="color: #49bcf7; font-weight: bold;">${data.value}</span>
            </div>
          </div>
        `
        }
      }
    }
    
    chartInstance.setOption(option)
    
    // 添加点击事件
    chartInstance.on('click', (params) => {
      console.log('点击了月份:', params.name, '履职数量:', params.value)
    })
    
    return true
  } catch (error) {
    console.error('图表初始化错误:', error)
    return false
  }
}

// 响应式调整
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 组件挂载
onMounted(async () => {
  try {
    // 先获取数据
    await fetchChartData()
    // 再初始化图表，确保数据和DOM都准备好
    await initChart()
    window.addEventListener('resize', resizeChart)
  } catch (error) {
    console.error('组件初始化失败:', error)
  }
})

// 组件卸载
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
/* 主容器 - 使用flex布局 */
.right-block-2 {
  /* background: rgba(60, 24, 24, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(220, 20, 60, 0.15); */
  border-radius: 12px;
  padding: 16px;
  color: #ffffff;
  transition: all 0.3s ease;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  min-height: 0; /* 确保flex子项可以收缩 */
}

.right-block-2:hover {
  background: rgba(0, 20, 40, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 13, 74, 0.2);
  border-color: rgba(2, 166, 181, 0.3);
}

/* 图表头部 - flex布局 */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  position: relative;
  flex-shrink: 0; /* 防止头部被压缩 */
}

/* 美观的分割线设计 */
.chart-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(73, 188, 247, 0.3) 10%,
    rgba(73, 188, 247, 0.8) 30%,
    rgba(73, 188, 247, 1) 50%,
    rgba(73, 188, 247, 0.8) 70%,
    rgba(73, 188, 247, 0.3) 90%,
    transparent 100%
  );
  border-radius: 2px;
  box-shadow: 
    0 0 8px rgba(73, 188, 247, 0.4),
    0 2px 4px rgba(73, 188, 247, 0.2);
  animation: glow-pulse 3s ease-in-out infinite alternate;
}

/* 添加一个细的上层装饰线 */
.chart-header::before {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  border-radius: 1px;
  animation: shimmer 2s ease-in-out infinite alternate;
}

/* 分割线发光动画 */
@keyframes glow-pulse {
  0% {
    box-shadow: 
      0 0 8px rgba(73, 188, 247, 0.4),
      0 2px 4px rgba(73, 188, 247, 0.2);
  }
  100% {
    box-shadow: 
      0 0 12px rgba(73, 188, 247, 0.6),
      0 2px 6px rgba(73, 188, 247, 0.3);
  }
}

/* 上层装饰线闪烁动画 */
@keyframes shimmer {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-icon {
  font-size: 1.2rem;
  filter: drop-shadow(0 0 4px rgba(73, 188, 247, 0.8));
}

.chart-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.chart-subtitle {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  /* background: rgba(220, 20, 60, 0.2); */
  padding: 2px 8px;
  border-radius: 10px;
  /* border: 1px solid rgba(255, 215, 0, 0.3); */
  border: 1px solid #49bcf7;
}

/* 图表容器 - flex自适应 */
.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许容器收缩 */
  position: relative;
}

.chart-content {
  flex: 1;
  min-height: 120px; /* 最小高度确保图表可见 */
  width: 100%;
  position: relative;
}

/* 加载状态样式 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  min-height: 120px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(2, 166, 181, 0.2);
  border-top: 3px solid rgba(2, 166, 181, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .right-block-2 {
    padding: 12px;
  }
  
  .chart-title {
    font-size: 0.9rem;
  }
  
  .chart-subtitle {
    font-size: 0.7rem;
    padding: 1px 6px;
  }
  
  .chart-content {
    min-height: 100px;
  }
}

@media (max-width: 768px) {
  .right-block-2 {
    padding: 10px;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    margin-bottom: 8px;
  }
  
  .header-left {
    gap: 6px;
  }
  
  .chart-title {
    font-size: 0.85rem;
  }
  
  .chart-subtitle {
    font-size: 0.65rem;
    align-self: flex-end;
  }
  
  .chart-content {
    min-height: 80px;
  }
}

/* 加载动画效果 - 党建红主题 */
.right-block-2::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(2, 166, 181, 0.15),
    transparent
  );
  transition: left 0.5s ease;
}

.right-block-2:hover::after {
  left: 100%;
}

/* 确保图表在容器变化时正确响应 */
.chart-content > div {
  width: 100% !important;
  height: 100% !important;
}
</style> 