/**
 * 履职统计相关API
 * 用于获取代表履职统计数据，包括每月履职数量等
 */

import { mockRequest } from './request.js'

/**
 * 获取年度履职统计数据 (折线图)
 * @returns {Promise} 返回包含各月份履职数据的Promise
 */
export const getYearlyDutyStatistics = () => {
  // 模拟年度履职统计数据 - 1月到12月
  const mockData = {
    '1月': 156,
    '2月': 143,
    '3月': 189,
    '4月': 167,
    '5月': 203,
    '6月': 178,
    '7月': 195,
    '8月': 224,
    '9月': 186,
    '10月': 234,
    '11月': 198,
    '12月': 245
  }
  
  return mockRequest('/api/duty/yearly-statistics', 'GET', null, {
    code: 200,
    message: '获取年度履职统计成功',
    data: mockData,
    totalCount: Object.values(mockData).reduce((sum, count) => sum + count, 0)
  })
}

/**
 * 获取月度履职明细数据
 * @param {number} month - 月份 (1-12)
 * @returns {Promise} 返回指定月份的履职明细数据
 */
export const getMonthlyDutyDetails = (month) => {
  // 模拟月度履职明细数据
  const mockDetails = {
    议案提案: Math.floor(Math.random() * 50) + 20,
    调研视察: Math.floor(Math.random() * 40) + 15,
    联系选民: Math.floor(Math.random() * 60) + 30,
    监督检查: Math.floor(Math.random() * 35) + 10,
    学习培训: Math.floor(Math.random() * 45) + 25
  }
  
  return mockRequest(`/api/duty/monthly-details/${month}`, 'GET', null, {
    code: 200,
    message: `获取${month}月履职明细成功`,
    data: mockDetails,
    month: month,
    totalCount: Object.values(mockDetails).reduce((sum, count) => sum + count, 0)
  })
} 