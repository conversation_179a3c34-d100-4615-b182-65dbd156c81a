<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>人大代表履职服务与管理平台</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>

    <!-- 自定义 AI 助手按钮 -->
    <div id="custom-ai-assistant" style="
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #1C64F2, #3B82F6);
      border-radius: 50%;
      cursor: pointer;
      box-shadow: 0 4px 20px rgba(28, 100, 242, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: all 0.3s ease;
      border: 2px solid rgba(255, 255, 255, 0.2);
    " onclick="openAIAssistant()" title="AI智能助手">
      <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        <path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2z"/>
      </svg>
    </div>

    <script>
      function openAIAssistant() {
        // 在新窗口中打开 Dify 聊天机器人
        const chatWindow = window.open(
          'https://dify.gxaigc.cn/chatbot/BcwlrHx1D4bYfJ2h',
          'ai-assistant',
          'width=400,height=600,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no'
        );

        if (chatWindow) {
          chatWindow.focus();
        } else {
          // 如果弹窗被阻止，提示用户
          alert('请允许弹窗以使用AI助手，或者手动访问：https://dify.gxaigc.cn/chatbot/BcwlrHx1D4bYfJ2h');
        }
      }

      // 添加悬浮效果
      document.addEventListener('DOMContentLoaded', function() {
        const button = document.getElementById('custom-ai-assistant');
        if (button) {
          button.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1)';
            this.style.boxShadow = '0 6px 25px rgba(28, 100, 242, 0.4)';
          });

          button.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.boxShadow = '0 4px 20px rgba(28, 100, 242, 0.3)';
          });
        }
      });
    </script>
  </body>
</html> 