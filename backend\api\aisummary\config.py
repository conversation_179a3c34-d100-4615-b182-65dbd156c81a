"""
AI总结模块配置文件

包含AI服务提供商的配置信息
"""

import os
from django.conf import settings

# Dify AI总结应用配置
AI_SUMMARY_DIFY_CONFIG = {
    # 从环境变量获取API密钥，如果没有则使用默认值
    'api_key': os.getenv('AI_SUMMARY_DIFY_API_KEY', 'app-khXs9VonOa46O2IZhY2cAmrt'),
    'base_url': os.getenv('AI_SUMMARY_DIFY_BASE_URL', 'https://dify.gxaigc.cn/v1'),
    'timeout': int(os.getenv('AI_SUMMARY_TIMEOUT', '120')),  # 默认2分钟超时
    'max_retries': int(os.getenv('AI_SUMMARY_MAX_RETRIES', '3')),  # 最大重试次数
}

# AI总结应用的提示词模板（在Dify应用中配置）
AI_SUMMARY_PROMPT_TEMPLATE = """
基于代表的履职数据，生成年度AI分析报告。

输入变量：
- representative_name: 代表姓名
- representative_level: 代表层级
- analysis_year: 分析年份
- performance_total: 履职记录总数
- opinion_total: 意见建议总数
- total_activities: 总活动数
- performance_types: 履职类型统计（JSON）
- performance_status: 履职状态统计（JSON）
- performance_monthly: 履职月度统计（JSON）
- opinion_categories: 意见分类统计（JSON）
- opinion_monthly: 意见月度统计（JSON）
- ai_assisted_rate: AI辅助使用率
- performance_details: 履职记录详情（JSON）
- opinion_details: 意见建议详情（JSON）

请返回JSON格式的分析报告，包含：
1. overview: 概览信息
2. coreMetrics: 核心指标数组
3. activityDistribution: 活动类型分布
4. timeInvestment: 时间投入分析
5. highlights: 履职亮点
6. keywords: 关键词云
7. aiSummary: AI智能总结
"""

# 配置验证
def validate_ai_config():
    """验证AI配置是否正确"""
    errors = []
    
    if not AI_SUMMARY_DIFY_CONFIG['api_key'] or AI_SUMMARY_DIFY_CONFIG['api_key'] == 'app-AISummaryAppKey123456':
        errors.append("AI_SUMMARY_DIFY_API_KEY 环境变量未设置或使用默认值")
    
    if not AI_SUMMARY_DIFY_CONFIG['base_url']:
        errors.append("AI_SUMMARY_DIFY_BASE_URL 环境变量未设置")
    
    return errors

# 获取AI配置
def get_ai_config():
    """获取AI配置信息"""
    return AI_SUMMARY_DIFY_CONFIG.copy()

# 是否使用真实AI服务
def should_use_real_ai():
    """判断是否应该使用真实AI服务"""
    # 如果是生产环境且配置了真实的API密钥
    return (
        not settings.DEBUG or 
        (AI_SUMMARY_DIFY_CONFIG['api_key'] != 'app-AISummaryAppKey123456')
    ) 