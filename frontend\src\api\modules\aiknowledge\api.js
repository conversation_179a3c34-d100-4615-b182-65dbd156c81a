/**
 * AI知识库API请求方法
 * 包含AI聊天、语音转文字、意见建议生成等接口
 */
import httpClient from '../../http/client'
import { AIKNOWLEDGE_ENDPOINTS, API_CONFIG } from '../../http/config'

/**
 * AI知识库API
 */
export const aiKnowledgeAPI = {
  /**
   * AI聊天SSE流式接口
   * @param {Object} chatData 聊天数据
   * @param {string} chatData.query 用户问题
   * @param {string} chatData.user 用户ID（可选）
   * @param {string} chatData.conversation_id 对话ID（可选）
   * @param {Object} chatData.inputs 变量输入（可选）
   * @param {Function} onMessage 消息回调函数
   * @param {Function} onError 错误回调函数
   * @param {Function} onComplete 完成回调函数
   * @returns {Promise} SSE流响应
   */
  async chatSSE(chatData, onMessage, onError, onComplete) {
    try {
      const token = localStorage.getItem('access_token')
      const baseURL = API_CONFIG.BASE_URL
      const url = `${baseURL}${AIKNOWLEDGE_ENDPOINTS.CHAT_SSE}`
      
      // 创建EventSource连接
      const eventSource = new EventSource(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })
      
      // 但是EventSource不支持POST，所以我们需要使用fetch
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify(chatData)
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          if (onComplete) onComplete()
          break
        }
        
        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim()
            if (data && data !== '[DONE]') {
              try {
                const parsedData = JSON.parse(data)
                if (onMessage) onMessage(parsedData)
              } catch (e) {
                console.warn('Failed to parse SSE data:', data)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('SSE connection error:', error)
      if (onError) onError(error)
    }
  },

  /**
   * 语音转文字
   * @param {FormData} audioFormData 包含音频文件的FormData
   * @returns {Promise} 转换结果
   */
  audioToText(audioFormData) {
    return httpClient.post(AIKNOWLEDGE_ENDPOINTS.AUDIO_TO_TEXT, audioFormData, {
      showLoading: false // 禁用全局loading，使用组件内的loading状态
    })
    // 注意：不要手动设置Content-Type，让浏览器自动设置multipart/form-data的boundary
  },

  /**
   * AI生成意见建议
   * @param {Object} aiData AI生成请求数据
   * @param {string} aiData.original_content 原始意见内容
   * @param {string} aiData.category 意见分类
   * @param {string} aiData.context 补充背景信息（可选）
   * @returns {Promise} AI生成结果
   */
  generateOpinionSuggestion(aiData) {
    return httpClient.post(AIKNOWLEDGE_ENDPOINTS.OPINION_GENERATE, aiData, {
      timeout: API_CONFIG.AI_TIMEOUT, // 使用AI专用的更长超时时间
      showLoading: false // 在组件中已有loading状态，避免重复显示
    })
  },

  /**
   * 语音转意见建议（语音转文字 + AI内容解析）
   * @param {FormData} audioFormData 包含音频文件的FormData
   * @param {string} category 意见建议分类（可选）
   * @returns {Promise} 解析结果，包含title和content
   */
  voiceToOpinion(audioFormData, category = '') {
    // 如果提供了分类，添加到FormData中
    if (category && category.trim()) {
      audioFormData.append('category', category)
    }

    return httpClient.post(AIKNOWLEDGE_ENDPOINTS.VOICE_TO_OPINION, audioFormData, {
      timeout: API_CONFIG.AI_TIMEOUT, // 使用AI专用的更长超时时间
      showLoading: false // 禁用全局loading，使用组件内的loading状态
    })
    // 注意：不要手动设置Content-Type，让浏览器自动设置multipart/form-data的boundary
  },

  /**
   * AI生成意见建议SSE流式接口
   * @param {Object} opinionData 意见建议数据
   * @param {string} opinionData.original_content 原始意见内容
   * @param {string} opinionData.category 意见分类（可选）
   * @param {string} opinionData.context 补充背景信息（可选）
   * @param {Function} onMessage 消息回调函数
   * @param {Function} onError 错误回调函数
   * @param {Function} onComplete 完成回调函数
   * @returns {Promise} SSE流响应
   */
  async generateOpinionSSE(opinionData, onMessage, onError, onComplete) {
    try {
      const token = localStorage.getItem('access_token')
      const baseURL = API_CONFIG.BASE_URL
      const url = `${baseURL}${AIKNOWLEDGE_ENDPOINTS.OPINION_GENERATE_SSE}`

      console.log('🚀 开始SSE请求:', { url, opinionData })

      // 发送POST请求启动SSE流
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(opinionData)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      console.log('✅ SSE连接建立成功')

      // 处理SSE流
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = '' // 用于处理不完整的数据

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            console.log('📡 SSE流结束')
            break
          }

          // 解码数据并添加到缓冲区
          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk

          // 按行分割处理
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // 保留最后一行（可能不完整）

          for (const line of lines) {
            if (line.trim() === '') continue

            if (line.startsWith('data: ')) {
              try {
                const jsonStr = line.slice(6).trim()
                if (jsonStr === '[DONE]') {
                  console.log('🏁 收到结束标记')
                  continue
                }

                const data = JSON.parse(jsonStr)
                console.log('📨 收到SSE数据:', data)

                if (onMessage) {
                  onMessage(data)
                }
              } catch (e) {
                console.warn('⚠️ 解析SSE数据失败:', e, 'Line:', line)
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
        console.log('🔚 SSE流处理完成')
        if (onComplete) onComplete()
      }

    } catch (error) {
      console.error('❌ 意见建议SSE流错误:', error)
      if (onError) onError(error)
    }
  }
}

/**
 * AI知识库工具函数
 */
export const aiKnowledgeUtils = {
  /**
   * 验证音频文件格式
   * @param {File} file 音频文件
   * @returns {Object} 验证结果 { valid: boolean, error?: string }
   */
  validateAudioFile(file) {
    const allowedTypes = [
      'audio/mp3', 'audio/mpeg', 'audio/mp4', 'audio/mpga', 
      'audio/m4a', 'audio/wav', 'audio/webm'
    ]
    
    const maxSize = 50 * 1024 * 1024 // 50MB
    
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `不支持的文件类型: ${file.type}，支持的格式: mp3, mp4, mpeg, mpga, m4a, wav, webm`
      }
    }
    
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `文件过大，最大支持50MB，当前文件大小: ${(file.size / 1024 / 1024).toFixed(2)}MB`
      }
    }
    
    return { valid: true }
  },

  /**
   * 创建音频FormData
   * @param {File} audioFile 音频文件
   * @returns {FormData} 格式化的FormData
   */
  createAudioFormData(audioFile) {
    const formData = new FormData()
    formData.append('file', audioFile)
    return formData
  },

  /**
   * 验证意见建议生成参数
   * @param {Object} params 生成参数
   * @returns {Object} 验证结果 { valid: boolean, errors: Array }
   */
  validateOpinionParams(params) {
    const errors = []

    if (!params.original_content || params.original_content.trim().length < 10) {
      errors.push('原始意见内容长度不能少于10个字符')
    }

    if (params.original_content && params.original_content.trim().length > 1000) {
      errors.push('原始意见内容长度不能超过1000个字符')
    }

    if (!params.category) {
      errors.push('请选择意见建议分类')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
}

// 导出默认对象
export default {
  aiKnowledgeAPI,
  aiKnowledgeUtils
} 