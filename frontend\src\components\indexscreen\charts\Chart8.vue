<template>
  <div ref="myChart" style="width: 100%; height: 90%"></div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    this.initChart()
  },
  watch: {
    data: {
      handler() {
        this.initChart()
      },
      deep: true
    }
  },
  methods: {
    // 计算Y轴的合适范围
    calculateYAxisRange() {
      if (!this.data || this.data.length === 0) {
        return { min: 0, max: 100 }
      }

      const values = this.data.map(item => item.value || 0)
      const maxValue = Math.max(...values)
      const minValue = Math.min(...values)

      // 如果最大值为0或很小，设置一个合理的默认范围
      if (maxValue <= 0) {
        return { min: 0, max: 100 }
      }

      // 计算合适的Y轴范围，给数据留出一些空间
      const range = maxValue - minValue
      const padding = Math.max(range * 0.1, maxValue * 0.1) // 至少10%的padding

      let yMin = Math.max(0, minValue - padding)
      let yMax = maxValue + padding

      // 如果数据范围很小，确保有足够的视觉差异
      if (yMax - yMin < maxValue * 0.5) {
        yMax = maxValue * 1.2
        yMin = Math.max(0, maxValue * 0.1)
      }

      // 将数值调整为整数，便于显示
      yMin = Math.floor(yMin)
      yMax = Math.ceil(yMax)

      return { min: yMin, max: yMax }
    },

    async initChart() {
      const myChart = this.$echarts.init(this.$refs.myChart)
      const yAxisRange = this.calculateYAxisRange()

      const option = {
        xAxis: {
          type: 'category',
          name: '',
          axisLabel: {
            color: '#ffffff', // 使用白色字体
            fontSize: 10,
          },
          data: this.data.map((item) => item.month),
        },
        yAxis: {
          type: 'value',
          min: yAxisRange.min,
          max: yAxisRange.max,
          axisLine: {
            show: 'true',
          },
          axisLabel: {
            color: '#ffffff', // 使用白色字体
            fontSize: 10,
          },
          splitNumber: 5,
          splitLine: {
            lineStyle: {
              color: '#0c4787',
            },
          },
        },
        grid: {
          top: 25,
          bottom: 20,
          borderWidth: 1,
        },
        legend: {
          data: ['记录数（条）'],
          textStyle: {
            color: '#fff',
            fontSize: 11,
          },
          itemWidth: 20,
          itemHeight: 13,
          top: '0%',
          right: 20,
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          textStyle: {
            color: '#34495e',
          },
          axisPointer: {
            type: 'shadow',
          },
        },
        series: [
          {
            type: 'line',
            name: '记录数（条）',
            data: this.data.map((item) => item.value),
            smooth: true,
            lineStyle: {
              color: '#5effff',
            },
            itemStyle: {
              color: '#5effff',
            },
            areaStyle: {
              color: 'rgba(94, 255, 255, 0.2)',
            },
            zlevel: 5,
          },
        ],
      }
      await myChart.setOption(option)
    },
  },
}
</script>

<style scoped></style>
