import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { mockLogin } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value.id)
  const isRepresentative = computed(() => userInfo.value.role === 'representative')
  const isStaff = computed(() => userInfo.value.role === 'staff')
  const userName = computed(() => userInfo.value.name || '用户')
  const roleText = computed(() => {
    const roleMap = {
      'representative': '人大代表',
      'staff': '站点工作人员'
    }
    return roleMap[userInfo.value.role] || '未知角色'
  })
  
  // 登录方法
  const login = async (loginForm) => {
    try {
      const response = await mockLogin(loginForm)
      
      if (response.success) {
        // 保存用户信息和token
        token.value = response.data.token
        userInfo.value = response.data.userInfo
        
        // 持久化存储
        localStorage.setItem('token', token.value)
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
        
        ElMessage.success('登录成功')
        return { success: true, userInfo: userInfo.value }
      } else {
        ElMessage.error(response.message || '登录失败')
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('登录错误:', error)
      ElMessage.error('登录异常，请重试')
      return { success: false, message: '登录异常' }
    }
  }
  
  // 登出方法
  const logout = () => {
    token.value = ''
    userInfo.value = {}
    
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    
    ElMessage.success('已安全退出')
  }
  
  // 更新用户信息
  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
    localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
  }
  
  // 检查权限
  const hasPermission = (requiredRole) => {
    if (!requiredRole) return true
    return userInfo.value.role === requiredRole
  }
  
  return {
    // 状态
    token,
    userInfo,
    
    // 计算属性
    isLoggedIn,
    isRepresentative,
    isStaff,
    userName,
    roleText,
    
    // 方法
    login,
    logout,
    updateUserInfo,
    hasPermission
  }
}) 