<template>
  <el-dialog
    v-model="visible"
    title="📋 工作计划提醒"
    width="600px"
    center
    @close="handleClose"
  >
    <div v-if="loading" style="text-align: center; padding: 20px;">
      <el-icon><Loading /></el-icon>
      <p>正在检查您的工作计划...</p>
    </div>

    <div v-else style="padding: 20px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <el-icon size="32" color="#409eff"><Bell /></el-icon>
        <h3 style="margin: 10px 0;">您好！欢迎回来 👋</h3>
        
        <p v-if="totalCount > 0" style="color: #e6a23c; font-size: 16px;">
          您有 <strong>{{ totalCount }}</strong> 个工作计划需要关注
        </p>
        <p v-else style="color: #67c23a; font-size: 16px;">
          🎉 太棒了！您目前没有需要紧急处理的工作计划
        </p>
      </div>

      <!-- 已逾期计划 -->
      <div v-if="overduePlans.length > 0" style="margin-bottom: 15px;">
        <el-tag type="danger" size="large" style="margin-bottom: 10px;">
          ⚠️ 已逾期 ({{ overduePlans.length }})
        </el-tag>
        <div v-for="plan in overduePlans" :key="plan.id" 
             style="padding: 10px; background: #fef0f0; border-radius: 6px; margin-bottom: 8px;">
          <div style="font-weight: 600;">{{ plan.title }}</div>
          <div style="font-size: 13px; color: #f56c6c;">
            已逾期 {{ Math.abs(plan.days_until_end) }} 天
          </div>
        </div>
      </div>

      <!-- 即将到期计划 -->
      <div v-if="upcomingPlans.length > 0" style="margin-bottom: 15px;">
        <el-tag type="warning" size="large" style="margin-bottom: 10px;">
          ⏰ 即将到期 ({{ upcomingPlans.length }})
        </el-tag>
        <div v-for="plan in upcomingPlans" :key="plan.id" 
             style="padding: 10px; background: #fdf6ec; border-radius: 6px; margin-bottom: 8px;">
          <div style="font-weight: 600;">{{ plan.title }}</div>
          <div style="font-size: 13px; color: #e6a23c;">
            还有 {{ plan.days_until_end }} 天到期
          </div>
        </div>
      </div>

      <!-- 无提醒时显示 -->
      <div v-if="totalCount === 0" style="text-align: center; padding: 20px;">
        <el-icon size="48" color="#67c23a"><SuccessFilled /></el-icon>
        <p style="font-size: 16px; color: #67c23a; margin-top: 10px;">
          工作安排井然有序，继续保持！
        </p>
      </div>
    </div>

    <template #footer>
      <div style="text-align: center;">
        <!-- <el-button v-if="totalCount > 0" type="primary" @click="handleGoToWorkPlan">
          <el-icon><Edit /></el-icon>
          确定
        </el-button> -->
        <el-button @click="handleClose" :type="totalCount === 0 ? 'primary' : 'default'">
          {{ totalCount > 0 ? '确定' : '确定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, defineExpose } from 'vue'
import { Bell, Loading, SuccessFilled, Edit } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getWorkPlanReminders } from '@/api/modules/workplan'

console.log('📦 WorkPlanReminderDialog 组件正在加载...')

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const reminderData = ref({
  reminder_plans: [],
  overdue_plans: [],
  reminder_count: 0,
  overdue_count: 0
})

// 计算属性
const totalCount = computed(() => {
  return (reminderData.value.reminder_count || 0) + (reminderData.value.overdue_count || 0)
})

const overduePlans = computed(() => {
  return reminderData.value.overdue_plans || []
})

const upcomingPlans = computed(() => {
  return reminderData.value.reminder_plans || []
})

// 事件定义
const emit = defineEmits(['close', 'goToWorkPlan'])

// 方法
const show = async () => {
  console.log('🎯 显示工作计划提醒弹窗')
  visible.value = true
  await loadReminderData()
  
  // 无论是否有提醒数据，都显示弹窗让用户确认
  console.log('📋 提醒弹窗已显示，等待用户操作')
}

const hide = () => {
  console.log('🔒 隐藏工作计划提醒弹窗')
  visible.value = false
}

const loadReminderData = async () => {
  try {
    loading.value = true
    console.log('🔄 开始加载工作计划提醒数据...')
    
    const response = await getWorkPlanReminders()
    console.log('📊 提醒API响应:', response)
    
    if (response.success) {
      reminderData.value = response.data
      const count = (response.data.reminder_count || 0) + (response.data.overdue_count || 0)
      console.log(`✅ 提醒数据加载成功: 共 ${count} 个需要关注的计划`)
      
      if (count === 0) {
        console.log('🎉 没有需要提醒的计划，显示鼓励信息')
      } else {
        console.log('⚠️ 发现需要关注的计划，显示提醒详情')
      }
    } else {
      console.error('❌ 提醒API返回失败:', response.message)
      ElMessage.error(response.message || '获取提醒数据失败')
    }
  } catch (error) {
    console.error('❌ 获取工作计划提醒失败:', error)
    ElMessage.error('网络错误，无法获取提醒数据')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  console.log('👋 用户关闭提醒弹窗')
  hide()
  emit('close')
}

const handleGoToWorkPlan = () => {
  console.log('📝 用户选择管理工作计划')
  hide()
  emit('goToWorkPlan')
}

// 暴露方法给父组件
defineExpose({
  show,
  hide
})

console.log('✅ WorkPlanReminderDialog 组件加载完成')
</script> 