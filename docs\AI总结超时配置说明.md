# AI总结超时配置说明

## 概述

由于Dify AI服务响应时间较长（约1分钟），我们已将AI总结相关的超时时间延长到2分钟，以确保请求不会因超时而失败。

## 配置层级

### 1. 前端配置（2分钟）

#### 1.1 API请求超时
位置：`frontend/src/api/modules/aisummary/config.js`
```javascript
export const AI_SUMMARY_CONFIG = {
  REQUEST_TIMEOUT: 120000, // 2分钟
}
```

#### 1.2 Vite代理超时（3分钟）
位置：`frontend/src/api/http/config.js`
```javascript
export const getProxyConfig = () => ({
  '/api': {
    timeout: 180000, // 3分钟超时，确保比前端长
    proxyTimeout: 180000
  }
})
```

### 2. 后端配置（2分钟）

#### 2.1 AI服务超时
位置：`backend/api/aisummary/config.py`
```python
AI_SUMMARY_DIFY_CONFIG = {
    'timeout': int(os.getenv('AI_SUMMARY_TIMEOUT', '120')),  # 2分钟
}
```

#### 2.2 环境变量（可选）
```bash
export AI_SUMMARY_TIMEOUT=120
```

## 超时时间分层策略

```
Vite代理: 180秒 (3分钟)
    ↓
前端请求: 120秒 (2分钟) 
    ↓
后端到Dify: 120秒 (2分钟)
```

**设计原则**：
- 代理层超时时间最长，避免在错误层级触发超时
- 前端和后端保持一致的超时时间
- 给Dify足够的响应时间

## 错误处理机制

### 1. 前端错误处理
- 超时错误不跳转错误页面，显示友好提示
- 支持用户稍后查看结果或重新生成
- 使用轮询机制检查生成状态

### 2. 用户提示信息
```javascript
'AI分析生成超时，Dify服务响应时间较长。您可以稍后刷新页面查看结果，或者重新生成。'
```

## 轮询机制

### 配置参数
```javascript
POLLING: {
  MAX_ATTEMPTS: 60,  // 最大轮询次数
  INTERVAL: 2000,    // 轮询间隔（2秒）
  TIMEOUT: 120000    // 总轮询超时时间（2分钟）
}
```

### 工作流程
1. 发送生成请求
2. 如果返回"正在生成"状态，开始轮询
3. 每2秒检查一次生成状态
4. 最多轮询60次（共2分钟）
5. 完成或失败时停止轮询

## 测试验证

### 1. 超时测试
```bash
# 模拟网络延迟测试
curl -X POST "http://localhost:3000/api/v1/ai-summaries/generate/" \
  -H "Content-Type: application/json" \
  -d '{"analysis_year": 2025}' \
  --max-time 130
```

### 2. 配置检查
```javascript
console.log('AI总结超时配置:', AI_SUMMARY_CONFIG.REQUEST_TIMEOUT)
console.log('代理超时配置:', getProxyConfig()['/api'].timeout)
```

## 故障排查

### 1. 仍然出现超时错误
- 检查Vite开发服务器是否重启
- 确认环境变量是否正确设置
- 查看浏览器Network面板的实际请求时间

### 2. 配置不生效
- 清除浏览器缓存
- 重启前端开发服务器
- 检查配置文件是否正确导入

### 3. Dify服务响应慢
- 检查Dify服务状态
- 考虑优化提示词减少处理时间
- 联系Dify服务提供商

## 监控和日志

### 1. 前端日志
```javascript
console.log('[AI总结] 开始生成，超时时间:', AI_SUMMARY_CONFIG.REQUEST_TIMEOUT)
console.error('[AI总结] 请求超时:', error)
```

### 2. 后端日志
```python
logger.info(f"调用Dify API，超时时间: {self.config['timeout']}秒")
logger.error(f"Dify API请求超时: {response.status_code}")
```

## 部署注意事项

### 1. 生产环境
- 确保防火墙允许长时间连接
- 配置反向代理的超时时间
- 监控AI服务的响应时间

### 2. 负载均衡
- 设置适当的连接超时
- 考虑AI请求的特殊性
- 避免过早中断长时间运行的请求

## 更新记录

- 2025-01-15: 初始配置，设置2分钟超时
- 2025-01-15: 添加分层超时策略
- 2025-01-15: 完善错误处理机制 