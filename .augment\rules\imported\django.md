---
description: A comprehensive guide to best practices for developing REST APIs using Django REST Framework (DRF), covering code structure, design patterns, security, performance, and testing.
globs: *.py
type: "agent_requested"
---
---
# django-rest-framework Best Practices

This document provides a comprehensive guide to best practices for developing robust, scalable, and maintainable REST APIs using the Django REST Framework (DRF). It covers various aspects of DRF development, including code organization, design patterns, security considerations, performance optimization, testing strategies, and common pitfalls.

## 1. Code Organization and Structure

### 1.1. Directory Structure

A well-structured project layout is crucial for maintainability and scalability. Here's a recommended directory structure:


myproject/
  manage.py
  myproject/
    __init__.py
    settings/
      __init__.py
      base.py       # Base settings for all environments
      development.py  # Settings for development environment
      production.py   # Settings for production environment
    urls.py         # Root URL configuration
    asgi.py
    wsgi.py
  api/            # Top-level for all apps interacting via REST API
    __init__.py
    app1/
      __init__.py
      models.py     # Database models
      serializers.py# DRF serializers
      views.py        # DRF viewsets and API views
      urls.py         # App-specific URL configuration
      permissions.py  # Custom permissions
      filters.py      # Custom filters
      tasks.py        # Celery tasks (if applicable)
      tests.py        # Unit and integration tests
    app2/...
  core/            # Core app for user management, etc.
  utils/          # General utility functions and modules
  docs/           # API documentation (e.g., OpenAPI specification)


### 1.2. File Naming Conventions

*   `models.py`: Contains database models.
*   `serializers.py`: Contains DRF serializers.
*   `views.py`: Contains DRF viewsets and API views.
*   `urls.py`: Contains app-specific URL configurations.
*   `permissions.py`: Contains custom permissions.
*   `filters.py`: Contains custom filters.
*   `tasks.py`: Contains Celery tasks (if applicable).
*   `tests.py`: Contains unit and integration tests.
*   Descriptive names for custom serializers, views, permissions, and filters (e.g., `UserSerializer`, `ProductListView`, `IsAdminOrReadOnly`, `ProductFilter`).

### 1.3. Module Organization

*   **Keep apps focused:** Each app should represent a distinct feature or domain within your API.
*   **Abstract common logic:** Move reusable code into utility modules within each app or in a shared `utils` directory.
*   **Separate concerns:** Ensure that models, serializers, views, and other components are logically separated within their respective modules.

### 1.4. Component Architecture

*   **Models:** Define the data structure and relationships using Django's ORM.
*   **Serializers:** Convert model instances to JSON and vice versa, handling data validation.
*   **Viewsets:** Group related API endpoints (CRUD operations) for a model. Use Generic Views for simpler endpoints.
*   **Permissions:** Control access to API endpoints based on user roles or other criteria.
*   **Filters:** Allow clients to filter and sort data.
*   **Pagination:** Manage large datasets by returning results in pages.

### 1.5. Code Splitting

*   **Break down large viewsets:** Decompose complex viewsets into smaller, more manageable classes or functions.
*   **Use mixins:** Employ DRF's mixins to reuse common viewset logic.
*   **Custom methods for complex logic:** Move complex logic out of views and into helper functions or service classes.

## 2. Common Patterns and Anti-patterns

### 2.1. Design Patterns

*   **Serializer Pattern:** Encapsulates the conversion of model instances to and from JSON.  Enforces data validation rules.
*   **Service Layer Pattern:** Encapsulates business logic and data access. Decouples views from models.
*   **Repository Pattern:** An abstraction layer between data access logic and the rest of the application. (Less common in Django, but can be helpful for complex scenarios)
*   **Factory Pattern:** Creates objects in a centralized location. Simplifies object creation logic.
*   **Decorator Pattern:** Adds behavior to existing functions or classes. Enhances functionality without modifying core code.

### 2.2. Recommended Approaches

*   **CRUD operations:** Use DRF's ModelViewSet for standard CRUD operations on a model.
*   **Custom API endpoints:** Create custom views using `APIView` or generic views for specialized functionality.
*   **Data validation:** Leverage DRF's serializers for robust data validation.
*   **Authentication:** Use token authentication, session authentication, or OAuth for secure access.
*   **Authorization:** Implement permission classes to control access to resources.
*   **Filtering and sorting:** Use DRF's filtering and ordering capabilities.
*   **Pagination:** Implement pagination for list endpoints with large datasets.
*   **Versioning:** Implement API versioning to support evolving APIs.

### 2.3. Anti-patterns and Code Smells

*   **Fat models:** Avoid putting business logic directly into models.
*   **Fat views:** Keep views thin and delegate business logic to service classes.
*   **Inconsistent naming:** Use consistent naming conventions throughout the project.
*   **Lack of documentation:** Document all API endpoints and code components.
*   **Ignoring errors:** Handle errors gracefully and provide meaningful error messages.
*   **Over-engineering:** Don't overcomplicate solutions for simple problems.
*   **Duplicated code:** Identify and refactor duplicated code into reusable functions or components.
*   **Hardcoded values:** Avoid hardcoding configuration values in the code.

### 2.4. State Management

*   **Stateless API:**  DRF is inherently stateless, relying on tokens, sessions, or other mechanisms for authentication rather than server-side state. Each request should contain all the necessary information for processing.
*   **Client-side state:** State management should primarily be handled on the client-side using libraries like Redux, Vuex, or React Context.
*   **Avoid server-side sessions for API state:** Server-side sessions are less scalable and can introduce complexity.

### 2.5. Error Handling

*   **Use DRF's exception handling:** DRF provides built-in exception handling that returns appropriate HTTP status codes and error messages.
*   **Custom exception handling:**  Extend DRF's exception handling to customize error responses.
*   **Logging:** Log errors and warnings for debugging and monitoring.
*   **Meaningful error messages:**  Provide clear and helpful error messages to clients.
*   **Centralized error handling:**  Create middleware to catch exceptions and log/report.

## 3. Performance Considerations

### 3.1. Optimization Techniques

*   **Database query optimization:**  Use Django's ORM efficiently, using `select_related` and `prefetch_related` to reduce database queries. Analyze queries with Django Debug Toolbar.
*   **Caching:**  Implement caching (using Django's caching framework or Redis) for frequently accessed data.
*   **Pagination:** Use pagination for list endpoints with large datasets.
*   **Filtering:**  Implement efficient filtering mechanisms to reduce the amount of data processed.
*   **Gzip compression:** Enable Gzip compression to reduce the size of API responses.
*   **Throttling:**  Implement API throttling to prevent abuse.
*   **Use `defer()` and `only()`:** Retrieve only the necessary fields from the database.
*   **Raw SQL Queries:** For very specific and highly optimized queries where the ORM becomes inefficient, consider using raw SQL queries.
*   **Asynchronous tasks:** Use Celery to offload long-running tasks to background workers.
*   **Read-only serializers:** Use `read_only=True` for fields that should not be updated by clients.
*   **Caching Serialized Data:** Cache the results of serializers when appropriate.

### 3.2. Memory Management

*   **Avoid loading large datasets into memory:** Use iterators or generators to process data in chunks.
*   **Close database connections:** Ensure that database connections are closed properly after use.
*   **Garbage collection:** Understand Python's garbage collection mechanisms and avoid creating circular references.

### 3.3. Rendering Optimization

*   **Use DRF's built-in renderers:** DRF provides renderers for JSON, XML, and other formats.  Use the appropriate renderer for your API.
*   **Custom renderers:**  Create custom renderers for specialized output formats.
*   **Streaming responses:** Use streaming responses for large datasets.

### 3.4. Bundle Size Optimization (If applicable - primarily for client-side rendering)

*   **Tree shaking:**  Remove unused code from JavaScript bundles.
*   **Code splitting:**  Split JavaScript bundles into smaller chunks that can be loaded on demand.
*   **Minification:**  Minify JavaScript and CSS files to reduce their size.

### 3.5. Lazy Loading

*   **Lazy load related data:**  Load related data only when it is needed.
*   **Use DRF's `depth` option:**  Control the depth of nested serialization.

## 4. Security Best Practices

### 4.1. Common Vulnerabilities

*   **SQL injection:** Prevent SQL injection by using Django's ORM and parameterized queries.
*   **Cross-site scripting (XSS):**  Sanitize user input to prevent XSS attacks (more relevant for templates, but consider it for any user-provided data).
*   **Cross-site request forgery (CSRF):**  Protect against CSRF attacks by using Django's CSRF protection mechanisms.
*   **Authentication bypass:**  Ensure that authentication is properly enforced for all API endpoints.
*   **Authorization flaws:**  Implement fine-grained authorization to restrict access to resources.
*   **Data leakage:**  Avoid exposing sensitive data in API responses.
*   **Denial of Service (DoS):**  Implement rate limiting and other measures to prevent DoS attacks.

### 4.2. Input Validation

*   **Use DRF's serializers for input validation:** Serializers provide a convenient way to validate incoming data.
*   **Validate data types:**  Ensure that data types match the expected types.
*   **Validate data ranges:**  Ensure that data values fall within the allowed ranges.
*   **Sanitize user input:**  Sanitize user input to prevent XSS attacks.
*   **Escape output:** Escape data when rendering it in templates to prevent XSS attacks.
*   **Custom Validation:** Implement custom validation logic in serializers to enforce complex business rules.

### 4.3. Authentication and Authorization

*   **Choose the appropriate authentication scheme:** Use token authentication, session authentication, or OAuth based on your requirements.
*   **Implement permission classes:**  Use permission classes to control access to resources based on user roles or other criteria.
*   **Use JWT (JSON Web Tokens) for stateless authentication:** JWTs are a popular choice for stateless authentication in REST APIs.
*   **Role-Based Access Control (RBAC):** Implement RBAC to manage user permissions based on roles.
*   **Principle of Least Privilege:** Grant users only the minimum necessary permissions.
*   **Multi-Factor Authentication (MFA):** Implement MFA for enhanced security.
*   **Regularly rotate API keys:** Rotate API keys regularly to reduce the risk of compromise.

### 4.4. Data Protection

*   **Encrypt sensitive data:** Encrypt sensitive data at rest and in transit.
*   **Use HTTPS:**  Use HTTPS for all API communication.
*   **Store passwords securely:**  Use Django's password hashing mechanisms to store passwords securely.
*   **Data masking:** Mask sensitive data when displaying it to users.
*   **Data retention policies:** Implement data retention policies to ensure that data is not stored longer than necessary.
*   **Audit logging:** Implement audit logging to track access to sensitive data.

### 4.5. Secure API Communication

*   **Use HTTPS:**  Use HTTPS for all API communication.
*   **Implement CORS:**  Configure CORS (Cross-Origin Resource Sharing) to allow requests from specific domains.
*   **Rate limiting:** Implement API throttling to prevent abuse.
*   **Input Validation:** Implement robust data validation and sanitization.
*   **Secure Headers:** Use secure HTTP headers like `X-Content-Type-Options`, `Strict-Transport-Security`, and `X-Frame-Options`.

## 5. Testing Approaches

### 5.1. Unit Testing

*   **Test serializers:**  Test that serializers correctly serialize and deserialize data, and that validation rules are enforced.
*   **Test views:**  Test that views return the expected responses for different requests, and that permissions are enforced.
*   **Test models:** Test model methods and data integrity.
*   **Isolate units of code:**  Use mocking and stubbing to isolate units of code during testing.
*   **Follow Arrange-Act-Assert pattern:** Arrange test data, act on the code, and assert the expected result.

### 5.2. Integration Testing

*   **Test API endpoints:**  Test that API endpoints work correctly when integrated with other components.
*   **Test database interactions:**  Test that database interactions are performed correctly.
*   **Test authentication and authorization:**  Test that authentication and authorization mechanisms work as expected.
*   **Use Django's test client:** Use Django's test client to send HTTP requests to API endpoints.
*   **Test with real database:** Use a separate test database for integration tests.

### 5.3. End-to-End Testing

*   **Test the entire API workflow:**  Test the entire API workflow from end to end.
*   **Use tools like Selenium or Cypress:**  Use tools like Selenium or Cypress to automate end-to-end tests.
*   **Test with a production-like environment:** Test with a production-like environment to ensure that the API works correctly in a real-world setting.

### 5.4. Test Organization

*   **Create a separate `tests.py` file for each app:** This promotes modularity and makes it easier to find tests for a specific app.
*   **Organize tests within `tests.py` by component:** Group tests for serializers, views, models, and other components.
*   **Use descriptive test names:** Use descriptive test names that clearly indicate what each test is testing.
*   **Follow a consistent naming convention:** Use a consistent naming convention for test functions and classes.

### 5.5. Mocking and Stubbing

*   **Use mocking to isolate units of code:** Mock external dependencies to isolate units of code during testing.
*   **Use stubbing to provide controlled responses:** Stub external dependencies to provide controlled responses during testing.
*   **Use libraries like `unittest.mock`:**  Use libraries like `unittest.mock` for mocking and stubbing.
*   **Avoid over-mocking:** Only mock the necessary dependencies to keep tests realistic.

## 6. Common Pitfalls and Gotchas

### 6.1. Frequent Mistakes

*   **Not using serializers for validation:**  Skipping serializer validation can lead to data integrity issues and security vulnerabilities.
*   **Incorrectly configuring permissions:** Misconfigured permissions can expose resources to unauthorized users.
*   **Over-fetching data:**  Fetching more data than necessary can impact performance.
*   **Ignoring database query optimization:** Inefficient database queries can lead to slow API responses.
*   **Not handling errors gracefully:** Unhandled exceptions can cause the API to crash or return unhelpful error messages.
*   **Using `CharField` without `max_length`**: Can lead to unexpected database errors or vulnerabilities.

### 6.2. Edge Cases

*   **Handling null values:**  Ensure that null values are handled correctly in serializers and views.
*   **Handling empty lists:**  Ensure that empty lists are handled correctly in serializers and views.
*   **Handling unicode characters:**  Ensure that unicode characters are handled correctly in serializers and views.
*   **Concurrency issues:** Be aware of potential concurrency issues when handling data updates.
*   **Timezones:** Be aware of timezone issues and use timezone-aware datetime objects.

### 6.3. Version-Specific Issues

*   **Consult DRF's release notes:** Refer to DRF's release notes for information on version-specific issues and breaking changes.
*   **Test with different DRF versions:** Test your API with different DRF versions to ensure compatibility.

### 6.4. Compatibility Concerns

*   **Django version compatibility:**  Ensure that your DRF version is compatible with your Django version.
*   **Python version compatibility:**  Ensure that your DRF version is compatible with your Python version.
*   **Third-party library compatibility:**  Ensure that your DRF version is compatible with any third-party libraries that you are using.

### 6.5. Debugging Strategies

*   **Use Django Debug Toolbar:** Use Django Debug Toolbar to inspect database queries, template rendering, and other performance metrics.
*   **Use logging:**  Use logging to track the flow of execution and identify errors.
*   **Use a debugger:** Use a debugger to step through code and inspect variables.
*   **Test API endpoints with a tool like Postman:** Use Postman or a similar tool to test API endpoints and inspect responses.
*   **Read traceback carefully**: Python tracebacks are extremely helpful in locating the origin of an error.

## 7. Tooling and Environment

### 7.1. Recommended Tools

*   **Virtual environment:** Use a virtual environment to isolate project dependencies.
*   **Pip:** Use pip to manage Python packages.
*   **Django:**  Use Django as the web framework.
*   **Django REST Framework:**  Use DRF for building REST APIs.
*   **Django Debug Toolbar:** Use Django Debug Toolbar for debugging and performance analysis.
*   **Postman or Insomnia:** Use Postman or Insomnia to test API endpoints.
*   **Git:** Use Git for version control.
*   **EditorConfig:** Use EditorConfig to maintain consistent coding styles across different editors.
*   **Black, isort, flake8**: For code formatting and linting
*   **drf-yasg or Spectacular OpenAPI:** For automatic OpenAPI schema generation.

### 7.2. Build Configuration

*   **Use a `requirements.txt` file:**  Use a `requirements.txt` file to specify project dependencies.
*   **Use a `setup.py` file:**  Use a `setup.py` file to package and distribute your API.
*   **Use environment variables:**  Use environment variables to store configuration values.
*   **Use Docker:**  Use Docker to containerize your API for consistent deployment.

### 7.3. Linting and Formatting

*   **Use PEP 8:** Follow PEP 8 guidelines for Python code.
*   **Use Black:** Use Black for code formatting.
*   **Use flake8:**  Use flake8 for code linting.
*   **Use isort:** Use isort for import sorting.
*   **Configure pre-commit hooks:** Set up pre-commit hooks to automatically format and lint code before committing.

### 7.4. Deployment

*   **Use a production-ready web server:** Use a production-ready web server like Gunicorn or uWSGI.
*   **Use a load balancer:** Use a load balancer to distribute traffic across multiple servers.
*   **Use a database server:**  Use a database server like PostgreSQL or MySQL.
*   **Use a caching server:** Use a caching server like Redis or Memcached.
*   **Use a content delivery network (CDN):** Use a CDN to serve static assets.
*   **Monitor API performance:**  Monitor API performance to identify and address performance issues.

### 7.5. CI/CD Integration

*   **Use a CI/CD pipeline:**  Use a CI/CD pipeline to automate the build, test, and deployment process.
*   **Use a CI/CD tool like Jenkins, GitLab CI, or CircleCI:** Use a CI/CD tool to manage your CI/CD pipeline.
*   **Automate testing:**  Automate unit, integration, and end-to-end tests as part of the CI/CD pipeline.
*   **Automate code formatting and linting:** Automate code formatting and linting as part of the CI/CD pipeline.
*   **Automate deployment:** Automate deployment to production as part of the CI/CD pipeline.

- 后端技术参数
    - python django 5.2 lts
    - 使用uv管理包
    - 纯后端实现，不使用模板，也不与自带的admin集成
    - 使用DFR框架
        - 所有API使用APIView方式编写，参考下面
    - 集成 Swagger 文档


- APIView编写参考方式如下:
```
# APIView_test.py
class SnippetList(APIView):

    def get(self, request, format=None):
        snippets = Snippet.objects.all()
        serializer = SnippetSerializer(snippets, many=True)
        return Response(serializer.data)

    def post(self, request, format=None):
        serializer = SnippetSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
```


By following these best practices, you can create robust, scalable, maintainable, and secure REST APIs using the Django REST Framework.