/**
 * 人大代表统计数据相关API
 * 包含各级人大代表统计图表所需的数据接口
 */

import { get, post } from './request.js'

/**
 * 获取人大代表统计数据
 * @returns {Promise} 返回人大代表统计数据
 */
export const getRepresentativeStatistics = async () => {
  try {
    // 模拟数据 - 后期替换为真实API调用
    const mockData = {
      code: 200,
      message: 'success',
      data: [
        { 
          name: '省人大代表', 
          value: 156, 
          color: '#02a6b5',
          trend: '+3%', // 相比上期的增长趋势
          icon: '🏛️'
        },
        { 
          name: '市人大代表', 
          value: 289, 
          color: '#1a5490',
          trend: '+5%',
          icon: '🏢'
        },
        { 
          name: '县人大代表', 
          value: 432, 
          color: '#337ab7',
          trend: '+2%',
          icon: '🏘️'
        },
        { 
          name: '镇人大代表', 
          value: 678, 
          color: '#49bcf7',
          trend: '+7%',
          icon: '🏠'
        }
      ],
      totalCount: 1555, // 总代表人数
      timestamp: Date.now()
    }

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    console.log('🏛️ 获取人大代表统计数据成功:', mockData.data)
    return mockData
    
    // 后期替换为真实API调用：
    // return await get('/statistics/representatives')
    
  } catch (error) {
    console.error('❌ 获取人大代表统计数据失败:', error)
    throw error
  }
}

/**
 * 获取实时监控数据
 * @returns {Promise} 返回实时监控数据
 */
export const getRealtimeMonitoring = async () => {
  try {
    const mockData = {
      code: 200,
      message: 'success',
      data: {
        onlineUsers: 1248,
        systemLoad: 65,
        memoryUsage: 78,
        networkSpeed: '125 MB/s',
        lastUpdate: new Date().toLocaleTimeString()
      },
      timestamp: Date.now()
    }

    await new Promise(resolve => setTimeout(resolve, 200))
    
    console.log('📡 获取实时监控数据成功:', mockData.data)
    return mockData
    
    // 后期替换为：
    // return await get('/monitoring/realtime')
    
  } catch (error) {
    console.error('❌ 获取实时监控数据失败:', error)
    throw error
  }
}

/**
 * 获取系统状态数据
 * @returns {Promise} 返回系统状态数据
 */
export const getSystemStatus = async () => {
  try {
    const mockData = {
      code: 200,
      message: 'success',
      data: {
        cpuUsage: 45,
        memoryUsage: 68,
        diskUsage: 52,
        networkStatus: 'normal',
        services: [
          { name: 'Web服务', status: 'running', uptime: '99.9%' },
          { name: '数据库', status: 'running', uptime: '99.8%' },
          { name: '缓存服务', status: 'running', uptime: '99.7%' },
          { name: '消息队列', status: 'warning', uptime: '98.5%' }
        ]
      },
      timestamp: Date.now()
    }

    await new Promise(resolve => setTimeout(resolve, 250))
    
    console.log('⚙️ 获取系统状态数据成功:', mockData.data)
    return mockData
    
    // 后期替换为：
    // return await get('/system/status')
    
  } catch (error) {
    console.error('❌ 获取系统状态数据失败:', error)
    throw error
  }
}

/**
 * 获取用户信息数据
 * @returns {Promise} 返回用户信息数据
 */
export const getUserInfo = async () => {
  try {
    const mockData = {
      code: 200,
      message: 'success',
      data: {
        totalUsers: 15420,
        activeUsers: 8965,
        newUsers: 234,
        userGrowth: '+12.5%',
        topRegions: [
          { region: '北京', count: 3245 },
          { region: '上海', count: 2876 },
          { region: '广州', count: 2134 },
          { region: '深圳', count: 1987 }
        ]
      },
      timestamp: Date.now()
    }

    await new Promise(resolve => setTimeout(resolve, 280))
    
    console.log('👤 获取用户信息数据成功:', mockData.data)
    return mockData
    
    // 后期替换为：
    // return await get('/users/info')
    
  } catch (error) {
    console.error('❌ 获取用户信息数据失败:', error)
    throw error
  }
}

/**
 * 获取通知中心数据
 * @returns {Promise} 返回通知数据
 */
export const getNotifications = async () => {
  try {
    const mockData = {
      code: 200,
      message: 'success',
      data: {
        unreadCount: 5,
        notifications: [
          {
            id: 1,
            title: '系统维护通知',
            content: '系统将于今晚22:00-24:00进行维护',
            type: 'warning',
            time: '2小时前',
            read: false
          },
          {
            id: 2,
            title: '新用户注册',
            content: '今日新增用户234人',
            type: 'info',
            time: '4小时前',
            read: false
          },
          {
            id: 3,
            title: '数据备份完成',
            content: '数据库备份已成功完成',
            type: 'success',
            time: '6小时前',
            read: true
          }
        ]
      },
      timestamp: Date.now()
    }

    await new Promise(resolve => setTimeout(resolve, 200))
    
    console.log('🔔 获取通知数据成功:', mockData.data)
    return mockData
    
    // 后期替换为：
    // return await get('/notifications')
    
  } catch (error) {
    console.error('❌ 获取通知数据失败:', error)
    throw error
  }
} 