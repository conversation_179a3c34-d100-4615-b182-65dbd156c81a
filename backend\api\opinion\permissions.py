"""
意见建议管理权限类

定义意见建议系统中的权限控制规则：
1. IsRepresentativeOwnerOrStaff - 只有意见建议的提交代表或工作人员可以访问
2. IsRepresentativeOwner - 只有意见建议的提交代表可以访问
3. CanReviewOpinion - 可以审核意见建议的权限（仅工作人员）
4. CanViewOpinionList - 可以查看意见建议列表的权限
5. CanCreateOpinion - 可以创建意见建议的权限（仅代表）
6. CanModifyOpinion - 可以修改意见建议的权限
"""

from rest_framework import permissions


class IsRepresentativeOwnerOrStaff(permissions.BasePermission):
    """
    只有意见建议的提交代表或工作人员可以访问
    用于意见建议的查看和修改权限控制
    """
    
    def has_permission(self, request, view):
        """检查用户是否已认证"""
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        """检查对象级权限"""
        # 工作人员可以访问所有意见建议
        if request.user.role == 'staff':
            return True
        
        # 代表只能访问自己提交的意见建议
        if request.user.role == 'representative':
            return hasattr(request.user, 'representative') and obj.representative == request.user.representative
        
        return False


class IsRepresentativeOwner(permissions.BasePermission):
    """
    只有意见建议的提交代表可以访问
    用于代表专属操作（如修改自己的意见建议）
    """
    
    def has_permission(self, request, view):
        """检查用户是否为代表"""
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'representative' and
            hasattr(request.user, 'representative')
        )
    
    def has_object_permission(self, request, view, obj):
        """检查对象级权限"""
        # 只有提交代表可以访问
        return obj.representative == request.user.representative


class CanReviewOpinion(permissions.BasePermission):
    """
    可以审核意见建议的权限
    只有工作人员可以执行审核操作
    """
    
    def has_permission(self, request, view):
        """检查用户是否为工作人员"""
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'staff' and
            hasattr(request.user, 'staffmember')
        )
    
    def has_object_permission(self, request, view, obj):
        """检查对象级权限"""
        # 工作人员可以审核所有意见建议
        return request.user.role == 'staff'


class CanViewOpinionList(permissions.BasePermission):
    """
    可以查看意见建议列表的权限
    代表只能查看自己的意见建议，工作人员可以查看所有
    """
    
    def has_permission(self, request, view):
        """检查基本权限"""
        return request.user and request.user.is_authenticated
    
    def get_queryset_filter(self, request, queryset):
        """根据用户角色过滤查询集"""
        if request.user.role == 'staff':
            # 工作人员可以查看所有意见建议
            return queryset
        elif request.user.role == 'representative' and hasattr(request.user, 'representative'):
            # 代表只能查看自己提交的意见建议
            return queryset.filter(representative=request.user.representative)
        else:
            # 其他情况返回空查询集
            return queryset.none()


class CanCreateOpinion(permissions.BasePermission):
    """
    可以创建意见建议的权限
    只有人大代表可以创建意见建议
    """
    
    def has_permission(self, request, view):
        """检查创建权限"""
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'representative' and
            hasattr(request.user, 'representative')
        )


class CanModifyOpinion(permissions.BasePermission):
    """
    可以修改意见建议的权限
    只有提交代表可以修改自己的意见建议，且仅限草稿或被驳回状态
    """
    
    def has_permission(self, request, view):
        """检查基本权限"""
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'representative' and
            hasattr(request.user, 'representative')
        )
    
    def has_object_permission(self, request, view, obj):
        """检查对象级权限"""
        # 只有提交代表可以修改
        if obj.representative != request.user.representative:
            return False
        
        # 只有草稿或被驳回的意见建议才能修改
        if obj.current_status not in ['draft', 'rejected']:
            return False
        
        return True


class CanDeleteOpinion(permissions.BasePermission):
    """
    可以删除意见建议的权限
    只有提交代表可以删除自己的草稿状态意见建议，工作人员可以删除任何意见建议
    """
    
    def has_permission(self, request, view):
        """检查基本权限"""
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        """检查对象级权限"""
        # 工作人员可以删除任何意见建议
        if request.user.role == 'staff':
            return True
        
        # 代表只能删除自己的草稿状态意见建议
        if (request.user.role == 'representative' and 
            hasattr(request.user, 'representative') and
            obj.representative == request.user.representative):
            return obj.current_status == 'draft'
        
        return False


class CanSubmitOpinion(permissions.BasePermission):
    """
    可以提交意见建议的权限
    只有提交代表可以提交自己的草稿或被驳回的意见建议
    """
    
    def has_permission(self, request, view):
        """检查基本权限"""
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'representative' and
            hasattr(request.user, 'representative')
        )
    
    def has_object_permission(self, request, view, obj):
        """检查对象级权限"""
        # 只有提交代表可以提交
        if obj.representative != request.user.representative:
            return False
        
        # 只有草稿或被驳回的意见建议才能提交
        if obj.current_status not in ['draft', 'rejected']:
            return False
        
        return True


class CanUseAI(permissions.BasePermission):
    """
    可以使用AI功能的权限
    只有人大代表可以使用AI辅助生成功能
    """
    
    def has_permission(self, request, view):
        """检查AI使用权限"""
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'representative' and
            hasattr(request.user, 'representative')
        )


class CanViewStatistics(permissions.BasePermission):
    """
    可以查看统计数据的权限
    代表可以查看自己的统计，工作人员可以查看全局统计
    """
    
    def has_permission(self, request, view):
        """检查统计查看权限"""
        return request.user and request.user.is_authenticated


class ReadOnlyOrOwnerOrStaff(permissions.BasePermission):
    """
    只读权限或所有者权限或工作人员权限
    - 所有认证用户可以读取（但会根据角色过滤数据）
    - 只有所有者或工作人员可以修改
    """
    
    def has_permission(self, request, view):
        """检查基本权限"""
        if not (request.user and request.user.is_authenticated):
            return False
        
        # 对于安全的方法（GET, HEAD, OPTIONS），所有认证用户都可以访问
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 对于修改操作，需要进一步检查
        return True
    
    def has_object_permission(self, request, view, obj):
        """检查对象级权限"""
        # 读取权限：所有认证用户都可以读取（但视图层会过滤数据）
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 修改权限：只有所有者或工作人员可以修改
        if request.user.role == 'staff':
            return True
        
        if (request.user.role == 'representative' and 
            hasattr(request.user, 'representative') and
            obj.representative == request.user.representative):
            return True
        
        return False 