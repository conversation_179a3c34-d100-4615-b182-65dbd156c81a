# 履职服务平台数据大屏

## 项目概述

这是一个基于Vue3 + ECharts的数据可视化大屏项目，展示履职服务平台的各种统计数据。

## 最新更新

### 数据重构 (2024-01-15)

- **数据集中管理**: 将所有模块的数据统一整合到 `public/data.json` 文件中
- **模拟API请求**: 在 `App.vue` 中实现了模拟请求获取数据的功能
- **组件数据驱动**: 所有图表组件现在通过props接收数据，实现了数据与视图的分离
- **加载状态**: 添加了数据加载时的loading动画

### 数据文件结构

```json
{
  "chart1": {
    "data": [
      { "value": 10, "name": "镇级" },
      { "value": 20, "name": "县级" },
      { "value": 30, "name": "市级" }
    ]
  },
  "chart2": {
    "data": [
      { "value": 1156, "name": "中国共产党" },
      { "value": 5, "name": "九三学社" },
      { "value": 1, "name": "中国民主促进会" },
      { "value": 171, "name": "群众" }
    ]
  },
  // ... 其他图表数据
}
```

### 组件改动

所有图表组件 (`Chart1.vue` 到 `Chart9.vue`) 都进行了以下改动：

1. **添加props定义**: 每个组件都定义了相应的props来接收数据
2. **移除内部数据**: 删除了组件内部的静态数据
3. **添加数据监听**: 使用watch监听props变化，自动更新图表
4. **默认值处理**: 为所有props提供了合理的默认值

### 主要功能模块

- **Chart1**: 代表层级统计 (饼图)
- **Chart2**: 代表结构组成 (饼图)
- **Chart4**: 意见建议 (滚动列表)
- **Chart5**: 地图展示 (区域统计)
- **Chart6**: 履职明细 (柱状图)
- **Chart7**: 代表名单 (排行榜)
- **Chart8**: 履职统计 (折线图)
- **Chart9**: AI知识库 (列表展示)

## 技术栈

- Vue 3 (Composition API)
- ECharts 5.6.0
- Vite 4.1.0
- Less 4.4.0

## 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 项目结构

```
Vue-keShihu4/
├── public/
│   ├── data.json          # 统一数据文件
│   └── ...
├── src/
│   ├── components/
│   │   ├── Chart1.vue     # 代表层级统计
│   │   ├── Chart2.vue     # 代表结构组成
│   │   ├── Chart4.vue     # 意见建议
│   │   ├── Chart5.vue     # 地图展示
│   │   ├── Chart6.vue     # 履职明细
│   │   ├── Chart7.vue     # 代表名单
│   │   ├── Chart8.vue     # 履职统计
│   │   └── Chart9.vue     # AI知识库
│   ├── App.vue            # 主应用组件
│   └── main.js            # 应用入口
└── package.json
```

## 数据更新

如需更新数据，只需修改 `public/data.json` 文件中的相应部分，页面会自动重新加载并显示最新数据。

## 注意事项

1. 确保 `public/data.json` 文件格式正确，避免JSON语法错误
2. 如果数据加载失败，组件会显示空状态或默认数据
3. 所有图表组件都支持响应式数据更新
