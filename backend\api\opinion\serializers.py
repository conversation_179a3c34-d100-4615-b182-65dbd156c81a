"""
意见建议管理序列化器

包含以下序列化器：
1. OpinionSuggestionSerializer - 意见建议基础序列化器
2. OpinionSuggestionDetailSerializer - 意见建议详情序列化器
3. OpinionSuggestionCreateSerializer - 意见建议创建序列化器
4. OpinionReviewSerializer - 审核记录序列化器
5. OpinionReviewCreateSerializer - 审核记录创建序列化器
6. AIGenerateRequestSerializer - AI生成请求序列化器
7. OpinionStatusSerializer - 意见状态序列化器
"""

import json
from rest_framework import serializers
from django.utils import timezone
from django.utils.html import strip_tags
from api.users.serializers import RepresentativeSerializer, RepresentativeListSerializer, StaffMemberSerializer
from .models import OpinionSuggestion, OpinionReview


class OpinionReviewSerializer(serializers.ModelSerializer):
    """
    审核记录序列化器
    用于展示审核记录信息
    """
    
    # 审核人信息（嵌套序列化）
    reviewer_info = StaffMemberSerializer(
        source='reviewer',
        read_only=True,
        help_text='审核人详细信息'
    )
    
    # 操作动作显示名称
    action_display = serializers.CharField(
        source='get_action_display',
        read_only=True,
        help_text='操作动作显示名称'
    )
    
    # 状态显示名称
    status_display = serializers.CharField(
        source='get_status_display',
        read_only=True,
        help_text='状态显示名称'
    )
    
    # 附件列表（解析JSON）
    attachment_list = serializers.SerializerMethodField(
        help_text='附件文件列表'
    )
    
    # 操作人姓名（特殊处理创建操作）
    operator_name = serializers.SerializerMethodField(
        help_text='操作人姓名'
    )
    
    class Meta:
        model = OpinionReview
        fields = [
            'id', 'action', 'action_display', 'status', 'status_display',
            'transferred_department', 'review_comment', 'processing_result',
            'attachments', 'attachment_list', 'action_time', 'created_at',
            'reviewer_info', 'operator_name'
        ]
        read_only_fields = ['id', 'created_at']
    
    def get_attachment_list(self, obj):
        """解析附件JSON字符串为列表"""
        if obj.attachments:
            try:
                return json.loads(obj.attachments)
            except (json.JSONDecodeError, TypeError):
                return []
        return []
    
    def get_operator_name(self, obj):
        """获取操作人姓名，特殊处理创建操作"""
        if obj.action == 'create' and obj.reviewer is None:
            # 创建草稿操作，显示代表的姓名
            return obj.opinion.representative.name if obj.opinion and obj.opinion.representative else '代表'
        elif obj.action == 'submit' and obj.reviewer is None:
            # 代表提交操作，显示代表的姓名
            return obj.opinion.representative.name if obj.opinion and obj.opinion.representative else '代表'
        elif obj.reviewer:
            # 工作人员操作，显示工作人员姓名
            return obj.reviewer.name
        else:
            # 其他系统操作
            return '系统'


class OpinionSuggestionListSerializer(serializers.ModelSerializer):
    """
    意见建议列表序列化器
    用于列表展示，不包含头像等大数据字段以优化性能
    """

    # 提交代表信息（嵌套序列化，不包含头像）
    representative_info = RepresentativeListSerializer(
        source='representative',
        read_only=True,
        help_text='提交代表基本信息（不含头像）'
    )

    # 分类显示名称
    category_display = serializers.CharField(
        source='get_category_display',
        read_only=True,
        help_text='分类显示名称'
    )

    # 当前状态（计算属性）
    current_status = serializers.ReadOnlyField(
        help_text='当前处理状态'
    )

    # 当前状态显示名称
    current_status_display = serializers.SerializerMethodField(
        help_text='当前状态显示名称'
    )

    # 最新审核信息
    latest_review = OpinionReviewSerializer(
        source='current_review',
        read_only=True,
        help_text='最新审核记录'
    )

    # 真正的最后更新时间（基于最新审核记录的action_time）
    last_updated_time = serializers.SerializerMethodField(
        help_text='最后更新时间（基于最新审核记录的操作时间）'
    )

    class Meta:
        model = OpinionSuggestion
        fields = [
            'id', 'title', 'category', 'category_display', 'reporter_name',
            'original_content', 'final_suggestion', 'ai_assisted',
            'current_status', 'current_status_display', 'created_at', 'updated_at',
            'last_updated_time', 'representative_info', 'latest_review'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_current_status_display(self, obj):
        """获取当前状态显示名称"""
        status_choices = {
            'draft': '草稿',
            'submitted': '已提交',
            'approved': '审核通过',
            'rejected': '审核驳回',
            'transferred': '已转交',
            'in_progress': '处理中',
            'completed': '已办结'
        }
        return status_choices.get(obj.current_status, obj.current_status)

    def get_last_updated_time(self, obj):
        """获取最后更新时间"""
        if hasattr(obj, 'current_review') and obj.current_review:
            return obj.current_review.action_time
        return obj.created_at


class OpinionSuggestionSerializer(serializers.ModelSerializer):
    """
    意见建议基础序列化器
    用于详情展示和基本信息（包含头像）
    """

    # 提交代表信息（嵌套序列化）
    representative_info = RepresentativeSerializer(
        source='representative',
        read_only=True,
        help_text='提交代表详细信息'
    )
    
    # 分类显示名称
    category_display = serializers.CharField(
        source='get_category_display',
        read_only=True,
        help_text='分类显示名称'
    )
    
    # 当前状态（计算属性）
    current_status = serializers.ReadOnlyField(
        help_text='当前处理状态'
    )
    
    # 当前状态显示名称
    current_status_display = serializers.SerializerMethodField(
        help_text='当前状态显示名称'
    )
    
    # 最新审核信息
    latest_review = OpinionReviewSerializer(
        source='current_review',
        read_only=True,
        help_text='最新审核记录'
    )
    
    # 真正的最后更新时间（基于最新审核记录的action_time）
    last_updated_time = serializers.SerializerMethodField(
        help_text='最后更新时间（基于最新审核记录的操作时间）'
    )
    
    class Meta:
        model = OpinionSuggestion
        fields = [
            'id', 'title', 'category', 'category_display', 'reporter_name',
            'original_content', 'final_suggestion', 'ai_assisted',
            'current_status', 'current_status_display', 'created_at', 'updated_at',
            'last_updated_time', 'representative_info', 'latest_review'
        ]
        read_only_fields = [
            'id', 'current_status', 'created_at', 'updated_at', 'last_updated_time',
            'representative_info', 'latest_review'
        ]
    
    def get_current_status_display(self, obj):
        """获取当前状态显示名称"""
        status_map = dict(OpinionReview.STATUS_CHOICES)
        return status_map.get(obj.current_status, obj.current_status)
    
    def get_last_updated_time(self, obj):
        """
        获取真正的最后更新时间
        基于最新审核记录的action_time，而不是OpinionSuggestion的updated_at
        """
        latest_review = obj.current_review
        if latest_review:
            return latest_review.action_time
        # 如果没有审核记录，fallback到创建时间
        return obj.created_at


class OpinionSuggestionDetailSerializer(OpinionSuggestionSerializer):
    """
    意见建议详情序列化器
    包含完整信息和审核历史
    """
    
    # 审核历史（所有审核记录）
    review_history = serializers.SerializerMethodField(
        help_text='完整审核历史记录，按时间和ID正序排列'
    )
    
    # AI生成内容（在详情中显示）
    ai_generated_content = serializers.CharField(
        read_only=True,
        allow_null=True,
        help_text='AI生成的建议内容'
    )
    
    class Meta(OpinionSuggestionSerializer.Meta):
        fields = OpinionSuggestionSerializer.Meta.fields + [
            'ai_generated_content', 'review_history'
        ]
    
    def get_review_history(self, obj):
        """
        获取审核历史记录，按时间和ID正序排序
        确保时间线显示顺序正确
        """
        reviews = obj.reviews.order_by('action_time', 'id')
        return OpinionReviewSerializer(reviews, many=True).data


class OpinionSuggestionCreateSerializer(serializers.ModelSerializer):
    """
    意见建议创建序列化器
    用于代表创建新的意见建议
    """
    
    class Meta:
        model = OpinionSuggestion
        fields = [
            'title', 'category', 'reporter_name', 'original_content',
            'final_suggestion', 'ai_assisted', 'ai_generated_content'
        ]
    
    def validate_title(self, value):
        """验证标题"""
        if not value or not value.strip():
            raise serializers.ValidationError('标题不能为空')
        
        # 清理HTML标签防止XSS攻击
        cleaned_value = strip_tags(value).strip()
        
        if len(cleaned_value) < 5:
            raise serializers.ValidationError('标题长度不能少于5个字符')
        
        return cleaned_value
    
    def validate_reporter_name(self, value):
        """验证反映人姓名"""
        if not value or not value.strip():
            raise serializers.ValidationError('反映人姓名不能为空')
        
        # 清理HTML标签防止XSS攻击
        cleaned_value = strip_tags(value).strip()
        
        return cleaned_value
    
    def validate_original_content(self, value):
        """验证原始意见内容"""
        if not value or not value.strip():
            raise serializers.ValidationError('原始意见内容不能为空')
        
        # 清理HTML标签防止XSS攻击
        cleaned_value = strip_tags(value).strip()
        
        if len(cleaned_value) < 10:
            raise serializers.ValidationError('意见内容长度不能少于10个字符')
        
        return cleaned_value
    
    def validate(self, attrs):
        """整体验证"""
        # 如果标记为AI辅助，但没有AI生成内容，则报错
        if attrs.get('ai_assisted') and not attrs.get('ai_generated_content'):
            raise serializers.ValidationError({
                'ai_generated_content': '使用AI辅助时必须提供AI生成的内容'
            })
        
        # 清理final_suggestion的HTML标签
        if attrs.get('final_suggestion'):
            attrs['final_suggestion'] = strip_tags(attrs['final_suggestion']).strip()
        
        # 如果没有最终建议内容，使用原始内容
        if not attrs.get('final_suggestion'):
            attrs['final_suggestion'] = attrs['original_content']
        
        # 清理ai_generated_content的HTML标签
        if attrs.get('ai_generated_content'):
            attrs['ai_generated_content'] = strip_tags(attrs['ai_generated_content']).strip()
        
        return attrs
    
    def create(self, validated_data):
        """创建意见建议"""
        # 获取当前用户的代表信息
        request = self.context.get('request')
        if not request or not hasattr(request.user, 'representative'):
            raise serializers.ValidationError('只有人大代表可以创建意见建议')
        
        validated_data['representative'] = request.user.representative
        
        # 创建意见建议
        opinion = super().create(validated_data)
        
        # 自动创建初始审核记录（草稿状态）
        # 创建草稿时的操作人应该是代表本人，而不是系统
        OpinionReview.objects.create(
            opinion=opinion,
            reviewer=None,  # 代表创建操作，reviewer仍为null，但在序列化时会特殊处理
            action='create',
            status='draft',
            review_comment='代表创建意见草稿',
            action_time=timezone.now()
        )
        
        return opinion


class OpinionSuggestionUpdateSerializer(serializers.ModelSerializer):
    """
    意见建议更新序列化器
    用于代表修改自己的意见建议
    """
    
    class Meta:
        model = OpinionSuggestion
        fields = [
            'title', 'category', 'reporter_name', 'original_content',
            'final_suggestion', 'ai_assisted', 'ai_generated_content'
        ]
    
    def validate(self, attrs):
        """验证更新数据"""
        # 只有草稿或被驳回的意见建议才能修改
        instance = self.instance
        if instance and instance.current_status not in ['draft', 'rejected']:
            raise serializers.ValidationError('只有草稿或被驳回的意见建议才能修改')
        
        return attrs


class OpinionReviewCreateSerializer(serializers.ModelSerializer):
    """
    审核记录创建序列化器
    用于工作人员创建审核记录
    """
    
    # 附件文件列表（接收列表格式，自动转换为JSON）
    attachment_files = serializers.ListField(
        child=serializers.CharField(max_length=500),
        required=False,
        allow_empty=True,
        write_only=True,
        help_text='附件文件路径列表'
    )
    
    class Meta:
        model = OpinionReview
        fields = [
            'action', 'transferred_department', 'review_comment',
            'processing_result', 'attachment_files'
        ]
    
    def validate_action(self, value):
        """验证操作动作"""
        # 工作人员只能执行特定操作
        allowed_actions = ['approve', 'reject', 'transfer', 'update_progress', 'close']
        if value not in allowed_actions:
            raise serializers.ValidationError(f'无效的操作动作，允许的操作：{allowed_actions}')
        
        return value
    
    def validate(self, attrs):
        """整体验证"""
        action = attrs.get('action')
        
        # 清理HTML标签防止XSS攻击
        if attrs.get('review_comment'):
            attrs['review_comment'] = strip_tags(attrs['review_comment']).strip()
        
        if attrs.get('transferred_department'):
            attrs['transferred_department'] = strip_tags(attrs['transferred_department']).strip()
        
        if attrs.get('processing_result'):
            attrs['processing_result'] = strip_tags(attrs['processing_result']).strip()
        
        # 根据不同操作验证必需字段
        if action == 'reject' and not attrs.get('review_comment'):
            raise serializers.ValidationError({
                'review_comment': '驳回时必须填写驳回理由'
            })
        
        if action == 'transfer' and not attrs.get('transferred_department'):
            raise serializers.ValidationError({
                'transferred_department': '转交时必须指定转交部门'
            })
        
        if action in ['update_progress', 'close'] and not attrs.get('processing_result'):
            raise serializers.ValidationError({
                'processing_result': '更新进展或办结时必须填写处理结果'
            })
        
        return attrs
    
    def create(self, validated_data):
        """创建审核记录"""
        # 处理附件列表
        attachment_files = validated_data.pop('attachment_files', [])
        if attachment_files:
            validated_data['attachments'] = json.dumps(attachment_files)
        
        # 获取当前用户的工作人员信息
        request = self.context.get('request')
        if not request or not hasattr(request.user, 'staffmember'):
            raise serializers.ValidationError('只有工作人员可以执行审核操作')
        
        validated_data['reviewer'] = request.user.staffmember
        
        # 根据action自动设置status
        action_status_map = {
            'approve': 'approved',
            'reject': 'rejected',
            'transfer': 'transferred',
            'update_progress': 'in_progress',
            'close': 'completed'
        }
        validated_data['status'] = action_status_map[validated_data['action']]
        
        return super().create(validated_data)


class AIGenerateRequestSerializer(serializers.Serializer):
    """
    AI生成请求序列化器
    用于AI辅助生成意见建议
    """
    
    original_content = serializers.CharField(
        required=True,
        help_text='原始意见内容'
    )
    
    category = serializers.ChoiceField(
        choices=OpinionSuggestion.CATEGORY_CHOICES,
        required=True,
        help_text='意见分类'
    )
    
    context = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text='补充背景信息'
    )
    
    def validate_original_content(self, value):
        """验证原始内容"""
        if not value or not value.strip():
            raise serializers.ValidationError('原始意见内容不能为空')
        
        # 清理HTML标签防止XSS攻击
        cleaned_value = strip_tags(value).strip()
        
        if len(cleaned_value) < 10:
            raise serializers.ValidationError('意见内容长度不能少于10个字符')
        
        return cleaned_value


class AIGenerateResponseSerializer(serializers.Serializer):
    """
    AI生成响应序列化器
    用于返回AI生成的结果
    """
    
    generated_suggestion = serializers.CharField(
        help_text='AI生成的规范化建议'
    )
    
    key_points = serializers.ListField(
        child=serializers.CharField(),
        help_text='关键要点列表'
    )
    
    suggested_actions = serializers.ListField(
        child=serializers.CharField(),
        help_text='建议措施列表'
    )
    
    confidence_score = serializers.FloatField(
        help_text='置信度分数（0-1）'
    )


class OpinionStatusSerializer(serializers.Serializer):
    """
    意见状态序列化器
    用于状态统计和查询
    """
    
    status = serializers.CharField(help_text='状态代码')
    status_display = serializers.CharField(help_text='状态显示名称')
    count = serializers.IntegerField(help_text='该状态的意见数量')


class OpinionStatisticsSerializer(serializers.Serializer):
    """
    意见统计序列化器
    用于工作台统计数据
    """
    
    # 代表统计
    total_opinions = serializers.IntegerField(help_text='总意见数')
    pending_opinions = serializers.IntegerField(help_text='未完成意见数（除已办结外的所有意见）')
    completed_opinions = serializers.IntegerField(help_text='已完成意见数（已办结状态的意见）')
    monthly_opinions = serializers.IntegerField(help_text='本月意见数')
    
    # 工作人员统计
    pending_review_count = serializers.IntegerField(
        required=False,
        help_text='待审核意见数'
    )
    monthly_completed_count = serializers.IntegerField(
        required=False,
        help_text='本月办结数'
    )
    approved_count = serializers.IntegerField(
        required=False,
        help_text='审核通过数量'
    )
    transferred_count = serializers.IntegerField(
        required=False,
        help_text='已转交数量'
    )
    in_progress_count = serializers.IntegerField(
        required=False,
        help_text='处理中数量'
    )
    
    # 状态分布
    status_distribution = OpinionStatusSerializer(
        many=True,
        required=False,
        help_text='状态分布统计'
    )
    
    # 最近意见
    recent_opinions = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        help_text='最近5条更新的意见建议'
    )
    
    # 待审核列表（工作人员专用）
    pending_reviews = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        help_text='最近5条待审核的意见建议'
    ) 