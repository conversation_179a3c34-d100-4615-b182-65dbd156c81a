"""
履职管理权限控制

包含以下权限类：
1. IsPerformanceOwner - 履职记录所有者权限
2. IsAttachmentOwner - 附件所有者权限  
3. PerformanceRecordPermission - 履职记录操作权限
4. PerformanceAttachmentPermission - 附件操作权限

设计原则：
- 严格的权限控制，确保数据安全
- 只能操作自己的数据
- 详细的权限检查日志
- 友好的错误提示
"""

from rest_framework import permissions
from rest_framework.permissions import BasePermission
from django.contrib.auth.models import AnonymousUser
from .models import PerformanceRecord, PerformanceAttachment


class IsRepresentativeUser(BasePermission):
    """
    代表用户权限
    只有角色为representative的用户才能访问
    """
    
    def has_permission(self, request, view):
        """检查用户是否为代表"""
        # 必须是已认证的用户
        if not request.user or request.user.is_anonymous:
            return False
        
        # 必须具有representative角色
        if request.user.role != 'representative':
            return False
        
        # 必须有关联的代表信息
        if not hasattr(request.user, 'representative'):
            return False
        
        return True
    
    def has_object_permission(self, request, view, obj):
        """对象级权限检查"""
        return self.has_permission(request, view)


class IsPerformanceOwner(BasePermission):
    """
    履职记录所有者权限
    只能操作自己的履职记录
    """
    
    def has_permission(self, request, view):
        """基础权限检查"""
        # 必须是代表用户
        return (request.user and 
                not request.user.is_anonymous and
                request.user.role == 'representative' and
                hasattr(request.user, 'representative'))
    
    def has_object_permission(self, request, view, obj):
        """对象级权限检查"""
        # 确保obj是PerformanceRecord实例
        if not isinstance(obj, PerformanceRecord):
            return False
        
        # 检查是否为记录的所有者
        return obj.representative == request.user.representative


class IsAttachmentOwner(BasePermission):
    """
    附件所有者权限
    只能操作自己履职记录的附件
    """
    
    def has_permission(self, request, view):
        """基础权限检查"""
        return (request.user and 
                not request.user.is_anonymous and
                request.user.role == 'representative' and
                hasattr(request.user, 'representative'))
    
    def has_object_permission(self, request, view, obj):
        """对象级权限检查"""
        # 确保obj是PerformanceAttachment实例
        if not isinstance(obj, PerformanceAttachment):
            return False
        
        # 检查是否为附件关联履职记录的所有者
        return obj.performance_record.representative == request.user.representative


class PerformanceRecordPermission(BasePermission):
    """
    履职记录操作权限
    综合权限控制类，根据不同操作类型进行权限检查
    """
    
    def has_permission(self, request, view):
        """
        视图级权限检查
        所有操作都需要代表身份
        """
        # 检查用户认证状态
        if not request.user or request.user.is_anonymous:
            return False
        
        # 检查用户角色
        if request.user.role != 'representative':
            return False
        
        # 检查是否有关联的代表信息
        if not hasattr(request.user, 'representative'):
            return False
        
        # 检查代表信息是否有效
        try:
            representative = request.user.representative
            if not representative.user.is_active:
                return False
        except Exception:
            return False
        
        return True
    
    def has_object_permission(self, request, view, obj):
        """
        对象级权限检查
        根据HTTP方法进行不同的权限检查
        """
        # 确保对象类型正确
        if not isinstance(obj, PerformanceRecord):
            return False
        
        # 获取当前用户的代表信息
        try:
            user_representative = request.user.representative
        except Exception:
            return False
        
        # 检查所有权
        if obj.representative != user_representative:
            return False
        
        # 根据HTTP方法进行具体权限检查
        if request.method in permissions.SAFE_METHODS:
            # GET, HEAD, OPTIONS - 读取权限
            return True
        elif request.method in ['PUT', 'PATCH']:
            # 更新权限 - 可以更新自己的记录
            return True
        elif request.method == 'DELETE':
            # 删除权限 - 可以删除自己的记录
            # 但可以添加额外的业务逻辑，比如不允许删除有重要附件的记录
            return True
        else:
            # 其他方法默认拒绝
            return False


class PerformanceAttachmentPermission(BasePermission):
    """
    履职记录附件操作权限
    控制附件的上传、下载、删除等操作
    """
    
    def has_permission(self, request, view):
        """
        视图级权限检查
        """
        # 检查用户认证状态
        if not request.user or request.user.is_anonymous:
            return False
        
        # 检查用户角色
        if request.user.role != 'representative':
            return False
        
        # 检查是否有关联的代表信息
        if not hasattr(request.user, 'representative'):
            return False
        
        return True
    
    def has_object_permission(self, request, view, obj):
        """
        对象级权限检查
        """
        # 确保对象类型正确
        if not isinstance(obj, PerformanceAttachment):
            return False
        
        # 获取当前用户的代表信息
        try:
            user_representative = request.user.representative
        except Exception:
            return False
        
        # 检查附件所属履职记录的所有权
        if obj.performance_record.representative != user_representative:
            return False
        
        # 根据HTTP方法进行权限检查
        if request.method in permissions.SAFE_METHODS:
            # 读取权限
            return True
        elif request.method == 'DELETE':
            # 删除权限
            return True
        elif request.method in ['PUT', 'PATCH']:
            # 更新权限（通常只允许更新排序等非核心字段）
            return True
        else:
            return False


class ReadOnlyOrOwnerPermission(BasePermission):
    """
    只读或所有者权限
    管理员可以只读查看所有记录，代表只能操作自己的记录
    """
    
    def has_permission(self, request, view):
        """视图级权限检查"""
        if not request.user or request.user.is_anonymous:
            return False
        
        # 管理员用户可以只读访问
        if request.user.is_staff and request.method in permissions.SAFE_METHODS:
            return True
        
        # 代表用户的权限检查
        if request.user.role == 'representative' and hasattr(request.user, 'representative'):
            return True
        
        return False
    
    def has_object_permission(self, request, view, obj):
        """对象级权限检查"""
        # 管理员用户只读权限
        if request.user.is_staff and request.method in permissions.SAFE_METHODS:
            return True
        
        # 代表用户权限检查
        if (request.user.role == 'representative' and 
            hasattr(request.user, 'representative')):
            
            # 获取对象的所有者
            if isinstance(obj, PerformanceRecord):
                owner = obj.representative
            elif isinstance(obj, PerformanceAttachment):
                owner = obj.performance_record.representative
            else:
                return False
            
            # 检查所有权
            return owner == request.user.representative
        
        return False


class FileUploadPermission(BasePermission):
    """
    文件上传权限
    控制文件上传的安全性
    """
    
    def has_permission(self, request, view):
        """
        文件上传权限检查
        """
        # 必须是已认证的代表用户
        if not request.user or request.user.is_anonymous:
            return False
        
        if request.user.role != 'representative':
            return False
        
        if not hasattr(request.user, 'representative'):
            return False
        
        # 检查用户状态
        if not request.user.is_active:
            return False
        
        return True
    
    def check_upload_limits(self, request, performance_record_id, file_type):
        """
        检查上传限制
        返回 (是否允许, 错误信息)
        """
        try:
            # 获取履职记录
            record = PerformanceRecord.objects.get(id=performance_record_id)
            
            # 检查所有权
            if record.representative != request.user.representative:
                return False, '您只能向自己的履职记录上传附件'
            
            # 检查文件数量限制
            if not record.can_add_attachment(file_type):
                limits = PerformanceAttachment.get_file_type_limits()
                max_count = limits.get(file_type, 0)
                return False, f'{file_type}类型的文件已达到上限（{max_count}个）'
            
            return True, ''
            
        except PerformanceRecord.DoesNotExist:
            return False, '履职记录不存在'
        except Exception as e:
            return False, f'权限检查失败：{str(e)}'


class SafeMethodsOrOwnerPermission(BasePermission):
    """
    安全方法或所有者权限
    允许安全方法（GET, HEAD, OPTIONS）的访问，其他方法需要所有者权限
    """
    
    def has_permission(self, request, view):
        """视图级权限检查"""
        # 安全方法允许通过
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 其他方法需要代表身份
        return (request.user and 
                not request.user.is_anonymous and
                request.user.role == 'representative' and
                hasattr(request.user, 'representative'))
    
    def has_object_permission(self, request, view, obj):
        """对象级权限检查"""
        # 安全方法允许通过
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 检查所有权
        if isinstance(obj, PerformanceRecord):
            return obj.representative == request.user.representative
        elif isinstance(obj, PerformanceAttachment):
            return obj.performance_record.representative == request.user.representative
        
        return False


# 权限组合预设
class PerformanceReadWritePermission(BasePermission):
    """
    履职记录读写权限组合
    结合了认证、角色和所有权检查
    """
    
    def has_permission(self, request, view):
        """基础权限检查"""
        return IsRepresentativeUser().has_permission(request, view)
    
    def has_object_permission(self, request, view, obj):
        """对象权限检查"""
        if isinstance(obj, PerformanceRecord):
            return IsPerformanceOwner().has_object_permission(request, view, obj)
        elif isinstance(obj, PerformanceAttachment):
            return IsAttachmentOwner().has_object_permission(request, view, obj)
        return False 