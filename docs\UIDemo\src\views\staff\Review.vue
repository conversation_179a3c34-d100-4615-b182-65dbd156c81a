<template>
  <div class="review-container">
    <div class="page-header">
      <h2>意见建议审核</h2>
      <p>审核人大代表提交的群众意见建议</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-container">
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.submitted }}</div>
              <div class="stat-label">待审核</div>
            </div>
            <el-icon class="stat-icon" style="color: #e6a23c;"><Clock /></el-icon>
          </el-card>
        </div>
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.approved }}</div>
              <div class="stat-label">已审核通过</div>
            </div>
            <el-icon class="stat-icon" style="color: #67c23a;"><CircleCheck /></el-icon>
          </el-card>
        </div>
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.transferred }}</div>
              <div class="stat-label">已转交</div>
            </div>
            <el-icon class="stat-icon" style="color: #409eff;"><Share /></el-icon>
          </el-card>
        </div>
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.completed }}</div>
              <div class="stat-label">已办结</div>
            </div>
            <el-icon class="stat-icon" style="color: #67c23a;"><CircleCheck /></el-icon>
          </el-card>
        </div>
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.overdue }}</div>
              <div class="stat-label">超期未办结</div>
            </div>
            <el-icon class="stat-icon" style="color: #f56c6c;"><Warning /></el-icon>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-card>
        <el-form :model="filterForm" inline>
          <el-form-item label="状态筛选">
            <el-select v-model="filterForm.status" placeholder="全部状态" style="width: 150px;" @change="loadOpinions">
              <el-option label="全部状态" value="" />
              <el-option label="待审核" value="submitted" />
              <el-option label="审核通过" value="approved" />
              <el-option label="需要修改" value="rejected" />
            </el-select>
          </el-form-item>
          <el-form-item label="分类筛选">
            <el-select v-model="filterForm.category" placeholder="全部分类" style="width: 150px;" @change="loadOpinions">
              <el-option label="全部分类" value="" />
              <el-option label="民生保障" value="民生保障" />
              <el-option label="城市建设" value="城市建设" />
              <el-option label="交通出行" value="交通出行" />
              <el-option label="环境保护" value="环境保护" />
              <el-option label="教育医疗" value="教育医疗" />
              <el-option label="社会治安" value="社会治安" />
              <el-option label="经济发展" value="经济发展" />
            </el-select>
          </el-form-item>
          <el-form-item label="提交人">
            <el-input 
              v-model="filterForm.submitter" 
              placeholder="输入代表姓名" 
              style="width: 150px;"
              @input="loadOpinions"
              clearable
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 意见列表 -->
    <el-card>
      <el-table :data="opinionsList" v-loading="loading" @row-click="viewOpinion">
        <el-table-column prop="title" label="意见建议标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            <el-tag :type="getCategoryColor(row.category)">{{ row.category }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="submitterName" label="提交代表" width="120" />
        <el-table-column prop="submitDate" label="提交时间" width="120" />
        <el-table-column prop="transferDate" label="转交时间" width="120">
          <template #default="{ row }">
            <span>{{ row.transferDate || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="lastUpdateDate" label="最后更新" width="120">
          <template #default="{ row }">
            <span>{{ row.lastUpdateDate || row.submitDate }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">{{ getStatusText(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click.stop="viewOpinion(row)">
              查看
            </el-button>
            <el-button 
              v-if="row.status === 'submitted'" 
              size="small" 
              type="primary" 
              @click.stop="reviewOpinion(row)"
            >
              审核
            </el-button>
            <el-button 
              v-if="row.status === 'approved'" 
              size="small" 
              type="success" 
              @click.stop="transferOpinion(row)"
            >
              转交
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="意见详情"
      width="900px"
    >
      <div v-if="currentOpinion" class="opinion-detail">
        <el-row :gutter="20">
          <el-col :span="16">
            <div class="detail-section">
              <h3>基本信息</h3>
              <div class="detail-grid">
                <div class="detail-item">
                  <span class="label">意见标题：</span>
                  <span class="value">{{ currentOpinion.title }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">意见分类：</span>
                  <el-tag :type="getCategoryColor(currentOpinion.category)">{{ currentOpinion.category }}</el-tag>
                </div>
                <div class="detail-item">
                  <span class="label">提交代表：</span>
                  <span class="value">{{ currentOpinion.submitterName }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">反映人：</span>
                  <span class="value">{{ currentOpinion.reflectorInfo }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">提交时间：</span>
                  <span class="value">{{ currentOpinion.submitDate }}</span>
                </div>
                <div v-if="currentOpinion.transferDate" class="detail-item">
                  <span class="label">转交时间：</span>
                  <span class="value">{{ currentOpinion.transferDate }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">最后更新：</span>
                  <span class="value">{{ currentOpinion.lastUpdateDate || currentOpinion.submitDate }}</span>
                </div>
              </div>
            </div>

            <div class="detail-section">
              <h3>意见建议内容</h3>
              <div class="content-box">{{ currentOpinion.content }}</div>
            </div>

            <div v-if="currentOpinion.aiSuggestion" class="detail-section">
              <h3>AI生成内容</h3>
              <div class="ai-suggestion">{{ currentOpinion.aiSuggestion }}</div>
            </div>

            <div v-if="currentOpinion.suggestion" class="detail-section">
              <h3>代表最终意见建议</h3>
              <div class="content-box">{{ currentOpinion.suggestion }}</div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="action-panel">
              <h3>操作面板</h3>
              <div v-if="currentOpinion.status === 'submitted'" class="action-buttons">
                <el-button type="success" @click="approveOpinion" block>
                  <el-icon><Check /></el-icon>
                  审核通过
                </el-button>
                <el-button type="warning" @click="showRejectDialog" block>
                  <el-icon><Close /></el-icon>
                  需要修改
                </el-button>
              </div>
              
              <div v-if="currentOpinion.status === 'approved'" class="action-buttons">
                <el-button type="primary" @click="showTransferDialog" block>
                  <el-icon><Share /></el-icon>
                  转交部门
                </el-button>
              </div>

              <div class="status-info">
                <h4>当前状态</h4>
                <el-tag :type="getStatusColor(currentOpinion.status)" size="large">
                  {{ getStatusText(currentOpinion.status) }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="reviewDialogVisible"
      title="意见建议审核"
      width="600px"
    >
      <el-form
        ref="reviewFormRef"
        :model="reviewForm"
        :rules="reviewRules"
        label-width="120px"
      >
        <el-form-item label="审核结果" prop="result">
          <el-radio-group v-model="reviewForm.result">
            <el-radio value="approved">审核通过</el-radio>
            <el-radio value="rejected">需要修改</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="审核意见" prop="reviewComment">
          <el-input
            v-model="reviewForm.reviewComment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见或修改建议"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>

        <el-form-item v-if="reviewForm.result === 'approved'" label="建议转交部门">
          <el-select v-model="reviewForm.targetDepartment" placeholder="选择相关部门" style="width: 100%">
            <el-option label="城管局" value="城管局" />
            <el-option label="交通局" value="交通局" />
            <el-option label="环保局" value="环保局" />
            <el-option label="教育局" value="教育局" />
            <el-option label="卫健委" value="卫健委" />
            <el-option label="住建局" value="住建局" />
            <el-option label="民政局" value="民政局" />
            <el-option label="公安局" value="公安局" />
            <el-option label="其他部门" value="其他部门" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="reviewDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitReview">提交审核</el-button>
      </template>
    </el-dialog>

    <!-- 转交对话框 -->
    <el-dialog
      v-model="transferDialogVisible"
      title="转交部门"
      width="500px"
    >
      <el-form
        ref="transferFormRef"
        :model="transferForm"
        :rules="transferRules"
        label-width="100px"
      >
        <el-form-item label="转交部门" prop="department">
          <el-select v-model="transferForm.department" placeholder="请选择转交部门" style="width: 100%">
            <el-option label="城管局" value="城管局" />
            <el-option label="交通局" value="交通局" />
            <el-option label="环保局" value="环保局" />
            <el-option label="教育局" value="教育局" />
            <el-option label="卫健委" value="卫健委" />
            <el-option label="住建局" value="住建局" />
            <el-option label="民政局" value="民政局" />
            <el-option label="公安局" value="公安局" />
            <el-option label="其他部门" value="其他部门" />
          </el-select>
        </el-form-item>

        <el-form-item label="转交说明" prop="transferNote">
          <el-input
            v-model="transferForm.transferNote"
            type="textarea"
            :rows="3"
            placeholder="请输入转交说明"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="期望回复时间">
          <el-date-picker
            v-model="transferForm.expectedDate"
            type="date"
            placeholder="选择期望回复时间"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="transferDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitTransfer">确认转交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Clock, CircleCheck, Share, Check, Close, Warning } from '@element-plus/icons-vue'

const route = useRoute()

// 数据状态
const loading = ref(false)
const opinionsList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 统计数据
const stats = ref({
  submitted: 0,
  approved: 0,
  transferred: 0,
  completed: 0,
  overdue: 0
})

// 筛选条件
const filterForm = reactive({
  status: '',
  category: '',
  submitter: ''
})

// 对话框状态
const viewDialogVisible = ref(false)
const reviewDialogVisible = ref(false)
const transferDialogVisible = ref(false)

// 表单引用
const reviewFormRef = ref()
const transferFormRef = ref()

// 当前意见
const currentOpinion = ref(null)

// 审核表单
const reviewForm = reactive({
  result: 'approved',
  reviewComment: '',
  targetDepartment: ''
})

// 转交表单
const transferForm = reactive({
  department: '',
  transferNote: '',
  expectedDate: ''
})

// 表单验证规则
const reviewRules = {
  result: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  reviewComment: [
    { required: true, message: '请输入审核意见', trigger: 'blur' }
  ]
}

const transferRules = {
  department: [
    { required: true, message: '请选择转交部门', trigger: 'change' }
  ],
  transferNote: [
    { required: true, message: '请输入转交说明', trigger: 'blur' }
  ]
}

// 获取分类颜色
const getCategoryColor = (category) => {
  const colorMap = {
    '民生保障': 'primary',
    '城市建设': 'success',
    '交通出行': 'warning',
    '环境保护': 'info',
    '教育医疗': 'danger',
    '社会治安': '',
    '经济发展': 'success'
  }
  return colorMap[category] || ''
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'submitted': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'transferred': 'primary',
    'processing': 'info',
    'completed': 'success'
  }
  return colorMap[status] || ''
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'submitted': '待审核',
    'approved': '审核通过',
    'rejected': '需要修改',
    'transferred': '已转交',
    'processing': '处理中',
    'completed': '已办结'
  }
  return textMap[status] || status
}

// 查看意见详情
const viewOpinion = (row) => {
  currentOpinion.value = row
  viewDialogVisible.value = true
}

// 审核意见
const reviewOpinion = (row) => {
  currentOpinion.value = row
  Object.assign(reviewForm, {
    result: 'approved',
    reviewComment: '',
    targetDepartment: ''
  })
  reviewDialogVisible.value = true
}

// 批准意见
const approveOpinion = () => {
  Object.assign(reviewForm, {
    result: 'approved',
    reviewComment: '',
    targetDepartment: ''
  })
  reviewDialogVisible.value = true
}

// 显示驳回对话框
const showRejectDialog = () => {
  Object.assign(reviewForm, {
    result: 'rejected',
    reviewComment: '',
    targetDepartment: ''
  })
  reviewDialogVisible.value = true
}

// 显示转交对话框
const showTransferDialog = () => {
  Object.assign(transferForm, {
    department: '',
    transferNote: '',
    expectedDate: ''
  })
  transferDialogVisible.value = true
}

// 转交意见
const transferOpinion = (row) => {
  currentOpinion.value = row
  showTransferDialog()
}

// 提交审核
const submitReview = async () => {
  if (!reviewFormRef.value) return
  
  const valid = await reviewFormRef.value.validate().catch(() => false)
  if (!valid) return

  try {
    // 模拟审核操作
    const index = opinionsList.value.findIndex(item => item.id === currentOpinion.value.id)
    if (index !== -1) {
      opinionsList.value[index].status = reviewForm.result
      opinionsList.value[index].reviewComment = reviewForm.reviewComment
      opinionsList.value[index].reviewDate = new Date().toLocaleDateString()
      opinionsList.value[index].reviewer = '李四'
      opinionsList.value[index].lastUpdateDate = new Date().toLocaleDateString()
      
      if (reviewForm.result === 'approved' && reviewForm.targetDepartment) {
        opinionsList.value[index].suggestedDepartment = reviewForm.targetDepartment
      }
    }
    
    ElMessage.success('审核完成')
    reviewDialogVisible.value = false
    viewDialogVisible.value = false
    loadOpinions()
  } catch (error) {
    ElMessage.error('审核失败，请重试')
  }
}

// 提交转交
const submitTransfer = async () => {
  if (!transferFormRef.value) return
  
  const valid = await transferFormRef.value.validate().catch(() => false)
  if (!valid) return

  try {
    // 模拟转交操作
    const index = opinionsList.value.findIndex(item => item.id === currentOpinion.value.id)
    if (index !== -1) {
      opinionsList.value[index].status = 'transferred'
      opinionsList.value[index].transferDepartment = transferForm.department
      opinionsList.value[index].transferNote = transferForm.transferNote
      opinionsList.value[index].transferDate = new Date().toLocaleDateString()
      opinionsList.value[index].expectedDate = transferForm.expectedDate ? 
        new Date(transferForm.expectedDate).toLocaleDateString() : ''
      opinionsList.value[index].lastUpdateDate = new Date().toLocaleDateString()
    }
    
    ElMessage.success('转交成功')
    transferDialogVisible.value = false
    viewDialogVisible.value = false
    loadOpinions()
  } catch (error) {
    ElMessage.error('转交失败，请重试')
  }
}

// 计算统计数据
const calculateStats = (data) => {
  stats.value.submitted = data.filter(item => item.status === 'submitted').length
  stats.value.approved = data.filter(item => item.status === 'approved').length
  stats.value.transferred = data.filter(item => item.status === 'transferred').length
  stats.value.completed = data.filter(item => item.status === 'completed').length
  
  // 计算超期未办结：转交时间超过3个月且未办结
  const currentDate = new Date()
  stats.value.overdue = data.filter(item => {
    if (!item.transferDate || item.status === 'completed') return false
    
    // 计算转交时间是否超过3个月
    const transferDate = new Date(item.transferDate)
    const diffInMonths = (currentDate.getFullYear() - transferDate.getFullYear()) * 12 + 
                        (currentDate.getMonth() - transferDate.getMonth())
    
    return diffInMonths >= 3
  }).length
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  loadOpinions()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadOpinions()
}

// 加载意见列表
const loadOpinions = () => {
  loading.value = true
  
  // 模拟数据
  setTimeout(() => {
    let mockData = [
      {
        id: 1,
        title: '关于小区停车难问题',
        category: '城市建设',
        submitterName: '张三',
        reflectorInfo: '张三 13812345678',
        content: '我们小区停车位严重不足，经常出现车辆乱停乱放的情况，希望能够增设停车位或规范停车管理。',
        aiSuggestion: '建议联系城管部门和物业公司进行实地调研，评估增设停车位的可行性。',
        suggestion: '建议协调相关部门，制定停车位增设计划，同时加强停车管理。',
        status: 'submitted',
        submitDate: '2024-12-20',
        lastUpdateDate: '2024-12-20'
      },
      {
        id: 2,
        title: '建议增设儿童游乐设施',
        category: '民生保障',
        submitterName: '王五',
        reflectorInfo: '李四 13987654321',
        content: '社区内缺少儿童游乐设施，希望能够在公园内增设一些适合儿童的娱乐设备。',
        status: 'approved',
        submitDate: '2024-12-18',
        reviewDate: '2024-12-19',
        reviewer: '李四',
        reviewComment: '意见合理，建议转交相关部门处理',
        lastUpdateDate: '2024-12-19'
      },
      {
        id: 3,
        title: '交通信号灯时间调整建议',
        category: '交通出行',
        submitterName: '张三',
        reflectorInfo: '王五 13511112222',
        content: '某路口的交通信号灯绿灯时间过短，经常造成交通拥堵，建议适当延长绿灯时间。',
        status: 'transferred',
        submitDate: '2024-12-10',
        reviewDate: '2024-12-10',
        transferDate: '2024-12-11',
        transferDepartment: '交通局',
        transferNote: '需要实地调研信号灯配时情况',
        lastUpdateDate: '2024-12-11'
      },
      {
        id: 4,
        title: '社区环境卫生整治',
        category: '环境保护',
        submitterName: '李明',
        reflectorInfo: '赵六 13699887766',
        content: '社区垃圾分类不到位，环境卫生需要改善，建议加强宣传和管理。',
        status: 'transferred',
        submitDate: '2024-09-15',
        reviewDate: '2024-09-16',
        transferDate: '2024-09-17',
        transferDepartment: '环保局',
        transferNote: '需要制定环境整治方案',
        lastUpdateDate: '2024-10-15'
      },
      {
        id: 5,
        title: '教育资源配置优化',
        category: '教育医疗',
        submitterName: '陈华',
        reflectorInfo: '孙七 13788996655',
        content: '建议优化学校资源配置，改善教学条件，提升教育质量。',
        status: 'completed',
        submitDate: '2024-08-20',
        reviewDate: '2024-08-21',
        transferDate: '2024-08-22',
        transferDepartment: '教育局',
        transferNote: '需要评估教育资源现状',
        completedDate: '2024-11-20',
        lastUpdateDate: '2024-11-20'
      },
      {
        id: 6,
        title: '老旧小区改造',
        category: '城市建设',
        submitterName: '王芳',
        reflectorInfo: '周八 13677889900',
        content: '老旧小区基础设施老化，建议进行综合改造，改善居住环境。',
        status: 'transferred',
        submitDate: '2024-08-10',
        reviewDate: '2024-08-11',
        transferDate: '2024-08-12',
        transferDepartment: '住建局',
        transferNote: '需要制定改造计划和预算',
        lastUpdateDate: '2024-09-12'
      }
    ]
    
    // 先计算统计数据（使用完整数据）
    calculateStats(mockData)
    
    // 再应用筛选条件
    if (filterForm.status) {
      mockData = mockData.filter(item => item.status === filterForm.status)
    }
    if (filterForm.category) {
      mockData = mockData.filter(item => item.category === filterForm.category)
    }
    if (filterForm.submitter) {
      mockData = mockData.filter(item => 
        item.submitterName.includes(filterForm.submitter)
      )
    }
    
    opinionsList.value = mockData
    total.value = mockData.length
    
    loading.value = false
  }, 500)
}

// 初始化
onMounted(() => {
  // 如果URL中有id参数，直接打开对应意见
  if (route.query.id) {
    // 这里可以根据id加载特定意见
  }
  
  loadOpinions()
})
</script>

<style scoped>
.review-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: var(--china-red);
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  margin: 0;
}

.stats-section {
  margin-bottom: 20px;
}

.stats-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  flex: 1;
  min-width: 200px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 16px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(200, 16, 46, 0.15);
}

.stat-card .el-card__body {
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.stat-content {
  text-align: center;
  margin-bottom: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: var(--china-red);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  white-space: nowrap;
}

.stat-icon {
  font-size: 28px;
  opacity: 0.8;
}

/* 响应式调整 */
@media (min-width: 1200px) {
  .stat-item {
    min-width: 220px;
  }
  
  .stat-card .el-card__body {
    padding: 20px;
  }
  
  .stat-content {
    margin-bottom: 12px;
  }
  
  .stat-number {
    font-size: 28px;
  }
  
  .stat-label {
    font-size: 14px;
    margin-top: 5px;
  }
  
  .stat-icon {
    font-size: 32px;
  }
}

@media (max-width: 992px) {
  .stat-item {
    min-width: 180px;
  }
}

@media (max-width: 768px) {
  .stats-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .stat-item {
    flex: none;
    min-width: auto;
  }
  
  .stat-card {
    margin-bottom: 0;
  }
  
  .stat-card .el-card__body {
    padding: 12px;
  }
  
  .stat-content {
    margin-bottom: 6px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .stat-icon {
    font-size: 24px;
  }
}

@media (max-width: 576px) {
  .stats-container {
    gap: 8px;
  }
  
  .stat-card .el-card__body {
    padding: 10px;
  }
  
  .stat-content {
    margin-bottom: 4px;
  }
}

.filter-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.opinion-detail {
  line-height: 1.6;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  color: var(--china-red);
  margin-bottom: 12px;
  font-size: 16px;
  border-bottom: 2px solid var(--china-red);
  padding-bottom: 4px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  color: var(--text-color);
  min-width: 80px;
  margin-right: 10px;
}

.value {
  flex: 1;
  color: var(--text-color);
}

.content-box {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid var(--china-red);
  line-height: 1.6;
  white-space: pre-wrap;
}

.ai-suggestion {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 15px;
  color: #0369a1;
  line-height: 1.6;
  font-size: 14px;
}

.action-panel {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  height: fit-content;
}

.action-panel h3 {
  color: var(--china-red);
  margin-bottom: 15px;
  font-size: 16px;
}

.action-buttons {
  margin-bottom: 20px;
}

.action-buttons .el-button {
  margin-bottom: 10px;
}

.status-info {
  text-align: center;
}

.status-info h4 {
  margin-bottom: 10px;
  color: var(--text-color);
}

/* 表格行可点击样式 */
:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: rgba(200, 16, 46, 0.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .review-container {
    padding: 10px;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  /* 移动端隐藏部分列 */
  :deep(.el-table .el-table__cell:nth-child(3)),
  :deep(.el-table .el-table__cell:nth-child(4)),
  :deep(.el-table .el-table__cell:nth-child(6)) {
    display: none;
  }
}
</style> 