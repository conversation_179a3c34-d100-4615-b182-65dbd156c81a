<template>
  <div class="right-block-1">
    <!-- 标题区域 -->
    <div class="header">
      <!-- <div class="header-icon">🏆</div> -->
      <div class="header-title">{{ blockTitle }}</div>
    </div>
    
    <!-- 榜单内容 -->
    <div class="ranking-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">数据加载中...</div>
      </div>
      
      <!-- 榜单列表 - 轮播容器 -->
      <div v-else class="ranking-carousel">
        <div 
          class="ranking-list"
          :style="{ transform: `translateY(-${translateY}px)` }"
        >
          <div 
            v-for="(item, index) in displayList" 
            :key="item.id"
            class="ranking-item"
            :ref="el => setItemRef(el, index)"
            @click="handleItemClick(item)"
          >
            <!-- 头像 -->
            <div class="avatar">{{ item.avatar }}</div>
            
            <!-- 代表信息 -->
            <div class="info">
              <div class="name">{{ item.name }}</div>
            </div>
            
            <!-- 履职数量 -->
            <div class="count">{{ item.dutyCount }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { getDutyRanking } from '@/api/indexscreen/dutyRanking.js'

// 响应式数据
const blockTitle = ref('代表名单')
const loading = ref(false)
const rankingList = ref([])
const currentRowIndex = ref(0) // 当前显示的行索引
const rowHeight = ref(60) // 每行的高度
const carouselTimer = ref(null)
const itemRefs = ref([])
const containerHeight = ref(0) // 容器高度
const visibleRowCount = ref(0) // 可见行数量
const columnsPerRow = ref(2) // 每行列数

// 显示的列表 - 保持原始数据结构
const displayList = computed(() => {
  return rankingList.value
})

// 计算总行数
const totalRows = computed(() => {
  return Math.ceil(rankingList.value.length / columnsPerRow.value)
})

// 设置项目引用
const setItemRef = (el, index) => {
  if (el) {
    itemRefs.value[index] = el
  }
}

// 计算可见行数量
const calculateVisibleRows = () => {
  const container = document.querySelector('.right-block-1 .ranking-carousel')
  if (container && itemRefs.value[0]) {
    containerHeight.value = container.offsetHeight
    
    // 获取第一个项目的实际高度
    const firstItem = itemRefs.value[0]
    const itemRect = firstItem.getBoundingClientRect()
    
    // 由于是网格布局，行高 = 项目高度 + gap
    // gap在CSS中设置为6px
    rowHeight.value = itemRect.height + 6
    visibleRowCount.value = Math.floor(containerHeight.value / rowHeight.value)
    
    console.log('🏆 容器高度:', containerHeight.value)
    console.log('🏆 项目实际高度:', itemRect.height)
    console.log('🏆 行高度(含gap):', rowHeight.value)
    console.log('🏆 可见行数:', visibleRowCount.value)
    console.log('🏆 总行数:', totalRows.value)
    console.log('🏆 每行列数:', columnsPerRow.value)
  }
}

// 计算当前显示位置（按行计算）
const translateY = computed(() => {
  return currentRowIndex.value * rowHeight.value
})

// 获取榜单数据
const fetchRankingData = async () => {
  try {
    loading.value = true
    const response = await getDutyRanking({
      pageSize: 20,
      pageNum: 1
    })
    
    if (response.code === 200) {
      rankingList.value = response.data.list
      console.log('🏆 代表履职榜单数据加载成功:', rankingList.value.length, '条记录')
      
      // 数据加载完成后启动轮播
      await nextTick()
      // 延迟一点时间确保DOM完全渲染
      setTimeout(() => {
        calculateVisibleRows()
        startCarousel()
      }, 100)
    } else {
      console.error('❌ 获取榜单数据失败:', response.message)
    }
  } catch (error) {
    console.error('❌ 榜单数据请求异常:', error)
  } finally {
    loading.value = false
  }
}

// 启动轮播
const startCarousel = () => {
  // 如果总行数小于等于可见行数，不需要轮播
  if (totalRows.value <= visibleRowCount.value) {
    console.log('🏆 行数不足，无需轮播. 总行数:', totalRows.value, '可见行数:', visibleRowCount.value)
    return
  }
  
  carouselTimer.value = setInterval(() => {
    // 按行滚动，确保不会出现空白区域
    const nextRowIndex = currentRowIndex.value + 1
    
    // 如果下一行位置会导致底部空白，则重新开始
    // 判断条件：当前行索引 + 可见行数 >= 总行数时重新开始
    if (nextRowIndex + visibleRowCount.value > totalRows.value) {
      currentRowIndex.value = 0
      console.log('🏆 重新开始轮播. 下一行索引:', nextRowIndex, '可见行数:', visibleRowCount.value, '总行数:', totalRows.value)
    } else {
      currentRowIndex.value = nextRowIndex
      console.log('🏆 轮播到行索引:', currentRowIndex.value)
    }
  }, 3000) // 每3秒切换一次
}

// 停止轮播
const stopCarousel = () => {
  if (carouselTimer.value) {
    clearInterval(carouselTimer.value)
    carouselTimer.value = null
  }
}

// 处理榜单项点击事件
const handleItemClick = (item) => {
  console.log('点击了代表:', item.name, '履职数量:', item.dutyCount)
}

// 组件挂载
onMounted(async () => {
  await fetchRankingData()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopCarousel()
})
</script>

<style scoped>
/* 主容器 - 使用flex布局 */
.right-block-1 {
  /* background: rgba(60, 24, 24, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(220, 20, 60, 0.15); */
  border-radius: 12px;
  padding: 16px;
  color: #ffffff;
  transition: all 0.3s ease;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.right-block-1:hover {
  background: rgba(0, 20, 40, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 13, 74, 0.2);
  border-color: rgba(2, 166, 181, 0.3);
}

/* 头部区域 */
.header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  position: relative;
  flex-shrink: 0;
}

/* 美观的分割线设计 */
.header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(73, 188, 247, 0.3) 10%,
    rgba(73, 188, 247, 0.8) 30%,
    rgba(73, 188, 247, 1) 50%,
    rgba(73, 188, 247, 0.8) 70%,
    rgba(73, 188, 247, 0.3) 90%,
    transparent 100%
  );
  border-radius: 2px;
  box-shadow: 
    0 0 8px rgba(73, 188, 247, 0.4),
    0 2px 4px rgba(73, 188, 247, 0.2);
  animation: glow-pulse 3s ease-in-out infinite alternate;
}

/* 添加一个细的上层装饰线 */
.header::before {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  border-radius: 1px;
  animation: shimmer 2s ease-in-out infinite alternate;
}

/* 分割线发光动画 */
@keyframes glow-pulse {
  0% {
    box-shadow: 
      0 0 8px rgba(73, 188, 247, 0.4),
      0 2px 4px rgba(73, 188, 247, 0.2);
  }
  100% {
    box-shadow: 
      0 0 12px rgba(73, 188, 247, 0.6),
      0 2px 6px rgba(73, 188, 247, 0.3);
  }
}

/* 上层装饰线闪烁动画 */
@keyframes shimmer {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.header-icon {
  font-size: 1.1rem;
  filter: drop-shadow(0 0 4px rgba(73, 188, 247, 0.8));
}

.header-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 榜单容器 */
.ranking-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 轮播容器 */
.ranking-carousel {
  flex: 1;
  overflow: hidden;
  position: relative;
  min-height: 120px; /* 确保至少能显示2行 */
}

/* 榜单列表 */
.ranking-list {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 两列布局 */
  gap: 6px;
  transition: transform 0.8s ease-in-out;
  grid-auto-rows: min-content; /* 行高自适应内容 */
}

/* 榜单项 */
.ranking-item {
  display: flex;
  align-items: center;
  gap: 6px;
  /* padding: 6px 8px; */
  /* background: rgba(60, 24, 24, 0.3); */
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  /* border: 1px solid rgba(220, 20, 60, 0.08); */
  min-height: 40px;
  flex-shrink: 0;
}

.ranking-item:hover {
  background: rgba(0, 20, 40, 0.5);
  transform: translateY(-1px);
  border-color: rgba(2, 166, 181, 0.2);
}

/* 头像 */
.avatar {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

/* 代表信息 */
.info {
  flex: 1;
  min-width: 0;
}

.name {
  font-size: 0.8rem;
  font-weight: 500;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

/* 履职数量 - 放在右侧与名字同一水平线 */
.count {
  font-size: 0.75rem;
  color: #49bcf7;
  font-weight: 600;
  flex-shrink: 0;
  min-width: 30px;
  text-align: right;
}

/* 加载状态样式 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  min-height: 120px;
}

.loading-spinner {
  width: 28px;
  height: 28px;
  border: 2px solid rgba(2, 166, 181, 0.2);
  border-top: 2px solid rgba(2, 166, 181, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移除滚动条相关样式，使用轮播替代 */

/* 响应式设计 */
@media (max-width: 1200px) {
  .right-block-1 {
    padding: 12px;
  }
  
  .ranking-list {
    gap: 6px;
  }
  
  .ranking-item {
    padding: 6px;
  }
  
  .avatar {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }
  
  .name {
    font-size: 0.75rem;
  }
  
  .count {
    font-size: 0.65rem;
  }
}

@media (max-width: 768px) {
  .ranking-list {
    grid-template-columns: 1fr; /* 小屏幕时改为单列 */
    gap: 4px;
  }
  
  .ranking-item {
    padding: 8px;
    min-height: 48px;
  }
  
  .avatar {
    width: 26px;
    height: 26px;
    font-size: 13px;
  }
  
  .count {
    font-size: 0.7rem;
    min-width: 25px;
  }
}

/* 加载动画效果 */
.right-block-1::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 193, 7, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.right-block-1:hover::after {
  left: 100%;
}
</style> 