<template>
  <div class="representative-summary-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>代表工作总结</h2>
      <p>查看本站点各个代表的年度履职AI分析展示</p>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <div class="filter-section">
        <div class="filter-item">
          <label>分析年度：</label>
          <el-date-picker
            v-model="selectedYear"
            type="year"
            placeholder="选择年度"
            value-format="YYYY"
            style="margin-left: 10px;"
            @change="loadRepresentatives"
          />
        </div>
        <div class="filter-item">
          <label>代表状态：</label>
          <el-select v-model="statusFilter" placeholder="全部状态" style="margin-left: 10px;" @change="filterRepresentatives">
            <el-option label="全部状态" value="" />
            <el-option label="已生成分析" value="analyzed" />
            <el-option label="未生成分析" value="not_analyzed" />
          </el-select>
        </div>
        <div class="filter-item">
          <el-button type="primary" @click="batchGenerate" :disabled="!selectedRepresentatives.length || isGenerating">
            <el-icon><Document /></el-icon>
            批量生成分析
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 代表列表 -->
    <el-card class="representatives-card">
      <template #header>
        <div class="card-header">
          <span>代表履职分析列表</span>
          <div class="header-actions">
            <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
            <span class="selected-count">已选中 {{ selectedRepresentatives.length }} 人</span>
          </div>
        </div>
      </template>

      <div v-loading="isLoading" class="representatives-list">
        <div v-if="!filteredRepresentatives.length" class="empty-state">
          <el-empty description="暂无代表数据" />
        </div>
        
        <div v-else>
          <div 
            v-for="representative in filteredRepresentatives" 
            :key="representative.id"
            class="representative-item"
            :class="{ 'selected': selectedRepresentatives.includes(representative.id) }"
          >
            <div class="representative-header">
              <el-checkbox 
                :model-value="selectedRepresentatives.includes(representative.id)"
                @change="handleSelectRepresentative(representative.id, $event)"
              />
              <div class="representative-info">
                <div class="representative-name">
                  <el-avatar :size="40" style="background-color: #c62d2d;">
                    {{ representative.name.charAt(0) }}
                  </el-avatar>
                  <div class="name-section">
                    <h4>{{ representative.name }}</h4>
                    <span class="representative-level">{{ representative.level }}</span>
                  </div>
                </div>
                <div class="representative-meta">
                  <el-tag size="small" type="info">{{ representative.department }}</el-tag>
                  <span class="contact">{{ representative.phone }}</span>
                </div>
              </div>
            </div>

            <div class="representative-stats">
              <div class="stat-item">
                <span class="stat-label">履职记录</span>
                <span class="stat-value">{{ representative.recordCount }}条</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">提交意见</span>
                <span class="stat-value">{{ representative.opinionCount }}条</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">分析状态</span>
                <el-tag 
                  :type="representative.analysisStatus === 'analyzed' ? 'success' : 'warning'"
                  size="small"
                >
                  {{ representative.analysisStatus === 'analyzed' ? '已生成' : '未生成' }}
                </el-tag>
              </div>
            </div>

            <div class="representative-actions">
              <el-button 
                v-if="representative.analysisStatus === 'analyzed'"
                type="primary" 
                size="small"
                @click="viewAnalysis(representative)"
              >
                <el-icon><View /></el-icon>
                查看分析
              </el-button>
              <el-button 
                type="success" 
                size="small"
                @click="generateAnalysis(representative)"
                :loading="generatingIds.includes(representative.id)"
              >
                <el-icon><Refresh /></el-icon>
                {{ representative.analysisStatus === 'analyzed' ? '重新生成' : '生成分析' }}
              </el-button>
              <el-button 
                v-if="representative.analysisStatus === 'analyzed'"
                type="info" 
                size="small"
                @click="exportAnalysis(representative)"
              >
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 批量生成进度弹窗 -->
    <el-dialog v-model="batchDialogVisible" title="批量生成进度" width="50%" :close-on-click-modal="false">
      <div class="batch-progress">
        <el-progress 
          :percentage="batchProgress" 
          :stroke-width="10"
          :text-inside="true"
          status="active"
        />
        <div class="progress-info">
          <p>正在为 {{ selectedRepresentatives.length }} 位代表生成分析报告...</p>
          <p>当前进度：{{ batchCurrentIndex + 1 }} / {{ selectedRepresentatives.length }}</p>
          <p v-if="batchCurrentName">正在处理：{{ batchCurrentName }}</p>
        </div>
      </div>
      <template #footer>
        <el-button @click="cancelBatchGenerate" :disabled="!isGenerating">取消</el-button>
      </template>
    </el-dialog>

    <!-- 分析结果查看弹窗 -->
    <el-dialog 
      v-model="analysisDialogVisible" 
      :title="`${currentRepresentative?.name} - ${selectedYear}年度履职分析报告`"
      width="85%"
      class="analysis-dialog"
    >
      <div v-if="currentAnalysis" class="analysis-content">
        <!-- 代表信息头部 -->
        <div class="analysis-header">
          <div class="header-content">
            <el-avatar :size="60" style="background-color: #c62d2d;">
              {{ currentRepresentative?.name.charAt(0) }}
            </el-avatar>
            <div class="header-info">
              <h1>{{ currentRepresentative?.name }}</h1>
              <h2>{{ currentRepresentative?.level }}</h2>
              <p class="subtitle">{{ currentRepresentative?.department }}</p>
            </div>
            <div class="header-decoration">
              <div class="year-badge">{{ selectedYear }}年度</div>
            </div>
          </div>
        </div>

        <!-- 核心指标 -->
        <div class="analysis-metrics">
          <h3 class="section-title">
            <el-icon><DataLine /></el-icon>
            核心履职指标
          </h3>
          <el-row :gutter="20">
            <el-col :span="6" v-for="metric in currentAnalysis.metrics" :key="metric.label">
              <div class="metric-card">
                <div class="metric-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value">{{ metric.value }}</div>
                  <div class="metric-label">{{ metric.label }}</div>
                  <div class="metric-trend positive" v-if="metric.trend">
                    +{{ metric.trend }}%
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 履职亮点 -->
        <div class="analysis-highlights">
          <h3 class="section-title">
            <el-icon><Star /></el-icon>
            履职突出亮点
          </h3>
          <div class="highlights-grid">
            <div v-for="(highlight, index) in currentAnalysis.highlights" :key="index" class="highlight-card">
              <div class="highlight-content">
                <div class="highlight-icon">
                  <el-icon><Trophy /></el-icon>
                </div>
                <div class="highlight-text">
                  <h4>亮点 {{ index + 1 }}</h4>
                  <p>{{ highlight }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI智能总结 -->
        <div class="analysis-summary">
          <h3 class="section-title">
            <el-icon><User /></el-icon>
            AI智能总结
          </h3>
          <div class="summary-card">
            <div class="summary-content">
              <div class="summary-text">
                <h4>综合评价</h4>
                <p>{{ currentAnalysis.summary }}</p>
                
                <h4>主要成就</h4>
                <ul class="achievement-list">
                  <li v-for="achievement in currentAnalysis.achievements" :key="achievement">{{ achievement }}</li>
                </ul>
                
                <h4>改进建议</h4>
                <ul class="suggestion-list">
                  <li v-for="suggestion in currentAnalysis.suggestions" :key="suggestion">{{ suggestion }}</li>
                </ul>
              </div>
              <div class="summary-decoration">
                <el-icon :size="60" color="#c62d2d"><Star /></el-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- 关键词云 -->
        <div class="keywords-section" v-if="currentAnalysis.keywords">
          <h3 class="section-title">
            <el-icon><DataLine /></el-icon>
            履职关键词
          </h3>
          <div class="keywords-card">
            <div class="keywords-cloud">
              <span 
                v-for="(keyword, index) in currentAnalysis.keywords" 
                :key="keyword.word"
                :class="`keyword-tag weight-${keyword.weight}`"
              >
                {{ keyword.word }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="actions-section">
          <div class="action-buttons">
            <el-button @click="analysisDialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="exportAnalysis(currentRepresentative)">
              <el-icon><Download /></el-icon>
              导出分析
            </el-button>
            <el-button type="success" @click="shareAnalysis(currentRepresentative)">
              <el-icon><Share /></el-icon>
              分享报告
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Document, View, Refresh, Download,
  DataLine, TrendCharts, Star, Trophy, User, Share
} from '@element-plus/icons-vue'
import { 
  getRepresentativesList, 
  generateRepresentativeAnalysis, 
  batchGenerateAnalysis,
  getRepresentativeAnalysis,
  exportAnalysisReport,
  shareAnalysisReport
} from '@/api/workAnalysis'

export default {
  name: 'RepresentativeSummary',
  components: {
    Document, View, Refresh, Download,
    DataLine, TrendCharts, Star, Trophy, User, Share
  },
  setup() {
    // 响应式数据
    const selectedYear = ref(new Date().getFullYear().toString())
    const statusFilter = ref('')
    const isLoading = ref(false)
    const isGenerating = ref(false)
    const representatives = ref([])
    const selectedRepresentatives = ref([])
    const generatingIds = ref([])
    const selectAll = ref(false)
    
    // 批量生成相关
    const batchDialogVisible = ref(false)
    const batchProgress = ref(0)
    const batchCurrentIndex = ref(0)
    const batchCurrentName = ref('')
    
    // 分析查看相关
    const analysisDialogVisible = ref(false)
    const currentRepresentative = ref(null)
    const currentAnalysis = ref(null)

    // 计算属性
    const filteredRepresentatives = computed(() => {
      if (!statusFilter.value) return representatives.value
      return representatives.value.filter(rep => rep.analysisStatus === statusFilter.value)
    })

    // 加载代表列表
    const loadRepresentatives = async () => {
      if (!selectedYear.value) return
      
      isLoading.value = true
      try {
        const response = await getRepresentativesList(selectedYear.value)
        if (response.success) {
          representatives.value = response.data
        } else {
          throw new Error(response.message || '加载失败')
        }
      } catch (error) {
        console.error('加载代表列表失败：', error)
        ElMessage.error('加载代表列表失败')
      } finally {
        isLoading.value = false
      }
    }

    // 筛选代表
    const filterRepresentatives = () => {
      selectedRepresentatives.value = []
      selectAll.value = false
    }

    // 全选/取消全选
    const handleSelectAll = (checked) => {
      if (checked) {
        selectedRepresentatives.value = filteredRepresentatives.value.map(rep => rep.id)
      } else {
        selectedRepresentatives.value = []
      }
    }

    // 选择/取消选择代表
    const handleSelectRepresentative = (id, checked) => {
      if (checked) {
        if (!selectedRepresentatives.value.includes(id)) {
          selectedRepresentatives.value.push(id)
        }
      } else {
        const index = selectedRepresentatives.value.indexOf(id)
        if (index > -1) {
          selectedRepresentatives.value.splice(index, 1)
        }
      }
      
      // 更新全选状态
      selectAll.value = selectedRepresentatives.value.length === filteredRepresentatives.value.length
    }

    // 为单个代表生成分析
    const generateAnalysis = async (representative) => {
      generatingIds.value.push(representative.id)
      
      try {
        ElMessage.info(`正在为${representative.name}生成分析报告...`)
        
        const response = await generateRepresentativeAnalysis(representative.id, selectedYear.value)
        
        if (response.success) {
          // 更新代表状态
          const index = representatives.value.findIndex(rep => rep.id === representative.id)
          if (index !== -1) {
            representatives.value[index].analysisStatus = 'analyzed'
            representatives.value[index].lastAnalysisTime = response.data.generateTime
          }
          
          ElMessage.success(`${representative.name}的分析报告生成成功！`)
        } else {
          throw new Error(response.message || '生成失败')
        }
      } catch (error) {
        console.error('生成分析失败：', error)
        ElMessage.error(`${representative.name}的分析报告生成失败`)
      } finally {
        const index = generatingIds.value.indexOf(representative.id)
        if (index > -1) {
          generatingIds.value.splice(index, 1)
        }
      }
    }

    // 批量生成分析
    const batchGenerate = async () => {
      if (!selectedRepresentatives.value.length) {
        ElMessage.warning('请选择要生成分析的代表')
        return
      }

      const result = await ElMessageBox.confirm(
        `确定要为选中的 ${selectedRepresentatives.value.length} 位代表批量生成分析报告吗？`,
        '批量生成确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).catch(() => false)

      if (!result) return

      batchDialogVisible.value = true
      isGenerating.value = true
      batchProgress.value = 0
      batchCurrentIndex.value = 0

      try {
        const response = await batchGenerateAnalysis(
          selectedRepresentatives.value, 
          selectedYear.value,
          (progress) => {
            batchProgress.value = progress.progress
            batchCurrentIndex.value = progress.current
            
            if (progress.currentId) {
              const rep = representatives.value.find(r => r.id === progress.currentId)
              batchCurrentName.value = rep ? rep.name : ''
            }
          }
        )

        if (response.success) {
          // 更新所有代表状态
          selectedRepresentatives.value.forEach(repId => {
            const index = representatives.value.findIndex(rep => rep.id === repId)
            if (index !== -1) {
              representatives.value[index].analysisStatus = 'analyzed'
              representatives.value[index].lastAnalysisTime = new Date().toLocaleString()
            }
          })

          ElMessage.success('批量生成完成！')
          selectedRepresentatives.value = []
          selectAll.value = false
        } else {
          throw new Error(response.message || '批量生成失败')
        }
      } catch (error) {
        console.error('批量生成失败：', error)
        ElMessage.error('批量生成失败')
      } finally {
        isGenerating.value = false
        batchDialogVisible.value = false
        batchCurrentName.value = ''
      }
    }

    // 取消批量生成
    const cancelBatchGenerate = () => {
      if (isGenerating.value) {
        ElMessageBox.confirm('确定要取消批量生成吗？', '取消确认')
          .then(() => {
            isGenerating.value = false
            batchDialogVisible.value = false
            ElMessage.info('已取消批量生成')
          })
          .catch(() => {})
      } else {
        batchDialogVisible.value = false
      }
    }

    // 查看分析结果
    const viewAnalysis = async (representative) => {
      currentRepresentative.value = representative
      
      try {
        const response = await getRepresentativeAnalysis(representative.id, selectedYear.value)
        if (response.success) {
          currentAnalysis.value = response.data
          analysisDialogVisible.value = true
        } else {
          throw new Error(response.message || '获取分析结果失败')
        }
      } catch (error) {
        console.error('获取分析结果失败：', error)
        ElMessage.error('获取分析结果失败')
      }
    }

    // 导出分析结果
    const exportAnalysis = async (representative) => {
      try {
        const response = await exportAnalysisReport('representative', {
          year: selectedYear.value,
          representativeId: representative.id,
          representativeName: representative.name
        })
        
        if (response.success) {
          ElMessage.success(response.message || '导出成功')
        } else {
          throw new Error(response.message || '导出失败')
        }
      } catch (error) {
        console.error('导出分析报告失败：', error)
        ElMessage.error('导出分析报告失败')
      }
    }

    // 分享分析报告
    const shareAnalysis = async (representative) => {
      try {
        const response = await shareAnalysisReport('representative', {
          year: selectedYear.value,
          representativeId: representative.id,
          representativeName: representative.name
        })
        
        if (response.success) {
          ElMessage.success(response.message || '分享链接生成成功')
        } else {
          throw new Error(response.message || '分享失败')
        }
      } catch (error) {
        console.error('分享报告失败：', error)
        ElMessage.error('分享报告失败')
      }
    }

    onMounted(() => {
      loadRepresentatives()
    })

    return {
      selectedYear,
      statusFilter,
      isLoading,
      isGenerating,
      representatives,
      filteredRepresentatives,
      selectedRepresentatives,
      generatingIds,
      selectAll,
      batchDialogVisible,
      batchProgress,
      batchCurrentIndex,
      batchCurrentName,
      analysisDialogVisible,
      currentRepresentative,
      currentAnalysis,
      loadRepresentatives,
      filterRepresentatives,
      handleSelectAll,
      handleSelectRepresentative,
      generateAnalysis,
      batchGenerate,
      cancelBatchGenerate,
      viewAnalysis,
      exportAnalysis,
      shareAnalysis
    }
  }
}
</script>

<style scoped>
.representative-summary-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  text-align: center;
}

.page-header h2 {
  color: #c62d2d;
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: bold;
}

.page-header p {
  color: #666;
  margin: 0;
  font-size: 16px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  padding: 20px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-item label {
  font-weight: bold;
  color: #333;
  white-space: nowrap;
}

.representatives-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.selected-count {
  color: #666;
  font-size: 14px;
}

.representatives-list {
  min-height: 200px;
}

.representative-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 15px;
  padding: 20px;
  background: white;
  transition: all 0.3s;
}

.representative-item:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.representative-item.selected {
  border-color: #c62d2d;
  background: #fff2f2;
}

.representative-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.representative-info {
  flex: 1;
}

.representative-name {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.name-section h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.representative-level {
  color: #666;
  font-size: 14px;
}

.representative-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.contact {
  color: #666;
  font-size: 14px;
}

.representative-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.representative-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.batch-progress {
  padding: 20px;
}

.progress-info {
  margin-top: 20px;
  text-align: center;
}

.progress-info p {
  margin: 5px 0;
  color: #666;
}

.analysis-content {
  max-height: 70vh;
  overflow-y: auto;
}

/* 分析头部 */
.analysis-header {
  margin-bottom: 30px;
  background: linear-gradient(135deg, #c62d2d 0%, #8b1e1e 100%);
  color: white;
  border-radius: 8px;
  padding: 30px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  min-height: 80px;
}

.header-info h1 {
  font-size: 24px;
  margin: 0 0 8px 0;
  font-weight: bold;
}

.header-info h2 {
  font-size: 18px;
  margin: 0 0 8px 0;
  opacity: 0.9;
}

.subtitle {
  font-size: 14px;
  opacity: 0.8;
  margin: 0;
}

.header-decoration {
  margin-left: auto;
  align-self: flex-start;
}

.year-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

/* 章节标题 */
.section-title {
  font-size: 20px;
  color: #c62d2d;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #c62d2d;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
}

.analysis-summary,
.analysis-metrics,
.analysis-highlights,
.keywords-section {
  margin-bottom: 30px;
}

.metric-card {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  border: 2px solid #f5f5f5;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.metric-card:hover {
  border-color: #c62d2d;
  transform: translateY(-5px);
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c62d2d;
  box-shadow: 0 2px 8px rgba(198, 45, 45, 0.2);
}

.metric-content {
  text-align: center;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #c62d2d;
  margin-bottom: 5px;
  line-height: 1;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.metric-trend {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: bold;
}

.metric-trend.positive {
  background: #e6f7e6;
  color: #52c41a;
}

/* 履职亮点网格 */
.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.highlight-card {
  border-left: 4px solid #c62d2d;
  background: #fff2f2;
  border-radius: 8px;
  transition: transform 0.3s ease;
  padding: 20px;
}

.highlight-card:hover {
  transform: translateY(-3px);
}

.highlight-content {
  display: flex;
  gap: 15px;
}

.highlight-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c62d2d;
  box-shadow: 0 2px 8px rgba(198, 45, 45, 0.2);
}

.highlight-text h4 {
  color: #c62d2d;
  font-size: 16px;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.highlight-text p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* AI智能总结 */
.summary-card {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  border: 2px solid #f5f5f5;
  border-radius: 8px;
  padding: 20px;
}

.summary-content {
  display: flex;
  gap: 30px;
  position: relative;
}

.summary-text {
  flex: 1;
}

.summary-text h4 {
  color: #c62d2d;
  font-size: 16px;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.summary-text p {
  color: #333;
  line-height: 1.8;
  margin-bottom: 20px;
  font-size: 15px;
}

.achievement-list,
.suggestion-list {
  padding-left: 20px;
  margin-bottom: 20px;
}

.achievement-list li,
.suggestion-list li {
  color: #555;
  line-height: 1.8;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-decoration {
  position: absolute;
  right: 20px;
  top: 20px;
}

/* 关键词云 */
.keywords-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

.keywords-cloud {
  text-align: center;
  line-height: 2;
}

.keyword-tag {
  display: inline-block;
  margin: 5px 8px;
  padding: 6px 12px;
  background: #f5f5f5;
  color: #333;
  border-radius: 16px;
  transition: all 0.3s ease;
  cursor: default;
}

.keyword-tag:hover {
  background: #c62d2d;
  color: white;
  transform: scale(1.05);
}

.keyword-tag.weight-5 {
  font-size: 18px;
  font-weight: bold;
  background: #c62d2d;
  color: white;
}

.keyword-tag.weight-4 {
  font-size: 16px;
  font-weight: bold;
  background: #e85555;
  color: white;
}

.keyword-tag.weight-3 {
  font-size: 14px;
  font-weight: 500;
  background: #ffebee;
  color: #c62d2d;
}

.keyword-tag.weight-2 {
  font-size: 13px;
  background: #f5f5f5;
  color: #666;
}

.keyword-tag.weight-1 {
  font-size: 12px;
  background: #fafafa;
  color: #999;
}

/* 操作按钮区域 */
.actions-section {
  text-align: center;
  padding: 20px 0;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  padding: 10px 20px;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 50px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .representative-summary-container {
    padding: 10px;
  }

  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .filter-item {
    justify-content: space-between;
  }

  .header-actions {
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
  }

  .representative-stats {
    flex-direction: column;
    gap: 15px;
  }

  .representative-actions {
    flex-direction: column;
  }

  .analysis-dialog {
    width: 95% !important;
  }
}
</style> 