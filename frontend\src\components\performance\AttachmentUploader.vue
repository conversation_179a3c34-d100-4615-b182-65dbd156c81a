<template>
  <div class="attachment-uploader">
    <h3 class="section-title">相关资料</h3>
    <p class="section-tip">您可以上传现场照片、录音、视频等资料（可选）</p>
    
    <!-- 现场照片上传 -->
    <div class="upload-section">
      <h4 class="upload-title">
        <el-icon><Camera /></el-icon>
        现场照片
        <span class="upload-count">({{ imageList.length }}/9)</span>
      </h4>
      <el-upload
        v-model:file-list="imageList"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="uploadData.image"
        :before-upload="beforeImageUpload"
        :on-success="onImageSuccess"
        :on-error="onUploadError"
        :on-remove="onImageRemove"
        :on-preview="onImagePreview"
        list-type="picture-card"
        :limit="9"
        accept="image/*"
        multiple
        class="image-uploader"
      >
        <el-icon><Plus /></el-icon>
        <div class="upload-text">上传照片</div>
      </el-upload>
      <div class="upload-tip">
        支持 JPG、PNG、GIF 格式，单张最大 10MB，最多 9 张
      </div>
    </div>

    <!-- 录音文件上传 -->
    <div class="upload-section">
      <h4 class="upload-title">
        <el-icon><Microphone /></el-icon>
        录音文件
        <span class="upload-count">({{ audioList.length }}/3)</span>
      </h4>
      <el-upload
        v-model:file-list="audioList"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="uploadData.audio"
        :before-upload="beforeAudioUpload"
        :on-success="onAudioSuccess"
        :on-error="onUploadError"
        :on-remove="onAudioRemove"
        :limit="3"
        accept="audio/*"
        multiple
        class="file-uploader"
      >
        <el-button type="primary" plain>
          <el-icon><Upload /></el-icon>
          选择录音文件
        </el-button>
      </el-upload>
      <div class="upload-tip">
        支持 MP3、WAV、AAC 格式，单个最大 50MB，最多 3 个
      </div>
    </div>

    <!-- 视频文件上传 -->
    <div class="upload-section">
      <h4 class="upload-title">
        <el-icon><VideoCamera /></el-icon>
        视频文件
        <span class="upload-count">({{ videoList.length }}/2)</span>
      </h4>
      <el-upload
        v-model:file-list="videoList"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="uploadData.video"
        :before-upload="beforeVideoUpload"
        :on-success="onVideoSuccess"
        :on-error="onUploadError"
        :on-remove="onVideoRemove"
        :limit="2"
        accept="video/*"
        multiple
        class="file-uploader"
      >
        <el-button type="primary" plain>
          <el-icon><Upload /></el-icon>
          选择视频文件
        </el-button>
      </el-upload>
      <div class="upload-tip">
        支持 MP4、AVI、MOV 格式，单个最大 100MB，最多 2 个
      </div>
    </div>

    <!-- 相关文档上传 -->
    <div class="upload-section">
      <h4 class="upload-title">
        <el-icon><Document /></el-icon>
        相关文档
        <span class="upload-count">({{ documentList.length }}/5)</span>
      </h4>
      <el-upload
        v-model:file-list="documentList"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="uploadData.document"
        :before-upload="beforeDocumentUpload"
        :on-success="onDocumentSuccess"
        :on-error="onUploadError"
        :on-remove="onDocumentRemove"
        :limit="5"
        accept=".pdf,.doc,.docx,.txt,.xls,.xlsx"
        multiple
        class="file-uploader"
      >
        <el-button type="primary" plain>
          <el-icon><Upload /></el-icon>
          选择文档文件
        </el-button>
      </el-upload>
      <div class="upload-tip">
        支持 PDF、Word、Excel、TXT 格式，单个最大 20MB，最多 5 个
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="图片预览"
      width="800px"
      append-to-body
    >
      <div class="preview-container">
        <img 
          :src="previewImageUrl" 
          alt="预览图片" 
          style="width: 100%; max-height: 600px; object-fit: contain;"
          @load="onImageLoadSuccess"
          @error="onImageLoadError($event, currentPreviewFile)"
          v-show="!imageLoading"
        />
        <div v-if="imageLoading" class="loading-overlay">
          <el-icon class="is-loading" size="50"><Loading /></el-icon>
          <p style="margin-top: 10px;">正在加载图片...</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Camera, Microphone, VideoCamera, Document, Plus, Upload, Loading } from '@element-plus/icons-vue'
import { performanceAttachmentAPI, performanceFileAPI } from '@/api/modules/performance'
import { API_CONFIG } from '@/api/http/config'

// Props
const props = defineProps({
  recordId: {
    type: [Number, String],
    required: true
  },
  // 初始附件列表
  initialAttachments: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['attachment-change', 'upload-success', 'upload-error'])

// 文件列表
const imageList = ref([])
const audioList = ref([])
const videoList = ref([])
const documentList = ref([])

// 图片预览
const previewVisible = ref(false)
const previewImageUrl = ref('')
const imageLoading = ref(false)
const currentPreviewFile = ref(null)

// 上传配置
const uploadAction = computed(() => {
  return performanceFileAPI.getUploadUrl()
})

const uploadHeaders = computed(() => {
  const token = localStorage.getItem('access_token')
  return {
    'Authorization': token ? `Bearer ${token}` : ''
  }
})

// 上传数据配置 - 使用computed确保响应式
const uploadData = computed(() => ({
  image: { file_type: 'image', performance_record_id: props.recordId },
  audio: { file_type: 'audio', performance_record_id: props.recordId },
  video: { file_type: 'video', performance_record_id: props.recordId },
  document: { file_type: 'document', performance_record_id: props.recordId }
}))

// 文件类型限制配置
const FILE_LIMITS = {
  image: {
    maxSize: 10 * 1024 * 1024, // 10MB
    maxCount: 9,
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
    allowedExtensions: ['jpg', 'jpeg', 'png', 'gif']
  },
  audio: {
    maxSize: 50 * 1024 * 1024, // 50MB
    maxCount: 3,
    allowedTypes: ['audio/mpeg', 'audio/wav', 'audio/aac'],
    allowedExtensions: ['mp3', 'wav', 'aac', 'm4a']
  },
  video: {
    maxSize: 100 * 1024 * 1024, // 100MB
    maxCount: 2,
    allowedTypes: ['video/mp4', 'video/avi', 'video/quicktime'],
    allowedExtensions: ['mp4', 'avi', 'mov', 'wmv']
  },
  document: {
    maxSize: 20 * 1024 * 1024, // 20MB
    maxCount: 5,
    allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'],
    allowedExtensions: ['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx']
  }
}

// 文件上传前验证
const validateFile = (file, type) => {
  const config = FILE_LIMITS[type]
  
  // 检查文件大小
  if (file.size > config.maxSize) {
    const maxSizeMB = Math.floor(config.maxSize / (1024 * 1024))
    ElMessage.error(`文件大小不能超过 ${maxSizeMB}MB`)
    return false
  }
  
  // 检查文件扩展名
  const extension = file.name.split('.').pop().toLowerCase()
  if (!config.allowedExtensions.includes(extension)) {
    ElMessage.error(`不支持的文件格式，请选择 ${config.allowedExtensions.join('、')} 格式的文件`)
    return false
  }
  
  // 检查MIME类型
  if (!config.allowedTypes.includes(file.type)) {
    ElMessage.error('文件格式验证失败，请选择正确的文件')
    return false
  }
  
  return true
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  if (imageList.value.length >= FILE_LIMITS.image.maxCount) {
    ElMessage.error(`最多只能上传 ${FILE_LIMITS.image.maxCount} 张图片`)
    return false
  }
  return validateFile(file, 'image')
}

// 录音上传前验证
const beforeAudioUpload = (file) => {
  if (audioList.value.length >= FILE_LIMITS.audio.maxCount) {
    ElMessage.error(`最多只能上传 ${FILE_LIMITS.audio.maxCount} 个录音文件`)
    return false
  }
  return validateFile(file, 'audio')
}

// 视频上传前验证
const beforeVideoUpload = (file) => {
  if (videoList.value.length >= FILE_LIMITS.video.maxCount) {
    ElMessage.error(`最多只能上传 ${FILE_LIMITS.video.maxCount} 个视频文件`)
    return false
  }
  return validateFile(file, 'video')
}

// 文档上传前验证
const beforeDocumentUpload = (file) => {
  if (documentList.value.length >= FILE_LIMITS.document.maxCount) {
    ElMessage.error(`最多只能上传 ${FILE_LIMITS.document.maxCount} 个文档文件`)
    return false
  }
  return validateFile(file, 'document')
}

// 上传成功处理
const onImageSuccess = (response, file) => {
  console.log('图片上传响应:', response)
  // 后端直接返回附件数据，包含id字段表示成功
  if (response && response.id) {
    ElMessage.success('图片上传成功')
    file.attachmentId = response.id
    emit('upload-success', { type: 'image', file, response })
    emitAttachmentChange()
  } else {
    ElMessage.error('图片上传失败：响应数据格式错误')
    // 从列表中移除失败的文件
    const index = imageList.value.findIndex(item => item.uid === file.uid)
    if (index > -1) {
      imageList.value.splice(index, 1)
    }
  }
}

const onAudioSuccess = (response, file) => {
  console.log('录音上传响应:', response)
  if (response && response.id) {
    ElMessage.success('录音文件上传成功')
    file.attachmentId = response.id
    emit('upload-success', { type: 'audio', file, response })
    emitAttachmentChange()
  } else {
    ElMessage.error('录音文件上传失败：响应数据格式错误')
    const index = audioList.value.findIndex(item => item.uid === file.uid)
    if (index > -1) {
      audioList.value.splice(index, 1)
    }
  }
}

const onVideoSuccess = (response, file) => {
  console.log('视频上传响应:', response)
  if (response && response.id) {
    ElMessage.success('视频文件上传成功')
    file.attachmentId = response.id
    emit('upload-success', { type: 'video', file, response })
    emitAttachmentChange()
  } else {
    ElMessage.error('视频文件上传失败：响应数据格式错误')
    const index = videoList.value.findIndex(item => item.uid === file.uid)
    if (index > -1) {
      videoList.value.splice(index, 1)
    }
  }
}

const onDocumentSuccess = (response, file) => {
  console.log('文档上传响应:', response)
  if (response && response.id) {
    ElMessage.success('文档文件上传成功')
    file.attachmentId = response.id
    emit('upload-success', { type: 'document', file, response })
    emitAttachmentChange()
  } else {
    ElMessage.error('文档文件上传失败：响应数据格式错误')
    const index = documentList.value.findIndex(item => item.uid === file.uid)
    if (index > -1) {
      documentList.value.splice(index, 1)
    }
  }
}

// 上传失败处理
const onUploadError = (error, file) => {
  console.error('文件上传失败详情:', error)
  
  let errorMessage = '文件上传失败，请检查网络连接或联系管理员'
  
  // 尝试解析后端返回的错误信息
  if (error && error.response && error.response.data) {
    const errorData = error.response.data
    if (errorData.detail) {
      errorMessage = errorData.detail
      // 如果有具体的错误列表，也显示出来
      if (errorData.errors && Array.isArray(errorData.errors)) {
        errorMessage += `（${errorData.errors.join(', ')}）`
      }
    }
  } else if (error && error.message) {
    errorMessage = error.message
  }
  
  ElMessage.error(errorMessage)
  emit('upload-error', { file, error })
}

// 文件移除处理
const onImageRemove = async (file) => {
  if (file.attachmentId) {
    await removeAttachment(file.attachmentId, 'image')
  }
  emitAttachmentChange()
}

const onAudioRemove = async (file) => {
  if (file.attachmentId) {
    await removeAttachment(file.attachmentId, 'audio')
  }
  emitAttachmentChange()
}

const onVideoRemove = async (file) => {
  if (file.attachmentId) {
    await removeAttachment(file.attachmentId, 'video')
  }
  emitAttachmentChange()
}

const onDocumentRemove = async (file) => {
  if (file.attachmentId) {
    await removeAttachment(file.attachmentId, 'document')
  }
  emitAttachmentChange()
}

// 删除附件
const removeAttachment = async (attachmentId, type) => {
  try {
    await performanceAttachmentAPI.deleteAttachment(attachmentId)
    ElMessage.success('附件删除成功')
  } catch (error) {
    console.error('删除附件失败:', error)
    ElMessage.error('删除附件失败，请重试')
  }
}

// 图片预览 - 使用Fetch API + Blob方案
const onImagePreview = async (file) => {
  try {
    imageLoading.value = true
    currentPreviewFile.value = file
    
    // 如果有attachmentId，使用安全API获取文件Blob
    if (file.attachmentId) {
      ElMessage.info('正在加载图片预览...')
      const blobUrl = await performanceFileAPI.getSecureFileBlobUrl(file.attachmentId)
      previewImageUrl.value = blobUrl
    } else {
      // 新上传但未保存的文件，使用原URL（本地blob）
      previewImageUrl.value = file.originalUrl || file.url
    }
    
    previewVisible.value = true
  } catch (error) {
    imageLoading.value = false
    console.error('获取图片预览失败:', error)
    ElMessage.error('获取图片预览失败，请重试')
  }
}

// 图片加载成功处理
const onImageLoadSuccess = () => {
  imageLoading.value = false
}

// 图片加载失败处理
const onImageLoadError = async (event, file) => {
  imageLoading.value = false
  console.error('图片加载失败:', { file, error: event })
  ElMessage.error('图片加载失败，请检查网络连接')
}

// 发送附件变更事件
const emitAttachmentChange = () => {
  const attachments = {
    images: imageList.value.filter(file => file.attachmentId),
    audios: audioList.value.filter(file => file.attachmentId),
    videos: videoList.value.filter(file => file.attachmentId),
    documents: documentList.value.filter(file => file.attachmentId)
  }
  emit('attachment-change', attachments)
}

// 初始化附件列表 - 使用Fetch API + Blob方案
const initializeAttachments = async () => {
  // 先清空所有附件列表，避免旧数据残留
  imageList.value = []
  audioList.value = []
  videoList.value = []
  documentList.value = []
  
  console.log('初始化附件列表，数据:', props.initialAttachments)
  
  if (props.initialAttachments && props.initialAttachments.length > 0) {
    for (const attachment of props.initialAttachments) {
      let displayUrl = null
      let originalUrl = null
      
      try {
        // 对于图片类型，创建安全的Blob URL用于显示
        if (attachment.file_type === 'image') {
          displayUrl = await performanceFileAPI.getSecureFileBlobUrl(attachment.id)
          originalUrl = displayUrl
          console.log(`为图片附件 ${attachment.id} 生成Blob URL`)
        } else {
          // 对于非图片文件，使用占位符，在需要时再获取
          displayUrl = null
          originalUrl = null
        }
      } catch (error) {
        console.error(`为附件 ${attachment.id} 获取Blob URL失败:`, error)
        // 如果获取失败，使用错误状态
        displayUrl = '#error'
        originalUrl = '#error'
        ElMessage.warning(`附件 ${attachment.original_filename} 预览获取失败`)
      }
      
      const fileItem = {
        name: attachment.original_filename || attachment.original_name,
        url: displayUrl, // 显示用的URL（用于缩略图显示）
        originalUrl: originalUrl, // 原始文件URL
        attachmentId: attachment.id,
        uid: attachment.id,
        status: displayUrl === '#error' ? 'error' : 'success'
      }
      
      switch (attachment.file_type) {
        case 'image':
          imageList.value.push(fileItem)
          break
        case 'audio':
          audioList.value.push(fileItem)
          break
        case 'video':
          videoList.value.push(fileItem)
          break
        case 'document':
          documentList.value.push(fileItem)
          break
      }
    }
    
    console.log('附件列表初始化完成:', {
      images: imageList.value.length,
      audios: audioList.value.length,
      videos: videoList.value.length,
      documents: documentList.value.length
    })
  } else {
    console.log('没有初始附件数据')
  }
}

// 监听初始附件变化
watch(() => props.initialAttachments, (newAttachments, oldAttachments) => {
  console.log('initialAttachments变化:', {
    oldAttachments: oldAttachments,
    newAttachments: newAttachments,
    newLength: newAttachments ? newAttachments.length : 0
  })
  initializeAttachments()
}, { immediate: true, deep: true })

// 获取所有附件统计
const getAttachmentStats = () => {
  return {
    total: imageList.value.length + audioList.value.length + videoList.value.length + documentList.value.length,
    images: imageList.value.length,
    audios: audioList.value.length,
    videos: videoList.value.length,
    documents: documentList.value.length
  }
}

// 清空所有附件
const clearAllAttachments = () => {
  imageList.value = []
  audioList.value = []
  videoList.value = []
  documentList.value = []
  emitAttachmentChange()
}

// 监听recordId变化
watch(() => props.recordId, (newRecordId, oldRecordId) => {
  console.log('recordId变化:', { oldRecordId, newRecordId })
  
  // 当recordId发生变化时（切换到不同的记录），清理附件状态
  if (oldRecordId !== undefined && oldRecordId !== newRecordId) {
    console.log('切换到不同的记录，清理附件状态')
    imageList.value = []
    audioList.value = []
    videoList.value = []
    documentList.value = []
  }
  
  if (newRecordId && newRecordId !== null) {
    console.log('recordId已设置，可以开始上传附件')
  }
}, { immediate: true })

// 监听预览对话框关闭，清理Blob URL
watch(previewVisible, (newVisible, oldVisible) => {
  // 当对话框关闭时，清理Blob URL
  if (oldVisible && !newVisible && previewImageUrl.value && previewImageUrl.value.startsWith('blob:')) {
    console.log('清理预览图片Blob URL:', previewImageUrl.value)
    URL.revokeObjectURL(previewImageUrl.value)
    previewImageUrl.value = ''
  }
})

// 暴露方法给父组件
defineExpose({
  getAttachmentStats,
  clearAllAttachments
})
</script>

<style scoped>
.attachment-uploader {
  margin-top: 20px;
}

.section-title {
  font-size: 18px;
  color: #303133;
  margin: 0 0 10px 0;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.section-tip {
  color: #909399;
  font-size: 14px;
  margin-bottom: 20px;
  line-height: 1.5;
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
}

.upload-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.upload-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 15px 0;
}

.upload-title .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.upload-count {
  margin-left: 8px;
  font-size: 14px;
  color: #909399;
  font-weight: normal;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* 图片上传样式 */
.image-uploader :deep(.el-upload--picture-card) {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
  transition: border-color 0.3s;
}

.image-uploader :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
}

.image-uploader :deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 120px;
  height: 120px;
  border-radius: 8px;
}

.upload-text {
  font-size: 12px;
  color: #8c939d;
  margin-top: 5px;
}

/* 文件上传样式 */
.file-uploader {
  margin-bottom: 10px;
}

.file-uploader :deep(.el-upload-list) {
  margin-top: 10px;
}

.file-uploader :deep(.el-upload-list__item) {
  margin-bottom: 5px;
  border-radius: 6px;
}

/* 老年人友好设计 */
:deep(.el-button) {
  min-height: 36px;
  font-size: 14px;
  border-radius: 6px;
}

:deep(.el-upload-dragger) {
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-section {
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .image-uploader :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
  }
  
  .image-uploader :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 100px;
    height: 100px;
  }
}

.preview-container {
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.is-loading {
  color: #409eff;
  font-size: 50px;
}
</style> 