# 数据库设计文档

## 1. 文档说明

本文档基于《用户故事清单.md》、《用户旅程图.md》和《功能规格说明书.md》设计数据库结构，确保数据库设计能完全支撑系统的业务需求。

**最新更新 (V1.3 - 2024-12-21)**：
- ✅ 新增代表表的`district`字段（所属片区）和`avatar`字段（头像）
- ✅ 新增`people_opinion`表（群众意见表）
- ✅ 调整工作计划表关联关系（直接关联users表而非staff_members表）
- ✅ 更新文件大小限制（图片10MB、音频50MB、文档20MB）
- ✅ 完善索引设计和约束条件

## 2. 数据库设计原则

1. **数据一致性**: 根据存储的记忆，意见建议的状态管理完全移到OpinionReview实体中，避免状态同步问题
2. **可扩展性**: 预留必要的扩展字段，支持系统迭代
3. **性能优化**: 合理设计索引，确保查询性能
4. **数据完整性**: 通过外键约束和数据校验保证数据质量
5. **审计追踪**: 记录关键操作的时间和操作人

## 3. 核心实体关系图

```mermaid
erDiagram
    User ||--o{ Representative : "一对一"
    User ||--o{ StaffMember : "一对一"
    Representative ||--o{ PerformanceRecord : "一对多"
    Representative ||--o{ OpinionSuggestion : "一对多"
    Representative ||--o{ RepresentativeAISummary : "一对多"
    PerformanceRecord ||--o{ PerformanceAttachment : "一对多"
    OpinionSuggestion ||--o{ OpinionReview : "一对多"
    StaffMember ||--o{ OpinionReview : "一对多"
    User ||--o{ WorkPlan : "一对多"
    User ||--o{ Notification : "一对多"
    User ||--o{ SystemLog : "一对多"

    User {
        bigint id PK "用户主键ID"
        varchar username "用户名(登录凭据)"
        varchar password_hash "密码哈希值"
        varchar role "用户角色(代表/工作人员)"
        boolean is_active "账号是否激活"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }

    Representative {
        bigint id PK "代表主键ID"
        bigint user_id FK "关联用户表ID"
        varchar level "代表层级"
        varchar name "姓名"
        varchar gender "性别"
        varchar nationality "民族"
        varchar district "所属片区"
        date birth_date "出生日期"
        varchar birthplace "籍贯"
        varchar party "党派"
        varchar current_position "现任职务"
        varchar mobile_phone "移动电话号码"
        varchar education "学历"
        varchar graduated_school "毕业院校"
        varchar major "所学专业"
        text avatar "头像(base64格式)"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }

    StaffMember {
        bigint id PK "工作人员主键ID"
        bigint user_id FK "关联用户表ID"
        varchar name "姓名"
        varchar position "职位"
        varchar mobile_phone "移动电话号码"
        varchar email "邮箱"
        varchar station_name "所属站点名称"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }

    PerformanceRecord {
        bigint id PK "履职记录主键ID"
        bigint representative_id FK "关联代表表ID"
        date performance_date "履职日期"
        varchar performance_type "履职类型"
        text performance_content "履职内容(主要内容)"
        varchar activity_location "活动地点"
        text detailed_description "详细描述"
        varchar performance_status "履职状态"
        boolean has_attachments "是否有附件"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }

    PerformanceAttachment {
        bigint id PK "附件主键ID"
        bigint performance_record_id FK "关联履职记录表ID"
        varchar file_type "文件类型(image/audio/video/document)"
        varchar original_filename "原始文件名"
        varchar stored_filename "存储文件名(UUID)"
        varchar file_path "文件存储路径"
        bigint file_size "文件大小(字节)"
        varchar mime_type "MIME类型"
        varchar file_hash "文件MD5哈希值"
        varchar thumbnail_path "缩略图路径"
        int duration "媒体时长(秒)"
        int width "图片/视频宽度"
        int height "图片/视频高度"
        varchar upload_status "上传状态"
        int sort_order "排序序号"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }

    OpinionSuggestion {
        bigint id PK "意见建议主键ID"
        bigint representative_id FK "提交代表ID"
        varchar title "意见建议标题"
        varchar category "意见建议分类"
        varchar reporter_name "反映人姓名"
        text original_content "原始意见内容"
        text final_suggestion "最终建议内容"
        boolean ai_assisted "是否使用AI辅助"
        text ai_generated_content "AI生成的建议内容"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }

    OpinionReview {
        bigint id PK "审核记录主键ID"
        bigint opinion_id FK "关联意见建议表ID"
        bigint reviewer_id FK "审核人ID(工作人员)"
        varchar action "操作动作"
        varchar status "操作后状态"
        varchar transferred_department "转交部门名称"
        text review_comment "审核备注/驳回理由"
        text processing_result "处理结果描述"
        text attachments "附件文件路径(JSON格式)"
        datetime action_time "操作时间"
        datetime created_at "创建时间"
    }

    WorkPlan {
        bigint id PK "工作计划主键ID"
        bigint staff_member_id FK "关联用户表ID(工作人员)"
        varchar title "计划标题"
        date start_date "开始时间"
        date end_date "结束时间"
        text content "计划内容"
        varchar status "计划状态"
        int reminder_days "提醒时间（到期多少天前提醒）"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }

    PeopleOpinion {
        bigint id PK "群众意见主键ID"
        varchar title "意见标题"
        text content "意见内容"
        varchar contact_info "联系方式"
        varchar name "姓名"
        datetime created_at "创建时间"
    }

    Notification {
        bigint id PK "通知主键ID"
        bigint user_id FK "接收用户ID"
        varchar type "通知类型"
        varchar title "通知标题"
        text content "通知内容"
        bigint related_object_id "关联对象ID"
        varchar related_object_type "关联对象类型"
        boolean is_read "是否已读"
        datetime sent_at "发送时间"
        datetime read_at "阅读时间"
        datetime created_at "创建时间"
    }

    SystemLog {
        bigint id PK "日志主键ID"
        bigint user_id FK "操作用户ID"
        varchar action "操作动作"
        varchar module "操作模块"
        text description "操作描述"
        text ip_address "客户端IP地址"
        text user_agent "用户代理信息"
        datetime created_at "创建时间"
    }

    RepresentativeAISummary {
        bigint id PK "AI总结主键ID"
        bigint representative_id FK "关联代表表ID"
        int analysis_year "分析年份"
        json source_data_summary "数据来源概要"
        json ai_result "AI分析结果"
        varchar status "生成状态"
        float generation_duration "生成耗时(秒)"
        text error_message "错误信息"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
        datetime completed_at "完成时间"
    }
```

## 4. 数据表详细设计

### 4.1 用户表 (users)

**表说明**: 存储系统所有用户的基本认证信息，包括人大代表和站点工作人员

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 外键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | PK | - | PRIMARY | 用户主键ID |
| username | VARCHAR | 50 | NO | - | - | - | UNIQUE | 用户名，登录凭据 |
| password_hash | VARCHAR | 255 | NO | - | - | - | - | 密码哈希值 |
| role | ENUM | - | NO | - | - | - | INDEX | 用户角色：'representative', 'staff' |
| is_active | BOOLEAN | - | NO | 1 | - | - | INDEX | 账号是否激活 |
| last_login_at | DATETIME | - | YES | NULL | - | - | - | 最后登录时间 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | - | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP ON UPDATE | - | - | - | 更新时间 |

**业务规则**:
- username必须唯一
- role枚举值：'representative'(人大代表), 'staff'(站点工作人员)
- password_hash使用BCrypt等安全哈希算法
- is_active为false时用户无法登录

### 4.2 人大代表信息表 (representatives)

**表说明**: 存储人大代表的详细个人信息，对应用户故事US-RM-001的11个完整属性

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 外键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | PK | - | PRIMARY | 代表主键ID |
| user_id | BIGINT | - | NO | - | - | FK(users.id) | UNIQUE | 关联用户表，一对一关系 |
| level | VARCHAR | 50 | NO | - | - | - | INDEX | 代表层级 |
| name | VARCHAR | 50 | NO | - | - | - | INDEX | 姓名 |
| gender | ENUM | - | NO | - | - | - | - | 性别：'male', 'female' |
| nationality | VARCHAR | 50 | NO | - | - | - | - | 民族 |
| district | VARCHAR | 20 | NO | - | - | - | INDEX | 所属片区 |
| birth_date | DATE | - | NO | - | - | - | - | 出生日期 |
| birthplace | VARCHAR | 100 | NO | - | - | - | - | 籍贯 |
| party | VARCHAR | 50 | NO | - | - | - | - | 党派 |
| current_position | VARCHAR | 100 | NO | - | - | - | - | 现任职务 |
| mobile_phone | VARCHAR | 11 | NO | - | - | - | - | 移动电话号码 |
| education | VARCHAR | 50 | NO | - | - | - | - | 学历 |
| graduated_school | VARCHAR | 100 | YES | NULL | - | - | - | 毕业院校 |
| major | VARCHAR | 100 | YES | NULL | - | - | - | 所学专业 |
| avatar | TEXT | - | YES | NULL | - | - | - | 头像(base64格式) |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | - | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP ON UPDATE | - | - | - | 更新时间 |

**业务规则**:
- user_id与users表一对一关联，且只能关联role为'representative'的用户
- mobile_phone需符合手机号码格式（11位数字）
- gender枚举值：'male'(男), 'female'(女)
- district枚举值：'那洪片区', '那历片区', '沛鸿片区'
- avatar存储base64格式的头像数据，可为空

### 4.3 站点工作人员表 (staff_members)

**表说明**: 存储站点工作人员的基本信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 外键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | PK | - | PRIMARY | 工作人员主键ID |
| user_id | BIGINT | - | NO | - | - | FK(users.id) | UNIQUE | 关联用户表，一对一关系 |
| name | VARCHAR | 50 | NO | - | - | - | INDEX | 姓名 |
| position | VARCHAR | 100 | NO | - | - | - | - | 职位 |
| mobile_phone | VARCHAR | 11 | NO | - | - | - | - | 移动电话号码 |
| email | VARCHAR | 100 | YES | NULL | - | - | - | 邮箱 |
| station_name | VARCHAR | 100 | NO | - | - | - | INDEX | 所属站点名称 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | - | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP ON UPDATE | - | - | - | 更新时间 |

**业务规则**:
- user_id与users表一对一关联，且只能关联role为'staff'的用户
- mobile_phone需符合手机号码格式

### 4.4 履职记录表 (performance_records)

**表说明**: 存储人大代表的履职活动记录，对应用户故事US-RM-002的6个完整属性。支持多媒体附件存储。

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 外键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | PK | - | PRIMARY | 履职记录主键ID |
| representative_id | BIGINT | - | NO | - | - | FK(representatives.id) | INDEX | 关联代表表 |
| performance_date | DATE | - | NO | - | - | - | INDEX | 履职日期 |
| performance_type | VARCHAR | 50 | NO | - | - | - | INDEX | 履职类型 |
| performance_content | TEXT | - | NO | - | - | - | - | 履职内容（主要内容） |
| activity_location | VARCHAR | 200 | NO | - | - | - | - | 活动地点 |
| detailed_description | TEXT | - | YES | NULL | - | - | - | 详细描述 |
| performance_status | VARCHAR | 50 | NO | - | - | - | INDEX | 履职状态 |
| has_attachments | BOOLEAN | - | NO | 0 | - | - | INDEX | 是否有附件 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | - | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP ON UPDATE | - | - | - | 更新时间 |

**业务规则**:
- performance_type预设值：('会议参与','实地调研','走访群众','议案提交','建议办理','监督检查','法律宣传','信访接待','培训学习','联络活动','专题调研','视察活动','座谈交流','执法检查','民生走访','政策宣讲','其他活动')
- performance_status预设值：'进行中', '已完成', '已暂停'
- 同一代表同一天可以有多条履职记录
- has_attachments字段用于快速查询是否有附件，避免关联查询
- 履职内容支持文字录入，多媒体内容通过附件表关联存储

### 4.4.1 履职记录附件表 (performance_attachments)

**表说明**: 存储履职记录的多媒体附件信息，支持图片、音频、视频、文档等文件类型

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 外键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | PK | - | PRIMARY | 附件主键ID |
| performance_record_id | BIGINT | - | NO | - | - | FK(performance_records.id) | INDEX | 关联履职记录表 |
| file_type | VARCHAR | 20 | NO | - | - | - | INDEX | 文件类型 |
| original_filename | VARCHAR | 255 | NO | - | - | - | - | 原始文件名 |
| stored_filename | VARCHAR | 255 | NO | - | - | - | - | 存储文件名（UUID） |
| file_path | VARCHAR | 500 | NO | - | - | - | - | 文件存储路径 |
| file_size | BIGINT | - | NO | - | - | - | INDEX | 文件大小（字节） |
| mime_type | VARCHAR | 100 | NO | - | - | - | - | MIME类型 |
| file_hash | VARCHAR | 64 | NO | - | - | - | INDEX | 文件MD5哈希值 |
| thumbnail_path | VARCHAR | 500 | YES | NULL | - | - | - | 缩略图路径（图片/视频） |
| duration | INT | - | YES | NULL | - | - | - | 媒体时长（秒，音视频文件） |
| width | INT | - | YES | NULL | - | - | - | 图片/视频宽度 |
| height | INT | - | YES | NULL | - | - | - | 图片/视频高度 |
| upload_status | VARCHAR | 20 | NO | 'uploaded' | - | - | INDEX | 上传状态 |
| sort_order | INT | - | NO | 0 | - | - | - | 排序序号 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | INDEX | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP ON UPDATE | - | - | - | 更新时间 |

**业务规则**:
- file_type枚举值：'image'(图片), 'audio'(音频), 'video'(视频), 'document'(文档)
- upload_status预设值：'uploading'(上传中), 'uploaded'(已上传), 'failed'(上传失败), 'processing'(处理中)
- 文件数量限制：
  - 图片文件：单个履职记录最多9张，支持jpg/png/gif格式，单文件最大10MB
  - 音频文件：单个履职记录最多3个，支持mp3/wav/m4a格式，单文件最大50MB
  - 视频文件：单个履职记录最多2个，支持mp4/avi/mov格式，单文件最大100MB
  - 文档文件：单个履职记录最多5个，支持pdf/doc/docx/txt格式，单文件最大20MB
- file_hash用于文件去重和完整性校验
- thumbnail_path在图片和视频文件上传后自动生成
- duration字段仅对音频和视频文件有效
- width/height字段仅对图片和视频文件有效
- sort_order用于控制附件在界面上的显示顺序
- 删除履职记录时，相关附件文件需要级联删除（物理文件和数据库记录）

### 4.5 意见建议表 (opinion_suggestions)

**表说明**: 存储意见建议的基础信息，不包含状态字段（状态通过OpinionReview表管理）

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 外键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | PK | - | PRIMARY | 意见建议主键ID |
| representative_id | BIGINT | - | NO | - | - | FK(representatives.id) | INDEX | 提交代表ID |
| title | VARCHAR | 200 | NO | - | - | - | INDEX | 意见建议标题 |
| category | VARCHAR | 50 | NO | - | - | - | INDEX | 意见建议分类 |
| reporter_name | VARCHAR | 100 | NO | - | - | - | - | 反映人姓名 |
| original_content | TEXT | - | NO | - | - | - | - | 原始意见内容 |
| final_suggestion | TEXT | - | YES | NULL | - | - | - | 最终建议内容 |
| ai_assisted | BOOLEAN | - | NO | 0 | - | - | INDEX | 是否使用AI辅助 |
| ai_generated_content | TEXT | - | YES | NULL | - | - | - | AI生成的建议内容 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | INDEX | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP ON UPDATE | - | - | - | 更新时间 |

**业务规则**:
- category预设值：'城建环保', '交通出行', '教育文化', '医疗卫生', '社会保障', '经济发展', '政务服务', '其他'
- ai_assisted为true时，ai_generated_content不能为空
- final_suggestion为代表最终确认的建议内容

### 4.6 意见审核表 (opinion_reviews)

**表说明**: 记录意见建议的所有审核动作和状态变更，每次操作都新增一条记录

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 外键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | PK | - | PRIMARY | 审核记录主键ID |
| opinion_id | BIGINT | - | NO | - | - | FK(opinion_suggestions.id) | INDEX | 关联意见建议表 |
| reviewer_id | BIGINT | - | YES | NULL | - | FK(staff_members.id) | INDEX | 审核人ID（工作人员） |
| action | VARCHAR | 50 | NO | - | - | - | INDEX | 操作动作 |
| status | VARCHAR | 50 | NO | - | - | - | INDEX | 操作后状态 |
| transferred_department | VARCHAR | 100 | YES | NULL | - | - | INDEX | 转交部门名称 |
| review_comment | TEXT | - | YES | NULL | - | - | - | 审核备注/驳回理由 |
| processing_result | TEXT | - | YES | NULL | - | - | - | 处理结果描述 |
| attachments | TEXT | - | YES | NULL | - | - | - | 附件文件路径（JSON格式存储） |
| action_time | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | INDEX | 操作时间 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | - | 创建时间 |

**业务规则**:
- action枚举值：'submit'(代表提交), 'approve'(审核通过), 'reject'(审核驳回), 'transfer'(标记转交), 'update_progress'(更新进展), 'close'(标记办结)
- status枚举值：'draft'(草稿), 'submitted'(已提交), 'under_review'(待审核), 'approved'(审核通过), 'rejected'(审核驳回), 'transferred'(已转交), 'in_progress'(处理中), 'completed'(已办结)
- **action与status映射关系**：应用层必须确保action和status的一致性，映射关系如下：
  ```
  'submit' → 'submitted'        (代表提交意见)
  'approve' → 'approved'        (工作人员审核通过)
  'reject' → 'rejected'         (工作人员审核驳回)
  'transfer' → 'transferred'    (工作人员标记转交)
  'update_progress' → 'in_progress' (工作人员更新处理进展)
  'close' → 'completed'         (工作人员标记办结)
  ```
- 当前状态通过查询最新的审核记录获得
- transferred_department在action为'transfer'时必填
- review_comment在action为'reject'时必填
- processing_result在action为'update_progress'或'close'时必填

### 4.7 工作计划表 (work_plans)

**表说明**: 存储站点工作人员的工作计划，基于简化的6个核心字段设计

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 外键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | PK | - | PRIMARY | 工作计划主键ID |
| staff_member_id | BIGINT | - | NO | - | - | FK(users.id) | INDEX | 关联用户表ID(工作人员) |
| title | VARCHAR | 200 | NO | - | - | - | INDEX | 计划标题 |
| start_date | DATE | - | NO | - | - | - | INDEX | 开始时间 |
| end_date | DATE | - | NO | - | - | - | INDEX | 结束时间 |
| content | TEXT | - | NO | - | - | - | - | 计划内容 |
| status | VARCHAR | 50 | NO | 'planned' | - | - | INDEX | 计划状态 |
| reminder_days | INT | - | NO | 3 | - | - | - | 提醒时间（到期多少天前提醒） |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | - | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP ON UPDATE | - | - | - | 更新时间 |

**业务规则**:
- staff_member_id关联users表，限制为is_staff=True的用户
- status预设值：'planned'(计划中), 'in_progress'(进行中), 'completed'(已完成), 'cancelled'(已取消)
- end_date必须大于等于start_date
- reminder_days表示到期前多少天提醒，用于登录时弹窗提醒功能，范围0-30天
- 登录提醒条件：当前日期距离end_date小于等于reminder_days天且状态为'planned'或'in_progress'

### 4.8 通知表 (notifications)

**表说明**: 存储系统内通知信息，对应用户故事US-NT-001

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 外键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | PK | - | PRIMARY | 通知主键ID |
| user_id | BIGINT | - | NO | - | - | FK(users.id) | INDEX | 接收用户ID |
| type | VARCHAR | 50 | NO | - | - | - | INDEX | 通知类型 |
| title | VARCHAR | 200 | NO | - | - | - | - | 通知标题 |
| content | TEXT | - | NO | - | - | - | - | 通知内容 |
| related_object_id | BIGINT | - | YES | NULL | - | - | INDEX | 关联对象ID |
| related_object_type | VARCHAR | 50 | YES | NULL | - | - | - | 关联对象类型 |
| is_read | BOOLEAN | - | NO | 0 | - | - | INDEX | 是否已读 |
| sent_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | INDEX | 发送时间 |
| read_at | DATETIME | - | YES | NULL | - | - | - | 阅读时间 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | - | 创建时间 |

**业务规则**:
- type预设值：'opinion_submitted'(意见已提交), 'opinion_reviewed'(意见已审核), 'opinion_transferred'(意见已转交), 'opinion_updated'(意见有更新), 'opinion_completed'(意见已办结), 'work_plan_reminder'(工作计划提醒)
- related_object_type预设值：'opinion', 'work_plan'等
- read_at在用户阅读通知时更新

### 4.9 系统日志表 (system_logs)

**表说明**: 记录用户操作日志，用于审计和问题追踪

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 外键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | PK | - | PRIMARY | 日志主键ID |
| user_id | BIGINT | - | YES | NULL | - | FK(users.id) | INDEX | 操作用户ID |
| action | VARCHAR | 100 | NO | - | - | - | INDEX | 操作动作 |
| module | VARCHAR | 50 | NO | - | - | - | INDEX | 操作模块 |
| description | TEXT | - | YES | NULL | - | - | - | 操作描述 |
| ip_address | VARCHAR | 45 | YES | NULL | - | - | - | 客户端IP地址 |
| user_agent | TEXT | - | YES | NULL | - | - | - | 用户代理信息 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | INDEX | 创建时间 |

**业务规则**:
- action记录具体操作，如'login', 'create_opinion', 'review_opinion'等
- module预设值：'auth', 'opinion', 'performance', 'work_plan', 'user_management'等
- ip_address支持IPv4和IPv6格式

### 4.10 群众意见表 (people_opinion)

**表说明**: 存储群众通过二维码等渠道提交的意见建议，独立于代表意见建议系统

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 外键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | PK | - | PRIMARY | 群众意见主键ID |
| title | VARCHAR | 200 | NO | - | - | - | INDEX | 意见标题 |
| content | TEXT | - | NO | - | - | - | - | 意见内容 |
| contact_info | VARCHAR | 100 | YES | NULL | - | - | - | 联系方式 |
| name | VARCHAR | 50 | YES | NULL | - | - | - | 姓名 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | INDEX | 创建时间 |

**业务规则**:
- 群众可匿名提交意见，name和contact_info可为空
- 主要用于大屏展示的二维码收集功能
- 与代表意见建议系统(opinion_suggestions)独立管理
- 按创建时间倒序排列显示

### 4.11 代表AI总结表 (representative_ai_summaries)

**表说明**: 存储代表年度AI分析总结结果，用于生成年度履职成果展示

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 外键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|------|
| id | BIGINT | - | NO | AUTO_INCREMENT | PK | - | PRIMARY | AI总结主键ID |
| representative_id | BIGINT | - | NO | - | - | FK(representatives.id) | INDEX | 关联代表表 |
| analysis_year | INT | - | NO | - | - | - | INDEX | 分析年份 |
| source_data_summary | JSON | - | NO | '{}' | - | - | - | 数据来源概要 |
| ai_result | JSON | - | YES | NULL | - | - | - | AI分析结果 |
| status | VARCHAR | 20 | NO | 'pending' | - | - | INDEX | 生成状态 |
| generation_duration | FLOAT | - | YES | NULL | - | - | - | 生成耗时（秒） |
| error_message | TEXT | - | YES | NULL | - | - | - | 错误信息 |
| created_at | DATETIME | - | NO | CURRENT_TIMESTAMP | - | - | INDEX | 创建时间 |
| updated_at | DATETIME | - | NO | CURRENT_TIMESTAMP ON UPDATE | - | - | - | 更新时间 |
| completed_at | DATETIME | - | YES | NULL | - | - | - | 完成时间 |

**业务规则**:
- status枚举值：'pending'(等待生成), 'generating'(生成中), 'completed'(生成完成), 'failed'(生成失败)
- 每个代表每年只能有一个完成状态的AI总结记录
- source_data_summary存储用于AI分析的源数据统计信息（JSON格式）
- ai_result存储第三方AI API返回的完整分析结果（JSON格式），包含前端展示所需的所有数据结构
- generation_duration记录AI生成的耗时，用于性能监控
- error_message在生成失败时记录详细错误信息

## 5. 数据库DDL创建语句

### 5.1 履职记录附件表DDL

```sql
-- 履职记录附件表
CREATE TABLE performance_attachments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '附件主键ID',
    performance_record_id BIGINT NOT NULL COMMENT '关联履职记录表',
    file_type VARCHAR(20) NOT NULL COMMENT '文件类型',
    original_filename VARCHAR(255) NOT NULL COMMENT '原始文件名',
    stored_filename VARCHAR(255) NOT NULL COMMENT '存储文件名(UUID)',
    file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
    file_hash VARCHAR(64) NOT NULL COMMENT '文件MD5哈希值',
    thumbnail_path VARCHAR(500) NULL COMMENT '缩略图路径(图片/视频)',
    duration INT NULL COMMENT '媒体时长(秒,音视频文件)',
    width INT NULL COMMENT '图片/视频宽度',
    height INT NULL COMMENT '图片/视频高度',
    upload_status VARCHAR(20) NOT NULL DEFAULT 'uploaded' COMMENT '上传状态',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序序号',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_performance_record_id (performance_record_id),
    INDEX idx_file_type (file_type),
    INDEX idx_upload_status (upload_status),
    INDEX idx_file_hash (file_hash),
    INDEX idx_created_at (created_at),
    INDEX idx_record_sort (performance_record_id, sort_order),
    
    FOREIGN KEY (performance_record_id) REFERENCES performance_records(id) ON DELETE CASCADE,
    
    CONSTRAINT chk_file_type CHECK (file_type IN ('image', 'audio', 'video', 'document')),
    CONSTRAINT chk_upload_status CHECK (upload_status IN ('uploading', 'uploaded', 'failed', 'processing')),
    CONSTRAINT chk_file_size CHECK (file_size > 0 AND file_size <= 104857600)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='履职记录附件表';
```

### 5.2 群众意见表DDL

```sql
-- 群众意见表
CREATE TABLE people_opinion (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '群众意见主键ID',
    title VARCHAR(200) NOT NULL COMMENT '意见标题',
    content TEXT NOT NULL COMMENT '意见内容',
    contact_info VARCHAR(100) NULL COMMENT '联系方式',
    name VARCHAR(50) NULL COMMENT '姓名',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_title (title),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='群众意见表';
```

### 5.3 代表AI总结表DDL

```sql
-- 代表AI总结表
CREATE TABLE representative_ai_summaries (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'AI总结主键ID',
    representative_id BIGINT NOT NULL COMMENT '关联代表表',
    analysis_year INT NOT NULL COMMENT '分析年份',
    source_data_summary JSON NOT NULL DEFAULT ('{}') COMMENT '数据来源概要',
    ai_result JSON NULL COMMENT 'AI分析结果',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '生成状态',
    generation_duration FLOAT NULL COMMENT '生成耗时(秒)',
    error_message TEXT NULL COMMENT '错误信息',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    completed_at DATETIME NULL COMMENT '完成时间',
    
    INDEX idx_representative_id (representative_id),
    INDEX idx_analysis_year (analysis_year),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_rep_year (representative_id, analysis_year),
    
    FOREIGN KEY (representative_id) REFERENCES representatives(id) ON DELETE CASCADE,
    
    CONSTRAINT chk_status CHECK (status IN ('pending', 'generating', 'completed', 'failed')),
    CONSTRAINT chk_analysis_year CHECK (analysis_year >= 2020 AND analysis_year <= 2099),
    
    -- 每个代表每年只能有一个完成状态的AI总结
    UNIQUE KEY uk_rep_year_completed (representative_id, analysis_year, status) 
    WHERE status = 'completed'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代表AI总结表';
```

## 6. 索引设计

### 6.1 主要查询索引

```sql
-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role_active ON users(role, is_active);

-- 人大代表表索引
CREATE INDEX idx_representatives_user_id ON representatives(user_id);
CREATE INDEX idx_representatives_name ON representatives(name);
CREATE INDEX idx_representatives_district ON representatives(district);

-- 站点工作人员表索引
CREATE INDEX idx_staff_members_user_id ON staff_members(user_id);
CREATE INDEX idx_staff_members_station ON staff_members(station_name);

-- 履职记录表索引
CREATE INDEX idx_performance_records_rep_id ON performance_records(representative_id);
CREATE INDEX idx_performance_records_date ON performance_records(performance_date);
CREATE INDEX idx_performance_records_type ON performance_records(performance_type);
CREATE INDEX idx_performance_records_has_attachments ON performance_records(has_attachments);
CREATE INDEX idx_performance_records_rep_date ON performance_records(representative_id, performance_date);

-- 履职记录附件表索引
CREATE INDEX idx_performance_attachments_record_id ON performance_attachments(performance_record_id);
CREATE INDEX idx_performance_attachments_file_type ON performance_attachments(file_type);
CREATE INDEX idx_performance_attachments_upload_status ON performance_attachments(upload_status);
CREATE INDEX idx_performance_attachments_file_hash ON performance_attachments(file_hash);
CREATE INDEX idx_performance_attachments_created_at ON performance_attachments(created_at);
CREATE INDEX idx_performance_attachments_record_sort ON performance_attachments(performance_record_id, sort_order);

-- 意见建议表索引
CREATE INDEX idx_opinion_suggestions_rep_id ON opinion_suggestions(representative_id);
CREATE INDEX idx_opinion_suggestions_category ON opinion_suggestions(category);
CREATE INDEX idx_opinion_suggestions_created ON opinion_suggestions(created_at);

-- 意见审核表索引
CREATE INDEX idx_opinion_reviews_opinion_id ON opinion_reviews(opinion_id);
CREATE INDEX idx_opinion_reviews_reviewer_id ON opinion_reviews(reviewer_id);
CREATE INDEX idx_opinion_reviews_status ON opinion_reviews(status);
CREATE INDEX idx_opinion_reviews_action_time ON opinion_reviews(action_time);
CREATE INDEX idx_opinion_reviews_opinion_time ON opinion_reviews(opinion_id, action_time);

-- 工作计划表索引
CREATE INDEX idx_work_plans_staff_id ON work_plans(staff_member_id);
CREATE INDEX idx_work_plans_title ON work_plans(title);
CREATE INDEX idx_work_plans_dates ON work_plans(start_date, end_date);
CREATE INDEX idx_work_plans_status ON work_plans(status);
CREATE INDEX idx_work_plans_end_status ON work_plans(end_date, status);

-- 群众意见表索引
CREATE INDEX idx_people_opinion_title ON people_opinion(title);
CREATE INDEX idx_people_opinion_created_at ON people_opinion(created_at);

-- 通知表索引
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_notifications_sent_at ON notifications(sent_at);
CREATE INDEX idx_notifications_user_read ON notifications(user_id, is_read);

-- 系统日志表索引
CREATE INDEX idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX idx_system_logs_action ON system_logs(action);
CREATE INDEX idx_system_logs_module ON system_logs(module);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);

-- 代表AI总结表索引
CREATE INDEX idx_ai_summaries_representative_id ON representative_ai_summaries(representative_id);
CREATE INDEX idx_ai_summaries_analysis_year ON representative_ai_summaries(analysis_year);
CREATE INDEX idx_ai_summaries_status ON representative_ai_summaries(status);
CREATE INDEX idx_ai_summaries_created_at ON representative_ai_summaries(created_at);
CREATE INDEX idx_ai_summaries_rep_year ON representative_ai_summaries(representative_id, analysis_year);
```

## 7. 核心业务查询

### 7.1 获取意见建议当前状态

```sql
-- 获取意见建议的当前状态（通过最新审核记录）
SELECT 
    os.*,
    or_latest.status as current_status,
    or_latest.action_time as last_action_time,
    or_latest.transferred_department,
    sm.name as last_reviewer_name
FROM opinion_suggestions os
LEFT JOIN (
    SELECT 
        opinion_id,
        status,
        action_time,
        transferred_department,
        reviewer_id,
        ROW_NUMBER() OVER (PARTITION BY opinion_id ORDER BY action_time DESC) as rn
    FROM opinion_reviews
) or_latest ON os.id = or_latest.opinion_id AND or_latest.rn = 1
LEFT JOIN staff_members sm ON or_latest.reviewer_id = sm.id
WHERE os.representative_id = ?;
```

### 7.2 获取待处理的意见建议

```sql
-- 站点工作人员待审核的意见建议
SELECT 
    os.*,
    r.name as representative_name,
    or_latest.status as current_status
FROM opinion_suggestions os
JOIN representatives r ON os.representative_id = r.id
JOIN (
    SELECT 
        opinion_id,
        status,
        ROW_NUMBER() OVER (PARTITION BY opinion_id ORDER BY action_time DESC) as rn
    FROM opinion_reviews
) or_latest ON os.id = or_latest.opinion_id AND or_latest.rn = 1
WHERE or_latest.status = 'submitted'
ORDER BY os.created_at DESC;
```

### 7.3 获取工作台统计数据

```sql
-- 人大代表工作台统计
SELECT 
    (SELECT COUNT(*) FROM performance_records 
     WHERE representative_id = ? AND MONTH(performance_date) = MONTH(CURRENT_DATE())) as monthly_performance_count,
    (SELECT COUNT(*) FROM opinion_suggestions os
     JOIN (
         SELECT opinion_id, status, ROW_NUMBER() OVER (PARTITION BY opinion_id ORDER BY action_time DESC) as rn
         FROM opinion_reviews
     ) or_latest ON os.id = or_latest.opinion_id AND or_latest.rn = 1
     WHERE os.representative_id = ? AND or_latest.status NOT IN ('completed')) as pending_opinions_count;

-- 站点工作人员工作台统计
SELECT 
    (SELECT COUNT(*) FROM opinion_suggestions os
     JOIN (
         SELECT opinion_id, status, ROW_NUMBER() OVER (PARTITION BY opinion_id ORDER BY action_time DESC) as rn
         FROM opinion_reviews
     ) or_latest ON os.id = or_latest.opinion_id AND or_latest.rn = 1
     WHERE or_latest.status = 'submitted') as pending_review_count,
    (SELECT COUNT(*) FROM opinion_suggestions os
     JOIN (
         SELECT opinion_id, status, action_time, ROW_NUMBER() OVER (PARTITION BY opinion_id ORDER BY action_time DESC) as rn
         FROM opinion_reviews
     ) or_latest ON os.id = or_latest.opinion_id AND or_latest.rn = 1
     WHERE or_latest.status = 'completed' AND MONTH(or_latest.action_time) = MONTH(CURRENT_DATE())) as monthly_completed_count;
```

## 8. 数据约束和触发器

### 8.1 外键约束

```sql
-- 代表表外键约束
ALTER TABLE representatives 
ADD CONSTRAINT fk_representatives_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 站点工作人员表外键约束
ALTER TABLE staff_members 
ADD CONSTRAINT fk_staff_members_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 履职记录表外键约束
ALTER TABLE performance_records 
ADD CONSTRAINT fk_performance_records_representative_id 
FOREIGN KEY (representative_id) REFERENCES representatives(id) ON DELETE CASCADE;

-- 履职记录附件表外键约束
ALTER TABLE performance_attachments
ADD CONSTRAINT fk_performance_attachments_record_id
FOREIGN KEY (performance_record_id) REFERENCES performance_records(id) ON DELETE CASCADE;

-- 意见建议表外键约束
ALTER TABLE opinion_suggestions 
ADD CONSTRAINT fk_opinion_suggestions_representative_id 
FOREIGN KEY (representative_id) REFERENCES representatives(id) ON DELETE CASCADE;

-- 意见审核表外键约束
ALTER TABLE opinion_reviews 
ADD CONSTRAINT fk_opinion_reviews_opinion_id 
FOREIGN KEY (opinion_id) REFERENCES opinion_suggestions(id) ON DELETE CASCADE;

ALTER TABLE opinion_reviews 
ADD CONSTRAINT fk_opinion_reviews_reviewer_id 
FOREIGN KEY (reviewer_id) REFERENCES staff_members(id) ON DELETE SET NULL;

-- 工作计划表外键约束
ALTER TABLE work_plans
ADD CONSTRAINT fk_work_plans_staff_member_id
FOREIGN KEY (staff_member_id) REFERENCES users(id) ON DELETE CASCADE;

-- 通知表外键约束
ALTER TABLE notifications 
ADD CONSTRAINT fk_notifications_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 系统日志表外键约束
ALTER TABLE system_logs 
ADD CONSTRAINT fk_system_logs_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;
```

### 8.2 检查约束

```sql
-- 用户表检查约束
ALTER TABLE users 
ADD CONSTRAINT chk_users_role 
CHECK (role IN ('representative', 'staff'));

-- 人大代表表检查约束
ALTER TABLE representatives
ADD CONSTRAINT chk_representatives_gender
CHECK (gender IN ('male', 'female'));

ALTER TABLE representatives
ADD CONSTRAINT chk_representatives_mobile_phone
CHECK (mobile_phone REGEXP '^[0-9]{11}$');

ALTER TABLE representatives
ADD CONSTRAINT chk_representatives_district
CHECK (district IN ('那洪片区', '那历片区', '沛鸿片区'));

-- 履职记录表检查约束
ALTER TABLE performance_records 
ADD CONSTRAINT chk_performance_records_status 
CHECK (performance_status IN ('进行中', '已完成', '已暂停'));

-- 履职记录附件表检查约束
ALTER TABLE performance_attachments
ADD CONSTRAINT chk_performance_attachments_file_type
CHECK (file_type IN ('image', 'audio', 'video', 'document'));

ALTER TABLE performance_attachments
ADD CONSTRAINT chk_performance_attachments_upload_status
CHECK (upload_status IN ('uploading', 'uploaded', 'failed', 'processing'));

ALTER TABLE performance_attachments
ADD CONSTRAINT chk_performance_attachments_file_size
CHECK (file_size > 0 AND file_size <= 104857600); -- 最大100MB

-- 工作计划表检查约束
ALTER TABLE work_plans
ADD CONSTRAINT chk_work_plans_status
CHECK (status IN ('planned', 'in_progress', 'completed', 'cancelled'));

ALTER TABLE work_plans
ADD CONSTRAINT chk_work_plans_dates
CHECK (end_date >= start_date);

ALTER TABLE work_plans
ADD CONSTRAINT chk_work_plans_reminder_days
CHECK (reminder_days >= 0 AND reminder_days <= 30);

-- 意见审核表检查约束
ALTER TABLE opinion_reviews 
ADD CONSTRAINT chk_opinion_reviews_action 
CHECK (action IN ('submit', 'approve', 'reject', 'transfer', 'update_progress', 'close'));

ALTER TABLE opinion_reviews 
ADD CONSTRAINT chk_opinion_reviews_status 
CHECK (status IN ('draft', 'submitted', 'under_review', 'approved', 'rejected', 'transferred', 'in_progress', 'completed'));
```

## 9. 初始化数据脚本

### 9.1 创建管理员账号

```sql
-- 创建系统管理员账号
INSERT INTO users (username, password_hash, role, is_active) 
VALUES ('admin', '$2y$10$...', 'staff', 1);

-- 创建管理员对应的工作人员记录
INSERT INTO staff_members (user_id, name, position, mobile_phone, station_name)
VALUES (1, '系统管理员', '系统管理员', '13800000000', '系统管理');
```

### 9.2 初始化字典数据

```sql
-- 创建字典表（可选，也可以在代码中硬编码）
CREATE TABLE dictionaries (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(50) NOT NULL,
    code VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_dictionaries_category_code (category, code),
    INDEX idx_dictionaries_category (category)
);

-- 插入履职类型字典
INSERT INTO dictionaries (category, code, name, sort_order) VALUES
('performance_type', 'inspection_research', '视察调研', 1),
('performance_type', 'learning_training', '学习培训', 2),
('performance_type', 'reception_visit', '接待走访', 3),
('performance_type', 'law_enforcement_check', '执法检查', 4),
('performance_type', 'theme_activity', '主题活动', 5),
('performance_type', 'report_duty', '述职', 6),
('performance_type', 'regulation_consultation', '法规征询意见', 7),
('performance_type', 'policy_regulation_publicity', '政策法规宣传', 8),
('performance_type', 'proposal_suggestion_supervision', '议案建议办理督办', 9),
('performance_type', 'meeting', '会议', 10),
('performance_type', 'other', '其它', 11);

-- 插入意见建议分类字典
INSERT INTO dictionaries (category, code, name, sort_order) VALUES
('opinion_category', 'urban_construction', '城建环保', 1),
('opinion_category', 'transportation', '交通出行', 2),
('opinion_category', 'education', '教育文化', 3),
('opinion_category', 'healthcare', '医疗卫生', 4),
('opinion_category', 'social_security', '社会保障', 5),
('opinion_category', 'economic', '经济发展', 6),
('opinion_category', 'government_service', '政务服务', 7),
('opinion_category', 'other', '其他', 8);
```

## 10. 数据库维护和优化建议

### 10.1 定期维护任务

1. **日志清理**: 定期清理超过6个月的系统日志
2. **通知清理**: 定期清理超过3个月的已读通知
3. **索引优化**: 定期分析查询性能，优化索引
4. **数据备份**: 每日增量备份，每周全量备份

### 10.2 性能监控

1. **慢查询监控**: 监控执行时间超过1秒的查询
2. **索引使用率**: 监控索引使用情况
3. **存储空间**: 监控数据库存储空间使用情况
4. **连接数监控**: 监控数据库连接数

### 10.3 扩展性考虑

1. **分区策略**: 对于日志表等大数据量表考虑按时间分区
2. **读写分离**: 高并发场景下考虑主从复制
3. **缓存策略**: 对于字典数据和统计数据考虑缓存
4. **归档策略**: 对于历史数据制定归档策略

## 11. 应用层实现指南

### 11.1 意见审核状态管理

为确保`opinion_reviews`表中`action`和`status`字段的一致性，后端开发必须实现以下映射逻辑：

#### 11.1.1 状态映射常量定义

```python
# Python 示例
ACTION_STATUS_MAP = {
    'submit': 'submitted',           # 代表提交意见
    'approve': 'approved',           # 工作人员审核通过
    'reject': 'rejected',            # 工作人员审核驳回
    'transfer': 'transferred',       # 工作人员标记转交
    'update_progress': 'in_progress', # 工作人员更新处理进展
    'close': 'completed'             # 工作人员标记办结
}
```

```javascript
// JavaScript 示例
const ACTION_STATUS_MAP = {
    'submit': 'submitted',
    'approve': 'approved', 
    'reject': 'rejected',
    'transfer': 'transferred',
    'update_progress': 'in_progress',
    'close': 'completed'
};
```

```java
// Java 示例
public enum ActionStatusMapping {
    SUBMIT("submit", "submitted"),
    APPROVE("approve", "approved"),
    REJECT("reject", "rejected"), 
    TRANSFER("transfer", "transferred"),
    UPDATE_PROGRESS("update_progress", "in_progress"),
    CLOSE("close", "completed");
    
    private final String action;
    private final String status;
    
    ActionStatusMapping(String action, String status) {
        this.action = action;
        this.status = status;
    }
}
```

#### 11.1.2 业务服务层实现

```python
# Python Django 示例
class OpinionReviewService:
    @staticmethod
    def create_review(opinion_id, reviewer_id, action, **kwargs):
        """创建审核记录，自动设置对应状态"""
        # 根据action自动设置status
        status = ACTION_STATUS_MAP.get(action)
        if not status:
            raise ValueError(f"Invalid action: {action}")
        
        # 验证必填字段
        if action == 'transfer' and not kwargs.get('transferred_department'):
            raise ValueError("transferred_department is required for transfer action")
        if action == 'reject' and not kwargs.get('review_comment'):
            raise ValueError("review_comment is required for reject action")
        if action in ['update_progress', 'close'] and not kwargs.get('processing_result'):
            raise ValueError("processing_result is required for update_progress/close action")
        
        # 创建审核记录
        return OpinionReview.objects.create(
            opinion_id=opinion_id,
            reviewer_id=reviewer_id,
            action=action,
            status=status,
            **kwargs
        )
```

#### 11.1.3 数据验证

```sql
-- 数据库层面可以添加检查约束确保映射正确
ALTER TABLE opinion_reviews 
ADD CONSTRAINT chk_action_status_mapping 
CHECK (
    (action = 'submit' AND status = 'submitted') OR
    (action = 'approve' AND status = 'approved') OR  
    (action = 'reject' AND status = 'rejected') OR
    (action = 'transfer' AND status = 'transferred') OR
    (action = 'update_progress' AND status = 'in_progress') OR
    (action = 'close' AND status = 'completed')
);
```

### 11.2 前端状态显示

前端在显示意见建议状态时，应优先使用`status`字段，并可参考以下状态显示规范：

```javascript
// 前端状态显示映射
const STATUS_DISPLAY_MAP = {
    'draft': { text: '草稿', color: 'gray', icon: 'edit' },
    'submitted': { text: '已提交', color: 'blue', icon: 'upload' },
    'under_review': { text: '待审核', color: 'orange', icon: 'clock' },
    'approved': { text: '审核通过', color: 'green', icon: 'check' },
    'rejected': { text: '审核驳回', color: 'red', icon: 'close' },
    'transferred': { text: '已转交', color: 'purple', icon: 'share' },
    'in_progress': { text: '处理中', color: 'yellow', icon: 'loading' },
    'completed': { text: '已办结', color: 'green', icon: 'check-circle' }
};
```

### 11.3 获取当前状态的标准查询

```sql
-- 获取意见建议当前状态的标准SQL
SELECT 
    os.*,
    latest_review.status as current_status,
    latest_review.action as last_action,
    latest_review.action_time as last_action_time,
    latest_review.transferred_department,
    sm.name as last_reviewer_name
FROM opinion_suggestions os
LEFT JOIN (
    SELECT 
        opinion_id,
        action,
        status,
        action_time,
        transferred_department,
        reviewer_id,
        ROW_NUMBER() OVER (PARTITION BY opinion_id ORDER BY action_time DESC) as rn
    FROM opinion_reviews
) latest_review ON os.id = latest_review.opinion_id AND latest_review.rn = 1
LEFT JOIN staff_members sm ON latest_review.reviewer_id = sm.id
WHERE os.id = ?;
```

### 11.4 履职记录附件管理

#### 11.4.1 文件上传服务实现

```python
# Python Django 示例
class PerformanceAttachmentService:
    
    # 文件类型和大小限制配置
    FILE_LIMITS = {
        'image': {'max_count': 9, 'max_size': 5 * 1024 * 1024, 'formats': ['jpg', 'png', 'gif']},
        'audio': {'max_count': 3, 'max_size': 20 * 1024 * 1024, 'formats': ['mp3', 'wav', 'm4a']},
        'video': {'max_count': 2, 'max_size': 100 * 1024 * 1024, 'formats': ['mp4', 'avi', 'mov']},
        'document': {'max_count': 5, 'max_size': 10 * 1024 * 1024, 'formats': ['pdf', 'doc', 'docx', 'txt']}
    }
    
    @staticmethod
    def upload_attachment(performance_record_id, file, file_type):
        """上传附件文件"""
        # 验证文件类型和大小
        if not PerformanceAttachmentService.validate_file(file, file_type):
            raise ValueError("文件格式或大小不符合要求")
        
        # 检查数量限制
        existing_count = PerformanceAttachment.objects.filter(
            performance_record_id=performance_record_id,
            file_type=file_type
        ).count()
        
        max_count = PerformanceAttachmentService.FILE_LIMITS[file_type]['max_count']
        if existing_count >= max_count:
            raise ValueError(f"{file_type}类型文件数量已达上限({max_count}个)")
        
        # 生成唯一文件名和路径
        import uuid
        import hashlib
        import re
        from datetime import datetime
        
        file_hash = hashlib.md5(file.read()).hexdigest()
        file.seek(0)  # 重置文件指针
        
        # 获取文件扩展名
        file_ext = file.name.split('.')[-1].lower()
        
        # 清理原始文件名（去除路径和特殊字符）
        original_name_clean = re.sub(r'[^\w\u4e00-\u9fff.-]', '_', file.name.rsplit('.', 1)[0])
        
        # 生成文件名：{uuid}_{timestamp}_{original_name}
        now = datetime.now()
        uuid_part = uuid.uuid4().hex[:8]  # 取UUID前8位
        timestamp_part = now.strftime('%Y%m%d%H%M%S')
        stored_filename = f"{uuid_part}_{timestamp_part}_{original_name_clean}.{file_ext}"
        
        # 按日期分目录存储
        relative_path = f"performance/{now.year}/{now.month:02d}/{now.day:02d}/{file_type}s"
        file_path = f"media/{relative_path}/{stored_filename}"
        
        # 保存文件到磁盘
        import os
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'wb') as f:
            for chunk in file.chunks():
                f.write(chunk)
        
        # 获取媒体信息（尺寸、时长等）
        media_info = PerformanceAttachmentService.get_media_info(file_path, file_type)
        
        # 生成缩略图（如果需要）
        thumbnail_path = None
        if file_type in ['image', 'video']:
            # 缩略图路径：在thumbnails目录下，文件名添加_thumb后缀
            thumbnail_name = f"{uuid_part}_{timestamp_part}_thumb.jpg"
            thumbnail_relative_path = f"thumbnails/performance/{now.year}/{now.month:02d}/{now.day:02d}"
            thumbnail_path = f"media/{thumbnail_relative_path}/{thumbnail_name}"
            PerformanceAttachmentService.generate_thumbnail(file_path, thumbnail_path, file_type)
        
        # 创建数据库记录
        attachment = PerformanceAttachment.objects.create(
            performance_record_id=performance_record_id,
            file_type=file_type,
            original_filename=file.name,
            stored_filename=stored_filename,
            file_path=file_path,
            file_size=file.size,
            mime_type=file.content_type,
            file_hash=file_hash,
            thumbnail_path=thumbnail_path,
            width=media_info.get('width'),
            height=media_info.get('height'),
            duration=media_info.get('duration'),
            upload_status='uploaded',
            sort_order=existing_count
        )
        
        # 更新履职记录的has_attachments字段
        PerformanceRecord.objects.filter(id=performance_record_id).update(has_attachments=True)
        
        return attachment
```

#### 11.4.2 前端文件上传组件

```vue
<!-- Vue3 文件上传组件示例 -->
<template>
  <div class="attachment-upload">
    <div class="upload-sections">
      <!-- 图片上传区域 -->
      <div class="upload-section">
        <h4>现场照片 (最多9张，每张不超过5MB)</h4>
        <el-upload
          v-model:file-list="imageFiles"
          :action="uploadUrl"
          :data="{ file_type: 'image', performance_record_id: recordId }"
          :before-upload="beforeImageUpload"
          :on-success="onUploadSuccess"
          :on-remove="onFileRemove"
          list-type="picture-card"
          :limit="9"
          accept=".jpg,.png,.gif"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </div>
      
      <!-- 录音上传区域 -->
      <div class="upload-section">
        <h4>录音文件 (最多3个，每个不超过20MB)</h4>
        <el-upload
          v-model:file-list="audioFiles"
          :action="uploadUrl"
          :data="{ file_type: 'audio', performance_record_id: recordId }"
          :before-upload="beforeAudioUpload"
          :on-success="onUploadSuccess"
          :on-remove="onFileRemove"
          :limit="3"
          accept=".mp3,.wav,.m4a"
        >
          <el-button type="primary">上传录音</el-button>
        </el-upload>
      </div>
      
      <!-- 视频上传区域 -->
      <div class="upload-section">
        <h4>视频文件 (最多2个，每个不超过100MB)</h4>
        <el-upload
          v-model:file-list="videoFiles"
          :action="uploadUrl"
          :data="{ file_type: 'video', performance_record_id: recordId }"
          :before-upload="beforeVideoUpload"
          :on-success="onUploadSuccess"
          :on-remove="onFileRemove"
          :limit="2"
          accept=".mp4,.avi,.mov"
        >
          <el-button type="primary">上传视频</el-button>
        </el-upload>
      </div>
      
      <!-- 文档上传区域 -->
      <div class="upload-section">
        <h4>相关文档 (最多5个，每个不超过10MB)</h4>
        <el-upload
          v-model:file-list="documentFiles"
          :action="uploadUrl"
          :data="{ file_type: 'document', performance_record_id: recordId }"
          :before-upload="beforeDocumentUpload"
          :on-success="onUploadSuccess"
          :on-remove="onFileRemove"
          :limit="5"
          accept=".pdf,.doc,.docx,.txt"
        >
          <el-button type="primary">上传文档</el-button>
        </el-upload>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  recordId: {
    type: Number,
    required: true
  }
})

const uploadUrl = '/api/performance-attachments/upload/'
const imageFiles = ref([])
const audioFiles = ref([])
const videoFiles = ref([])
const documentFiles = ref([])

// 文件上传前的校验
const beforeImageUpload = (file) => {
  const isValidFormat = ['image/jpeg', 'image/png', 'image/gif'].includes(file.type)
  const isValidSize = file.size < 5 * 1024 * 1024
  
  if (!isValidFormat) {
    ElMessage.error('图片格式只支持 JPG/PNG/GIF!')
    return false
  }
  if (!isValidSize) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const beforeAudioUpload = (file) => {
  const isValidFormat = ['audio/mpeg', 'audio/wav', 'audio/mp4'].includes(file.type)
  const isValidSize = file.size < 20 * 1024 * 1024
  
  if (!isValidFormat) {
    ElMessage.error('音频格式只支持 MP3/WAV/M4A!')
    return false
  }
  if (!isValidSize) {
    ElMessage.error('音频大小不能超过 20MB!')
    return false
  }
  return true
}

// 上传成功回调
const onUploadSuccess = (response, file) => {
  ElMessage.success('文件上传成功!')
  // 可以在这里处理上传成功后的逻辑
}

// 文件删除回调
const onFileRemove = (file) => {
  // 调用后端API删除文件
  if (file.response && file.response.id) {
    // 发送删除请求到后端
    deleteAttachment(file.response.id)
  }
}
</script>
```

#### 11.4.3 文件存储目录结构

```
media/
├── performance/
│   ├── 2024/
│   │   ├── 12/
│   │   │   ├── 20/
│   │   │   │   ├── images/
│   │   │   │   │   ├── a1b2c3d4_20241220143000_现场照片.jpg
│   │   │   │   │   └── e5f6g7h8_20241220143010_调研图片.png
│   │   │   │   ├── audios/
│   │   │   │   │   ├── m3n4o5p6_20241220143020_现场录音.mp3
│   │   │   │   │   └── q7r8s9t0_20241220143025_会议录音.wav
│   │   │   │   ├── videos/
│   │   │   │   │   └── i9j0k1l2_20241220143030_履职视频.mp4
│   │   │   │   └── documents/
│   │   │   │       └── u1v2w3x4_20241220143040_相关文档.pdf
│   │   │   └── 21/
│   │   └── 11/
│   └── 2023/
└── thumbnails/
    └── performance/
        └── 2024/
            └── 12/
                └── 20/
                    ├── a1b2c3d4_20241220143000_thumb.jpg
                    └── i9j0k1l2_20241220143030_thumb.jpg
```

**文件命名规则说明**：
- **格式**：`{uuid}_{timestamp}_{original_name}`
- **UUID部分**：8位十六进制字符，确保文件唯一性
- **时间戳部分**：`YYYYMMDDHHMMSS`格式，便于按时间排序
- **原始文件名**：保留用户上传时的文件名（去除路径和特殊字符）
- **缩略图命名**：在UUID和时间戳后添加`_thumb`后缀

#### 11.4.4 数据清理和维护

```python
# 定期清理孤立文件的任务
class AttachmentCleanupService:
    
    @staticmethod
    def cleanup_orphaned_files():
        """清理数据库中已删除但文件系统中仍存在的文件"""
        import os
        from django.conf import settings
        
        # 获取所有有效的附件文件路径
        valid_files = set(
            PerformanceAttachment.objects.values_list('file_path', flat=True)
        )
        valid_thumbnails = set(
            PerformanceAttachment.objects.exclude(thumbnail_path__isnull=True)
            .values_list('thumbnail_path', flat=True)
        )
        
        # 扫描文件系统中的实际文件
        media_root = settings.MEDIA_ROOT
        performance_dir = os.path.join(media_root, 'performance')
        
        if os.path.exists(performance_dir):
            for root, dirs, files in os.walk(performance_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, media_root)
                    
                    # 如果文件不在有效文件列表中，则删除
                    if relative_path not in valid_files and relative_path not in valid_thumbnails:
                        try:
                            os.remove(file_path)
                            print(f"Deleted orphaned file: {relative_path}")
                        except Exception as e:
                            print(f"Failed to delete {relative_path}: {e}")
    
    @staticmethod 
    def generate_missing_thumbnails():
        """为缺少缩略图的图片和视频生成缩略图"""
        attachments = PerformanceAttachment.objects.filter(
            file_type__in=['image', 'video'],
            thumbnail_path__isnull=True
        )
        
        for attachment in attachments:
            try:
                thumbnail_path = PerformanceAttachmentService.generate_thumbnail(
                    attachment.file_path, 
                    attachment.file_type
                )
                attachment.thumbnail_path = thumbnail_path
                attachment.save()
            except Exception as e:
                print(f"Failed to generate thumbnail for {attachment.id}: {e}")
```

## 12. 版本变更记录

| 版本 | 日期 | 变更内容 | 变更人 |
|------|------|----------|--------|
| V1.0 | 2024-12-20 | 初始版本，基于需求分析设计完整数据库结构 | AI助手 |
| V1.1 | 2024-12-20 | 添加action与status映射关系和应用层实现指南 | AI助手 |
| V1.2 | 2024-12-20 | 新增履职记录附件表设计，支持多媒体文件存储管理 | AI助手 |
| V1.3 | 2024-12-21 | 根据实际模型更新：新增代表所属片区和头像字段、群众意见表、调整工作计划关联关系、更新文件大小限制 | AI助手 |

---

**注意**: 本设计文档基于当前需求分析，随着业务发展可能需要调整。建议在实施过程中根据实际情况进行优化和完善。 