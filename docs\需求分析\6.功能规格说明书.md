# 功能规格说明书 (FSD)

## 1. 修订历史

| 版本 | 日期       | 修订人 | 修订说明         |
| :--- | :--------- | :----- | :--------------- |
| V1.0 | 2024-07-29 | AI助手 | 初稿，基于MVP需求 |
| V1.1 | 2024-07-30 | AI助手 | 补充辅助诉前调解模块P1功能规格 |
| V1.2 | 2024-07-31 | AI助手 | 修正MVP列表与P0详情对应关系；补充P1功能F-RM-003, F-RM-004详细规格 |
| V2.0 | 2024-12-20 | AI助手 | 根据原始需求重构：删除诉前调解功能，新增工作台概览、工作计划管理、站点工作分析功能 |

## 2. 引言

本文档详细描述了"人大代表履职服务与管理平台"的功能规格，旨在为开发团队提供清晰、准确的功能实现依据。文档内容将根据项目迭代逐步完善。

## 3. 功能总览

### 3.1 系统模块图 (更新版)

```mermaid
graph TD
    A["用户认证模块"] --> B{"人大代表履职服务与管理平台"};
    B --> C["工作台概览模块"];
    C --> C1["代表工作台概览 F-DB-001"];
    C --> C2["站点工作人员工作台概览 F-DB-002"];

    B --> D["代表信息与履职管理模块"];
    D --> D1["代表基本信息管理 F-RM-001"];
    D --> D2["履职记录管理 F-RM-002"];
    D --> D3["年度履职AI分析 F-RM-003 (P1)"];
    D --> D4["年度履职成果AI展示 F-RM-004 (P1)"];

    B --> E["工作计划管理模块"];
    E --> E1["工作计划录入管理 F-WP-001"];
    E --> E2["工作计划提醒功能 F-WP-002"];

    B --> F["站点工作分析模块"];
    F --> F1["站点工作总结 F-WA-001"];
    F --> F2["代表工作总结 F-WA-002"];

    B --> G["意见建议互动模块"];
    G --> G1["意见建议录入 F-IM-001"];
    G --> G2["AI辅助生成高质量意见建议 F-IM-002"];
    G --> G3["代表审核与提交 F-IM-003"];
    G --> G4["工作人员审核与标记 F-IM-004"];
    G --> G5["办理情况更新 F-IM-005"];

    B --> H["用户与权限管理模块"];
    H --> H1["用户登录 F-UM-001"];
    H --> H2["角色权限控制 F-UM-002"];
    H --> H3["代表密码修改 F-UM-003"];
    H --> H4["账号管理 F-UM-004"];

    B --> I["通知模块"];
    I --> I1["站内通知 F-NT-001 (P1)"];
    I --> I2["短信通知 F-NT-002 (P2)"];

    B --> J["法律政策知识问答模块 (P1)"];
    J --> J1["知识查询 F-KQ-001 (P1)"];

    K["外部AI系统"];
    L["短信服务商"];

    K -.-> D3;
    K -.-> D4;
    K -.-> G2;
    K -.-> F1;
    K -.-> F2;
    K -.-> J1;

    L -.-> I2;
```

### 3.2 MVP核心功能列表汇总

| 功能ID   | 所属模块         | 功能名称                     | 优先级 | 用户故事关联 |
| :------- | :--------------- | :--------------------------- | :----- | :----------- |
| F-DB-001 | 工作台概览模块   | 代表工作台概览               | P0     | US-DB-001    |
| F-DB-002 | 工作台概览模块   | 站点工作人员工作台概览       | P0     | US-DB-002    |
| F-RM-001 | 代表信息与履职管理 | 代表基本信息管理             | P0     | US-RM-001    |
| F-RM-002 | 代表信息与履职管理 | 履职记录管理                 | P0     | US-RM-002    |
| F-IM-001 | 意见建议互动模块 | 代表录入与AI辅助生成高质量意见建议 | P0     | US-IM-001    |
| F-IM-002 | 意见建议互动模块 | 代表提交意见建议至站点           | P0     | US-IM-002    |
| F-IM-003 | 意见建议互动模块 | 站点工作人员审核意见建议并标记转交 | P0     | US-IM-003    |
| F-IM-004 | 意见建议互动模块 | 站点工作人员更新意见建议办理情况   | P0     | US-IM-004    |
| F-IM-005 | 意见建议互动模块 | 站点工作人员标记意见建议办结       | P0     | US-IM-005    |
| F-IM-006 | 意见建议互动模块 | 人大代表查看意见建议处理全过程与结果 | P0     | US-IM-006    |
| F-UM-001 | 用户与权限管理   | 用户登录                     | P0     | US-UM-001    |
| F-UM-002 | 用户与权限管理   | 角色权限控制 (MVP范围)       | P0     | US-UM-001    |
| F-UM-003 | 用户与权限管理   | 代表密码修改                 | P0     | US-UM-002    |
| F-UM-004 | 用户与权限管理   | 账号管理                     | P0     | US-UM-003    |

### 3.3 P1 核心功能列表汇总 (MVP后迭代)

| 功能ID   | 所属模块         | 功能名称                     | 优先级 | 用户故事关联 |
| :------- | :--------------- | :--------------------------- | :----- | :----------- |
| F-WP-001 | 工作计划管理模块 | 工作计划录入管理             | P1     | US-WP-001    |
| F-WP-002 | 工作计划管理模块 | 工作计划提醒功能             | P1     | US-WP-002    |
| F-WA-001 | 站点工作分析模块 | 站点工作总结                 | P1     | US-WA-001    |
| F-WA-002 | 站点工作分析模块 | 代表工作总结                 | P1     | US-WA-002    |
| F-RM-003 | 代表信息与履职管理 | 年度履职AI分析               | P1     | US-RM-003    |
| F-RM-004 | 代表信息与履职管理 | 年度履职成果AI展示           | P1     | US-RM-004    |
| F-NT-001 | 通知模块         | 站内通知                     | P1     | US-NT-001    |
| F-KQ-001 | 法律政策知识问答模块 | 知识查询                 | P1     | US-KQ-001    |

## 4. 详细功能规格 (MVP - P0)

### 模块一：用户与权限管理 (User & Auth Management)

#### F-UM-001: 用户登录
*   **优先级**: P0
*   **所属模块**: 用户与权限管理
*   **功能描述**: 用户（人大代表、站点工作人员）通过系统提供的登录界面，使用预设的用户名和密码进行身份验证，成功后进入系统主界面。
*   **用户故事关联**: US-UM-001
*   **业务规则与逻辑**:
    1.  用户在登录页面输入用户名和密码。
    2.  系统校验用户名和密码的有效性。
    3.  校验成功，记录用户登录状态（如Session或Token），根据用户角色跳转到对应的主界面/仪表盘。
    4.  校验失败，提示错误信息（如"用户名或密码错误"），用户可重新输入。
    5.  应提供"记住密码"（可选）或"忘记密码"（P2或后续考虑）功能。
    6.  对连续登录失败次数进行限制，达到阈值后可临时锁定账户。
*   **业务流程图**:
    ```mermaid
    graph TD
        A[用户打开登录页] --> B{输入用户名密码};
        B --> C{校验信息是否为空};
        C -- 是 --> D[提示必填信息];
        D --> B;
        C -- 否 --> E{后端校验凭据};
        E -- 成功 --> F[记录登录态];
        F --> G[跳转至系统主页];
        E -- 失败 --> H[提示登录失败];
        H --> B;
    ```
*   **界面原型描述/线框图说明**:
    *   登录页面包含：系统名称/Logo、用户名输入框、密码输入框、登录按钮、"记住我"复选框。
*   **输入定义**:
    | 字段名   | 类型   | 长度    | 是否必填 | 校验规则                               | 默认值 | 来源/示例    |
    | :------- | :----- | :------ | :------- | :------------------------------------- | :----- | :----------- |
    | 用户名   | String | 50      | 是       | 非空, 符合特定字符集（如字母数字下划线） |        | 用户输入     |
    | 密码     | String | 50      | 是       | 非空                                   |        | 用户输入     |
*   **输出/系统响应**:
    *   **成功场景**: 页面跳转至用户对应角色的系统主界面。
    *   **失败/异常场景**:
        | 错误场景             | 错误码 (可选) | 提示信息         | 用户引导     | 系统逻辑         |
        | :------------------- | :------------ | :--------------- | :----------- | :--------------- |
        | 用户名或密码为空     | LGN_ERR_001   | 用户名和密码不能为空 | 重新输入     | 停留在登录页   |
        | 用户名或密码错误     | LGN_ERR_002   | 用户名或密码错误   | 重新输入     | 停留在登录页   |
        | 账户被锁定           | LGN_ERR_003   | 账户已锁定，请稍后再试 | 联系管理员 | 停留在登录页   |
*   **数据依赖与影响**: 依赖用户表 (存储用户名、加密密码、角色、状态等)。
*   **验收标准 (GWT)**:
    1.  **AC-UM-001.1**: Given 用户提供正确的用户名和密码; When 用户点击登录按钮; Then 系统验证通过并跳转到该用户对应角色的主页。
    2.  **AC-UM-001.2**: Given 用户提供错误的用户名或密码; When 用户点击登录按钮; Then 系统提示"用户名或密码错误"且仍停留在登录页。
    3.  **AC-UM-001.3**: Given 用户未输入用户名或密码; When 用户点击登录按钮; Then 系统提示"用户名和密码不能为空"且仍停留在登录页。

#### F-UM-002: 角色权限控制 (MVP范围)
*   **优先级**: P0
*   **所属模块**: 用户与权限管理
*   **功能描述**: 系统根据登录用户的角色（人大代表、站点工作人员），控制其对MVP范围内核心功能的访问权限。例如，代表不能审核意见，工作人员不能直接以代表身份录入履职。
*   **用户故事关联**: US-UM-001
*   **业务规则与逻辑**:
    1.  系统预定义"人大代表"和"站点工作人员"两种角色。
    2.  每种角色对应一组固定的功能访问权限列表（针对MVP核心功能）。
    3.  用户登录后，系统根据其角色加载对应的操作菜单和界面元素。
    4.  用户尝试访问无权限功能时，系统应明确阻止或隐藏入口。
    5.  MVP阶段，角色和权限为硬编码或简单配置，不提供动态权限管理界面。
*   **界面原型描述/线框图说明**: 无特定界面，权限控制体现在不同角色登录后可见菜单和操作按钮的差异。
*   **输入定义**: 用户角色 (系统内部获取)
*   **输出/系统响应**:
    *   **成功场景**: 用户只能看到并操作其角色权限范围内的功能。
    *   **失败/异常场景**: (例如通过URL直接访问无权限页面)
        | 错误场景         | 错误码 (可选) | 提示信息     | 用户引导         | 系统逻辑             |
        | :--------------- | :------------ | :----------- | :--------------- | :------------------- |
        | 访问无权限功能/页面 | AUTH_ERR_001  | 您没有权限访问此功能 | 返回上一页或主页 | 跳转到无权限提示页面 |
*   **数据依赖与影响**: 依赖用户角色表、角色权限关联表 (或硬编码逻辑)。
*   **验收标准 (GWT)**:
    1.  **AC-UM-002.1**: Given 用户以"人大代表"角色登录; When 用户访问系统; Then 用户可以看到并操作代表相关的履职记录、意见提交等功能; And 用户不能看到或操作站点工作人员的意见审核功能。
    2.  **AC-UM-002.2**: Given 用户以"站点工作人员"角色登录; When 用户访问系统; Then 用户可以看到并操作意见审核、更新办理状态等功能; And 用户不能直接操作代表的履职记录录入功能。

#### F-UM-003: 代表密码修改
*   **优先级**: P0
*   **所属模块**: 用户与权限管理
*   **功能描述**: 人大代表可以修改自己登录账号的密码。
*   **用户故事关联**: US-UM-002
*   **业务规则与逻辑**:
    1.  代表用户登录后，可访问"账号密码修改"页面。
    2.  页面包含：当前密码输入框、新密码输入框、确认新密码输入框。
    3.  系统校验当前密码是否正确。
    4.  校验新密码与确认密码是否一致。
    5.  校验新密码强度（长度、复杂度要求）。
    6.  修改成功后更新数据库，并提示用户操作成功。
*   **界面原型描述/线框图说明**:
    *   密码修改页面：表单形式，包含三个密码输入框和保存按钮。
*   **输入定义**:
    | 字段名     | 类型   | 长度    | 是否必填 | 校验规则               | 默认值   | 来源/示例  |
    | :--------- | :----- | :------ | :------- | :--------------------- | :------- | :--------- |
    | 当前密码   | String | 50      | 是       | 非空                   |          | 用户输入   |
    | 新密码     | String | 50      | 是       | 非空，符合密码强度要求 |          | 用户输入   |
    | 确认新密码 | String | 50      | 是       | 非空，与新密码一致     |          | 用户输入   |
*   **输出/系统响应**:
    *   **成功场景**: 密码修改成功后提示"密码修改成功"。
    *   **失败/异常场景**:
        | 错误场景       | 错误码 (可选) | 提示信息         | 用户引导   | 系统逻辑     |
        | :------------- | :------------ | :--------------- | :--------- | :----------- |
        | 当前密码错误   | PWD_ERR_001   | 当前密码输入错误 | 重新输入   | 停留在修改页 |
        | 两次密码不一致 | PWD_ERR_002   | 两次输入的新密码不一致 | 重新输入 | 停留在修改页 |
        | 密码强度不够   | PWD_ERR_003   | 密码强度不够，请重新设置 | 重新输入 | 停留在修改页 |
*   **数据依赖与影响**: 依赖用户表。
*   **验收标准 (GWT)**:
    1.  **AC-UM-003.1**: Given 人大代表输入正确的当前密码和符合要求的新密码; When 代表点击保存; Then 系统提示"密码修改成功"且代表可用新密码登录。

#### F-UM-004: 账号管理
*   **优先级**: P0
*   **所属模块**: 用户与权限管理
*   **功能描述**: 站点工作人员可以管理所有账号，包括代表和工作人员的账号信息。
*   **用户故事关联**: US-UM-003
*   **业务规则与逻辑**:
    1.  站点工作人员可访问"账号管理"页面。
    2.  页面展示所有用户账号列表，包括用户名、角色、状态等信息。
    3.  工作人员可以新增账号、编辑账号信息、重置密码、启用/禁用账号。
    4.  操作记录应被日志记录。
*   **界面原型描述/线框图说明**:
    *   账号管理页面：列表展示所有账号，提供新增、编辑、重置密码等操作按钮。
*   **验收标准 (GWT)**:
    1.  **AC-UM-004.1**: Given 站点工作人员登录系统; When 工作人员访问账号管理页面; Then 可以看到所有用户账号列表并进行管理操作。

### 模块二：工作台概览模块 (Dashboard Management)

#### F-DB-001: 代表工作台概览
*   **优先级**: P0
*   **所属模块**: 工作台概览模块
*   **功能描述**: 为人大代表提供类似dashboard的页面，展示个人工作概况和快捷操作入口。
*   **用户故事关联**: US-DB-001
*   **业务规则与逻辑**:
    1.  代表登录后的主页面，展示个人工作数据概况。
    2.  展示内容包括：本月履职活动数量、待处理意见数量、已提交意见状态分布等。
    3.  提供快捷操作入口：新增履职记录、查看待处理意见、录入群众意见等。
    4.  展示最近的履职记录和意见处理动态。
*   **界面原型描述/线框图说明**:
    *   Dashboard布局：数据卡片、快捷操作按钮、最新动态列表。
*   **验收标准 (GWT)**:
    1.  **AC-DB-001.1**: Given 人大代表登录系统; When 进入主页; Then 可以看到个人工作数据概况和快捷操作入口。

#### F-DB-002: 站点工作人员工作台概览
*   **优先级**: P0
*   **所属模块**: 工作台概览模块
*   **功能描述**: 为站点工作人员提供类似dashboard的页面，展示站点工作概况和快捷操作入口。
*   **用户故事关联**: US-DB-002
*   **业务规则与逻辑**:
    1.  工作人员登录后的主页面，展示站点工作数据概况。
    2.  展示内容包括：待审核意见数量、本月处理意见数量、工作计划完成情况等。
    3.  提供快捷操作入口：意见审核、工作计划管理、账号管理等。
    4.  展示最近的工作动态和提醒事项。
*   **验收标准 (GWT)**:
    1.  **AC-DB-002.1**: Given 站点工作人员登录系统; When 进入主页; Then 可以看到站点工作数据概况和快捷操作入口。

### 模块三：代表信息与履职管理 (Representative & Performance Management)

#### F-RM-001: 代表基本信息管理
*   **优先级**: P0
*   **所属模块**: 代表信息与履职管理
*   **功能描述**: 人大代表登录系统后，可以查看并编辑自己的基本信息，包含完整的11个属性字段。
*   **用户故事关联**: US-RM-001
*   **业务规则与逻辑**:
    1.  人大代表用户登录后，可访问"个人信息管理"页面。
    2.  页面展示代表的完整信息：代表层级、姓名、性别、民族、出生日期、籍贯、党派、现任职务、移动电话号码、学历、毕业院校、所学专业。
    3.  部分字段允许代表自行修改（如联系方式），部分关键信息（如姓名、代表层级）可能设为只读或需管理员修改。
    4.  修改后点击保存，系统更新数据库中对应信息。
*   **界面原型描述/线框图说明**:
    *   "个人信息管理"页面，表单形式展示各项信息，可编辑字段提供输入框，只读字段为文本显示。
    *   包含"编辑"和"保存/取消"按钮。
*   **输入定义**:
    | 字段名       | 类型   | 长度    | 是否必填 | 校验规则               | 默认值   | 来源/示例  |
    | :----------- | :----- | :------ | :------- | :--------------------- | :------- | :--------- |
    | 代表层级     | String | 50      | 是       | 只读字段               |          | 系统预设   |
    | 姓名         | String | 50      | 是       | 只读字段               |          | 系统预设   |
    | 性别         | String | 10      | 是       | 男/女                  |          | 用户选择   |
    | 民族         | String | 50      | 是       | 从民族列表选择         |          | 用户选择   |
    | 出生日期     | Date   |         | 是       | 有效日期格式           |          | 用户输入   |
    | 籍贯         | String | 100     | 是       |                        |          | 用户输入   |
    | 党派         | String | 50      | 是       | 从党派列表选择         |          | 用户选择   |
    | 现任职务     | String | 100     | 是       |                        |          | 用户输入   |
    | 移动电话号码 | String | 11      | 是       | 有效的手机号码格式     |          | 用户输入   |
    | 学历         | String | 50      | 是       | 从学历列表选择         |          | 用户选择   |
    | 毕业院校     | String | 100     | 否       |                        |          | 用户输入   |
    | 所学专业     | String | 100     | 否       |                        |          | 用户输入   |
*   **输出/系统响应**:
    *   **成功场景**: 保存成功后提示"信息更新成功"，页面数据刷新。
    *   **失败/异常场景**:
        | 错误场景     | 错误码 (可选) | 提示信息     | 用户引导   | 系统逻辑     |
        | :----------- | :------------ | :----------- | :--------- | :----------- |
        | 输入格式错误 | PROF_ERR_001  | 请输入有效的手机号/日期格式 | 重新输入 | 停留在编辑状态 |
        | 必填项为空   | PROF_ERR_002  | 请填写所有必填项 | 补充信息 | 停留在编辑状态 |
        | 保存失败     | PROF_ERR_003  | 信息保存失败，请重试 | 重试 | 停留在编辑状态 |
*   **数据依赖与影响**: 依赖代表信息表。
*   **验收标准 (GWT)**:
    1.  **AC-RM-001.1**: Given 人大代表登录并进入个人信息页; When 代表修改其可编辑字段并点击保存; Then 系统提示"信息更新成功"且页面显示的信息已更新。
    2.  **AC-RM-001.2**: Given 人大代表登录并进入个人信息页; When 代表查看信息; Then 页面显示完整的11个属性字段信息。

#### F-RM-002: 履职记录管理
*   **优先级**: P0
*   **所属模块**: 代表信息与履职管理
*   **功能描述**: 人大代表可以录入、查看、编辑和删除自己的履职记录，包含完整的6个履职属性字段。
*   **用户故事关联**: US-RM-002
*   **业务规则与逻辑**:
    1.  代表用户可访问"履职记录管理"模块。
    2.  提供"新增履职记录"功能，表单包含：履职日期、履职类型、履职内容、活动地点、详细描述、履职状态。
    3.  列表展示已录入的履职记录，按履职日期倒序排列，支持分页。
    4.  每条记录可进行"编辑"或"删除"操作。
    5.  删除前应有确认提示。
*   **界面原型描述/线框图说明**:
    *   列表页：包含"新增"按钮、履职记录列表（显示关键信息如日期、类型、内容摘要）。
    *   新增/编辑页：表单包含上述输入字段，以及"保存"、"取消"按钮。
*   **输入定义**:
    | 字段名     | 类型    | 长度    | 是否必填 | 校验规则                               | 默认值 | 来源/示例                                  |
    | :--------- | :------ | :------ | :------- | :------------------------------------- | :----- | :----------------------------------------- |
    | 履职日期   | Date    |         | 是       | 非空, 合法日期格式                     | 今天   | 用户选择                                   |
    | 履职类型   | String  | 50      | 是       | 非空, 从预设下拉列表中选择             |        | 用户选择 (会议参与, 议案建议等)            |
    | 履职内容   | Text    | 1000    | 是       | 非空                                   |        | 用户输入                                   |
    | 活动地点   | String  | 200     | 是       | 非空                                   |        | 用户输入                                   |
    | 详细描述   | Text    | 2000    | 否       |                                        |        | 用户输入                                   |
    | 履职状态   | String  | 50      | 是       | 非空, 从预设状态列表中选择             |        | 用户选择 (进行中, 已完成等)                |
*   **输出/系统响应**:
    *   **成功场景**: 新增/编辑保存成功后提示"操作成功"，列表刷新。删除成功后提示"删除成功"，记录从列表移除。
    *   **失败/异常场景**:
        | 错误场景     | 错误码 (可选) | 提示信息     | 用户引导   | 系统逻辑         |
        | :----------- | :------------ | :----------- | :--------- | :--------------- |
        | 必填项为空   | PERF_ERR_001  | 请填写所有必填项 | 补充信息   | 停留在编辑/新增页 |
        | 保存失败     | PERF_ERR_002  | 操作失败，请重试 | 重试       | 停留在编辑/新增页 |
*   **数据依赖与影响**: 依赖履职记录表。
*   **验收标准 (GWT)**:
    1.  **AC-RM-002.1**: Given 人大代表登录并进入履职记录模块; When 代表填写完整的6个履职属性字段并点击保存; Then 系统提示"操作成功"且新记录显示在列表顶部。
    2.  **AC-RM-002.2**: Given 人大代表查看其履职记录列表; When 代表点击某条记录的编辑按钮，修改内容后保存; Then 系统提示"操作成功"且列表中的该记录已更新。
    3.  **AC-RM-002.3**: Given 人大代表查看其履职记录列表; When 代表点击某条记录的删除按钮并确认; Then 系统提示"删除成功"且该记录从列表消失。

### 模块四：意见建议互动模块 (IM)

#### 功能点 IM-1: 代表录入与AI辅助生成高质量意见建议

*   **功能ID**: F-IM-001
*   **优先级**: P0
*   **所属模块**: 意见建议互动模块
*   **功能描述**: 人大代表录入收集到的群众意见建议信息（包括意见建议标题、分类、反映人、内容等），系统支持代表选择是否调用外部AI对意见内容进行分析，并生成高质量、规范化的意见建议草稿，供代表参考和修改。
*   **用户故事关联**: US-IM-001
*   **业务规则与逻辑**:
    1.  代表选择录入新的意见建议时，系统提供表单供代表填写，至少包括：意见建议标题（必填）、意见建议分类（必填）、反映人（必填）、意见建议内容（必填）。
    2.  表单提交前，代表可选择"AI辅助生成高质量意见建议"选项。
    3.  若选择AI辅助，系统在保存基础信息后，将意见建议内容通过API传递给外部AI知识库系统。
    4.  外部AI系统返回分析结果和高质量意见建议草稿。
    5.  系统将AI生成的高质量意见建议草稿展示给代表，代表可以此为基础进行修改。
    6.  若不选择AI辅助，代表可直接手动填写或修改意见建议内容。
    7.  系统保存原始信息和代表最终确认的意见建议（或其自行撰写的意见建议）。此时意见建议状态为"草稿"或"待提交"。
*   **业务流程图**:
    ```mermaid
    graph TD
        A[代表进入意见建议模块] --> B{选择新增意见建议};
        B --> C[填写意见建议表单];
        C --> D{是否使用AI辅助?};
        D -- 是 --> E[系统调用外部AI接口];
        E --> F[AI返回高质量意见建议草稿];
        F --> G[代表查看并修改AI建议];
        D -- 否 --> H[代表自行撰写/修改意见建议];
        G --> I[保存意见建议草稿];
        H --> I;
        I --> J[意见建议状态: 待提交];
    ```
*   **界面原型描述/线框图说明**: (略，详见UI原型 UIDemo/representative_submit_opinion.html)
    *   包含意见建议录入表单，有明确的"AI辅助生成高质量意见建议"的勾选项或按钮。
    *   AI高质量意见建议生成后，清晰展示在表单下方或旁边，并提供编辑功能。
*   **输入定义**:
    | 字段名         | 类型   | 长度    | 是否必填 | 校验规则         | 默认值 | 来源/示例                                |
    | :------------- | :----- | :------ | :------- | :--------------- | :----- | :--------------------------------------- |
    | 意见建议标题       | 文本   | 适中    | 是       | 非空             |        | "关于改善小区停车位不足问题的建议"          |
    | 意见建议分类       | 下拉选择 |         | 是       | 必选             |        | (预设分类，如：城建、交通、民生等)         |
    | 反映人 | 文本   | 适中    | 是       | 非空                  |        | "张三" 或 "匿名群众"                       |
    | 意见建议内容       | 文本域 | 较长    | 是       | 非空             |        | "我们小区停车位严重不足，建议增设立体停车场..."                            |
    | AI辅助选择    | 布尔   |         | 是       |                  | False  |                                          |
*   **输出/系统响应**:
    *   成功场景：意见建议及（可选的）AI生成的高质量意见建议草稿保存成功，代表可在个人意见建议列表中看到该条记录，状态为"待提交"。
    *   失败/异常场景:
        | 错误码 | 提示信息                 | 用户引导       | 系统逻辑                                     |
        | :----- | :----------------------- | :------------- | :------------------------------------------- |
        | IM1001 | 意见建议标题、分类、反映人、内容均不能为空。       | 提示用户填写   | 阻止提交                                     |
        | IM1002 | AI服务调用失败，请稍后重试或跳过AI辅助。 | 提示用户       | 记录错误日志，允许用户选择不使用AI辅助继续 |
*   **数据依赖与影响**: 无
*   **验收标准**:
    1.  AC-IM-001-01: Given 代表登录系统并进入意见建议模块, When 代表填写必填的意见建议标题、分类、反映人、内容，并点击保存, Then 系统成功保存意见建议，状态为"待提交"。
    2.  AC-IM-001-02: Given 代表在录入意见建议时选择了"AI辅助生成高质量意见建议", When 代表提交基础信息, Then 系统调用外部AI接口，并将返回的高质量意见建议草稿展示给代表进行编辑。
    3.  AC-IM-001-03: Given AI服务调用失败, When 代表尝试使用AI辅助, Then 系统应提示用户AI服务不可用，并允许用户选择跳过AI辅助，直接手动编辑意见建议。

#### 功能点 IM-2: 代表提交意见建议至站点

*   **功能ID**: F-IM-002
*   **优先级**: P0
*   **所属模块**: 意见建议互动模块
*   **功能描述**: 人大代表在确认意见建议内容无误后，将该意见建议从个人草稿状态正式提交给其所属服务站点的工作人员进行审核。
*   **用户故事关联**: US-IM-002
*   **业务规则与逻辑**:
    1.  代表在"我的意见"列表中，选择处于"待提交"状态的意见。
    2.  代表可以最后预览并确认意见的全部内容（原始意见和最终建议）。
    3.  代表点击"提交至站点"按钮。
    4.  系统校验该意见是否符合提交条件（例如，建议内容不能为空）。
    5.  校验通过后，系统将该意见的状态更新为"待站点审核"。
    6.  系统向对应的站点工作人员发送新意见待审核的通知（站内通知，后续可扩展短信通知）。
*   **业务流程图**:
    ```mermaid
    graph TD
        A[代表在个人意见列表选择待提交意见] --> B[预览并确认内容]
        B --> C{确认提交?}
        C -->|是| D[系统校验意见内容]
        D -->|通过| E[更新意见状态为"待站点审核"]
        E --> F[通知对应站点工作人员]
        F --> G[操作完成]
        D -->|不通过| H[提示错误信息]
    ```
*   **界面原型描述/线框图说明**: (略，详见UI原型 UIDemo/representative_submit_opinion.html)
    *   在意见详情或列表项中，有明确的"提交至站点"按钮。
    *   提交成功后有明确的提示信息。
*   **输入定义**: 无用户直接输入，为用户操作触发。
*   **输出/系统响应**:
    *   成功场景：意见成功提交至站点，状态更新为"待站点审核"。代表在"我的意见"列表中看到状态变化。对应的站点工作人员收到通知。
    *   失败/异常场景:
        | 错误码 | 提示信息             | 用户引导     | 系统逻辑         |
        | :----- | :------------------- | :----------- | :--------------- |
        | IM2001 | 建议内容不能为空后提交。 | 提示用户完善 | 阻止提交         |
*   **数据依赖与影响**: 依赖F-IM-001中已保存的意见数据。
*   **验收标准**:
    1.  AC-IM-002-01: Given 代表有一条状态为"待提交"的意见, When 代表点击"提交至站点"按钮, Then 该意见的状态更新为"待站点审核"，并且对应的站点工作人员收到通知。
    2.  AC-IM-002-02: Given 代表尝试提交一条建议内容为空的意见, When 代表点击"提交至站点"按钮, Then 系统应阻止提交，并提示用户"建议内容不能为空"。

#### 功能点 IM-3: 站点工作人员审核意见并标记转交

*   **功能ID**: F-IM-003
*   **优先级**: P0
*   **所属模块**: 互动模块
*   **功能描述**: 站点工作人员查看人大代表提交的意见和建议，进行审核。审核通过的，工作人员在系统外手动将意见转交给相关政府职能部门后，在系统中将该意见标记为"已转交【具体部门】"；审核不通过的，可退回给代表并说明理由。
*   **用户故事关联**: US-IM-003
*   **业务规则与逻辑**:
    1.  站点工作人员在其工作台或意见管理列表中看到状态为"待站点审核"的意见。
    2.  工作人员打开意见详情，查看原始意见和代表提交的建议。
    3.  工作人员进行审核，可选择操作：
        *   **审核通过并转交**：
            *   工作人员在系统外完成向具体职能部门的意见转交流程。
            *   在系统中选择"审核通过并标记转交"。
            *   系统要求工作人员输入或选择接收意见的"职能部门名称"（必填）。
            *   可选填写备注，如交接人、交接时间等。
            *   意见状态更新为"已转交【职能部门名称】"。
            *   通知人大代表其意见已审核通过并转交。
        *   **审核不通过**：
            *   选择"审核不通过"。
            *   必须填写不通过的理由/修改建议。
            *   意见状态更新为"站点审核不通过"。
            *   通知人大代表其意见被驳回及原因，代表可修改后重新提交。
    4.  所有操作均记录操作人（当前站点工作人员）和操作时间。
*   **业务流程图**:
    ```mermaid
    graph TD
        A[工作人员查看待审核意见] --> B{审核操作}
        B -->|审核通过并转交| C[系统外手动转交意见]
        C --> D[系统内填写转交部门名称]
        D --> E[更新意见状态为"已转交[部门]"]
        E --> F[通知代表]
        B -->|审核不通过| G[填写驳回理由]
        G --> H[更新意见状态为"站点审核不通过"]
        H --> F
        F --> I[操作完成]
    ```
*   **界面原型描述/线框图说明**: (略，详见UI原型 UIDemo/staff_review_opinion.html)
    *   意见详情页清晰展示原始意见和代表建议。
    *   提供"审核通过并标记转交"和"审核不通过"的操作按钮。
    *   若通过，则有输入字段供填写"转交部门名称"和可选的备注。
    *   若不通过，则有文本域供填写理由。
*   **输入定义**:
    | 字段名       | 类型   | 长度 | 是否必填 | 校验规则 | 默认值 | 来源/示例         |
    | :----------- | :----- | :--- | :------- | :------- | :----- | :---------------- |
    | 转交部门名称 | 文本   | 适中 | 是（若通过） | 非空     |        | "市交通局"      |
    | 审核备注/理由 | 文本域 | 较长 | 是（若驳回） | 非空     |        | "建议补充更详细数据" |
*   **输出/系统响应**:
    *   成功场景：
        *   审核通过：意见状态更新为"已转交[部门名称]"，代表收到通知。相关信息（部门、备注、操作人、时间）被记录。
        *   审核不通过：意见状态更新为"站点审核不通过"，代表收到通知及驳回理由。
    *   失败/异常场景:
        | 错误码 | 提示信息                   | 用户引导     | 系统逻辑         |
        | :----- | :------------------------- | :----------- | :--------------- |
        | IM3001 | 选择通过时，转交部门名称不能为空。 | 提示用户填写 | 阻止操作         |
        | IM3002 | 选择不通过时，驳回理由不能为空。 | 提示用户填写 | 阻止操作         |
*   **数据依赖与影响**: 依赖F-IM-002中已提交至站点的意见数据。
*   **验收标准**:
    1.  AC-IM-003-01: Given 站点工作人员查看一条"待站点审核"的意见, When 工作人员选择"审核通过并标记转交"，并填写了"转交部门名称", Then 意见状态更新为"已转交[部门名称]"，并通知提交该意见的人大代表。
    2.  AC-IM-003-02: Given 站点工作人员查看一条"待站点审核"的意见, When 工作人员选择"审核不通过"，并填写了驳回理由, Then 意见状态更新为"站点审核不通过"，并将驳回理由通知提交该意见的人大代表。
    3.  AC-IM-003-03: Given 站点工作人员尝试审核通过一个意见但不填写"转交部门名称", Then 系统应阻止操作并提示错误。
    4.  AC-IM-003-04: Given 站点工作人员尝试驳回一个意见但不填写理由, Then 系统应阻止操作并提示错误。

#### 功能点 IM-4: 站点工作人员更新意见办理情况

*   **功能ID**: F-IM-004
*   **优先级**: P0
*   **所属模块**: 互动模块
*   **功能描述**: 站点工作人员在从相关政府职能部门获取到已转交意见的办理进展或最终处理结果后，在系统中手动更新这些信息，包括但不限于办理状态的变更、处理结果的文字描述、相关附件的上传等。
*   **用户故事关联**: US-IM-004
*   **业务规则与逻辑**:
    1.  站点工作人员在意见管理列表中找到需要更新办理情况的意见（通常状态为"已转交[部门]"或"部门处理中"）。
    2.  工作人员选择"更新办理情况"或类似操作。
    3.  系统提供界面供工作人员更新以下信息：
        *   **办理状态**：可选择（如：已受理、部门处理中、部门处理完毕待办结、其他）。
        *   **处理结果描述**：文本域，详细记录部门的反馈和处理结果。
        *   **附件上传**：允许上传相关文件（如部门的正式回复函扫描件）。
        *   **本次更新日期**：默认为当前日期。
    4.  每次更新都作为一条新的办理记录追加到该意见下，形成办理历史。
    5.  系统记录本次更新的操作人（当前站点工作人员）和操作时间。
    6.  若本次更新表明意见已被职能部门完全处理完毕，工作人员后续可进行"标记办结"操作 (F-IM-005)。
    7.  更新后，相关的处理信息（除附件外摘要）应能被人大代表在其意见详情中看到。
*   **业务流程图**:
    ```mermaid
    graph TD
        A[工作人员选择要更新的意见] --> B[点击更新办理情况];
        B --> C[填写/选择办理状态、描述、上传附件];
        C --> D[保存更新];
        D --> E[系统记录本次更新历史];
        E --> F[代表可查看到更新的摘要];
        F --> G[操作完成];
    ```
*   **界面原型描述/线框图说明**: (略，详见UI原型 UIDemo/staff_update_progress.html)
    *   意见详情页提供"更新办理情况"按钮。
    *   弹窗或新页面提供办理状态选择、结果描述文本域、附件上传控件。
    *   历史办理记录以列表形式展示。
*   **输入定义**:
    | 字段名         | 类型     | 长度 | 是否必填 | 校验规则     | 默认值 | 来源/示例                                      |
    | :------------- | :------- | :--- | :------- | :----------- | :----- | :--------------------------------------------- |
    | 办理状态       | 下拉选择 |      | 是       |              |        | (已受理, 部门处理中, 部门处理完毕)             |
    | 处理结果描述   | 文本域   | 较长 | 是       | 非空         |        | "相关部门已派人核实，预计下周内解决..."        |
    | 附件           | 文件上传 |      | 否       |              |        |                                                |
*   **输出/系统响应**:
    *   成功场景：办理情况更新成功，新的办理记录被保存。人大代表可以查看到最新的处理进展摘要。
    *   失败/异常场景:
        | 错误码 | 提示信息               | 用户引导     | 系统逻辑     |
        | :----- | :--------------------- | :----------- | :----------- |
        | IM4001 | 办理状态或处理结果描述不能为空。 | 提示用户填写 | 阻止保存     |
*   **数据依赖与影响**: 依赖F-IM-003中已转交的意见数据。
*   **验收标准**:
    1.  AC-IM-004-01: Given 站点工作人员选择一条"已转交"的意见并点击"更新办理情况", When 工作人员填写了办理状态和处理结果描述并保存, Then 系统成功保存该条办理记录，并更新意见的最新处理信息。
    2.  AC-IM-004-02: Given 办理情况更新后, When 提交该意见的人大代表查看意见详情, Then 代表能看到最新的办理状态和处理结果描述摘要。
    3.  AC-IM-004-03: Given 工作人员尝试保存空的办理状态或处理结果描述, Then 系统应阻止保存并提示错误。

#### 功能点 IM-5: 站点工作人员标记意见办结

*   **功能ID**: F-IM-005
*   **优先级**: P0
*   **所属模块**: 互动模块
*   **功能描述**: 对于相关政府职能部门已经处理完毕，并且站点工作人员已在系统中录入完整、最终处理结果的意见，工作人员可以将其在系统中标记为"已办结"，表示该意见的处理流程结束。
*   **用户故事关联**: US-IM-005
*   **业务规则与逻辑**:
    1.  站点工作人员在意见管理列表中找到需要标记办结的意见（通常其最近的办理记录表明"部门处理完毕"或类似状态）。
    2.  工作人员确认该意见的所有处理流程确已完结，且最终结果已完整录入系统。
    3.  工作人员选择"标记办结"操作。
    4.  系统将该意见的最终状态更新为"已办结"。
    5.  意见办结后，通常变为只读状态，不允许再进行"更新办理情况"或修改核心内容等操作（可允许添加备注或追溯信息）。
    6.  系统通知提交该意见的人大代表，其意见已办结，并可查看最终结果。
    7.  记录办结操作人（当前站点工作人员）和操作时间。
*   **业务流程图**:
    ```mermaid
    graph TD
        A[工作人员选择已处理完毕的意见] --> B{确认是否办结?}
        B -->|是| C[点击标记办结按钮]
        C --> D[更新意见状态为"已办结"]
        D --> E[通知人大代表]
        E --> F[意见变为只读或有限操作]
        F --> G[操作完成]
        B -->|否| A
    ```
*   **界面原型描述/线框图说明**: (略，详见UI原型 UIDemo/staff_close_opinion.html)
    *   在意见详情页或列表项中，针对符合办结条件的意见，显示"标记办结"按钮。
    *   办结后，相关编辑操作按钮置灰或隐藏。
*   **输入定义**: 无用户直接输入，为用户操作触发。
*   **输出/系统响应**:
    *   成功场景：意见状态成功更新为"已办结"。人大代表收到通知，并能查看到包括最终结果在内的完整处理历史。该意见变为只读。
    *   失败/异常场景: 无明显业务异常，主要是权限和状态判断。
*   **数据依赖与影响**: 依赖F-IM-004中已更新的办理情况，特别是最终处理结果的录入。
*   **验收标准**:
    1.  AC-IM-005-01: Given 站点工作人员选择一条最终处理结果已完整录入的意见, When 工作人员点击"标记办结", Then 该意见的状态更新为"已办结"，并通知提交该意见的人大代表。
    2.  AC-IM-005-02: Given 一条意见已被标记为"已办结", Then 该意见的主要信息（如原始意见、代表建议、核心处理流程记录）应不可再编辑。

#### 功能点 IM-6: 人大代表查看意见处理全过程与结果

*   **功能ID**: F-IM-006
*   **优先级**: P0
*   **所属模块**: 互动模块
*   **功能描述**: 人大代表可以随时在系统中查看自己提交的群众意见的完整处理流程、当前办理状态以及站点工作人员录入的各阶段处理说明和最终的办理结果。
*   **用户故事关联**: US-IM-006
*   **业务规则与逻辑**:
    1.  人大代表登录系统后，进入"我的意见"或类似的个人意见管理列表。
    2.  列表清晰展示每条意见的关键信息，包括：意见摘要、提交日期、当前状态（如：待提交、待站点审核、站点审核不通过、已转交【部门】、部门处理中、已办结等）。
    3.  代表点击某条意见，可进入详情页面查看：
        *   完整的原始意见内容。
        *   自己提交的建议内容。
        *   站点工作人员的审核结果（包括驳回理由，如有）。
        *   转交的职能部门名称及转交时间（如有）。
        *   站点工作人员历次更新的办理情况记录（按时间倒序排列），包括每次更新的状态、描述、附件（可下载）。
        *   最终的办结状态和办结时间（如有）。
    4.  所有展示信息均为只读。
*   **业务流程图**:
    ```mermaid
    graph TD
        A[代表登录系统] --> B[进入我的意见列表];
        B --> C[列表展示意见摘要和当前状态];
        C --> D{选择查看某条意见详情};
        D --> E[详情页展示完整处理过程和结果];
        E --> F[代表查阅完毕];
    ```
*   **界面原型描述/线框图说明**: (略，详见UI原型 UIDemo/representative_view_opinion.html)
    *   "我的意见"列表，包含状态筛选和搜索功能。
    *   意见详情页，分区域清晰展示原始意见、代表建议、审核信息、转交信息、办理过程记录（列表）、最终结果等。
*   **输入定义**: 无用户直接输入，为数据查询和展示。
*   **输出/系统响应**:
    *   成功场景：代表能清晰、完整地查看到所选意见从提交到办结（或当前最新状态）的全过程信息。
    *   失败/异常场景: 无明显业务异常，主要是数据查询和展示的正确性。
*   **数据依赖与影响**: 依赖F-IM-001至F-IM-005所有功能点产生和更新的数据。
*   **验收标准**:
    1.  AC-IM-006-01: Given 代表在"我的意见"列表中选择一条意见, When 代表打开详情页, Then 页面能正确显示该意见的原始信息、代表的建议、站点审核意见（含驳回理由）、转交部门、历次办理情况更新（含附件下载链接）、以及最终办结状态和结果。
    2.  AC-IM-006-02: Given 意见的某个处理环节（如站点审核、部门反馈）有新的更新, When 代表再次查看该意见, Then 最新的处理信息应能正确展示。

### 模块五：通知模块 (Notification)

#### F-NT-001: 站内通知
*   **优先级**: P1
*   **所属模块**: 通知模块
*   **功能描述**: 在发生特定业务事件时（如代表提交意见给工作人员审核、工作人员完成意见审核等），系统向相关用户发送站内通知，提醒用户关注。
*   **用户故事关联**: US-NT-001
*   **业务规则与逻辑**:
    1.  定义需要触发站内通知的业务事件节点，例如：
        *   人大代表提交意见给工作人员 -> 通知对应的工作人员。
        *   工作人员审核意见（通过/不通过）-> 通知提交意见的人大代表。
        *   工作人员更新意见办理结果 -> 通知提交意见的人大代表。
        *   (其他需要通知的场景可后续补充)
    2.  通知内容应简洁明了，包含关键信息（如"您有一条新的待审核意见"、"您的意见[XXX]已审核通过"）。
    3.  用户登录系统后，在界面明显位置（如顶部导航栏）可以看到未读通知数量提示。
    4.  点击可进入通知列表页面，查看所有收到的通知，按时间倒序排列。
    5.  通知列表应包含通知标题/摘要、接收时间。点击可查看通知详情（如果需要）。
    6.  用户可以标记通知为已读，或系统在用户查看详情后自动标记为已读。
*   **界面原型描述/线框图说明**:
    *   导航栏：铃铛图标+未读消息角标。
    *   通知列表页：列表形式展示通知，区分已读/未读状态。
*   **输入定义**: (系统内部事件触发)
    *   接收用户ID
    *   通知类型 (区分不同业务事件)
    *   通知标题/内容
    *   关联的业务对象ID (如意见ID)
*   **输出/系统响应**:
    *   用户界面显示未读通知提示，用户可以查看通知内容。
*   **数据依赖与影响**: 需要通知表 (存储通知ID, 接收用户ID, 内容, 类型, 状态, 创建时间, 关联业务ID等)。
*   **验收标准 (GWT)**:
    1.  **AC-NT-001.1**: Given 人大代表提交了一条新意见给工作人员; When 提交成功后; Then 对应的站点工作人员登录系统时能收到一条关于新待审核意见的站内通知，并能看到未读消息提示。
    2.  **AC-NT-001.2**: Given 工作人员审核通过了一条意见; When 审核操作完成后; Then 提交该意见的人大代表能收到一条关于意见已审核通过的站内通知。
    3.  **AC-NT-001.3**: Given 用户有未读通知; When 用户查看通知列表并点击某条通知; Then 该通知被标记为已读，未读通知数相应减少。
    4.  **AC-NT-002.1**: Given 短信发送成功; When 系统尝试发送短信; Then 系统应记录发送状态，并可有重试机制或管理员告警。
    5.  **AC-NT-002.2**: Given 短信发送失败（如余额不足、接口异常）; When 系统尝试发送短信; Then 系统应记录失败状态，并可有重试机制或管理员告警。

### 模块六：法律政策知识问答模块 (Knowledge Question and Answer)

#### F-KQ-001: 知识查询
*   **优先级**: P1
*   **所属模块**: 法律政策知识问答模块
*   **功能描述**: 用户可以通过系统查询相关的法律政策知识，以辅助实际工作。
*   **用户故事关联**: US-KQ-001
*   **业务规则与逻辑**:
    1.  用户可以输入关键词进行搜索。
    2.  系统根据关键词返回相关的法律政策知识。
    3.  用户可以查看知识详情，了解详细内容。
    4.  用户可以收藏感兴趣的知识，以便后续查看。
*   **界面原型描述/线框图说明**:
    *   搜索框：用户输入关键词。
    *   搜索结果列表：展示相关法律政策知识。
    *   知识详情页：展示知识内容，用户可以点击查看。
    *   收藏按钮：用户可以点击收藏知识。
*   **输入定义**:
    | 字段名   | 类型   | 长度    | 是否必填 | 校验规则                               | 默认值 | 来源/示例    |
    | :------- | :----- | :------ | :------- | :------------------------------------- | :----- | :----------- |
    | 关键词   | String | 50      | 是       | 非空, 符合特定字符集（如字母数字下划线） |        | 用户输入     |
*   **输出/系统响应**:
    *   **成功场景**: 用户可以成功查询到相关的法律政策知识。
    *   **失败/异常场景**:
        | 错误场景             | 错误码 (可选) | 提示信息                     | 用户引导       | 系统逻辑                                 |
        | :------------------- | :------------ | :--------------------------- | :------------- | :--------------------------------------- |
        | 关键词为空           | KQ_ERR_001     | 关键词不能为空               | 提示用户输入   | 停留在搜索页面，不执行查询             |
        | 查询失败             | KQ_ERR_002     | 查询失败，请稍后再试         | 稍后重试       | 停留在搜索页面，不执行查询             |
        | 知识不存在           | KQ_ERR_003     | 未找到相关法律政策知识       | 提示用户无结果 | 停留在搜索结果页面，不执行查询         |
*   **数据依赖与影响**: 依赖知识库数据。
*   **验收标准 (GWT)**:
    1.  **AC-KQ-001.1**: Given 用户输入有效的关键词; When 用户点击搜索按钮; Then 系统成功返回相关法律政策知识。
    2.  **AC-KQ-001.2**: Given 用户输入无效的关键词; When 用户点击搜索按钮; Then 系统给出错误提示，用户可以重新输入关键词。
    3.  **AC-KQ-001.3**: Given 用户输入关键词后查询失败; When 用户点击搜索按钮; Then 系统给出错误提示，用户可以稍后重试。

## 5. 详细功能规格 (P1 - MVP后迭代)

### 模块二：代表信息与履职管理 (Representative & Performance Management) (P1)

#### F-RM-003: 年度履职AI分析
*   **优先级**: P1
*   **所属模块**: 代表信息与履职管理
*   **功能描述**: 系统基于代表在一个自然年度内所有已记录的履职数据 (源自F-RM-002)，通过调用外部AI能力，进行智能分析，生成年度履职情况的分析报告，帮助代表回顾和总结工作，发现履职亮点与潜在提升点。
*   **用户故事关联**: US-RM-003
*   **业务规则与逻辑**:
    1.  人大代表用户可访问"年度履职分析"功能。
    2.  用户选择需要分析的年度。
    3.  系统从F-RM-002模块获取并汇总该代表在该年度的所有履职记录（包括但不限于活动类型、数量、参与度、内容关键词等）。
    4.  系统将结构化的履职数据或其特征摘要，通过API安全地传递给外部AI分析服务。
    5.  外部AI服务对数据进行分析，可能输出包括：
        *   履职活动总体概览（如总次数、平均活跃度）。
        *   各类履职活动（会议、建议、联系群众、调研等）的分布与占比。
        *   履职内容的关键词云或主题分析。
        *   与上一年度的对比分析（若历史数据可用）。
        *   基于模型的履职亮点自动识别。
        *   潜在的履职提升方向或建议。
    6.  系统接收AI返回的分析结果，进行结构化存储，并与对应代表及年度关联。
    7.  分析报告以易于理解的方式（如图表、关键指标、文字小结）呈现给代表。
    8.  AI生成的分析内容原则上不可由用户直接编辑，但用户可添加个人备注或解读。
    9.  需处理AI服务调用失败、超时或返回无效数据等异常情况，并给出友好提示。
*   **业务流程图**:
    ```mermaid
    graph TD
        A[代表进入年度履职分析页面] --> B{选择分析年度};
        B --> C[系统汇总该年度履职数据];
        C --> D{数据是否充分?};
        D -- 否 --> E[提示数据不足，引导录入更多履职];
        D -- 是 --> F[调用外部AI分析服务];
        F -- 成功获取结果 --> G[存储AI分析结果];
        G --> H[向代表展示结构化的分析报告];
        F -- 调用失败/超时 --> I[提示错误信息，允许重试];
    ```
*   **界面原型描述/线框图说明**:
    *   年度选择控件。
    *   分析报告展示区：
        *   履职数据总览卡片（如总次数、参与会议次数、提交建议数等）。
        *   履职活动类型分布图（如饼图或柱状图）。
        *   履职关键词云。
        *   AI分析的文字小结、亮点、建议等模块化展示。
    *   可能包含"重新分析"、"添加备注"等操作按钮。
*   **输入定义**:
    | 字段名     | 类型   | 是否必填 | 校验规则/说明             | 来源/示例    |
    | :--------- | :----- | :------- | :------------------------ | :----------- |
    | 分析年度   | Year   | 是       | 合法的年份格式             | 用户选择     |
    | 用户ID     | String | 是       | (系统内部获取)            | 当前登录用户 |
    | 履职数据   | JSON/XML| 是       | (由F-RM-002提供并处理)    | 系统内部生成 |
*   **输出/系统响应**:
    *   **成功场景**: 页面清晰展示AI生成的年度履职分析报告，包含各项分析维度和数据。
    *   **失败/异常场景**:
        | 错误场景         | 错误码 (可选) | 提示信息                             | 用户引导     | 系统逻辑                     |
        | :--------------- | :------------ | :----------------------------------- | :----------- | :--------------------------- |
        | 履职数据不足     | RM_AI_ERR_001 | 所选年度履职数据过少，无法进行有效分析 | 鼓励用户补充数据 | 停留在分析请求页，不调用AI |
        | AI服务调用失败   | RM_AI_ERR_002 | AI分析服务暂不可用，请稍后再试         | 稍后重试     | 记录错误，不展示报告         |
        | AI分析超时       | RM_AI_ERR_003 | AI分析超时，请稍后再试                 | 稍后重试     | 记录错误，不展示报告         |
        | AI返回无效结果   | RM_AI_ERR_004 | AI未能生成有效的分析报告，请联系管理员 | 联系管理员/重试 | 记录错误，不展示报告         |
*   **数据依赖与影响**: 强依赖F-RM-002的履职记录数据。依赖外部AI分析服务接口。分析结果将存储并与用户关联。
*   **验收标准 (GWT)**:
    1.  **AC-RM-003.1**: Given 代表在系统中拥有指定年度的充足履职记录; When 代表请求对该年度进行AI履职分析; Then 系统成功调用AI服务并返回一份包含履职活跃度、活动类型分布、关键词云等信息的分析报告。
    2.  **AC-RM-003.2**: Given 代表选择的年度履职数据不足; When 代表请求AI履职分析; Then 系统应提示数据不足，无法分析。
    3.  **AC-RM-003.3**: Given 外部AI分析服务调用失败或超时; When 代表请求AI履职分析; Then 系统应给出明确的错误提示，并允许用户稍后重试。
    4.  **AC-RM-003.4**: Given AI分析报告已生成; When 代表查看报告; Then 报告内容结构清晰，图表和文字描述准确反映分析结果。

#### F-RM-004: 年度履职成果AI展示
*   **优先级**: P1
*   **所属模块**: 代表信息与履职管理
*   **功能描述**: 系统基于代表的年度履职数据 (源自F-RM-002) 和AI分析结果 (源自F-RM-003)，通过调用外部AI能力，生成图文并茂、重点突出的年度履职成果可视化展示，便于代表进行汇报、分享或个人总结。
*   **用户故事关联**: US-RM-004
*   **业务规则与逻辑**:
    1.  人大代表用户在查看年度履职AI分析报告 (F-RM-003) 后，或通过专门入口，可请求生成年度履职成果AI展示。
    2.  用户选择需要生成成果展示的年度。前提是该年度的AI分析报告 (F-RM-003) 已成功生成。
    3.  系统整合该年度的履职数据摘要、AI分析的关键结论、以及代表个人信息（如姓名、届次等，脱敏处理）。
    4.  系统将这些信息通过API安全地传递给外部AI成果展示服务（可能与分析服务是同一套AI能力的不同应用）。
    5.  外部AI服务根据预设模板或动态布局，生成包含数据可视化图表（如履职时长趋势图、建议采纳率饼图等）、关键履职事迹的精炼文字描述、数据亮点解读、以及个性化寄语（可选）等的综合性成果展示。
    6.  成果展示应注重视觉效果和信息传递效率，图文并茂。
    7.  系统接收AI生成的成果展示内容（可能是HTML片段、图片URL集合、或特定格式数据），并进行渲染呈现。
    8.  代表可预览AI生成的成果展示。
    9.  （P2或后续考虑）提供将成果展示导出为常见格式（如PDF、长图图片）的功能。
    10. AI生成的展示内容核心部分不可编辑，但可允许代表选择不同主题风格或对某些模块进行显隐控制（P2）。
*   **业务流程图**:
    ```mermaid
    graph TD
        A[代表进入年度履职成果展示页面] --> B{选择展示年度};
        B --> C{检查该年度AI分析报告是否存在?};
        C -- 否 --> D[提示需先完成AI分析];
        C -- 是 --> E[系统整合履职数据和AI分析结论];
        E --> F[调用外部AI成果展示服务];
        F -- 成功获取结果 --> G[存储/缓存AI展示内容];
        G --> H[向代表呈现图文并茂的成果展示];
        H --> I{预览满意?};
        I -- 是 (P2) --> J[可选导出];
        F -- 调用失败/超时 --> K[提示错误信息，允许重试];
    ```
*   **界面原型描述/线框图说明**:
    *   年度选择控件。
    *   成果展示区：
        *   个性化头部（如"李明代表 2024年度履职成果"）。
        *   核心数据指标卡片（如年度解决问题X件，提出高质量建议Y条）。
        *   履职活动数据可视化图表（如各类活动次数柱状图、履职时间投入饼图）。
        *   AI提炼的履职亮点/典型事例图文模块。
        *   简洁的总结与展望。
        *   整体风格应正式、美观、易于阅读。
    *   （P2）"导出为PDF"、"分享"等按钮。
*   **输入定义**:
    | 字段名       | 类型    | 是否必填 | 校验规则/说明                 | 来源/示例    |
    | :----------- | :------ | :------- | :---------------------------- | :----------- |
    | 展示年度     | Year    | 是       | 合法的年份格式，且已有AI分析报告 | 用户选择     |
    | 用户ID       | String  | 是       | (系统内部获取)                | 当前登录用户 |
    | AI分析结果   | JSON/XML| 是       | (由F-RM-003提供)              | 系统内部获取 |
    | 履职数据摘要 | JSON/XML| 是       | (由F-RM-002提供并处理)        | 系统内部生成 |
*   **输出/系统响应**:
    *   **成功场景**: 页面清晰展示AI生成的图文并茂的年度履职成果。
    *   **失败/异常场景**:
        | 错误场景             | 错误码 (可选) | 提示信息                                 | 用户引导             | 系统逻辑                         |
        | :------------------- | :------------ | :--------------------------------------- | :------------------- | :------------------------------- |
        | 对应年度AI分析未完成 | RM_SHOW_ERR_001 | 请先完成所选年度的AI履职分析             | 跳转至AI分析功能     | 阻止调用AI展示服务             |
        | AI服务调用失败       | RM_SHOW_ERR_002 | AI成果展示服务暂不可用，请稍后再试         | 稍后重试             | 记录错误，不展示成果             |
        | AI展示生成超时       | RM_SHOW_ERR_003 | AI成果展示生成超时，请稍后再试             | 稍后重试             | 记录错误，不展示成果             |
        | AI返回无效成果       | RM_SHOW_ERR_004 | AI未能生成有效的成果展示，请联系管理员   | 联系管理员/重试      | 记录错误，不展示成果             |
*   **数据依赖与影响**: 强依赖F-RM-002的履职记录数据和F-RM-003的AI分析结果。依赖外部AI成果展示服务接口。
*   **验收标准 (GWT)**:
    1.  **AC-RM-004.1**: Given 代表已完成某年度的AI履职分析 (F-RM-003); When 代表请求对该年度生成AI履职成果展示; Then 系统成功调用AI服务并返回一个图文并茂、包含数据图表和关键事迹的履职成果展示页面。
    2.  **AC-RM-004.2**: Given 代表选择的年度尚未进行AI履职分析; When 代表请求AI履职成果展示; Then 系统应提示用户需要先完成AI履职分析。
    3.  **AC-RM-004.3**: Given 外部AI成果展示服务调用失败或超时; When 代表请求AI履职成果展示; Then 系统应给出明确的错误提示，并允许用户稍后重试。
    4.  **AC-RM-004.4**: Given AI履职成果展示已生成; When 代表查看成果; Then 展示内容中的数据图表（如活动类型统计）应与原始履职数据和AI分析报告一致，文字描述准确精炼，整体视觉效果良好。

### 模块三：工作计划管理模块 (Work Plan Management) (P1)

#### F-WP-001: 工作计划录入管理
*   **优先级**: P1
*   **所属模块**: 工作计划管理模块
*   **功能描述**: 站点工作人员在年度开始时录入本年度的工作计划（包含季度、月度），支持工作计划的增删改查操作。
*   **用户故事关联**: US-WP-001
*   **业务规则与逻辑**:
    1.  站点工作人员可访问"工作计划管理"模块。
    2.  提供"新增工作计划"功能，表单包含：计划标题、计划类型（年度/季度/月度）、计划周期（开始时间、结束时间）、计划内容、计划目标、负责人、计划状态（待开始/进行中/已完成/已延期）。
    3.  列表展示已录入的工作计划，按计划类型和时间排序，支持分页和筛选。
    4.  每条计划可进行"编辑"、"删除"、"状态更新"操作。
    5.  支持批量操作和计划模板功能。
*   **界面原型描述/线框图说明**:
    *   列表页：包含"新增计划"按钮、工作计划列表、筛选条件。
    *   新增/编辑页：表单包含上述输入字段，以及"保存"、"取消"按钮。
*   **输入定义**:
    | 字段名     | 类型    | 长度    | 是否必填 | 校验规则                               | 默认值 | 来源/示例                                  |
    | :--------- | :------ | :------ | :------- | :------------------------------------- | :----- | :----------------------------------------- |
    | 计划标题   | String  | 200     | 是       | 非空                                   |        | 用户输入                                   |
    | 计划类型   | String  | 50      | 是       | 从预设列表选择（年度/季度/月度）       |        | 用户选择                                   |
    | 开始时间   | Date    |         | 是       | 非空, 合法日期格式                     |        | 用户选择                                   |
    | 结束时间   | Date    |         | 是       | 非空, 不能早于开始时间                 |        | 用户选择                                   |
    | 计划内容   | Text    | 2000    | 是       | 非空                                   |        | 用户输入                                   |
    | 计划目标   | Text    | 1000    | 否       |                                        |        | 用户输入                                   |
    | 负责人     | String  | 100     | 是       | 非空                                   |        | 用户输入或选择                             |
    | 计划状态   | String  | 50      | 是       | 从预设状态列表选择                     | 待开始 | 系统默认/用户选择                          |
*   **验收标准 (GWT)**:
    1.  **AC-WP-001.1**: Given 站点工作人员登录并进入工作计划管理模块; When 工作人员填写完整的工作计划信息并点击保存; Then 系统提示"操作成功"且新计划显示在列表中。

#### F-WP-002: 工作计划提醒功能
*   **优先级**: P1
*   **所属模块**: 工作计划管理模块
*   **功能描述**: 站点工作人员可以为工作计划设置提醒时间，在工作计划准备开始或结束时，如果工作计划没有完成或开始会进行提醒。
*   **用户故事关联**: US-WP-002
*   **业务规则与逻辑**:
    1.  在新增/编辑工作计划时，可设置提醒时间点（如计划开始前N天、计划结束前N天）。
    2.  系统定时任务检查工作计划状态和提醒时间点。
    3.  当到达提醒时间时，向相关工作人员发送站内通知和可选的短信提醒。
    4.  提醒内容包含计划标题、类型、时间等关键信息。
    5.  用户可以查看和管理所有提醒设置。
*   **输入定义**:
    | 字段名         | 类型    | 长度    | 是否必填 | 校验规则                               | 默认值 | 来源/示例                                  |
    | :------------- | :------ | :------ | :------- | :------------------------------------- | :----- | :----------------------------------------- |
    | 开始前提醒天数 | Integer |         | 否       | 非负整数                               | 3      | 用户输入                                   |
    | 结束前提醒天数 | Integer |         | 否       | 非负整数                               | 1      | 用户输入                                   |
    | 提醒方式       | String  | 50      | 是       | 站内通知/短信/邮件                     | 站内通知 | 用户选择                                 |
*   **验收标准 (GWT)**:
    1.  **AC-WP-002.1**: Given 工作计划设置了提醒时间; When 到达提醒时间点; Then 相关工作人员收到相应的提醒通知。

### 模块四：站点工作分析模块 (Site Work Analysis) (P1)

#### F-WA-001: 站点工作总结
*   **优先级**: P1
*   **所属模块**: 站点工作分析模块
*   **功能描述**: 站点工作人员可以查看本站点的年度工作分析结果，该结果由AI分析生成。
*   **用户故事关联**: US-WA-001
*   **业务规则与逻辑**:
    1.  站点工作人员可访问"站点工作分析"模块下的"站点工作总结"功能。
    2.  用户选择要分析的年度，系统调用外部AI接口分析本站点的年度工作数据。
    3.  AI分析基于站点的工作计划完成情况、意见处理数量和质量、代表管理情况等数据。
    4.  生成包含工作亮点、存在问题、改进建议的综合分析报告。
    5.  支持导出和分享功能。
*   **业务流程图**:
    ```mermaid
    graph TD
        A[工作人员进入站点工作总结] --> B[选择分析年度];
        B --> C[点击生成分析报告];
        C --> D[系统调用AI接口];
        D --> E[AI分析站点工作数据];
        E --> F[返回分析结果];
        F --> G[展示工作总结报告];
    ```
*   **验收标准 (GWT)**:
    1.  **AC-WA-001.1**: Given 站点工作人员选择年度并请求生成工作总结; When AI分析完成; Then 系统展示包含工作亮点、问题分析、改进建议的综合报告。

#### F-WA-002: 代表工作总结
*   **优先级**: P1
*   **所属模块**: 站点工作分析模块
*   **功能描述**: 站点工作人员可以查看本站点各个代表年度履职AI分析展示，可由站点工作人员生成，也可以由代表自己生成，无论由谁生成之后两者角色看到的结果是一致的。
*   **用户故事关联**: US-WA-002
*   **业务规则与逻辑**:
    1.  站点工作人员可访问"站点工作分析"模块下的"代表工作总结"功能。
    2.  页面展示本站点所有代表的列表，显示每个代表的履职分析状态。
    3.  工作人员可以为任一代表生成年度履职AI分析。
    4.  分析结果与代表在F-RM-003中看到的结果完全一致。
    5.  支持批量生成和对比分析功能。
*   **验收标准 (GWT)**:
    1.  **AC-WA-002.1**: Given 站点工作人员为某代表生成履职分析; When 分析完成后; Then 该代表在自己的履职分析页面看到的结果与工作人员看到的完全一致。

### 模块五：通知模块 (Notification) (P1)

#### F-NT-001: 站内通知
*   **优先级**: P1
*   **所属模块**: 通知模块
*   **功能描述**: 在发生特定业务事件时（如代表提交意见给工作人员审核、工作人员完成意见审核、工作计划提醒等），系统向相关用户发送站内通知，提醒用户关注。
*   **用户故事关联**: US-NT-001
*   **业务规则与逻辑**:
    1.  定义需要触发站内通知的业务事件节点，包括：
        *   人大代表提交意见给工作人员 -> 通知对应的工作人员。
        *   工作人员审核意见（通过/不通过）-> 通知提交意见的人大代表。
        *   工作人员更新意见办理结果 -> 通知提交意见的人大代表。
        *   工作计划提醒时间到达 -> 通知相关工作人员。
        *   其他业务事件。
    2.  通知内容应简洁明了，包含关键信息。
    3.  用户登录系统后，在界面明显位置可以看到未读通知数量提示。
    4.  点击可进入通知列表页面，查看所有收到的通知，按时间倒序排列。
    5.  通知列表应包含通知标题/摘要、接收时间。
    6.  用户可以标记通知为已读，或系统在用户查看详情后自动标记为已读。
*   **界面原型描述/线框图说明**:
    *   导航栏：铃铛图标+未读消息角标。
    *   通知列表页：列表形式展示通知，区分已读/未读状态。
*   **输入定义**: (系统内部事件触发)
    *   接收用户ID
    *   通知类型 (区分不同业务事件)
    *   通知标题/内容
    *   关联的业务对象ID (如意见ID、工作计划ID)
*   **输出/系统响应**:
    *   用户界面显示未读通知提示，用户可以查看通知内容。
*   **数据依赖与影响**: 需要通知表 (存储通知ID, 接收用户ID, 内容, 类型, 状态, 创建时间, 关联业务ID等)。
*   **验收标准 (GWT)**:
    1.  **AC-NT-001.1**: Given 人大代表提交了一条新意见给工作人员; When 提交成功后; Then 对应的站点工作人员登录系统时能收到一条关于新待审核意见的站内通知，并能看到未读消息提示。
    2.  **AC-NT-001.2**: Given 工作人员审核通过了一条意见; When 审核操作完成后; Then 提交该意见的人大代表能收到一条关于意见已审核通过的站内通知。
    3.  **AC-NT-001.3**: Given 用户有未读通知; When 用户查看通知列表并点击某条通知; Then 该通知被标记为已读，未读通知数相应减少。
    4.  **AC-NT-002.1**: Given 短信发送成功; When 系统尝试发送短信; Then 系统应记录发送状态，并可有重试机制或管理员告警。
    5.  **AC-NT-002.2**: Given 短信发送失败（如余额不足、接口异常）; When 系统尝试发送短信; Then 系统应记录失败状态，并可有重试机制或管理员告警。

### 模块六：法律政策知识问答模块 (Legal Policy Q&A) (P1)

#### F-KQ-001: 知识查询
*   **优先级**: P1
*   **所属模块**: 法律政策知识问答模块
*   **功能描述**: 代表和站点工作人员可以通过系统查询相关的法律政策知识，基于外部AI知识库提供问答服务。
*   **用户故事关联**: US-KQ-001
*   **业务规则与逻辑**:
    1.  用户可以输入问题进行法律政策知识查询。
    2.  系统调用外部AI知识库接口，获取相关回答。
    3.  支持自然语言问答和关键词搜索两种模式。
    4.  用户可以对回答进行评价，帮助优化服务质量。
    5.  提供常见问题快捷入口。
*   **业务流程图**:
    ```mermaid
    graph TD
        A[用户进入法律政策问答模块] --> B[输入问题或选择查询类型];
        B --> C[点击查询按钮];
        C --> D[系统调用外部AI知识库接口];
        D --> E[AI返回相关法律政策知识];
        E --> F[展示查询结果];
        F --> G[用户查看并可选择评价];
    ```
*   **界面原型描述/线框图说明**:
    *   搜索框：用户输入问题，支持自然语言输入。
    *   查询类型选择：自然语言问答/关键词搜索。
    *   搜索结果区域：展示AI返回的法律政策知识和回答。
    *   常见问题快捷入口：提供热门问题链接。
    *   评价功能：用户可对回答质量进行评分。
*   **输入定义**:
    | 字段名   | 类型   | 长度    | 是否必填 | 校验规则                               | 默认值 | 来源/示例    |
    | :------- | :----- | :------ | :------- | :------------------------------------- | :----- | :----------- |
    | 问题内容 | String | 500     | 是       | 非空                                   |        | 用户输入     |
    | 查询类型 | String | 50      | 是       | 自然语言问答/关键词搜索                | 自然语言问答 | 用户选择 |
*   **输出/系统响应**:
    *   **成功场景**: 系统通过AI接口返回相关的法律政策知识回答。
    *   **失败/异常场景**:
        | 错误场景             | 错误码 (可选) | 提示信息                     | 用户引导       | 系统逻辑                                 |
        | :------------------- | :------------ | :--------------------------- | :------------- | :--------------------------------------- |
        | 问题内容为空         | KQ_ERR_001     | 问题内容不能为空             | 提示用户输入   | 停留在查询页面，不执行查询             |
        | AI服务调用失败       | KQ_ERR_002     | AI服务暂不可用，请稍后再试   | 稍后重试       | 显示错误提示，不展示结果               |
        | AI返回无相关内容     | KQ_ERR_003     | 未找到相关法律政策知识       | 提示用户换个问题 | 显示无结果提示                       |
        | 网络超时             | KQ_ERR_004     | 网络超时，请稍后再试         | 稍后重试       | 显示超时提示                         |
*   **数据依赖与影响**: 依赖外部AI知识库系统接口。
*   **验收标准 (GWT)**:
    1.  **AC-KQ-001.1**: Given 用户输入法律政策相关问题; When 点击查询按钮; Then 系统通过AI接口返回相关的法律政策知识回答。
    2.  **AC-KQ-001.2**: Given AI服务调用失败; When 用户查询问题; Then 系统给出友好的错误提示。
    3.  **AC-KQ-001.3**: Given 用户选择常见问题; When 点击快捷入口; Then 系统自动填入问题并执行查询。

## 6. 非功能性需求 (NFRs) - 初步 (MVP)

| 类别         | 需求项         | 描述 (MVP阶段要求)                                                                 | 优先级 | 备注                             |
| :----------- | :------------- | :--------------------------------------------------------------------------------- | :----- | :------------------------------- |
| **性能**     | 响应时间       | 核心操作（如登录、页面加载、数据提交）在常规网络下应在3秒内响应。AI调用部分依赖外部接口。         | 高     |                                  |
|              | 并发用户数     | 初期支持至少50名代表+10名工作人员同时在线操作。                                          | 中     |                                  |
| **安全**     | 用户认证       | 提供基于用户名/密码的登录认证。密码应加密存储。                                                | 高     |                                  |
|              | 权限控制       | 实现基于角色的访问控制 (RBAC)，确保用户只能访问其授权范围内的功能和数据。                      | 高     | MVP阶段角色和权限固定            |
|              | 数据传输       | （建议P1或后续）敏感数据传输考虑使用HTTPS。                                                  | 中     | 外部AI接口调用安全依赖对方       |
| **可用性**   | 易学性         | 界面设计应简洁直观，常用功能易于查找和使用，减少用户学习成本。                                  | 高     |                                  |
|              | 防错机制       | 对关键输入提供格式校验和友好提示；重要删除操作前有确认。                                        | 高     |                                  |
| **可靠性**   | 系统稳定性     | 系统应能7x24小时稳定运行（除计划内维护）。核心功能无明显导致崩溃的Bug。                             | 高     | 依赖服务器和外部服务稳定性       |
| **可维护性** | 日志记录       | （P2或后续）记录关键操作日志和异常日志，便于问题排查和审计。                                  | 中     | MVP阶段侧重功能实现              |
| **兼容性**   | 浏览器兼容性   | 支持主流现代浏览器最新版本（如Chrome, Edge, Firefox）。                                    | 中     | 无需兼容IE旧版                   |