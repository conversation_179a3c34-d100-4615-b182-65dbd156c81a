<template>
  <div class="layout-container">
    <!-- 顶部标题栏组件 -->
    <HeaderBlock ref="headerRef" />
    
    <!-- 主体内容区域 -->
    <main class="main-content">
      <!-- 左侧面板组件 -->
      <LeftSidebar />
      
      <!-- 中间内容区 -->
      <div class="center-content">
        <!-- 主要内容区组件 -->
        <MainContent ref="mainContentRef" />
        
        <!-- 底部内容区组件 -->
        <BottomContent />
      </div>
      
      <!-- 右侧面板组件 -->
      <RightSidebar />
    </main>
  </div>
</template>

<script setup>
import { ref } from 'vue'
// 使用组合式API，导入所有子组件
import HeaderBlock from './layout/HeaderBlock.vue'
import LeftSidebar from './sidebar/LeftSidebar.vue'
import RightSidebar from './sidebar/RightSidebar.vue'
import MainContent from './content/MainContent.vue'
import BottomContent from './content/BottomContent.vue'

// 组件引用
const mainContentRef = ref(null)

// 提供一个全局方法来更新二维码URL
const updateQRCode = (newUrl) => {
  if (mainContentRef.value) {
    mainContentRef.value.updateQRCodeUrl(newUrl)
  }
}

// 暴露方法供外部调用
defineExpose({
  updateQRCode
})
</script>

<style scoped>
/* 整体容器样式 - 柔和党建红主题 */
.layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: url('@/assets/bg.jpg') no-repeat center center;
  background-size: cover;
  font-family: 'Microsoft YaHei', sans-serif;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

/* 添加柔和的党建红主题装饰效果 */
.layout-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(2, 166, 181, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(73, 188, 247, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 13, 74, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* 确保内容在装饰层之上 */
.layout-container > * {
  position: relative;
  z-index: 1;
}

/* 主体内容区域 */
.main-content {
  display: flex;
  flex: 1;
  padding: 20px;
  gap: 8px;
  overflow: hidden;
}

/* 中间内容区域 */
.center-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    padding: 15px;
    gap: 15px;
  }
  
  .center-content {
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 10px;
    gap: 10px;
  }
  
  .center-content {
    gap: 10px;
  }
}
</style> 