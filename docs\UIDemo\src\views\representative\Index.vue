<template>
  <div class="representative-layout">
    <!-- 头部 -->
    <el-header class="layout-header">
      <div class="header-left">
        <el-icon size="24" style="color: var(--china-red); margin-right: 10px;">
          <User />
        </el-icon>
        <span class="system-title">人大代表履职服务与管理平台</span>
      </div>
      <div class="header-right">
        <!-- 通知图标 -->
        <NotificationIcon />
        
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-avatar :size="32" style="background-color: var(--china-red);">
              {{ userStore.userName.charAt(0) }}
            </el-avatar>
            <span class="user-name">{{ userStore.userName }}</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人信息管理
              </el-dropdown-item>
              <el-dropdown-item command="logout" divided>
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主体布局 -->
    <el-container class="main-container">
      <!-- 侧边栏 -->
      <el-aside class="layout-aside">
        <el-menu
          :default-active="$route.path"
          router
          class="nav-menu"
        >
          <el-menu-item index="/representative">
            <el-icon><House /></el-icon>
            <span>工作台概览</span>
          </el-menu-item>
          <el-menu-item index="/representative/profile">
            <el-icon><User /></el-icon>
            <span>个人信息管理</span>
          </el-menu-item>
          <el-menu-item index="/representative/records">
            <el-icon><Document /></el-icon>
            <span>履职记录管理</span>
          </el-menu-item>
          <el-menu-item index="/representative/annual-achievements">
            <el-icon><Trophy /></el-icon>
            <span>年度履职AI分析展示</span>
          </el-menu-item>
          <el-menu-item index="/representative/opinions">
            <el-icon><ChatLineRound /></el-icon>
                          <span>意见建议</span>
          </el-menu-item>
          <el-menu-item index="/representative/knowledge-qa">
            <el-icon><DocumentCopy /></el-icon>
            <span>法律政策互动AI问答</span>
          </el-menu-item>
          <el-menu-item index="/representative/password-change">
            <el-icon><Key /></el-icon>
            <span>账号密码修改</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 内容区域 -->
      <el-main class="layout-main">
        <!-- 路由出口：如果是根路径显示概览，否则显示子页面 -->
        <div v-if="$route.path === '/representative'" class="dashboard-container">
          <!-- 欢迎卡片 -->
          <div class="welcome-card">
            <h2>欢迎回来，{{ userStore.userName }} 代表</h2>
            <p>{{ currentDate }} | {{ userStore.roleText }}</p>
            <div class="role-info">
              <div class="info-item">
                <span class="label">选区：</span>
                <span class="value">{{ userStore.userInfo.district || '某某街道' }}</span>
              </div>
              <div class="info-item">
                <span class="label">届次：</span>
                <span class="value">{{ userStore.userInfo.term || '第十四届' }}</span>
              </div>
              <div class="info-item">
                <span class="label">代表证号：</span>
                <span class="value">{{ userStore.userInfo.certificateNo || 'NPC2024001' }}</span>
              </div>
            </div>
          </div>

          <!-- 统计数据 -->
          <el-row :gutter="20" class="stats-row">
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.totalRecords }}</div>
                <div class="stat-label">履职记录</div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.pendingOpinions }}</div>
                <div class="stat-label">待处理意见</div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.completedOpinions }}</div>
                <div class="stat-label">已完成意见</div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.thisMonthRecords }}</div>
                <div class="stat-label">本月履职</div>
              </div>
            </el-col>
          </el-row>

          <!-- 最近活动 -->
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <el-card header="最近履职记录">
                <el-timeline>
                  <el-timeline-item
                    v-for="record in recentRecords"
                    :key="record.id"
                    :timestamp="record.date"
                  >
                    {{ record.title }}
                  </el-timeline-item>
                </el-timeline>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card header="待处理意见建议">
                <el-empty v-if="!pendingOpinionsList.length" description="暂无待处理意见" />
                <div v-else>
                  <div
                    v-for="opinion in pendingOpinionsList"
                    :key="opinion.id"
                    class="opinion-item"
                  >
                    <div class="opinion-title">{{ opinion.title }}</div>
                    <div class="opinion-meta">
                      <el-tag size="small" type="warning">{{ opinion.status }}</el-tag>
                      <span class="opinion-date">{{ opinion.date }}</span>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        
        <!-- 子路由内容 -->
        <router-view v-else />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import NotificationIcon from '@/components/NotificationIcon.vue'
import {
  User,
  ArrowDown,
  SwitchButton,
  House,
  Document,
  ChatLineRound,
  Trophy,
  DocumentCopy,
  Key
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 当前日期
const currentDate = computed(() => {
  const now = new Date()
  return now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

// 统计数据（模拟）
const stats = ref({
  totalRecords: 12,
  pendingOpinions: 3,
  completedOpinions: 8,
  thisMonthRecords: 4
})

// 最近履职记录（模拟）
const recentRecords = ref([
  { id: 1, title: '参加街道民情恳谈会', date: '2024-01-15' },
  { id: 2, title: '走访社区居民', date: '2024-01-12' },
  { id: 3, title: '参与议案讨论', date: '2024-01-10' }
])

// 待处理意见列表（模拟）
const pendingOpinionsList = ref([
  { id: 1, title: '关于小区停车难问题', status: '待提交', date: '2024-01-16' },
  { id: 2, title: '建议增设儿童游乐设施', status: '待审核', date: '2024-01-15' },
  { id: 3, title: '交通信号灯时间调整建议', status: '待提交', date: '2024-01-14' }
])

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/representative/profile')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    userStore.logout()
    router.push('/login')
  } catch {
    // 用户取消
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 这里可以加载实际的统计数据
})
</script>

<style scoped>
.representative-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: white;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
}

.system-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--china-red);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: var(--bg-color);
}

.user-name {
  font-weight: 500;
  color: var(--text-color);
}

.main-container {
  flex: 1;
  height: calc(100vh - 60px);
}

.layout-aside {
  width: 200px;
  background: white;
  border-right: 1px solid var(--border-color);
}

.nav-menu {
  border-right: none;
  height: 100%;
}

.layout-main {
  background: var(--bg-color);
  padding: 0;
  overflow-y: auto;
}

.stats-row {
  margin: 20px 0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: rgba(255, 255, 255, 0.8);
  margin-right: 8px;
  min-width: 80px;
}

.value {
  color: white;
  font-weight: 500;
}

.opinion-item {
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.opinion-item:last-child {
  border-bottom: none;
}

.opinion-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-color);
}

.opinion-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.opinion-date {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-aside {
    width: 160px;
  }
  
  .system-title {
    display: none;
  }
  
  .stats-row {
    margin: 10px 0;
  }
}

@media (max-width: 480px) {
  .layout-header {
    padding: 0 10px;
  }
  
  .layout-aside {
    width: 120px;
  }
  
  .user-name {
    display: none;
  }
}
</style> 