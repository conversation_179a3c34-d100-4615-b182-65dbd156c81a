<template>
  <header class="header-block">
    <div class="title-container">
      <h1>{{ title }}</h1>
      <!-- 标题下方的贝塞尔曲线装饰 -->
      <div class="arc-decoration">
        <svg class="arc-svg" viewBox="0 0 1000 30">
          <path 
            d="M 0 5 Q 500 40 1000 5" 
            stroke="url(#arcGradient)" 
            stroke-width="2" 
            fill="none"
          />
          <defs>
            <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style="stop-color:transparent;stop-opacity:0" />
                      <stop offset="20%" style="stop-color:#49bcf7;stop-opacity:0.4" />
        <stop offset="50%" style="stop-color:#49bcf7;stop-opacity:1" />
        <stop offset="80%" style="stop-color:#49bcf7;stop-opacity:0.4" />
              <stop offset="100%" style="stop-color:transparent;stop-opacity:0" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
    
    <!-- 右侧功能区 -->
    <div class="header-right">
      <!-- 时间显示 -->
      <div class="time-display">
        <div class="date">{{ currentDate }}</div>
        <div class="time">{{ currentTime }}</div>
      </div>
      
      <!-- 登录按钮 -->
      <button class="login-btn" @click="handleLogin">
        登录后台
      </button>
    </div>
  </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式标题数据
const title = ref('智慧人大AI辅助履职平台')
const currentDate = ref('')
const currentTime = ref('')
const timeTimer = ref(null)

// 更新时间
const updateTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  
  currentDate.value = `${year}/${month}/${day}`
  currentTime.value = `${hours}:${minutes}:${seconds}`
}

// 登录按钮点击事件
const handleLogin = () => {
  console.log('点击登录后台')
  // 这里可以添加登录逻辑
  router.push('/login')
}

// 组件挂载时启动定时器
onMounted(() => {
  updateTime()
  timeTimer.value = setInterval(updateTime, 1000)
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (timeTimer.value) {
    clearInterval(timeTimer.value)
    timeTimer.value = null
  }
})
</script>

<style scoped>
/* 顶部标题栏样式 - 蓝色科技主题 */
.header-block {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 8vh; /* 使用视口高度单位，占屏幕高度的8%，适应不同屏幕尺寸 */
  min-height: 80px; /* 设置最小高度，确保在小屏幕上也有足够的显示空间 */
  max-height: 120px; /* 设置最大高度，避免在超大屏幕上过高 */
  /* background: rgba(60, 24, 24, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(220, 20, 60, 0.2);
  box-shadow: 0 2px 10px rgba(139, 0, 0, 0.2); */
}

/* 标题容器 */
.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.header-block h1 {
  color: #49bcf7;
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 13, 74, 0.8), 0 0 10px rgba(73, 188, 247, 0.3);
  letter-spacing: 0.1em;
  transition: all 0.3s ease;
}


/* 响应式设计 */
@media (max-width: 1200px) {
  .header-block h1 {
    font-size: 2rem;
  }
  
  .arc-decoration {
    width: 250px;
    height: 25px;
  }
  
  .header-right {
    right: 30px;
    gap: 15px;
  }
  
  .time-display {
    font-size: 0.85rem;
  }
  
  .login-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .header-block h1 {
    font-size: 1.5rem;
  }
  
  .arc-decoration {
    width: 200px;
    height: 20px;
  }
  
  .header-right {
    right: 20px;
    gap: 10px;
  }
  
  .date {
    font-size: 0.75rem;
  }
  
  .time {
    font-size: 0.9rem;
  }
  
  .login-btn {
    padding: 5px 10px;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .header-block h1 {
    font-size: 1.2rem;
  }
  
  .arc-decoration {
    width: 150px;
    height: 18px;
  }
  
  .header-right {
    right: 15px;
    flex-direction: column;
    gap: 5px;
  }
  
  .time-display {
    align-items: center;
  }
  
  .date {
    font-size: 0.7rem;
  }
  
  .time {
    font-size: 0.8rem;
  }
  
  .login-btn {
    padding: 4px 8px;
    font-size: 0.7rem;
  }
}

/* 贝塞尔曲线装饰 */
.arc-decoration {
  width: 100vw;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: -30px; /* 贴近标题 */
}

.arc-svg {
  width: 100vw;
  height: 100%;
}

/* 右侧功能区 */
.header-right {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 10;
}

/* 时间显示 */
.time-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  color: #49bcf7;
  font-family: 'Courier New', monospace;
}

.date {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 2px;
  opacity: 0.9;
}

.time {
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.05em;
}

/* 登录按钮 */
.login-btn {
  padding: 8px 16px;
  background: transparent;
  border: 1px solid #49bcf7;
  border-radius: 4px;
  color: #49bcf7;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-btn:hover {
  background: #49bcf7;
  color: #000d4a;
  transform: translateY(-1px);
}
</style> 