"""
代表AI总结URL配置

定义AI总结管理模块的所有API端点路由
"""

from django.urls import path
from . import views

app_name = 'aisummary'

urlpatterns = [
    # AI总结生成和管理
    path('generate/', views.AISummaryGenerateView.as_view(), name='summary-generate'),
    path('<int:year>/', views.AISummaryDetailView.as_view(), name='summary-detail'),
    path('<int:year>/check/', views.AISummaryCheckDataView.as_view(), name='summary-check-data'),
    
    # AI总结列表（工作人员查看）
    path('', views.AISummaryListView.as_view(), name='summary-list'),
    
    # 工作分析相关（工作人员专用）
    path('representatives/', views.RepresentativesWorkSummaryView.as_view(), name='representatives-work-summary'),
] 