<template>
  <div class="profile-container">
    <div class="page-header">
      <h2>个人信息管理</h2>
      <p>管理您的基本信息和联系方式</p>
    </div>

    <el-card class="profile-card">
      <template #header>
        <span class="card-title">基本信息</span>
      </template>
      
      <el-form 
        :model="profileForm" 
        :rules="profileRules" 
        label-width="120px"
        size="large"
      >
        <!-- 第一行：代表层级、姓名 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="代表层级" prop="representativeLevel">
              <el-input v-model="profileForm.representativeLevel" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="profileForm.name" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行：性别、民族 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select 
                v-model="profileForm.gender" 
                :disabled="!isEditing"
                placeholder="请选择性别"
                style="width: 100%"
              >
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="民族" prop="ethnicity">
              <el-select 
                v-model="profileForm.ethnicity" 
                :disabled="!isEditing"
                placeholder="请选择民族"
                style="width: 100%"
                filterable
              >
                <el-option v-for="nation in ethnicityOptions" :key="nation" :label="nation" :value="nation" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：出生日期、籍贯 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birthDate">
              <el-date-picker
                v-model="profileForm.birthDate"
                type="date"
                :disabled="!isEditing"
                placeholder="请选择出生日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="籍贯" prop="nativePlace">
              <el-input 
                v-model="profileForm.nativePlace" 
                :disabled="!isEditing"
                placeholder="请输入籍贯"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第四行：党派、现任职务 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="党派" prop="politicalParty">
              <el-select 
                v-model="profileForm.politicalParty" 
                :disabled="!isEditing"
                placeholder="请选择党派"
                style="width: 100%"
              >
                <el-option v-for="party in politicalPartyOptions" :key="party" :label="party" :value="party" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="现任职务" prop="currentPosition">
              <el-input 
                v-model="profileForm.currentPosition" 
                :disabled="!isEditing"
                placeholder="请输入现任职务"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第五行：移动电话号码、学历 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="移动电话号码" prop="mobilePhone">
              <el-input 
                v-model="profileForm.mobilePhone" 
                :disabled="!isEditing"
                placeholder="请输入移动电话号码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学历" prop="education">
              <el-select 
                v-model="profileForm.education" 
                :disabled="!isEditing"
                placeholder="请选择学历"
                style="width: 100%"
              >
                <el-option v-for="edu in educationOptions" :key="edu" :label="edu" :value="edu" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第六行：毕业院校、所学专业 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="毕业院校" prop="graduatedSchool">
              <el-input 
                v-model="profileForm.graduatedSchool" 
                :disabled="!isEditing"
                placeholder="请输入毕业院校（可选）"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所学专业" prop="major">
              <el-input 
                v-model="profileForm.major" 
                :disabled="!isEditing"
                placeholder="请输入所学专业（可选）"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button 
            v-if="!isEditing" 
            type="primary" 
            @click="startEdit"
          >
            编辑信息
          </el-button>
          <div v-else>
            <el-button type="primary" @click="saveProfile">
              保存修改
            </el-button>
            <el-button @click="cancelEdit">
              取消
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 履职统计 -->
    <el-card class="stats-card">
      <template #header>
        <span class="card-title">履职统计</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalRecords }}</div>
            <div class="stat-label">履职记录总数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.opinionsSubmitted }}</div>
            <div class="stat-label">提交意见数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.opinionsResolved }}</div>
            <div class="stat-label">已办结意见</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.currentYearRecords }}</div>
            <div class="stat-label">本年履职次数</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()

// 编辑状态
const isEditing = ref(false)

// 表单数据 - 根据需求文档的11个属性字段
const profileForm = reactive({
  representativeLevel: '', // 代表层级（只读）
  name: '', // 姓名（只读）
  gender: '', // 性别
  ethnicity: '', // 民族
  birthDate: '', // 出生日期
  nativePlace: '', // 籍贯
  politicalParty: '', // 党派
  currentPosition: '', // 现任职务
  mobilePhone: '', // 移动电话号码
  education: '', // 学历
  graduatedSchool: '', // 毕业院校（可选）
  major: '' // 所学专业（可选）
})

// 原始数据备份
let originalData = {}

// 统计数据
const stats = ref({
  totalRecords: 12,
  opinionsSubmitted: 8,
  opinionsResolved: 6,
  currentYearRecords: 5
})

// 选项数据
const ethnicityOptions = ref([
  '汉族', '蒙古族', '回族', '藏族', '维吾尔族', '苗族', '彝族', '壮族', '布依族', '朝鲜族',
  '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族', '哈萨克族', '傣族', '黎族', '傈僳族',
  '佤族', '畲族', '高山族', '拉祜族', '水族', '东乡族', '纳西族', '景颇族', '柯尔克孜族',
  '土族', '达斡尔族', '仫佬族', '羌族', '布朗族', '撒拉族', '毛南族', '仡佬族', '锡伯族',
  '阿昌族', '普米族', '塔吉克族', '怒族', '乌孜别克族', '俄罗斯族', '鄂温克族', '德昂族',
  '保安族', '裕固族', '京族', '塔塔尔族', '独龙族', '鄂伦春族', '赫哲族', '门巴族',
  '珞巴族', '基诺族'
])

const politicalPartyOptions = ref([
  '中国共产党', '中国国民党革命委员会', '中国民主同盟', '中国民主建国会', '中国民主促进会',
  '中国农工民主党', '中国致公党', '九三学社', '台湾民主自治同盟', '无党派人士'
])

const educationOptions = ref([
  '博士研究生', '硕士研究生', '大学本科', '大学专科', '中等专业学校', '技工学校',
  '高中', '初中', '小学', '其他'
])

// 表单验证规则
const profileRules = {
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  ethnicity: [
    { required: true, message: '请选择民族', trigger: 'change' }
  ],
  birthDate: [
    { required: true, message: '请选择出生日期', trigger: 'change' }
  ],
  nativePlace: [
    { required: true, message: '请输入籍贯', trigger: 'blur' }
  ],
  politicalParty: [
    { required: true, message: '请选择党派', trigger: 'change' }
  ],
  currentPosition: [
    { required: true, message: '请输入现任职务', trigger: 'blur' }
  ],
  mobilePhone: [
    { required: true, message: '请输入移动电话号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  education: [
    { required: true, message: '请选择学历', trigger: 'change' }
  ]
}

// 开始编辑
const startEdit = () => {
  isEditing.value = true
  // 备份原始数据
  originalData = { ...profileForm }
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  // 恢复原始数据
  Object.assign(profileForm, originalData)
}

// 保存修改
const saveProfile = () => {
  // 验证必填项
  if (!profileForm.gender) {
    ElMessage.error('请选择性别')
    return
  }
  if (!profileForm.ethnicity) {
    ElMessage.error('请选择民族')
    return
  }
  if (!profileForm.birthDate) {
    ElMessage.error('请选择出生日期')
    return
  }
  if (!profileForm.nativePlace) {
    ElMessage.error('请输入籍贯')
    return
  }
  if (!profileForm.politicalParty) {
    ElMessage.error('请选择党派')
    return
  }
  if (!profileForm.currentPosition) {
    ElMessage.error('请输入现任职务')
    return
  }
  if (!profileForm.mobilePhone) {
    ElMessage.error('请输入移动电话号码')
    return
  }
  if (!/^1[3-9]\d{9}$/.test(profileForm.mobilePhone)) {
    ElMessage.error('请输入正确的手机号码')
    return
  }
  if (!profileForm.education) {
    ElMessage.error('请选择学历')
    return
  }

  // 更新用户信息
  userStore.updateUserInfo({
    gender: profileForm.gender,
    ethnicity: profileForm.ethnicity,
    birthDate: profileForm.birthDate,
    nativePlace: profileForm.nativePlace,
    politicalParty: profileForm.politicalParty,
    currentPosition: profileForm.currentPosition,
    mobilePhone: profileForm.mobilePhone,
    education: profileForm.education,
    graduatedSchool: profileForm.graduatedSchool,
    major: profileForm.major
  })

  isEditing.value = false
  ElMessage.success('信息更新成功')
}

// 初始化数据
onMounted(() => {
  const userInfo = userStore.userInfo
  Object.assign(profileForm, {
    representativeLevel: userInfo.representativeLevel || '市级人大代表',
    name: userInfo.name || '李明',
    gender: userInfo.gender || '',
    ethnicity: userInfo.ethnicity || '',
    birthDate: userInfo.birthDate || '',
    nativePlace: userInfo.nativePlace || '',
    politicalParty: userInfo.politicalParty || '',
    currentPosition: userInfo.currentPosition || '',
    mobilePhone: userInfo.mobilePhone || '',
    education: userInfo.education || '',
    graduatedSchool: userInfo.graduatedSchool || '',
    major: userInfo.major || ''
  })
})
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: var(--china-red);
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  margin: 0;
}

.profile-card,
.stats-card {
  margin-bottom: 20px;
}

.card-title {
  font-weight: bold;
  color: var(--china-red);
  font-size: 16px;
}

.stat-item {
  text-align: center;
  padding: 20px 0;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: var(--china-red);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 禁用状态的输入框样式 */
:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f5f5;
  box-shadow: 0 0 0 1px #e4e7ed inset;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-container {
    padding: 10px;
  }
  
  .stat-item {
    padding: 15px 0;
  }
  
  .stat-value {
    font-size: 24px;
  }
}
</style> 