"""
工作计划管理视图

包含以下视图：
1. WorkPlanListCreateView - 工作计划列表和创建
2. WorkPlanDetailView - 工作计划详情、更新、删除
3. WorkPlanStatisticsView - 工作计划统计
4. WorkPlanReminderView - 工作计划提醒
5. WorkPlanBatchUpdateView - 批量更新状态
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count
from django.utils import timezone
from django.http import Http404
from datetime import date, timedelta, datetime
import calendar

from .models import WorkPlan
from .serializers import (
    WorkPlanSerializer, WorkPlanCreateUpdateSerializer,
    WorkPlanListSerializer, WorkPlanReminderSerializer,
    WorkPlanStatisticsSerializer
)
from api.users.permissions import IsStaff


class WorkPlanListCreateView(APIView):
    """工作计划列表和创建"""
    
    permission_classes = [IsAuthenticated, IsStaff]
    
    def get(self, request):
        """获取工作计划列表"""
        try:
            # 权限检查已在permission_classes中完成，这里不需要额外检查
            
            # 获取分页参数
            page = int(request.query_params.get('page', 1))
            page_size = min(int(request.query_params.get('page_size', 10)), 100)  # 限制最大页面大小
            
            # 构建查询集（获取所有工作计划）
            queryset = WorkPlan.objects.select_related('staff_member__staffmember').all().order_by('-id')
            
            # 计算总数
            total_count = queryset.count()
            
            # 分页
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            queryset = queryset[start_index:end_index]
            
            # 序列化数据
            serializer = WorkPlanListSerializer(queryset, many=True)
            
            return Response({
                'success': True,
                'message': '获取工作计划列表成功',
                'data': {
                    'results': serializer.data,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total': total_count,
                        'pages': (total_count + page_size - 1) // page_size
                    }
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'获取工作计划列表失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def post(self, request):
        """创建工作计划"""
        try:
            # 权限检查已在permission_classes中完成，这里不需要额外检查
            
            # 数据验证和序列化
            serializer = WorkPlanCreateUpdateSerializer(data=request.data)
            if serializer.is_valid():
                # 保存工作计划，设置当前用户为负责人
                work_plan = serializer.save(staff_member=request.user)
                
                # 返回完整的工作计划信息
                detail_serializer = WorkPlanSerializer(work_plan)
                return Response({
                    'success': True,
                    'message': '创建工作计划成功',
                    'data': detail_serializer.data
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    'success': False,
                    'message': '数据验证失败',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            return Response({
                'success': False,
                'message': f'创建工作计划失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class WorkPlanDetailView(APIView):
    """工作计划详情、更新、删除"""
    
    permission_classes = [IsAuthenticated, IsStaff]
    
    def get(self, request, pk):
        """获取工作计划详情"""
        try:
            # 权限检查已在permission_classes中完成，这里不需要额外检查
            
            # 获取工作计划
            work_plan = get_object_or_404(WorkPlan.objects.select_related('staff_member__staffmember'), pk=pk)
            
            # 序列化数据
            serializer = WorkPlanSerializer(work_plan)
            
            return Response({
                'success': True,
                'message': '获取工作计划详情成功',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Http404:
            return Response({
                'success': False,
                'message': '工作计划不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'获取工作计划详情失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def put(self, request, pk):
        """更新工作计划"""
        try:
            # 权限检查已在permission_classes中完成，这里不需要额外检查
            
            # 获取工作计划
            work_plan = get_object_or_404(WorkPlan.objects.select_related('staff_member__staffmember'), pk=pk)
            
            # 数据验证和序列化
            serializer = WorkPlanCreateUpdateSerializer(work_plan, data=request.data)
            if serializer.is_valid():
                # 保存更新
                work_plan = serializer.save()
                
                # 返回完整的工作计划信息
                detail_serializer = WorkPlanSerializer(work_plan)
                return Response({
                    'success': True,
                    'message': '更新工作计划成功',
                    'data': detail_serializer.data
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': False,
                    'message': '数据验证失败',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Http404:
            return Response({
                'success': False,
                'message': '工作计划不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'更新工作计划失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request, pk):
        """删除工作计划"""
        try:
            # 权限检查已在permission_classes中完成，这里不需要额外检查
            
            # 获取工作计划
            work_plan = get_object_or_404(WorkPlan.objects.select_related('staff_member__staffmember'), pk=pk)
            
            # 删除工作计划
            work_plan.delete()
            
            return Response({
                'success': True,
                'message': '删除工作计划成功'
            }, status=status.HTTP_200_OK)
            
        except Http404:
            return Response({
                'success': False,
                'message': '工作计划不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'删除工作计划失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class WorkPlanStatisticsView(APIView):
    """工作计划统计"""
    
    permission_classes = [IsAuthenticated, IsStaff]
    
    def get(self, request):
        """获取工作计划统计数据"""
        try:
            # 权限检查已在permission_classes中完成，这里不需要额外检查
            
            # 基础统计
            total_count = WorkPlan.objects.count()
            planned_count = WorkPlan.objects.filter(status='planned').count()
            in_progress_count = WorkPlan.objects.filter(status='in_progress').count()
            completed_count = WorkPlan.objects.filter(status='completed').count()
            cancelled_count = WorkPlan.objects.filter(status='cancelled').count()
            
            # 即将到期的计划（7天内）
            upcoming_deadline = date.today() + timedelta(days=7)
            upcoming_count = WorkPlan.objects.filter(
                end_date__lte=upcoming_deadline,
                status__in=['planned', 'in_progress']
            ).count()
            
            # 逾期的计划
            overdue_count = WorkPlan.objects.filter(
                end_date__lt=date.today(),
                status__in=['planned', 'in_progress']
            ).count()
            
            # 月度统计（最近6个月）
            monthly_stats = []
            today = date.today()
            
            for i in range(6):
                # 计算月份
                target_date = today.replace(day=1) - timedelta(days=i * 30)
                year = target_date.year
                month = target_date.month
                
                # 获取该月的第一天和最后一天
                first_day = date(year, month, 1)
                last_day = date(year, month, calendar.monthrange(year, month)[1])
                
                # 统计该月创建的计划
                month_created = WorkPlan.objects.filter(
                    created_at__date__range=[first_day, last_day]
                ).count()
                
                # 统计该月完成的计划
                month_completed = WorkPlan.objects.filter(
                    status='completed',
                    updated_at__date__range=[first_day, last_day]
                ).count()
                
                monthly_stats.append({
                    'month': f'{year}-{month:02d}',
                    'created': month_created,
                    'completed': month_completed
                })
            
            # 按状态分组的统计数据
            status_stats = [
                {'status': 'planned', 'count': planned_count, 'label': '计划中'},
                {'status': 'in_progress', 'count': in_progress_count, 'label': '进行中'},
                {'status': 'completed', 'count': completed_count, 'label': '已完成'},
                {'status': 'cancelled', 'count': cancelled_count, 'label': '已取消'},
            ]
            
            # 组装统计数据
            statistics_data = {
                'total_count': total_count,
                'planned_count': planned_count,
                'in_progress_count': in_progress_count,
                'completed_count': completed_count,
                'cancelled_count': cancelled_count,
                'upcoming_count': upcoming_count,
                'overdue_count': overdue_count,
                'status_stats': status_stats,
                'monthly_stats': list(reversed(monthly_stats))  # 按时间正序
            }
            
            # 序列化数据
            serializer = WorkPlanStatisticsSerializer(statistics_data)
            
            return Response({
                'success': True,
                'message': '获取统计数据成功',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'获取统计数据失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class WorkPlanReminderView(APIView):
    """工作计划提醒"""
    
    permission_classes = [IsAuthenticated, IsStaff]
    
    def get(self, request):
        """获取需要提醒的工作计划"""
        try:
            # 权限检查已在permission_classes中完成，这里不需要额外检查
            
            # 获取需要提醒的计划
            reminder_plans = WorkPlan.get_reminder_plans()
            
            # 获取逾期的计划
            overdue_plans = WorkPlan.get_overdue_plans()
            
            # 序列化数据
            reminder_serializer = WorkPlanReminderSerializer(reminder_plans, many=True)
            overdue_serializer = WorkPlanReminderSerializer(overdue_plans, many=True)
            
            return Response({
                'success': True,
                'message': '获取提醒数据成功',
                'data': {
                    'reminder_plans': reminder_serializer.data,
                    'overdue_plans': overdue_serializer.data,
                    'reminder_count': len(reminder_plans),
                    'overdue_count': len(overdue_plans)
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'获取提醒数据失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
