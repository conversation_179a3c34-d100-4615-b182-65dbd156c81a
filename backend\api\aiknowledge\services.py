import requests
import json
import logging
from django.http import StreamingHttpResponse
from typing import Dict, Any, Optional, Iterator

logger = logging.getLogger(__name__)

# 硬编码的Dify配置
DIFY_CONFIG = {
    'api_key': 'app-HZFRyGtHXfTH3m2JGDucF42X',  # 请替换为您的Dify API密钥
    'base_url': 'https://dify.gxaigc.cn/v1',     # 请替换为您的Dify API地址
    'audio_to_text_url': 'https://dify.gxaigc.cn/v1/audio-to-text'  # 语音转文字API地址
}

# 新的意见建议生成应用配置（独立配置，不影响现有代码）
OPINION_DIFY_CONFIG = {
    'api_key': 'app-YyuN9U7rEw7wc8OeKJp4mzgy',  # 需要替换为实际的意见建议应用API密钥
    'base_url': 'https://dify.gxaigc.cn/v1',
}

# 语音内容解析应用配置（专门用于解析语音转文字结果）
VOICE_PARSER_DIFY_CONFIG = {
    'api_key': 'app-GUvPVL85o3iq4nMhGTIyMKbz',  # 语音内容解析智能体API密钥
    'base_url': 'https://dify.gxaigc.cn/v1',
}


class DifySSEProxyService:
    """Dify SSE代理服务"""
    
    def __init__(self):
        self.base_url = DIFY_CONFIG['base_url'].rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {DIFY_CONFIG["api_key"]}',
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
        }
    
    def chat_stream(self, query: str, user_id: str = 'anonymous',
                   conversation_id: Optional[str] = None,
                   inputs: Optional[Dict] = None,
                   app_key: Optional[str] = None) -> Iterator[str]:
        """
        流式聊天代理到Dify (根据官方文档规范)

        Args:
            query: 用户问题
            user_id: 用户ID
            conversation_id: 对话ID（可选）
            inputs: 变量输入（可选）
            app_key: 应用密钥（可选，默认使用DIFY_CONFIG中的密钥）

        Yields:
            SSE格式的数据流
        """
        url = f"{self.base_url}/chat-messages"

        payload = {
            "inputs": inputs or {},
            "query": query,
            "response_mode": "streaming",
            "user": user_id
        }
        logger.info(f"inputs: {inputs}")
        logger.info(f"query: {query}")
        # 只有当conversation_id不为空时才添加
        if conversation_id:
            payload["conversation_id"] = conversation_id

        # 使用传入的app_key或默认的api_key
        api_key = app_key or DIFY_CONFIG["api_key"]
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json',
        }

        try:
            print(f"[DEBUG] 发送SSE请求到Dify: {url}")
            print(f"[DEBUG] 请求数据: {payload}")
            logger.info(f"发送SSE请求到Dify: {url}")
            logger.debug(f"请求数据: {payload}")

            # 发送流式请求到Dify
            response = requests.post(
                url,
                json=payload,
                headers=headers,
                stream=True,
                timeout=60
            )
            
            print(f"[DEBUG] Dify响应状态码: {response.status_code}")
            print(f"[DEBUG] Dify响应头: {dict(response.headers)}")
            
            # 检查响应状态
            if response.status_code != 200:
                error_msg = f"Dify API请求失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                error_data = {
                    "event": "error",
                    "error": "AI服务请求失败",
                    "details": error_msg
                }
                yield f"data: {json.dumps(error_data)}\n\n"
                return
            
            # 处理流式响应 (根据官方文档格式)
            print("[DEBUG] 开始处理Dify流式响应")
            logger.info("开始处理Dify流式响应")
            
            line_count = 0
            for line in response.iter_lines():
                if line:
                    line_count += 1
                    decoded_line = line.decode('utf-8')
                    print(f"[DEBUG] 第{line_count}行原始数据: {decoded_line}")
                    logger.debug(f"收到原始行: {decoded_line}")
                    
                    if decoded_line.startswith('data:'):
                        data = decoded_line[5:].strip()
                        if data != '[DONE]':
                            try:
                                event_data = json.loads(data)
                                
                                # 详细调试：打印所有事件类型
                                event_type = event_data.get('event', 'unknown')
                                print(f"[DEBUG] 第{line_count}行事件类型: {event_type}")
                                
                                # 特别关注message_end事件
                                if event_type == 'message_end':
                                    print(f"[DEBUG] message_end事件完整数据: {json.dumps(event_data, ensure_ascii=False, indent=2)}")
                                
                                #logger.debug(f"解析的事件数据: {event_data}")
                                
                                # 根据官方文档，检查event字段
                                if 'event' in event_data:
                                    if event_data['event'] == 'message':
                                        # 转换为前端期望的格式
                                        frontend_data = {
                                            "event": "message",
                                            "answer": event_data.get('answer', ''),
                                            "conversation_id": event_data.get('conversation_id', ''),
                                            "message_id": event_data.get('message_id', ''),
                                            "created_at": event_data.get('created_at', '')
                                        }
                                        print(f"[DEBUG] 第{line_count}行发送给前端: answer='{frontend_data['answer']}'")
                                        logger.debug(f"发送给前端: {frontend_data}")
                                        yield f"data: {json.dumps(frontend_data)}\n\n"
                                    elif event_data['event'] == 'message_end':
                                        # 消息结束，完整转发所有字段包括metadata
                                        end_data = {
                                            "event": "message_end",
                                            "id": event_data.get('id', ''),
                                            "conversation_id": event_data.get('conversation_id', ''),
                                            "message_id": event_data.get('message_id', ''),
                                            "metadata": event_data.get('metadata', {})  # 完整转发metadata
                                        }
                                        
                                        # 调试日志：检查是否有参考文件
                                        if end_data['metadata'].get('retriever_resources'):
                                            print(f"[DEBUG] 转发参考文件数据: {len(end_data['metadata']['retriever_resources'])}条")
                                            logger.info(f"转发参考文件数据: {len(end_data['metadata']['retriever_resources'])}条")
                                        else:
                                            print(f"[DEBUG] 无参考文件数据")
                                            logger.info("无参考文件数据")
                                        
                                        logger.info("消息结束")
                                        yield f"data: {json.dumps(end_data)}\n\n"
                                    elif event_data['event'] == 'error':
                                        # API返回的错误
                                        error_data = {
                                            "event": "error",
                                            "error": event_data.get('message', 'API错误'),
                                            "details": str(event_data)
                                        }
                                        logger.error(f"API错误: {error_data}")
                                        yield f"data: {json.dumps(error_data)}\n\n"
                                elif 'error' in event_data:
                                    # 直接的错误响应
                                    error_data = {
                                        "event": "error",
                                        "error": event_data['error'],
                                        "details": str(event_data)
                                    }
                                    logger.error(f"直接错误: {error_data}")
                                    yield f"data: {json.dumps(error_data)}\n\n"
                            except json.JSONDecodeError as e:
                                logger.warning(f"JSON解析失败: {data}, 错误: {e}")
                                continue
                        else:
                            logger.info("收到[DONE]标记")
                            yield f"data: {json.dumps({'event': 'done'})}\n\n"
                    
        except requests.exceptions.RequestException as e:
            logger.error(f"Dify SSE请求失败: {str(e)}")
            error_data = {
                "event": "error",
                "error": "网络请求失败",
                "details": str(e)
            }
            yield f"data: {json.dumps(error_data)}\n\n"
        
        except Exception as e:
            logger.error(f"SSE代理异常: {str(e)}")
            error_data = {
                "event": "error",
                "error": "服务异常",
                "details": str(e)
            }
            yield f"data: {json.dumps(error_data)}\n\n"


class AIKnowledgeService:
    """AI知识库业务服务"""
    
    @classmethod
    def create_sse_response(cls, query: str, user_id: str = 'anonymous',
                           conversation_id: Optional[str] = None,
                           inputs: Optional[Dict] = None) -> StreamingHttpResponse:
        """
        创建SSE流式响应
        
        Args:
            query: 用户问题
            user_id: 用户ID
            conversation_id: 对话ID（可选）
            inputs: 变量输入（可选）
        
        Returns:
            StreamingHttpResponse对象
        """
        def event_stream():
            """SSE事件流生成器"""
            try:
                print(f"[DEBUG] 开始生成SSE流，查询: {query}")
                logger.info(f"开始生成SSE流，查询: {query}")
                
                # 使用Dify API进行真实的AI对话
                proxy_service = DifySSEProxyService()
                
                chunk_count = 0
                for data in proxy_service.chat_stream(
                    query=query,
                    user_id=user_id,
                    conversation_id=conversation_id,
                    inputs=inputs
                ):
                    chunk_count += 1
                    #print(f"[DEBUG] 发送第{chunk_count}个数据块: {data[:100]}...")
                    #logger.info(f"发送第{chunk_count}个数据块")
                    yield data
                
                #print(f"[DEBUG] SSE流结束，总共发送了{chunk_count}个数据块")
                #logger.info(f"SSE流结束，总共发送了{chunk_count}个数据块")
                    
            except Exception as e:
                #print(f"[DEBUG] SSE流异常: {str(e)}")
                #logger.error(f"SSE流生成异常: {str(e)}")
                error_data = {
                    "event": "error",
                    "error": "AI服务暂时不可用",
                    "details": str(e)
                }
                yield f"data: {json.dumps(error_data)}\n\n"
        
        response = StreamingHttpResponse(
            event_stream(),
            content_type='text/event-stream'
        )
        
        # 设置SSE相关的HTTP头
        response['Cache-Control'] = 'no-cache'
        response['X-Accel-Buffering'] = 'no'  # 禁用nginx缓冲
        response['Access-Control-Allow-Origin'] = '*'  # 允许跨域
        response['Access-Control-Allow-Headers'] = 'Content-Type,Authorization'
        
        return response
    
    @classmethod
    def audio_to_text(cls, audio_file) -> Dict[str, Any]:
        """
        语音转文字代理服务
        
        Args:
            audio_file: 音频文件对象
        
        Returns:
            Dict包含转换结果或错误信息
        """
        try:
            print(f"[DEBUG] 开始语音转文字，文件大小: {audio_file.size} bytes")
            logger.info(f"开始语音转文字，文件: {audio_file.name}, 大小: {audio_file.size}")
            
            # 准备请求头
            headers = {
                'Authorization': f'Bearer {DIFY_CONFIG["api_key"]}',
            }
            
            # 重置文件指针到开头（防止之前被读取过）
            audio_file.seek(0)
            
            # 准备文件数据
            files = {
                'file': (audio_file.name, audio_file.read(), audio_file.content_type)
            }
            
            print(f"[DEBUG] 发送语音转文字请求到: {DIFY_CONFIG['audio_to_text_url']}")
            logger.info(f"发送语音转文字请求到: {DIFY_CONFIG['audio_to_text_url']}")
            
            # 发送请求到第三方API
            response = requests.post(
                DIFY_CONFIG['audio_to_text_url'],
                headers=headers,
                files=files,
                timeout=30
            )
            
            print(f"[DEBUG] 语音转文字响应状态码: {response.status_code}")
            print(f"[DEBUG] 语音转文字响应内容: {response.text}")
            
            # 检查响应状态
            if response.status_code == 200:
                result = response.json()
                text = result.get('text', '')
                
                logger.info(f"语音转文字成功，结果长度: {len(text)}")
                return {
                    'success': True,
                    'text': text,
                    'message': '语音转文字成功'
                }
            else:
                error_msg = f"语音转文字API请求失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': '语音转文字失败',
                    'details': error_msg
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"语音转文字网络请求失败: {str(e)}")
            return {
                'success': False,
                'error': '网络请求失败',
                'details': str(e)
            }
            
        except Exception as e:
            logger.error(f"语音转文字异常: {str(e)}")
            return {
                'success': False,
                'error': '语音转文字服务异常',
                'details': str(e)
            } 


class OpinionAIService:
    """独立的意见建议AI服务"""
    
    def __init__(self):
        self.base_url = OPINION_DIFY_CONFIG['base_url'].rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {OPINION_DIFY_CONFIG["api_key"]}',
            'Content-Type': 'application/json',
        }
    
    def generate_suggestion(self, original_content: str, category: str, context: str = '', 
                          title: str = '', structured_text: str = '') -> Dict[str, Any]:
        """
        生成意见建议
        
        Args:
            original_content: 原始意见内容
            category: 意见类别
            context: 上下文信息
            title: 意见标题
            structured_text: 结构化文本（标题-分类-内容）
        
        Returns:
            Dict包含生成结果或错误信息
        """
        try:
            # 注释掉提示词生成步骤，直接发送用户原生内容
            # prompt = self._build_prompt(original_content, category, context)
            
            # 准备发送给AI的数据结构，优先使用结构化文本
            inputs_data = {
                "original_content": original_content,
                "category": category
            }
            
            # 添加标题信息
            if title and title.strip():
                inputs_data["title"] = title
            
            # 如果有上下文信息也包含进去
            if context and context.strip():
                inputs_data["context"] = context
            
            # 如果有结构化文本，也包含进去
            if structured_text and structured_text.strip():
                inputs_data["structured_text"] = structured_text
            
            # 决定主要查询内容：优先使用结构化文本，否则使用原始内容
            query_content = structured_text if structured_text and structured_text.strip() else original_content
            
            # 调用Dify API - 直接发送结构化内容，让Dify应用内的提示词来处理
            response = requests.post(
                f"{self.base_url}/chat-messages",
                headers=self.headers,
                json={
                    "inputs": inputs_data,
                    "query": query_content,  # 主要查询内容为结构化文本或原始意见
                    "response_mode": "blocking",
                    "user": "opinion_system"
                },
                timeout=100  # 增加到90秒，比前端120秒短，避免后端先超时
            )
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result.get('answer', '')
                
                # 清理AI生成内容，去除<think>标签
                cleaned_text = self._clean_generated_content(generated_text)
                
                logger.info(f"意见建议生成成功，原始长度: {len(generated_text)}, 清理后长度: {len(cleaned_text)}")
                return {
                    'success': True,
                    'generated_content': cleaned_text,
                    'message': '意见建议生成成功'
                }
            else:
                error_msg = f"意见建议生成API请求失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': '意见建议生成失败',
                    'details': error_msg
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"意见建议生成网络请求失败: {str(e)}")
            return {
                'success': False,
                'error': '网络请求失败',
                'details': str(e)
            }
            
        except Exception as e:
            logger.error(f"意见建议生成异常: {str(e)}")
            return {
                'success': False,
                'error': '意见建议生成服务异常',
                'details': str(e)
            }
    
    # 已注释掉：不再在后端生成提示词，改为直接发送原始数据给Dify应用
    # def _build_prompt(self, original_content: str, category: str, context: str = '') -> str:
    #     """
    #     构建AI提示词（已废弃）
    #     
    #     现在改为直接将原始数据发送给Dify应用，
    #     让Dify应用内配置的提示词来处理数据。
    #     
    #     Args:
    #         original_content: 原始意见内容
    #         category: 意见类别
    #         context: 上下文信息
    #     
    #     Returns:
    #         构建好的提示词
    #     """
    #     prompt = f"""请基于以下信息，生成一份高质量的人大代表意见建议：
    # 
    # 原始意见内容：
    # {original_content}
    # 
    # 意见类别：{category}
    # 
    # """
    #     
    #     if context:
    #         prompt += f"相关背景信息：\n{context}\n\n"
    #     
    #     prompt += """请按照以下要求优化和完善意见建议：
    # 
    # 1. 问题描述要具体明确，有数据支撑
    # 2. 建议措施要可操作、可落实
    # 3. 语言表达要规范、专业
    # 4. 结构要清晰，逻辑要严密
    # 5. 符合人大代表意见建议的格式要求
    # 
    # 请直接返回优化后的意见建议内容，不需要额外说明。"""
    #     
    #     return prompt
    
    def _clean_generated_content(self, content: str) -> str:
        """
        清理AI生成的内容，去除<think>标签等
        
        Args:
            content: 原始生成内容
        
        Returns:
            清理后的内容
        """
        import re
        
        if not content:
            return content
        
        # 去除<think>...</think>标签及其内容
        cleaned = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
        
        # 去除多余的空行
        cleaned = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned)
        
        # 去除开头和结尾的空白
        cleaned = cleaned.strip()
        
        return cleaned

    @classmethod
    def create_opinion_sse_response(cls, original_content: str, category: str = '',
                                   context: str = '', user_id: str = 'anonymous') -> StreamingHttpResponse:
        """
        创建意见建议AI生成的SSE流式响应

        Args:
            original_content: 原始意见内容
            category: 意见分类
            context: 补充背景信息
            user_id: 用户ID

        Returns:
            StreamingHttpResponse对象
        """
        def event_stream():
            """SSE事件流生成器"""
            try:
                print(f"[意见生成] 开始流式生成，原始内容: {original_content[:50]}{'...' if len(original_content) > 50 else ''}")
                logger.info(f"开始意见建议AI生成流式响应")

                # 使用Dify API进行意见建议生成
                proxy_service = DifySSEProxyService()

                # 构建意见建议生成的查询
                query = f"""请基于以下原始意见内容，生成一份高质量的意见建议：

原始内容：{original_content}
分类：{category if category else '未指定'}
补充信息：{context if context else '无'}

请生成规范、专业的意见建议内容。"""

                for data in proxy_service.chat_stream(
                    query=query,
                    user_id=user_id,
                    conversation_id=None,  # 意见建议生成不需要对话上下文
                    inputs={},
                    app_key=OPINION_DIFY_CONFIG['api_key']  # 使用意见建议专用的AI应用
                ):
                    yield data

            except Exception as e:
                print(f"[意见生成] 流式生成异常: {str(e)}")
                logger.error(f"意见建议SSE流生成异常: {str(e)}")
                error_data = {
                    "event": "error",
                    "error": "AI意见建议生成服务暂时不可用",
                    "details": str(e)
                }
                yield f"data: {json.dumps(error_data)}\n\n"

        response = StreamingHttpResponse(
            event_stream(),
            content_type='text/event-stream'
        )

        # 设置SSE相关的HTTP头
        response['Cache-Control'] = 'no-cache'
        response['X-Accel-Buffering'] = 'no'
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Headers'] = 'Content-Type,Authorization'

        return response


class VoiceContentParserService:
    """语音内容解析服务 - 专门用于解析语音转文字结果，提取标题和内容"""

    def __init__(self):
        self.base_url = VOICE_PARSER_DIFY_CONFIG['base_url'].rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {VOICE_PARSER_DIFY_CONFIG["api_key"]}',
            'Content-Type': 'application/json',
        }

    def parse_voice_text(self, voice_text: str, category: str = '', summarize_content: bool = True) -> Dict[str, Any]:
        """
        解析语音转文字结果，提取标题和内容

        Args:
            voice_text: 语音转换的文字内容
            category: 意见建议分类（可选）
            summarize_content: 是否让AI总结优化内容（默认True）

        Returns:
            Dict包含解析结果：title（标题）、content（内容）、category（分类）
        """
        try:
            print(f"[语音解析] 输入: {voice_text[:50]}{'...' if len(voice_text) > 50 else ''}")
            print(f"[语音解析] 分类: {category}, AI总结: {summarize_content}")
            logger.info(f"开始解析语音内容，文字长度: {len(voice_text)}")

            # 根据用户选择调整AI提示词
            if not summarize_content:
                content_instruction = "保持原始语音内容不变，不要总结或改写"
            else:
                content_instruction = "提取具体的意见建议内容（保持原始语音内容，不要过度总结或改写）"

            # 准备发送给AI的数据结构
            inputs_data = {
                "voice_text": voice_text
            }

            # 如果有分类信息也包含进去
            if category and category.strip():
                inputs_data["category"] = category

            # 构建明确的提示词，要求AI返回JSON格式（包含标题、内容和分类）
            prompt = f"""请从以下语音转换的文字中，提取意见建议的标题、具体内容，并推荐最合适的分类。

语音内容：{voice_text}

可选分类列表（必须从中选择一个）：
1. urban_construction - 城建环保
2. transportation - 交通出行
3. education - 教育文化
4. healthcare - 医疗卫生
5. social_security - 社会保障
6. economic - 经济发展
7. government_service - 政务服务
8. public_safety - 公共安全
9. community_service - 社区服务
10. housing - 住房保障
11. employment - 就业创业
12. elderly_care - 养老服务
13. food_safety - 食品安全
14. cultural_sports - 文体娱乐
15. digital_governance - 数字政务
16. other - 其他

要求：
1. 从语音内容中识别出合适的标题，格式必须为"关于xxxxx的建议"（简洁明确，不超过50字）
2. 内容处理：{content_instruction}
3. 根据内容选择最合适的分类（必须是上述16个分类中的一个value值）
4. 如果语音中没有明确标题，请根据内容生成"关于xxxxx的建议"格式的标题
5. 语音转后的文字可能有些识别不准确，你可以适当根据语境纠正文字词语
6. 必须严格按照以下JSON格式返回，不要添加任何其他内容：

{{"title": "提取或生成的标题", "content": "具体的意见建议内容", "category": "分类的value值"}}

请直接返回JSON，不要添加任何解释或其他文字。"""

            # 构建请求数据
            request_data = {
                "inputs": inputs_data,
                "query": prompt,  # 使用明确的提示词作为查询内容
                "response_mode": "blocking",
                "user": "voice_parser_system"
            }



            # 调用Dify API - 让智能体解析语音文字，提取标题和内容
            response = requests.post(
                f"{self.base_url}/chat-messages",
                headers=self.headers,
                json=request_data,
                timeout=60  # 语音解析相对简单，设置较短超时时间
            )

            if response.status_code == 200:
                result = response.json()
                generated_text = result.get('answer', '')
                logger.info(f"语音内容解析成功，AI返回长度: {len(generated_text)}")

                # 尝试解析AI返回的JSON格式结果
                parsed_result = self._parse_ai_response(generated_text)

                if parsed_result:
                    # 根据用户选择决定最终内容
                    final_content = voice_text if not summarize_content else parsed_result.get('content', '')

                    final_result = {
                        'success': True,
                        'title': parsed_result.get('title', ''),
                        'content': final_content,
                        'original_text': voice_text,
                        'message': f'语音内容解析成功（{"AI总结" if summarize_content else "保持原文"}）'
                    }
                    # 如果AI返回了分类建议，也包含进去
                    if 'category' in parsed_result:
                        final_result['category'] = parsed_result['category']

                    print(f"[语音解析] 输出: 标题={final_result['title']}, 分类={final_result.get('category', 'N/A')}")
                    return final_result
                else:
                    # 如果无法解析为JSON，则尝试简单的文本分割
                    fallback_result = self._fallback_parse(voice_text, generated_text)

                    # 根据用户选择决定最终内容
                    final_content = voice_text if not summarize_content else fallback_result.get('content', voice_text)

                    final_result = {
                        'success': True,
                        'title': fallback_result.get('title', ''),
                        'content': final_content,
                        'category': fallback_result.get('category', 'other'),
                        'original_text': voice_text,
                        'message': f'语音内容解析成功（备用方案，{"保持原文" if not summarize_content else "使用备用内容"}）'
                    }
                    print(f"[语音解析] 输出(备用): 标题={final_result['title']}, 分类={final_result.get('category', 'N/A')}")
                    return final_result
            else:
                error_msg = f"语音内容解析API请求失败: {response.status_code}"
                print(f"[语音解析] 错误: {error_msg}")
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': '语音内容解析失败',
                    'details': error_msg
                }

        except requests.exceptions.RequestException as e:
            print(f"[语音解析] 网络异常: {str(e)}")
            logger.error(f"语音内容解析网络请求失败: {str(e)}")
            return {
                'success': False,
                'error': '网络请求失败',
                'details': str(e)
            }

        except Exception as e:
            print(f"[语音解析] 异常: {str(e)}")
            logger.error(f"语音内容解析异常: {str(e)}")
            return {
                'success': False,
                'error': '语音内容解析服务异常',
                'details': str(e)
            }

    def _parse_ai_response(self, ai_response: str) -> Optional[Dict[str, str]]:
        """
        解析AI返回的响应，尝试提取JSON格式的标题和内容

        Args:
            ai_response: AI返回的原始响应

        Returns:
            解析后的字典，包含title和content，如果解析失败返回None
        """
        try:
            # 清理AI响应，去除可能的<think>标签
            cleaned_response = self._clean_ai_response(ai_response)

            # 尝试直接解析JSON
            try:
                parsed = json.loads(cleaned_response)
                if isinstance(parsed, dict) and 'title' in parsed and 'content' in parsed:
                    result = {
                        'title': str(parsed['title']).strip(),
                        'content': str(parsed['content']).strip()
                    }
                    # 如果有分类信息，也包含进去
                    if 'category' in parsed:
                        result['category'] = str(parsed['category']).strip()
                    return result
            except json.JSONDecodeError:
                pass

            # 尝试从文本中提取JSON块
            import re

            # 更精确的JSON匹配模式（支持包含category的JSON）
            json_patterns = [
                r'\{[^{}]*"title"[^{}]*"content"[^{}]*"category"[^{}]*\}',  # 包含category的模式
                r'\{[^{}]*"title"[^{}]*"content"[^{}]*\}',  # 原有模式（不含category）
                r'\{.*?"title".*?"content".*?"category".*?\}',  # 更宽松的模式（含category）
                r'\{.*?"title".*?"content".*?\}',  # 更宽松的模式（不含category）
                r'\{[\s\S]*?"title"[\s\S]*?"content"[\s\S]*?"category"[\s\S]*?\}',  # 包含换行的模式（含category）
                r'\{[\s\S]*?"title"[\s\S]*?"content"[\s\S]*?\}'  # 包含换行的模式（不含category）
            ]

            json_matches = []
            for pattern in json_patterns:
                matches = re.findall(pattern, cleaned_response, re.DOTALL)
                json_matches.extend(matches)
                if matches:
                    break

            for match in json_matches:
                try:
                    parsed = json.loads(match)
                    if isinstance(parsed, dict) and 'title' in parsed and 'content' in parsed:
                        result = {
                            'title': str(parsed['title']).strip(),
                            'content': str(parsed['content']).strip()
                        }
                        if 'category' in parsed:
                            result['category'] = str(parsed['category']).strip()
                        return result
                except json.JSONDecodeError:
                    continue

            return None

        except Exception as e:
            logger.warning(f"解析AI响应失败: {str(e)}")
            return None

    def _clean_ai_response(self, response: str) -> str:
        """
        清理AI响应，去除<think>标签等

        Args:
            response: 原始AI响应

        Returns:
            清理后的响应
        """
        import re

        if not response:
            return response

        # 去除<think>...</think>标签及其内容
        cleaned = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL)

        # 去除多余的空行
        cleaned = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned)

        # 去除开头和结尾的空白
        cleaned = cleaned.strip()

        return cleaned

    def _fallback_parse(self, original_text: str, ai_response: str = "") -> Dict[str, str]:
        """
        备用解析方案，当无法解析JSON时使用

        Args:
            original_text: 原始语音文字
            ai_response: AI响应（保留参数兼容性，实际未使用）

        Returns:
            包含title、content和category的字典
        """
        try:
            # 改进的启发式方法：尝试从原始文字中提取标题
            text = original_text.strip()

            # 按句号、问号、感叹号分割句子
            import re
            sentences = re.split(r'[。！？]', text)
            sentences = [s.strip() for s in sentences if s.strip()]

            if len(sentences) >= 2:
                # 如果有多个句子，第一句可能是标题或问题描述
                first_sentence = sentences[0]

                # 检查第一句是否像标题
                title_indicators = ['关于', '建议', '意见', '问题', '事项', '投诉', '反映']
                question_indicators = ['怎么', '如何', '能否', '可以', '有没有']

                if (any(indicator in first_sentence for indicator in title_indicators) or
                    any(indicator in first_sentence for indicator in question_indicators)):
                    # 第一句作为标题，其余作为内容
                    title = first_sentence
                    content = '。'.join(sentences[1:]) if len(sentences) > 1 else first_sentence
                    # 仍然需要推荐分类
                    _, category = self._generate_title_and_category(text)
                else:
                    # 生成描述性标题并推荐分类
                    title, category = self._generate_title_and_category(text)
                    content = text
            else:
                # 只有一个句子，生成描述性标题并推荐分类
                title, category = self._generate_title_and_category(text)
                content = text

            result = {
                'title': title,
                'content': content,
                'category': category
            }

            return result

        except Exception as e:
            logger.warning(f"备用解析失败: {str(e)}")
            return {
                'title': '语音录入的意见建议',
                'content': original_text,
                'category': 'other'
            }

    def _generate_title_and_category(self, text: str) -> tuple:
        """
        根据文本内容生成标题和推荐分类

        Args:
            text: 文本内容

        Returns:
            tuple: (标题, 分类)
        """
        # 关键词到分类的映射
        category_keywords = {
            'urban_construction': ['环保', '污染', '垃圾', '绿化', '建设', '规划', '市政', '基础设施'],
            'transportation': ['交通', '道路', '停车', '公交', '地铁', '出行', '拥堵', '红绿灯'],
            'education': ['教育', '学校', '老师', '学生', '培训', '课程', '教学', '幼儿园'],
            'healthcare': ['医疗', '医院', '看病', '药品', '健康', '医生', '护士', '治疗'],
            'social_security': ['社保', '养老金', '医保', '失业', '保险', '救助', '补贴'],
            'economic': ['经济', '发展', '投资', '企业', '就业', '收入', '税收', '产业'],
            'government_service': ['政务', '办事', '证件', '审批', '服务', '窗口', '效率'],
            'public_safety': ['安全', '治安', '犯罪', '消防', '应急', '救援', '监控'],
            'community_service': ['社区', '居委会', '物业', '邻里', '活动', '服务'],
            'housing': ['住房', '房价', '租房', '公租房', '拆迁', '安置', '小区'],
            'employment': ['就业', '招聘', '工作', '创业', '职业', '技能', '培训'],
            'elderly_care': ['养老', '老人', '敬老院', '护理', '老年', '退休'],
            'food_safety': ['食品', '安全', '卫生', '餐饮', '食堂', '市场', '检查'],
            'cultural_sports': ['文化', '体育', '娱乐', '图书馆', '健身', '运动', '活动'],
            'digital_governance': ['数字', '网络', '在线', '电子', '智能', '信息化', 'APP']
        }

        # 根据关键词匹配分类
        matched_category = 'other'
        max_matches = 0

        for category, keywords in category_keywords.items():
            matches = sum(1 for keyword in keywords if keyword in text)
            if matches > max_matches:
                max_matches = matches
                matched_category = category

        # 生成标题（统一为"关于xxxxx的建议"格式）
        if '味' in text and ('小区' in text or '社区' in text):
            title = "关于小区异味问题的建议"
        elif '停车' in text:
            title = "关于停车问题的建议"
        elif '噪音' in text or '吵' in text:
            title = "关于噪音问题的建议"
        elif '医院' in text or '看病' in text:
            title = "关于医疗服务的建议"
        elif '学校' in text or '教育' in text:
            title = "关于教育问题的建议"
        elif '道路' in text or '交通' in text:
            title = "关于交通出行的建议"
        elif '社区' in text or '物业' in text:
            title = "关于社区服务的建议"
        elif '安全' in text:
            title = "关于安全问题的建议"
        elif '环保' in text or '污染' in text:
            title = "关于环保问题的建议"
        elif '就业' in text or '工作' in text:
            title = "关于就业问题的建议"
        elif '养老' in text or '老人' in text:
            title = "关于养老服务的建议"
        else:
            # 根据内容生成通用标题
            if len(text) > 10:
                # 提取关键词生成标题
                key_phrase = text[:15].replace('。', '').replace('，', '').replace('！', '').replace('？', '')
                title = f"关于{key_phrase}的建议"
            else:
                title = "关于相关问题的建议"

        return title, matched_category