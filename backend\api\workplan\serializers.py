"""
工作计划管理序列化器

包含以下序列化器：
1. WorkPlanSerializer - 工作计划完整序列化器
2. WorkPlanCreateUpdateSerializer - 创建和更新序列化器
3. WorkPlanListSerializer - 列表序列化器（精简字段）
4. WorkPlanReminderSerializer - 提醒序列化器
5. WorkPlanStatisticsSerializer - 统计序列化器
"""

from rest_framework import serializers
from .models import WorkPlan


class WorkPlanSerializer(serializers.ModelSerializer):
    """工作计划完整序列化器"""
    
    # 关联工作人员信息
    staff_member_name = serializers.SerializerMethodField()
    
    # 状态显示名称
    status_display = serializers.CharField(
        source='get_status_display', 
        read_only=True
    )
    
    # 计算字段
    days_until_end = serializers.IntegerField(read_only=True)
    should_remind = serializers.BooleanField(read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    
    # 格式化日期
    start_date = serializers.DateField(format='%Y-%m-%d')
    end_date = serializers.DateField(format='%Y-%m-%d')
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    updated_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    def get_staff_member_name(self, obj):
        """获取工作人员姓名"""
        if obj.staff_member:
            try:
                # 获取工作人员的真实姓名
                return obj.staff_member.staffmember.name
            except AttributeError:
                # 如果没有关联的StaffMember记录，返回用户名
                return obj.staff_member.username
        return '未分配'
    
    class Meta:
        model = WorkPlan
        fields = [
            'id', 'staff_member', 'staff_member_name',
            'title', 'start_date', 'end_date', 'content', 'status', 'status_display',
            'reminder_days', 'days_until_end', 'should_remind', 'is_overdue',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class WorkPlanCreateUpdateSerializer(serializers.ModelSerializer):
    """工作计划创建和更新序列化器"""
    
    class Meta:
        model = WorkPlan
        fields = [
            'title', 'start_date', 'end_date', 'content', 
            'status', 'reminder_days'
        ]
    
    def validate(self, data):
        """数据验证"""
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        # 验证结束时间不能早于开始时间
        if start_date and end_date and end_date < start_date:
            raise serializers.ValidationError({
                'end_date': '结束时间不能早于开始时间'
            })
        
        return data
    
    def validate_title(self, value):
        """验证标题"""
        if not value or not value.strip():
            raise serializers.ValidationError('计划标题不能为空')
        
        if len(value.strip()) > 200:
            raise serializers.ValidationError('计划标题不能超过200个字符')
        
        return value.strip()
    
    def validate_content(self, value):
        """验证内容"""
        if value and len(value) > 2000:
            raise serializers.ValidationError('计划内容不能超过2000个字符')
        
        return value


class WorkPlanListSerializer(serializers.ModelSerializer):
    """工作计划列表序列化器（精简字段）"""
    
    staff_member_name = serializers.SerializerMethodField()
    status_display = serializers.CharField(
        source='get_status_display', 
        read_only=True
    )
    days_until_end = serializers.IntegerField(read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    
    # 格式化日期
    start_date = serializers.DateField(format='%Y-%m-%d')
    end_date = serializers.DateField(format='%Y-%m-%d')
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    updated_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    def get_staff_member_name(self, obj):
        """获取工作人员姓名"""
        if obj.staff_member:
            try:
                # 获取工作人员的真实姓名
                return obj.staff_member.staffmember.name
            except AttributeError:
                # 如果没有关联的StaffMember记录，返回用户名
                return obj.staff_member.username
        return '未分配'
    
    class Meta:
        model = WorkPlan
        fields = [
            'id', 'title', 'start_date', 'end_date', 'content', 'status', 'status_display',
            'staff_member_name', 'reminder_days', 'days_until_end', 'is_overdue',
            'created_at', 'updated_at'
        ]


class WorkPlanReminderSerializer(serializers.ModelSerializer):
    """工作计划提醒序列化器"""
    
    staff_member_name = serializers.SerializerMethodField()
    days_until_end = serializers.IntegerField(read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    
    # 提醒类型标识
    reminder_type = serializers.SerializerMethodField()
    
    # 格式化日期
    start_date = serializers.DateField(format='%Y-%m-%d')
    end_date = serializers.DateField(format='%Y-%m-%d')
    
    def get_staff_member_name(self, obj):
        """获取工作人员姓名"""
        if obj.staff_member:
            try:
                # 获取工作人员的真实姓名
                return obj.staff_member.staffmember.name
            except AttributeError:
                # 如果没有关联的StaffMember记录，返回用户名
                return obj.staff_member.username
        return '未分配'
    
    def get_reminder_type(self, obj):
        """获取提醒类型"""
        if obj.is_overdue:
            return 'overdue'  # 已逾期
        elif obj.should_remind:
            return 'upcoming'  # 即将到期
        return 'normal'  # 正常
    
    class Meta:
        model = WorkPlan
        fields = [
            'id', 'title', 'start_date', 'end_date', 'content', 'status',
            'staff_member_name', 'reminder_days', 'days_until_end', 
            'is_overdue', 'reminder_type'
        ]


class WorkPlanStatisticsSerializer(serializers.Serializer):
    """工作计划统计序列化器"""
    
    total_count = serializers.IntegerField(help_text="总计划数")
    planned_count = serializers.IntegerField(help_text="计划中数量")
    in_progress_count = serializers.IntegerField(help_text="进行中数量")
    completed_count = serializers.IntegerField(help_text="已完成数量")
    cancelled_count = serializers.IntegerField(help_text="已取消数量")
    upcoming_count = serializers.IntegerField(help_text="即将到期数量")
    overdue_count = serializers.IntegerField(help_text="逾期数量")
    status_stats = serializers.ListField(
        child=serializers.DictField(),
        help_text="按状态分组统计数据"
    )
    monthly_stats = serializers.ListField(
        child=serializers.DictField(),
        help_text="按月份统计数据"
    ) 