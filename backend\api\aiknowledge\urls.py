from django.urls import path
from .views import ChatSSEAPIView, AudioToTextAPIView, OpinionGenerateAPIView, VoiceToOpinionAPIView, OpinionGenerateSSEAPIView

app_name = 'aiknowledge'

urlpatterns = [
    # AI聊天SSE流
    path('chat/sse/', ChatSSEAPIView.as_view(), name='chat-sse'),
    # 语音转文字
    path('audio-to-text/', AudioToTextAPIView.as_view(), name='audio-to-text'),
    # 意见建议AI生成接口
    path('opinion/generate/', OpinionGenerateAPIView.as_view(), name='opinion_generate'),
    # 意见建议AI生成SSE流式接口
    path('opinion/generate/sse/', OpinionGenerateSSEAPIView.as_view(), name='opinion_generate_sse'),
    # 语音转意见建议（语音转文字 + AI内容解析）
    path('voice-to-opinion/', VoiceToOpinionAPIView.as_view(), name='voice-to-opinion'),
]