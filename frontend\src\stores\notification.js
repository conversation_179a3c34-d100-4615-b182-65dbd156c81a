import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  getNotifications, 
  getUnreadCount, 
  markAsRead, 
  markAllAsRead,
  batchMarkAsRead,
  deleteNotification 
} from '@/api/notification'
import { ElMessage } from 'element-plus'

export const useNotificationStore = defineStore('notification', () => {
  // 状态
  const notifications = ref([])
  const unreadCount = ref(0)
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const filters = ref({
    type: '',
    unreadOnly: false
  })

  // 计算属性
  const hasUnread = computed(() => unreadCount.value > 0)
  const totalPages = computed(() => Math.ceil(total.value / pageSize.value))

  // 获取通知列表
  const fetchNotifications = async (userId, refresh = false) => {
    if (refresh) {
      currentPage.value = 1
    }
    
    loading.value = true
    try {
      const params = {
        userId,
        page: currentPage.value,
        size: pageSize.value,
        ...filters.value
      }
      
      const response = await getNotifications(params)
      if (response.success) {
        notifications.value = response.data.list
        total.value = response.data.total
        currentPage.value = response.data.page
      }
    } catch (error) {
      console.error('获取通知列表失败:', error)
      ElMessage.error('获取通知列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取未读数量
  const fetchUnreadCount = async (userId) => {
    try {
      const response = await getUnreadCount(userId)
      if (response.success) {
        unreadCount.value = response.data.count
      }
    } catch (error) {
      console.error('获取未读数量失败:', error)
    }
  }

  // 标记单个通知为已读
  const markNotificationAsRead = async (notificationId) => {
    try {
      const response = await markAsRead(notificationId)
      if (response.success) {
        // 更新本地状态
        const notification = notifications.value.find(n => n.id === notificationId)
        if (notification && !notification.isRead) {
          notification.isRead = true
          unreadCount.value = Math.max(0, unreadCount.value - 1)
        }
        return true
      }
      return false
    } catch (error) {
      console.error('标记已读失败:', error)
      ElMessage.error('操作失败')
      return false
    }
  }

  // 批量标记为已读
  const markBatchAsRead = async (notificationIds) => {
    try {
      const response = await batchMarkAsRead(notificationIds)
      if (response.success) {
        // 更新本地状态
        let markedCount = 0
        notificationIds.forEach(id => {
          const notification = notifications.value.find(n => n.id === id)
          if (notification && !notification.isRead) {
            notification.isRead = true
            markedCount++
          }
        })
        unreadCount.value = Math.max(0, unreadCount.value - markedCount)
        ElMessage.success('批量标记成功')
        return true
      }
      return false
    } catch (error) {
      console.error('批量标记失败:', error)
      ElMessage.error('操作失败')
      return false
    }
  }

  // 全部标记为已读
  const markAllNotificationsAsRead = async (userId) => {
    try {
      const response = await markAllAsRead(userId)
      if (response.success) {
        // 更新本地状态
        notifications.value.forEach(n => n.isRead = true)
        unreadCount.value = 0
        ElMessage.success('全部标记为已读')
        return true
      }
      return false
    } catch (error) {
      console.error('全部标记失败:', error)
      ElMessage.error('操作失败')
      return false
    }
  }

  // 删除通知
  const removeNotification = async (notificationId) => {
    try {
      const response = await deleteNotification(notificationId)
      if (response.success) {
        // 更新本地状态
        const index = notifications.value.findIndex(n => n.id === notificationId)
        if (index !== -1) {
          const notification = notifications.value[index]
          if (!notification.isRead) {
            unreadCount.value = Math.max(0, unreadCount.value - 1)
          }
          notifications.value.splice(index, 1)
          total.value = Math.max(0, total.value - 1)
        }
        ElMessage.success('删除成功')
        return true
      }
      return false
    } catch (error) {
      console.error('删除通知失败:', error)
      ElMessage.error('删除失败')
      return false
    }
  }

  // 设置筛选条件
  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  // 清空筛选条件
  const clearFilters = () => {
    filters.value = {
      type: '',
      unreadOnly: false
    }
  }

  // 分页相关
  const setPage = (page) => {
    currentPage.value = page
  }

  const setPageSize = (size) => {
    pageSize.value = size
    currentPage.value = 1
  }

  // 重置状态
  const resetStore = () => {
    notifications.value = []
    unreadCount.value = 0
    loading.value = false
    currentPage.value = 1
    total.value = 0
    clearFilters()
  }

  // 实时更新通知（模拟接收新通知）
  const addNewNotification = (notification) => {
    notifications.value.unshift(notification)
    if (!notification.isRead) {
      unreadCount.value++
    }
    total.value++
  }

  return {
    // 状态
    notifications,
    unreadCount,
    loading,
    currentPage,
    pageSize,
    total,
    filters,

    // 计算属性
    hasUnread,
    totalPages,

    // 方法
    fetchNotifications,
    fetchUnreadCount,
    markNotificationAsRead,
    markBatchAsRead,
    markAllNotificationsAsRead,
    removeNotification,
    setFilters,
    clearFilters,
    setPage,
    setPageSize,
    resetStore,
    addNewNotification
  }
}) 