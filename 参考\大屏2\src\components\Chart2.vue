<template>
  <div ref="myChart" style="width: 100%; height: 90%"></div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    this.initChart()
  },
  watch: {
    data: {
      handler() {
        this.initChart()
      },
      deep: true
    }
  },
  methods: {
    async initChart() {
      const myChart = this.$echarts.init(this.$refs.myChart)
      const option = {
        legend: {
          orient: 'vertical',
          itemWidth: 15,
          itemHeight: 15,
          textStyle: {
            color: 'rgba(255,255,255,.5)',
          },
          top: '20%',
          right: 50,
          data: this.data.map((item) => item.name),
        },
        color: [
          '#37a2da',
          '#32c5e9',
          '#9fe6b8',
          '#ffdb5c',
          '#ff9f7f',
          '#fb7293',
          '#e7bcf3',
          '#8378ea',
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c} ({d}%)',
        },

        calculable: true,
        series: [
          {
            type: 'pie',
            radius: [0, 80],
            center: ['35%', '50%'],
            // roseType: 'area',
            data: this.data,
            label: {
              normal: {
                formatter: '{d}%',
              },
            },
            labelLine: {
              normal: {
                length: 5,
                length2: 15,
                lineStyle: { width: 1 },
              },
            },

            itemStyle: {
              normal: {
                shadowBlur: 30,
                shadowColor: 'rgba(0, 0, 0, 0.4)',
              },
            },
          },
        ],
      }
      await myChart.setOption(option)
    },
  },
}
</script>

<style scoped></style>
