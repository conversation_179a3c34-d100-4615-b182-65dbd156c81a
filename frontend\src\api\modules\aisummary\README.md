# AI总结模块 (aisummary)

## 模块概述

AI总结模块负责处理人大代表年度履职AI分析和成果展示相关的前端API调用。该模块与后端的`api.aisummary`应用对接，提供完整的AI履职分析功能。

## 主要功能

### 🎯 核心API

1. **AI总结生成** - 基于履职记录生成AI分析报告（支持首次生成和重新生成）
2. **总结详情查询** - 获取已生成的AI分析结果
3. **数据检查** - 检查年度数据是否足够生成分析
4. **总结列表** - 查看历史AI分析记录

### 🔧 工具函数

- **状态格式化** - 格式化AI生成状态显示
- **年份管理** - 获取可用年份列表和验证
- **轮询检查** - 自动轮询AI生成状态
- **摘要生成** - 提取AI分析关键信息

## 使用示例

### 基础使用

```javascript
import { aiSummaryAPI, aiSummaryUtils } from '@/api/modules/aisummary'

// 生成AI总结
const generateSummary = async (year) => {
  try {
    const result = await aiSummaryAPI.generate({
      analysis_year: year,
      force_regenerate: false
    })
    
    // 如果正在生成，开始轮询状态
    if (result.data.status === 'generating') {
      const finalResult = await aiSummaryAPI.pollGenerationStatus(year)
      return finalResult
    }
    
    return result.data
  } catch (error) {
    console.error('AI总结生成失败:', error)
    throw error
  }
}

// 获取总结详情
const getSummaryDetail = async (year) => {
  const response = await aiSummaryAPI.getDetail(year)
  return response.data.ai_result_data
}

// 检查数据可用性
const checkDataAvailability = async (year) => {
  const response = await aiSummaryAPI.checkData(year)
  return response.data.has_sufficient_data
}
```

### 在Vue组件中使用

```vue
<template>
  <div class="ai-summary">
    <el-select v-model="selectedYear" @change="onYearChange">
      <el-option
        v-for="year in availableYears"
        :key="year"
        :label="year + '年'"
        :value="year"
      />
    </el-select>
    
    <el-button 
      @click="generateAISummary"
      :loading="generating"
      :disabled="!canGenerate"
    >
      生成AI分析
    </el-button>
    
    <div v-if="summaryData" class="summary-display">
      <!-- AI分析结果展示 -->
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { aiSummaryAPI, aiSummaryUtils } from '@/api/modules/aisummary'

const selectedYear = ref(new Date().getFullYear())
const generating = ref(false)
const summaryData = ref(null)
const hasAnalysisData = ref(false)

// 获取可用年份
const availableYears = computed(() => {
  return aiSummaryUtils.getAvailableYears(5)
})

// 检查是否可以生成
const canGenerate = computed(() => {
  return selectedYear.value && hasAnalysisData.value && !generating.value
})

// 年份变化处理
const onYearChange = async () => {
  await checkAnalysisData()
  await loadSummaryData()
}

// 检查分析数据
const checkAnalysisData = async () => {
  try {
    const response = await aiSummaryAPI.checkData(selectedYear.value)
    hasAnalysisData.value = response.data.has_sufficient_data
  } catch (error) {
    console.error('检查数据失败:', error)
    hasAnalysisData.value = false
  }
}

// 加载AI总结数据
const loadSummaryData = async () => {
  try {
    const response = await aiSummaryAPI.getDetail(selectedYear.value)
    if (response.data.is_completed) {
      summaryData.value = response.data.ai_result_data
    }
  } catch (error) {
    // 如果没有找到数据，不算错误
    summaryData.value = null
  }
}

// 生成AI总结（首次生成）
const generateAISummary = async () => {
  generating.value = true
  
  try {
    const result = await aiSummaryAPI.generate({
      analysis_year: selectedYear.value,
      force_regenerate: false
    })
    
    // 如果正在生成，轮询状态
    if (result.data.status === 'generating') {
      const finalResult = await aiSummaryAPI.pollGenerationStatus(selectedYear.value)
      
      if (finalResult.is_completed) {
        summaryData.value = finalResult.ai_result_data
        ElMessage.success('AI分析生成完成！')
      } else {
        ElMessage.error(`AI分析生成失败：${finalResult.error_message}`)
      }
    }
  } catch (error) {
    console.error('AI总结生成失败:', error)
    ElMessage.error('AI分析生成失败，请稍后重试')
  } finally {
    generating.value = false
  }
}

// 重新生成AI总结
const regenerateAISummary = async () => {
  try {
    await ElMessageBox.confirm(
      '重新生成将覆盖当前的AI分析结果，是否继续？',
      '确认重新生成',
      { type: 'warning' }
    )
    
    generating.value = true
    
    const result = await aiSummaryAPI.generate({
      analysis_year: selectedYear.value,
      force_regenerate: true
    })
    
    // 处理生成结果...
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新生成失败:', error)
      ElMessage.error('重新生成失败，请稍后重试')
    }
  } finally {
    generating.value = false
  }
}

onMounted(() => {
  checkAnalysisData()
  loadSummaryData()
})
</script>
```

## API接口说明

### aiSummaryAPI.generate(data)
生成AI总结（支持首次生成和重新生成）
- **参数**: `{ analysis_year: number, force_regenerate?: boolean }`
  - `analysis_year`: 分析年份
  - `force_regenerate`: 是否强制重新生成（默认false）
- **返回**: Promise<GenerateResult>

### aiSummaryAPI.getDetail(year, params?)
获取AI总结详情
- **参数**: `year: number, params?: { representative_id?: number }`
- **返回**: Promise<SummaryDetail>

### aiSummaryAPI.checkData(year)
检查数据可用性
- **参数**: `year: number`
- **返回**: Promise<DataCheckResult>

### aiSummaryAPI.getList(params?)
获取AI总结列表
- **参数**: `params?: { page?, page_size?, year?, status?, representative_id? }`
- **返回**: Promise<SummaryList>

### aiSummaryAPI.pollGenerationStatus(year, maxAttempts?, interval?)
轮询检查生成状态
- **参数**: `year: number, maxAttempts?: number, interval?: number`
- **返回**: Promise<FinalResult>

## 工具函数说明

### aiSummaryUtils.formatStatus(status)
格式化状态显示
- **参数**: `status: string`
- **返回**: `{ text: string, color: string, icon: string }`

### aiSummaryUtils.getAvailableYears(yearRange?)
获取可用年份列表
- **参数**: `yearRange?: number` (默认5年)
- **返回**: `number[]`

### aiSummaryUtils.isValidYear(year)
验证年份有效性
- **参数**: `year: number`
- **返回**: `boolean`

### aiSummaryUtils.generateSummaryText(summary)
生成摘要文本
- **参数**: `summary: object`
- **返回**: `string`

## 数据结构

### AI分析结果格式
```typescript
interface AISummaryResult {
  overview: {
    subtitle: string
  }
  coreMetrics: Array<{
    label: string
    value: string
    icon: string
    color: string
    trend?: string
    trendType?: 'positive' | 'negative'
  }>
  activityDistribution: Array<{
    type: string
    count: number
    percentage: number
    color: string
  }>
  timeInvestment: {
    total: number
    monthly: number
    activity: number
    monthlyData: Array<{
      month: number
      percentage: number
    }>
  }
  highlights: Array<{
    title: string
    description: string
    metrics: Array<{
      label: string
      value: string
    }>
  }>
  keywords: Array<{
    word: string
    weight: number
  }>
  aiSummary: {
    evaluation: string
    achievements: string[]
    suggestions: string[]
  }
}
```

## 注意事项

1. **轮询机制**: AI生成可能需要较长时间，使用`pollGenerationStatus`进行状态轮询
2. **错误处理**: 所有API调用都应该包含适当的错误处理
3. **权限控制**: 代表只能查看自己的AI总结，工作人员可以查看所有代表的
4. **数据验证**: 生成AI总结前应先检查数据可用性
5. **状态管理**: 合理使用状态格式化工具提升用户体验

## 扩展说明

该模块可以根据需要扩展以下功能：
- 导出PDF功能
- 分享功能
- 多年度对比分析
- 自定义分析维度
- 模板选择功能 