"""
履职管理API视图

包含以下视图类：
1. PerformanceRecordListCreateAPIView - 履职记录列表和创建
2. PerformanceRecordDetailAPIView - 履职记录详情、更新、删除
3. PerformanceRecordStatsAPIView - 履职记录统计
4. PerformanceRecordExportAPIView - 履职记录导出
5. PerformanceAttachmentListAPIView - 附件列表
6. PerformanceAttachmentDetailAPIView - 附件详情、更新、删除
7. FileUploadAPIView - 文件上传
8. FileCleanupAPIView - 文件清理
9. PerformanceTypeChoicesAPIView - 履职类型选择项
10. PerformanceStatusChoicesAPIView - 履职状态选择项
11. FileTypeLimitsAPIView - 文件类型限制
12. SecureFileAccessAPIView - 安全文件访问

设计原则：
- RESTful API设计
- 使用APIView方式实现
- 严格的权限控制
- 完善的错误处理
- 详细的操作日志
- 性能优化（分页、缓存等）
"""

import logging
import json
from datetime import datetime, timedelta
from django.db import models
from django.db.models import Q, Count, Max
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.core.paginator import Paginator
from django.db import transaction
from django.shortcuts import get_object_or_404
from django.http import Http404
from rest_framework import status, permissions
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.exceptions import PermissionDenied
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .models import PerformanceRecord, PerformanceAttachment
from .serializers import (
    PerformanceRecordSerializer,
    PerformanceRecordCreateSerializer,
    PerformanceRecordUpdateSerializer,
    PerformanceRecordListSerializer,
    PerformanceAttachmentSerializer,
    FileUploadSerializer,
    PerformanceRecordStatsSerializer
)
from .permissions import (
    PerformanceRecordPermission,
    PerformanceAttachmentPermission,
    FileUploadPermission,
    IsRepresentativeUser
)
from .utils import FileHandler, FileValidator, FileCleanupService

# 配置日志
logger = logging.getLogger(__name__)


class PerformanceRecordListCreateAPIView(APIView):
    """
    履职记录列表和创建API
    
    GET: 获取履职记录列表（分页、筛选、排序）
    POST: 创建新的履职记录
    """
    
    permission_classes = [PerformanceRecordPermission]
    
    def get_queryset(self):
        """
        获取查询集
        只返回当前用户的履职记录，确保数据安全
        """
        if not self.request.user.is_authenticated:
            return PerformanceRecord.objects.none()
        
        if not hasattr(self.request.user, 'representative'):
            return PerformanceRecord.objects.none()
        
        # 基础查询集：当前代表的记录
        queryset = PerformanceRecord.objects.filter(
            representative=self.request.user.representative
        ).select_related('representative').prefetch_related('attachments')
        
        # 应用筛选条件
        queryset = self._apply_filters(queryset)
        
        return queryset
    
    def _apply_filters(self, queryset):
        """应用查询筛选条件"""
        request = self.request
        
        # 日期范围筛选
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(performance_date__gte=start_date)
            except ValueError:
                pass
        
        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(performance_date__lte=end_date)
            except ValueError:
                pass
        
        # 履职类型筛选
        performance_type = request.query_params.get('performance_type')
        if performance_type:
            queryset = queryset.filter(performance_type=performance_type)
        
        # 履职状态筛选
        performance_status = request.query_params.get('performance_status')
        if performance_status:
            queryset = queryset.filter(performance_status=performance_status)
        
        # 是否有附件筛选
        has_attachments = request.query_params.get('has_attachments')
        if has_attachments:
            queryset = queryset.filter(has_attachments=has_attachments.lower() == 'true')
        
        # 关键词搜索
        search = request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(performance_content__icontains=search) |
                Q(detailed_description__icontains=search) |
                Q(activity_location__icontains=search)
            )
        
        # 排序
        ordering = request.query_params.get('ordering', '-performance_date')
        if ordering:
            # 验证排序字段安全性
            allowed_fields = [
                'performance_date', '-performance_date',
                'performance_type', '-performance_type',
                'created_at', '-created_at',
                'updated_at', '-updated_at'
            ]
            if ordering in allowed_fields:
                queryset = queryset.order_by(ordering)
        
        return queryset
    
    @swagger_auto_schema(
        operation_description="获取履职记录列表",
        manual_parameters=[
            openapi.Parameter('start_date', openapi.IN_QUERY, description="开始日期(YYYY-MM-DD)", type=openapi.TYPE_STRING),
            openapi.Parameter('end_date', openapi.IN_QUERY, description="结束日期(YYYY-MM-DD)", type=openapi.TYPE_STRING),
            openapi.Parameter('performance_type', openapi.IN_QUERY, description="履职类型", type=openapi.TYPE_STRING),
            openapi.Parameter('performance_status', openapi.IN_QUERY, description="履职状态", type=openapi.TYPE_STRING),
            openapi.Parameter('has_attachments', openapi.IN_QUERY, description="是否有附件", type=openapi.TYPE_BOOLEAN),
            openapi.Parameter('search', openapi.IN_QUERY, description="搜索关键词", type=openapi.TYPE_STRING),
            openapi.Parameter('ordering', openapi.IN_QUERY, description="排序字段", type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description="页码", type=openapi.TYPE_INTEGER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description="每页数量", type=openapi.TYPE_INTEGER),
        ]
    )
    def get(self, request):
        """获取履职记录列表"""
        try:
            # 记录操作日志
            logger.info(f"用户 {request.user.username} 查询履职记录列表")
            
            queryset = self.get_queryset()
            
            # 分页处理
            page = request.query_params.get('page', 1)
            page_size = request.query_params.get('page_size', 20)
            
            try:
                page = int(page)
                page_size = int(page_size)
                # 验证页码和页面大小的有效性
                if page < 1:
                    logger.error(f"获取履职记录列表失败: 页码小于 1")
                    return Response(
                        {'detail': '页码必须大于等于1'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                if page_size < 1:
                    page_size = 20
                page_size = min(page_size, 100)  # 限制最大页面大小
            except (ValueError, TypeError):
                page = 1
                page_size = 20
            
            paginator = Paginator(queryset, page_size)
            page_obj = paginator.get_page(page)
            
            # 序列化数据
            serializer = PerformanceRecordListSerializer(page_obj.object_list, many=True)
            
            return Response({
                'count': paginator.count,
                'page': page,
                'page_size': page_size,
                'total_pages': paginator.num_pages,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
                'results': serializer.data
            })
            
        except Exception as e:
            logger.error(f"获取履职记录列表失败: {str(e)}")
            return Response(
                {'detail': '获取列表失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @swagger_auto_schema(
        operation_description="创建履职记录",
        request_body=PerformanceRecordCreateSerializer
    )
    def post(self, request):
        """创建履职记录"""
        try:
            # 检查用户是否有代表资料
            if not hasattr(request.user, 'representative'):
                return Response(
                    {'detail': '当前用户不是代表，无法创建履职记录'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            serializer = PerformanceRecordCreateSerializer(data=request.data, context={'request': request})
            if serializer.is_valid():
                # 创建记录
                performance_record = serializer.save()
                
                # 记录操作日志
                logger.info(f"用户 {request.user.username} 创建履职记录: {performance_record.id}")
                
                # 返回完整的记录信息
                result_serializer = PerformanceRecordSerializer(performance_record)
                return Response(result_serializer.data, status=status.HTTP_201_CREATED)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"创建履职记录失败: {str(e)}")
            return Response(
                {'detail': '创建失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PerformanceRecordDetailAPIView(APIView):
    """
    履职记录详情API
    
    GET: 获取履职记录详情
    PUT/PATCH: 更新履职记录
    DELETE: 删除履职记录
    """
    
    permission_classes = [PerformanceRecordPermission]
    
    def get_object(self, pk):
        """获取履职记录对象"""
        obj = get_object_or_404(PerformanceRecord, pk=pk)
        # 检查对象权限
        for permission in self.permission_classes:
            if not permission().has_object_permission(self.request, self, obj):
                from rest_framework.exceptions import PermissionDenied
                raise PermissionDenied('无权访问此履职记录')
        return obj
    
    @swagger_auto_schema(
        operation_description="获取履职记录详情"
    )
    def get(self, request, pk):
        """获取履职记录详情"""
        try:
            performance_record = self.get_object(pk)
            serializer = PerformanceRecordSerializer(performance_record)
            
            # 记录操作日志
            logger.info(f"用户 {request.user.username} 查看履职记录详情: {pk}")
            
            return Response(serializer.data)
            
        except (PermissionDenied, Http404) as e:
            # 权限和404错误直接抛出，让DRF处理
            raise e
        except Exception as e:
            logger.error(f"获取履职记录详情失败: {str(e)}")
            return Response(
                {'detail': '获取详情失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @swagger_auto_schema(
        operation_description="更新履职记录",
        request_body=PerformanceRecordUpdateSerializer
    )
    def put(self, request, pk):
        """完整更新履职记录"""
        try:
            performance_record = self.get_object(pk)
            serializer = PerformanceRecordUpdateSerializer(
                performance_record, 
                data=request.data,
                context={'request': request}
            )
            
            if serializer.is_valid():
                updated_record = serializer.save()
                
                # 记录操作日志
                logger.info(f"用户 {request.user.username} 更新履职记录: {pk}")
                
                # 返回更新后的完整信息
                result_serializer = PerformanceRecordSerializer(updated_record)
                return Response(result_serializer.data)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
                
        except (PermissionDenied, Http404) as e:
            # 权限和404错误直接抛出，让DRF处理
            raise e
        except Exception as e:
            logger.error(f"更新履职记录失败: {str(e)}")
            return Response(
                {'detail': '更新失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @swagger_auto_schema(
        operation_description="部分更新履职记录",
        request_body=PerformanceRecordUpdateSerializer
    )
    def patch(self, request, pk):
        """部分更新履职记录"""
        try:
            performance_record = self.get_object(pk)
            serializer = PerformanceRecordUpdateSerializer(
                performance_record, 
                data=request.data, 
                partial=True,
                context={'request': request}
            )
            
            if serializer.is_valid():
                updated_record = serializer.save()
                
                # 记录操作日志
                logger.info(f"用户 {request.user.username} 部分更新履职记录: {pk}")
                
                # 返回更新后的完整信息
                result_serializer = PerformanceRecordSerializer(updated_record)
                return Response(result_serializer.data)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
                
        except (PermissionDenied, Http404) as e:
            # 权限和404错误直接抛出，让DRF处理
            raise e
        except Exception as e:
            logger.error(f"部分更新履职记录失败: {str(e)}")
            return Response(
                {'detail': '更新失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @swagger_auto_schema(
        operation_description="删除履职记录"
    )
    def delete(self, request, pk):
        """删除履职记录"""
        try:
            performance_record = self.get_object(pk)
            
            # 使用事务确保数据一致性
            with transaction.atomic():
                # 删除关联的附件文件
                for attachment in performance_record.attachments.all():
                    try:
                        file_handler = FileHandler()
                        file_handler.delete_file(attachment.file_path)
                        if attachment.thumbnail_path:
                            file_handler.delete_file(attachment.thumbnail_path)
                    except Exception as file_error:
                        logger.warning(f"删除附件文件失败: {str(file_error)}")
                
                # 删除履职记录（级联删除附件记录）
                performance_record.delete()
            
            # 记录操作日志
            logger.info(f"用户 {request.user.username} 删除履职记录: {pk}")
            
            return Response(status=status.HTTP_204_NO_CONTENT)
            
        except Exception as e:
            logger.error(f"删除履职记录失败: {str(e)}")
            return Response(
                {'detail': '删除失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PerformanceRecordStatsAPIView(APIView):
    """
    履职记录统计API
    """
    
    permission_classes = [IsRepresentativeUser]
    
    @swagger_auto_schema(
        operation_description="获取当前用户的履职记录统计"
    )
    def get(self, request):
        """获取统计数据"""
        try:
            if not hasattr(request.user, 'representative'):
                return Response(
                    {'detail': '用户不是代表，无法获取统计数据'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            representative = request.user.representative
            
            # 获取所有履职记录
            all_records = PerformanceRecord.objects.filter(representative=representative)
            
            # 当前日期
            today = timezone.now().date()
            current_month_start = today.replace(day=1)
            current_year_start = today.replace(month=1, day=1)
            
            # 时间维度统计
            monthly_count = all_records.filter(
                performance_date__gte=current_month_start
            ).count()
            
            yearly_count = all_records.filter(
                performance_date__gte=current_year_start
            ).count()
            
            total_count = all_records.count()
            
            # 类型统计
            type_stats = {}
            for choice_value, choice_label in PerformanceRecord.PERFORMANCE_TYPE_CHOICES:
                count = all_records.filter(performance_type=choice_value).count()
                type_stats[choice_value] = {
                    'label': choice_label,
                    'count': count
                }
            
            # 状态统计
            status_stats = {}
            for choice_value, choice_label in PerformanceRecord.PERFORMANCE_STATUS_CHOICES:
                count = all_records.filter(performance_status=choice_value).count()
                status_stats[choice_value] = {
                    'label': choice_label,
                    'count': count
                }
            
            # 近12个月趋势数据
            monthly_trend = []
            for i in range(12):
                month_date = today.replace(day=1) - timedelta(days=i*30)
                month_start = month_date.replace(day=1)
                if month_date.month == 12:
                    month_end = month_date.replace(year=month_date.year+1, month=1, day=1) - timedelta(days=1)
                else:
                    month_end = month_date.replace(month=month_date.month+1, day=1) - timedelta(days=1)
                
                count = all_records.filter(
                    performance_date__gte=month_start,
                    performance_date__lte=month_end
                ).count()
                
                monthly_trend.append({
                    'month': month_date.strftime('%Y-%m'),
                    'count': count
                })
            
            # 反转趋势数据，按时间正序排列
            monthly_trend.reverse()
            
            # 附件统计
            attachment_stats = {
                'total_attachments': 0,
                'by_type': {}
            }
            
            # 统计各类型附件数量
            for choice_value, choice_label in PerformanceAttachment.FILE_TYPE_CHOICES:
                count = PerformanceAttachment.objects.filter(
                    performance_record__representative=representative,
                    file_type=choice_value
                ).count()
                attachment_stats['by_type'][choice_value] = {
                    'label': choice_label,
                    'count': count
                }
                attachment_stats['total_attachments'] += count
            
            # 获取最近5条履职记录用于工作台显示
            recent_records = all_records.order_by('-performance_date', '-created_at')[:5]
            recent_records_data = []
            for record in recent_records:
                recent_records_data.append({
                    'id': record.id,
                    'performance_content': record.performance_content,
                    'performance_date': record.created_at.isoformat(),  # 使用创建时间，精确到分钟
                    'performance_type': record.get_performance_type_display(),
                    'performance_status': record.get_performance_status_display()
                })
            
            # 组装统计数据
            stats_data = {
                'monthly_count': monthly_count,
                'yearly_count': yearly_count,
                'total_count': total_count,
                'type_stats': type_stats,
                'status_stats': status_stats,
                'monthly_trend': monthly_trend,
                'attachment_stats': attachment_stats,
                'recent_records': recent_records_data  # 添加最近记录
            }
            
            # 序列化返回
            serializer = PerformanceRecordStatsSerializer(stats_data)
            
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"获取统计数据失败: {str(e)}")
            return Response(
                {'detail': '获取统计数据失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PerformanceRecordExportAPIView(APIView):
    """
    履职记录导出API
    """
    
    permission_classes = [IsRepresentativeUser]
    
    @swagger_auto_schema(
        operation_description="导出履职记录数据"
    )
    def get(self, request):
        """导出数据"""
        try:
            if not hasattr(request.user, 'representative'):
                return Response(
                    {'detail': '用户不是代表，无法导出数据'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            representative = request.user.representative
            
            # 获取所有履职记录
            records = PerformanceRecord.objects.filter(
                representative=representative
            ).order_by('-performance_date')
            
            # 构建导出数据
            export_data = []
            for record in records:
                export_data.append({
                    'id': record.id,
                    'performance_date': record.performance_date.strftime('%Y-%m-%d'),
                    'performance_type': record.get_performance_type_display(),
                    'performance_content': record.performance_content,
                    'activity_location': record.activity_location,
                    'detailed_description': record.detailed_description,
                    'performance_status': record.get_performance_status_display(),
                    'has_attachments': '是' if record.has_attachments else '否',
                    'attachment_count': record.get_attachment_count(),
                    'created_at': record.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                })
            
            # 记录操作日志
            logger.info(f"用户 {request.user.username} 导出履职记录数据，共 {len(export_data)} 条")
            
            return Response({
                'count': len(export_data),
                'data': export_data,
                'export_time': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            })
            
        except Exception as e:
            logger.error(f"导出履职记录数据失败: {str(e)}")
            return Response(
                {'detail': '导出失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PerformanceAttachmentListAPIView(APIView):
    """
    履职记录附件列表API
    """
    
    permission_classes = [PerformanceAttachmentPermission]
    
    @swagger_auto_schema(
        operation_description="获取履职记录的附件列表",
        manual_parameters=[
            openapi.Parameter('performance_record_id', openapi.IN_QUERY, description="履职记录ID", type=openapi.TYPE_INTEGER, required=True),
        ]
    )
    def get(self, request):
        """获取附件列表"""
        try:
            performance_record_id = request.query_params.get('performance_record_id')
            if not performance_record_id:
                return Response(
                    {'detail': '缺少履职记录ID参数'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 验证履职记录是否存在且属于当前用户
            try:
                performance_record = PerformanceRecord.objects.get(
                    id=performance_record_id,
                    representative=request.user.representative
                )
            except PerformanceRecord.DoesNotExist:
                return Response(
                    {'detail': '履职记录不存在或无权访问'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # 获取附件列表
            attachments = PerformanceAttachment.objects.filter(
                performance_record=performance_record
            ).order_by('sort_order', 'created_at')
            
            serializer = PerformanceAttachmentSerializer(attachments, many=True)
            
            return Response({
                'performance_record_id': performance_record.id,
                'count': attachments.count(),
                'results': serializer.data
            })
            
        except Exception as e:
            logger.error(f"获取附件列表失败: {str(e)}")
            return Response(
                {'detail': '获取附件列表失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PerformanceAttachmentDetailAPIView(APIView):
    """
    履职记录附件详情API
    """
    
    permission_classes = [PerformanceAttachmentPermission]
    
    def get_object(self, pk):
        """获取附件对象"""
        obj = get_object_or_404(PerformanceAttachment, pk=pk)
        # 检查对象权限
        for permission in self.permission_classes:
            if not permission().has_object_permission(self.request, self, obj):
                from rest_framework.exceptions import PermissionDenied
                raise PermissionDenied('无权访问此附件')
        return obj
    
    @swagger_auto_schema(
        operation_description="获取附件详情"
    )
    def get(self, request, pk):
        """获取附件详情"""
        try:
            attachment = self.get_object(pk)
            serializer = PerformanceAttachmentSerializer(attachment)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"获取附件详情失败: {str(e)}")
            return Response(
                {'detail': '获取附件详情失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @swagger_auto_schema(
        operation_description="更新附件信息",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'sort_order': openapi.Schema(type=openapi.TYPE_INTEGER, description='排序顺序'),
            }
        )
    )
    def patch(self, request, pk):
        """更新附件信息（如排序）"""
        try:
            attachment = self.get_object(pk)
            
            # 只允许更新排序字段
            allowed_fields = ['sort_order']
            update_data = {k: v for k, v in request.data.items() if k in allowed_fields}
            
            if not update_data:
                return Response(
                    {'detail': '没有可更新的字段'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            serializer = PerformanceAttachmentSerializer(
                attachment, 
                data=update_data, 
                partial=True
            )
            
            if serializer.is_valid():
                updated_attachment = serializer.save()
                
                # 记录操作日志
                logger.info(f"用户 {request.user.username} 更新附件信息: {pk}")
                
                return Response(serializer.data)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
                
        except (PermissionDenied, Http404) as e:
            # 权限和404错误直接抛出，让DRF处理
            raise e
        except Exception as e:
            logger.error(f"更新附件信息失败: {str(e)}")
            return Response(
                {'detail': '更新附件信息失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @swagger_auto_schema(
        operation_description="删除附件"
    )
    def delete(self, request, pk):
        """删除附件"""
        try:
            attachment = self.get_object(pk)
            performance_record = attachment.performance_record
            
            # 使用事务确保数据一致性
            with transaction.atomic():
                # 删除文件
                try:
                    file_handler = FileHandler()
                    file_handler.delete_file(attachment.file_path)
                    if attachment.thumbnail_path:
                        file_handler.delete_file(attachment.thumbnail_path)
                except Exception as file_error:
                    logger.warning(f"删除附件文件失败: {str(file_error)}")
                
                # 删除数据库记录
                attachment.delete()
                
                # 更新履职记录的附件状态
                performance_record.update_attachment_status()
            
            # 记录操作日志
            logger.info(f"用户 {request.user.username} 删除附件: {pk}")
            
            return Response(status=status.HTTP_204_NO_CONTENT)
            
        except Exception as e:
            logger.error(f"删除附件失败: {str(e)}")
            return Response(
                {'detail': '删除附件失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class FileUploadAPIView(APIView):
    """
    文件上传API
    
    支持上传图片、音频、视频、文档四种类型的文件
    """
    
    permission_classes = [FileUploadPermission]
    parser_classes = [MultiPartParser, FormParser]
    
    @swagger_auto_schema(
        operation_description="上传履职记录附件",
        request_body=FileUploadSerializer,
        responses={
            200: openapi.Response(
                description="上传成功",
                schema=PerformanceAttachmentSerializer
            ),
            400: openapi.Response(description="上传失败")
        }
    )
    def post(self, request):
        """上传文件"""
        try:
            # 先检查必需的参数
            performance_record_id = request.data.get('performance_record_id')
            if not performance_record_id:
                return Response(
                    {'detail': '缺少履职记录ID参数'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 检查履职记录是否存在且有权限访问
            try:
                performance_record = PerformanceRecord.objects.get(
                    id=performance_record_id,
                    representative=request.user.representative
                )
            except PerformanceRecord.DoesNotExist:
                return Response(
                    {'detail': '履职记录不存在或无权访问'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # 验证上传数据
            serializer = FileUploadSerializer(data=request.data, context={'request': request})
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data
            file_obj = validated_data['file']
            file_type = validated_data['file_type']
            
            # 文件验证
            file_validator = FileValidator()
            validation_result = file_validator.validate_file(file_obj, file_type)
            
            if not validation_result['valid']:
                return Response(
                    {'detail': validation_result['message'], 'errors': validation_result['errors']},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 处理文件上传
            file_handler = FileHandler()
            
            # 使用事务确保数据一致性
            with transaction.atomic():
                # 保存文件
                file_info = file_handler.save_file(file_obj, file_type, performance_record.representative.id)
                
                # 创建附件记录
                attachment = PerformanceAttachment.objects.create(
                    performance_record=performance_record,
                    file_type=file_type,
                    original_filename=file_obj.name,
                    stored_filename=file_info['stored_filename'],
                    file_path=file_info['file_path'],
                    file_size=file_obj.size,
                    mime_type=file_obj.content_type,
                    file_hash=file_info['file_hash'],
                    duration=file_info.get('duration'),
                    width=file_info.get('width'),
                    height=file_info.get('height'),
                    thumbnail_path=file_info.get('thumbnail_path'),
                    upload_status='uploaded',
                    sort_order=performance_record.attachments.count()
                )
                
                # 更新履职记录的附件状态
                performance_record.update_attachment_status()
            
            # 记录操作日志
            logger.info(f"用户 {request.user.username} 上传文件: {attachment.id}")
            
            # 返回附件信息
            result_serializer = PerformanceAttachmentSerializer(attachment)
            return Response(result_serializer.data, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            return Response(
                {'detail': '文件上传失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class FileCleanupAPIView(APIView):
    """
    文件清理API
    用于管理员清理孤立文件
    """
    
    permission_classes = [permissions.IsAdminUser]
    
    @swagger_auto_schema(
        operation_description="清理孤立文件（仅管理员）",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={},
            description="空请求体"
        )
    )
    def post(self, request):
        """清理孤立文件"""
        try:
            cleanup_service = FileCleanupService()
            result = cleanup_service.cleanup_orphaned_files()
            
            # 记录操作日志
            logger.info(f"管理员 {request.user.username} 执行文件清理，清理了 {result['deleted_count']} 个文件")
            
            return Response(result)
            
        except Exception as e:
            logger.error(f"文件清理失败: {str(e)}")
            return Response(
                {'detail': '文件清理失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PerformanceTypeChoicesAPIView(APIView):
    """
    获取履职类型选择项
    """
    
    permission_classes = [IsRepresentativeUser]
    
    def get(self, request):
        """获取履职类型选择项"""
        choices = [
            {'value': choice[0], 'label': choice[1]}
            for choice in PerformanceRecord.PERFORMANCE_TYPE_CHOICES
        ]
        return Response({'choices': choices})


class PerformanceStatusChoicesAPIView(APIView):
    """
    获取履职状态选择项
    """
    
    permission_classes = [IsRepresentativeUser]
    
    def get(self, request):
        """获取履职状态选择项"""
        choices = [
            {'value': choice[0], 'label': choice[1]}
            for choice in PerformanceRecord.PERFORMANCE_STATUS_CHOICES
        ]
        return Response({'choices': choices})


class FileTypeLimitsAPIView(APIView):
    """
    获取文件类型限制信息
    """
    
    permission_classes = [IsRepresentativeUser]
    
    def get(self, request):
        """获取文件类型限制"""
        try:
            limits = FileValidator.get_file_type_limits()
            
            return Response({'limits': limits})
            
        except Exception as e:
            logger.error(f"获取文件类型限制失败: {str(e)}")
            return Response(
                {'detail': '获取文件类型限制失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SecureFileAccessAPIView(APIView):
    """
    安全文件访问API
    
    通过JWT认证提供安全的文件访问，支持权限验证
    """
    
    permission_classes = [PerformanceAttachmentPermission]
    
    @swagger_auto_schema(
        operation_description="安全文件访问",
        responses={
            200: openapi.Response(description="文件内容"),
            403: openapi.Response(description="无权访问"),
            404: openapi.Response(description="文件不存在")
        }
    )
    def get(self, request, attachment_id):
        """安全文件访问"""
        try:
            # 验证附件存在且有权限访问
            try:
                attachment = PerformanceAttachment.objects.get(
                    id=attachment_id,
                    performance_record__representative=request.user.representative
                )
            except PerformanceAttachment.DoesNotExist:
                return Response(
                    {'detail': '附件不存在或无权访问'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # 获取文件路径
            from django.core.files.storage import default_storage
            
            file_path = attachment.file_path
            if not default_storage.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return Response(
                    {'detail': '文件不存在'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # 返回文件内容
            from django.http import FileResponse
            from django.utils.encoding import escape_uri_path
            import mimetypes
            import os
            
            # 打开文件
            file_obj = default_storage.open(file_path, 'rb')
            
            # 构建响应
            response = FileResponse(
                file_obj,
                content_type=attachment.mime_type or 'application/octet-stream'
            )
            
            # 设置文件名
            filename = attachment.original_filename
            if filename:
                # 对文件名进行URL编码以支持中文
                encoded_filename = escape_uri_path(filename)
                response['Content-Disposition'] = f'inline; filename*=UTF-8\'\'{encoded_filename}'
            
            # 设置安全头部
            response['X-Content-Type-Options'] = 'nosniff'
            response['X-Frame-Options'] = 'DENY'
            response['Cache-Control'] = 'private, max-age=300'  # 5分钟缓存
            
            # 记录访问日志
            logger.info(f"用户 {request.user.username} 安全访问附件: {attachment_id}")
            
            return response
            
        except Exception as e:
            logger.error(f"文件访问失败: {str(e)}")
            return Response(
                {'detail': '文件访问失败，请稍后重试'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
