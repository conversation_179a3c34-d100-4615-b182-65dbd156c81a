"""
履职管理模块工具类

提供文件处理、验证、缩略图生成等功能
"""

import os
import uuid
import hashlib
import mimetypes
from datetime import datetime
from typing import Dict, Any, Optional
import logging

from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.uploadedfile import UploadedFile

logger = logging.getLogger(__name__)


class FileValidator:
    """文件验证工具类"""
    
    # 文件类型配置
    FILE_CONFIGS = {
        'image': {
            'max_size': 10 * 1024 * 1024,  # 10MB
            'max_count': 9,
            'allowed_extensions': ['jpg', 'jpeg', 'png', 'gif'],
            'allowed_mime_types': [
                'image/jpeg', 'image/png', 'image/gif', 'image/jpg'
            ],
            'description': '图片文件'
        },
        'audio': {
            'max_size': 50 * 1024 * 1024,  # 50MB
            'max_count': 3,
            'allowed_extensions': ['mp3', 'wav', 'aac', 'm4a'],
            'allowed_mime_types': [
                'audio/mpeg', 'audio/wav', 'audio/aac', 'audio/mp4'
            ],
            'description': '音频文件'
        },
        'video': {
            'max_size': 100 * 1024 * 1024,  # 100MB
            'max_count': 2,
            'allowed_extensions': ['mp4', 'avi', 'mov', 'wmv'],
            'allowed_mime_types': [
                'video/mp4', 'video/avi', 'video/quicktime', 'video/x-msvideo'
            ],
            'description': '视频文件'
        },
        'document': {
            'max_size': 20 * 1024 * 1024,  # 20MB
            'max_count': 5,
            'allowed_extensions': ['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx'],
            'allowed_mime_types': [
                'application/pdf', 'application/msword', 'text/plain',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ],
            'description': '文档文件'
        }
    }
    
    @classmethod
    def validate_file_type(cls, file_type: str) -> bool:
        """验证文件类型是否支持"""
        return file_type in cls.FILE_CONFIGS
    
    @classmethod
    def validate_file(cls, uploaded_file: UploadedFile, file_type: str) -> Dict[str, Any]:
        """
        验证上传文件
        
        Args:
            uploaded_file: 上传的文件对象
            file_type: 文件类型 (image, audio, video, document)
            
        Returns:
            Dict: 验证结果 {'valid': bool, 'message': str, 'errors': list}
        """
        errors = []
        
        # 检查文件类型配置
        config = cls.FILE_CONFIGS.get(file_type)
        if not config:
            return {
                'valid': False,
                'message': f'不支持的文件类型: {file_type}',
                'errors': [f'unsupported_file_type: {file_type}']
            }
        
        # 检查文件是否为空
        if not uploaded_file or uploaded_file.size == 0:
            errors.append('empty_file')
        
        # 检查文件大小
        if uploaded_file.size > config['max_size']:
            max_size_mb = config['max_size'] // (1024 * 1024)
            errors.append(f'file_too_large: {max_size_mb}MB')
        
        # 检查文件扩展名
        if uploaded_file.name:
            file_extension = uploaded_file.name.split('.')[-1].lower()
            if file_extension not in config['allowed_extensions']:
                errors.append(f'invalid_extension: {file_extension}')
        else:
            errors.append('missing_filename')
        
        # 检查MIME类型
        if uploaded_file.content_type not in config['allowed_mime_types']:
            errors.append(f'invalid_mime_type: {uploaded_file.content_type}')
        
        # 文件头魔数验证（安全检查）
        if not cls._validate_file_signature(uploaded_file, file_type):
            errors.append('invalid_file_signature')
        
        valid = len(errors) == 0
        message = '文件验证通过' if valid else f'{config["description"]}验证失败'
        
        return {
            'valid': valid,
            'message': message,
            'errors': errors
        }
    
    @classmethod
    def _validate_file_signature(cls, uploaded_file: UploadedFile, file_type: str) -> bool:
        """
        验证文件头魔数（简单的安全检查）
        
        Args:
            uploaded_file: 上传的文件对象
            file_type: 文件类型
            
        Returns:
            bool: 验证结果
        """
        try:
            # 读取文件头几个字节
            uploaded_file.seek(0)
            header = uploaded_file.read(16)
            uploaded_file.seek(0)  # 重置文件指针
            
            logger.info(f"验证文件签名 - 文件类型: {file_type}, 文件头: {header.hex()}")
            
            # 定义常见文件类型的魔数
            signatures = {
                'image': [
                    b'\xff\xd8\xff',  # JPEG
                    b'\x89PNG\r\n\x1a\n',  # PNG
                    b'GIF87a',  # GIF87a
                    b'GIF89a',  # GIF89a
                ],
                'video': [
                    b'\x00\x00\x00',  # MP4 (更宽松的检查，只检查前3个字节)
                    b'RIFF',  # AVI
                    b'ftyp',   # MP4的ftyp标识
                    b'\x1a\x45\xdf\xa3',  # WebM/MKV
                ],
                'audio': [
                    b'ID3',  # MP3 with ID3
                    b'\xff\xfb',  # MP3
                    b'\xff\xf3',  # MP3
                    b'RIFF',  # WAV
                    b'\xff\xf2',  # MP3
                    b'\xff\xfa',  # MP3
                ],
                'document': [
                    b'%PDF',  # PDF
                    b'\xd0\xcf\x11\xe0',  # DOC/XLS
                    b'PK\x03\x04',  # DOCX/XLSX (ZIP based)
                ]
            }
            
            file_signatures = signatures.get(file_type, [])
            
            # 如果没有定义魔数，直接通过验证
            if not file_signatures:
                return True
            
            # 检查文件头是否匹配任一魔数
            for signature in file_signatures:
                if header.startswith(signature):
                    return True
            
            # 对于视频文件，进行更灵活的检查
            if file_type == 'video':
                # 检查是否包含ftyp标识（MP4文件的特征）
                if b'ftyp' in header:
                    return True
                # 检查是否是AVI文件的RIFF头
                if header.startswith(b'RIFF') and b'AVI' in header:
                    return True
            
            # 对于文档类型，如果是纯文本也通过验证
            if file_type == 'document':
                try:
                    header.decode('utf-8')
                    return True
                except UnicodeDecodeError:
                    pass
            
            logger.warning(f"文件签名验证失败 - 文件类型: {file_type}, 文件头: {header.hex()}, 允许的签名: {[sig.hex() for sig in file_signatures]}")
            return False
            
        except Exception as e:
            logger.warning(f"文件签名验证失败: {e}")
            return True  # 验证失败时宽松处理

    @classmethod
    def get_file_type_limits(cls) -> Dict[str, Dict[str, Any]]:
        """获取文件类型限制信息"""
        return {
            file_type: {
                'max_size': config['max_size'],
                'max_count': config['max_count'],
                'allowed_extensions': config['allowed_extensions'],
                'description': config['description']
            }
            for file_type, config in cls.FILE_CONFIGS.items()
        }


class FileHandler:
    """文件处理工具类"""
    
    @classmethod
    def generate_unique_filename(cls, original_filename: str, file_type: str) -> str:
        """
        生成唯一文件名
        
        Args:
            original_filename: 原始文件名
            file_type: 文件类型
            
        Returns:
            str: 唯一文件名 格式: {uuid}_{timestamp}_{clean_name}
        """
        # 清理原始文件名
        import re
        
        # 分离文件名和扩展名
        if '.' in original_filename:
            name, ext = original_filename.rsplit('.', 1)
            ext = ext.lower()
        else:
            name, ext = original_filename, ''
        
        # 清理文件名（保留中文、英文、数字、下划线、横线）
        clean_name = re.sub(r'[^\w\u4e00-\u9fff.-]', '_', name)
        clean_name = re.sub(r'_{2,}', '_', clean_name)  # 合并多个下划线
        clean_name = clean_name.strip('_')
        
        # 生成UUID和时间戳
        file_uuid = str(uuid.uuid4()).replace('-', '')[:12]  # 12位UUID
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        
        # 组合文件名
        if ext:
            unique_filename = f"{file_uuid}_{timestamp}_{clean_name}.{ext}"
        else:
            unique_filename = f"{file_uuid}_{timestamp}_{clean_name}"
        
        return unique_filename
    
    @classmethod
    def generate_storage_path(cls, file_type: str, unique_filename: str) -> str:
        """
        生成存储路径
        
        Args:
            file_type: 文件类型
            unique_filename: 唯一文件名
            
        Returns:
            str: 相对存储路径
        """
        now = datetime.now()
        
        # 按日期分目录存储
        path = f"performance/{now.year}/{now.month:02d}/{now.day:02d}/{file_type}s/{unique_filename}"
        
        return path
    
    @classmethod
    def calculate_file_hash(cls, uploaded_file: UploadedFile) -> str:
        """
        计算文件MD5哈希值
        
        Args:
            uploaded_file: 上传的文件对象
            
        Returns:
            str: MD5哈希值
        """
        hash_md5 = hashlib.md5()
        
        # 重置文件指针
        uploaded_file.seek(0)
        
        # 分块读取文件计算哈希
        for chunk in uploaded_file.chunks():
            hash_md5.update(chunk)
        
        # 重置文件指针
        uploaded_file.seek(0)
        
        return hash_md5.hexdigest()
    
    @classmethod
    def save_uploaded_file(cls, uploaded_file: UploadedFile, file_path: str) -> str:
        """
        保存上传文件到存储系统
        
        Args:
            uploaded_file: 上传的文件对象
            file_path: 文件存储路径
            
        Returns:
            str: 实际保存的文件路径
        """
        # 重置文件指针
        uploaded_file.seek(0)
        
        # 使用Django存储系统保存文件
        saved_path = default_storage.save(file_path, uploaded_file)
        
        return saved_path
    
    @classmethod
    def get_file_url(cls, file_path: str) -> str:
        """
        获取文件访问URL
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件访问URL
        """
        return default_storage.url(file_path)
    
    @classmethod
    def delete_file(cls, file_path: str) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 删除是否成功
        """
        try:
            if default_storage.exists(file_path):
                default_storage.delete(file_path)
                return True
            return True  # 文件不存在也认为删除成功
        except Exception as e:
            logger.error(f"删除文件失败 {file_path}: {e}")
            return False
    
    @classmethod
    def save_file(cls, uploaded_file: UploadedFile, file_type: str, user_id: int) -> Dict[str, Any]:
        """
        保存上传文件并生成文件信息
        
        Args:
            uploaded_file: 上传的文件对象
            file_type: 文件类型
            user_id: 用户ID
            
        Returns:
            Dict: 文件信息字典
        """
        try:
            # 生成唯一文件名
            unique_filename = cls.generate_unique_filename(uploaded_file.name, file_type)
            
            # 生成存储路径
            file_path = cls.generate_storage_path(file_type, unique_filename)
            
            # 保存文件
            saved_path = cls.save_uploaded_file(uploaded_file, file_path)
            
            # 计算文件哈希
            file_hash = cls.calculate_file_hash(uploaded_file)
            
            # 生成缩略图（对图片和视频文件）
            thumbnail_path = None
            if file_type == 'image':
                thumbnail_path = ThumbnailGenerator.generate_image_thumbnail(saved_path)
                logger.info(f"为图片文件生成缩略图: {thumbnail_path}")
            elif file_type == 'video':
                thumbnail_path = ThumbnailGenerator.generate_video_thumbnail(saved_path)
                logger.info(f"为视频文件生成缩略图: {thumbnail_path}")
            
            # 提取媒体信息
            media_info = {}
            if file_type == 'image':
                media_info = MediaInfoExtractor.extract_image_info(saved_path)
            elif file_type == 'video':
                media_info = MediaInfoExtractor.extract_video_info(saved_path)
            elif file_type == 'audio':
                media_info = MediaInfoExtractor.extract_audio_info(saved_path)
            
            result = {
                'stored_filename': unique_filename,
                'file_path': saved_path,
                'file_hash': file_hash,
                'thumbnail_path': thumbnail_path,
                'width': media_info.get('width'),
                'height': media_info.get('height'),
                'duration': media_info.get('duration')
            }
            
            logger.info(f"文件保存完成: {file_type}, 缩略图: {thumbnail_path is not None}")
            return result
            
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            raise e


class ThumbnailGenerator:
    """缩略图生成工具类"""
    
    THUMBNAIL_SIZE = (300, 300)  # 缩略图尺寸
    THUMBNAIL_QUALITY = 85  # JPEG质量
    
    @classmethod
    def generate_thumbnail_path(cls, original_path: str) -> str:
        """
        生成缩略图路径
        
        Args:
            original_path: 原始文件路径
            
        Returns:
            str: 缩略图路径
        """
        # 解析原始路径
        path_parts = original_path.split('/')
        filename = path_parts[-1]
        
        # 生成缩略图文件名
        if '.' in filename:
            name, ext = filename.rsplit('.', 1)
        else:
            name, ext = filename, 'jpg'
        
        # 提取UUID和时间戳部分
        name_parts = name.split('_')
        if len(name_parts) >= 2:
            thumbnail_name = f"{name_parts[0]}_{name_parts[1]}_thumb.jpg"
        else:
            thumbnail_name = f"{name}_thumb.jpg"
        
        # 构建缩略图路径（替换目录）
        thumbnail_dir = '/'.join(path_parts[:-2] + ['thumbnails'] + path_parts[-2:-1])
        thumbnail_path = f"{thumbnail_dir}/{thumbnail_name}"
        
        return thumbnail_path
    
    @classmethod
    def generate_image_thumbnail(cls, file_path: str) -> Optional[str]:
        """
        为图片生成缩略图
        
        Args:
            file_path: 图片文件路径
            
        Returns:
            Optional[str]: 缩略图路径，失败返回None
        """
        try:
            from PIL import Image
            
            # 生成缩略图路径
            thumbnail_path = cls.generate_thumbnail_path(file_path)
            
            # 获取文件的完整路径
            if default_storage.exists(file_path):
                with default_storage.open(file_path, 'rb') as f:
                    # 打开图片
                    with Image.open(f) as img:
                        # 转换为RGB模式（处理RGBA等格式）
                        if img.mode in ('RGBA', 'LA', 'P'):
                            img = img.convert('RGB')
                        
                        # 生成缩略图
                        img.thumbnail(cls.THUMBNAIL_SIZE, Image.Resampling.LANCZOS)
                        
                        # 保存缩略图
                        from io import BytesIO
                        thumbnail_buffer = BytesIO()
                        img.save(thumbnail_buffer, 'JPEG', quality=cls.THUMBNAIL_QUALITY, optimize=True)
                        thumbnail_buffer.seek(0)
                        
                        # 保存到存储系统
                        saved_path = default_storage.save(thumbnail_path, thumbnail_buffer)
                        
                        return saved_path
            
        except ImportError:
            logger.warning("PIL库未安装，无法生成图片缩略图")
        except Exception as e:
            logger.error(f"生成图片缩略图失败 {file_path}: {e}")
        
        return None
    
    @classmethod
    def generate_video_thumbnail(cls, file_path: str) -> Optional[str]:
        """
        为视频生成缩略图
        
        Args:
            file_path: 视频文件路径
            
        Returns:
            Optional[str]: 缩略图路径，失败返回None
        """
        # 简化实现，暂时返回None
        logger.warning("视频缩略图生成功能暂未实现")
        return None


class MediaInfoExtractor:
    """媒体信息提取工具类"""
    
    @classmethod
    def extract_image_info(cls, file_path: str) -> Dict[str, Any]:
        """
        提取图片信息
        
        Args:
            file_path: 图片文件路径
            
        Returns:
            Dict: 图片信息 {'width': int, 'height': int, 'format': str}
        """
        info = {}
        
        try:
            from PIL import Image
            
            if default_storage.exists(file_path):
                with default_storage.open(file_path, 'rb') as f:
                    with Image.open(f) as img:
                        info['width'] = img.width
                        info['height'] = img.height
                        info['format'] = img.format
                        
        except ImportError:
            logger.warning("PIL库未安装，无法提取图片信息")
        except Exception as e:
            logger.error(f"提取图片信息失败 {file_path}: {e}")
        
        return info
    
    @classmethod
    def extract_video_info(cls, file_path: str) -> Dict[str, Any]:
        """
        提取视频信息
        
        Args:
            file_path: 视频文件路径
            
        Returns:
            Dict: 视频信息 {'width': int, 'height': int, 'duration': int}
        """
        # 简化实现，返回空字典
        logger.warning("视频信息提取功能暂未实现")
        return {}
    
    @classmethod
    def extract_audio_info(cls, file_path: str) -> Dict[str, Any]:
        """
        提取音频信息
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Dict: 音频信息 {'duration': int}
        """
        # 简化实现，返回空字典
        logger.warning("音频信息提取功能暂未实现")
        return {}


class FileCleanupService:
    """文件清理服务"""
    
    @classmethod
    def cleanup_orphaned_files(cls, performance_record_id: Optional[int] = None) -> Dict[str, int]:
        """
        清理孤立文件
        
        Args:
            performance_record_id: 可选，指定履职记录ID
            
        Returns:
            Dict: 清理统计 {'deleted_files': int, 'deleted_thumbnails': int}
        """
        # 简化实现，返回空统计
        logger.warning("文件清理功能暂未实现")
        return {'deleted_files': 0, 'deleted_thumbnails': 0}
    
    @classmethod
    def delete_attachment_files(cls, attachment) -> bool:
        """
        删除附件的所有相关文件
        
        Args:
            attachment: 附件对象
            
        Returns:
            bool: 删除是否成功
        """
        success = True
        
        # 删除主文件
        if attachment.file_path:
            if not FileHandler.delete_file(attachment.file_path):
                success = False
        
        # 删除缩略图
        if attachment.thumbnail_path:
            if not FileHandler.delete_file(attachment.thumbnail_path):
                success = False
        
        return success 