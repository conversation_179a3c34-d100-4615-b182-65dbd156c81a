#!/usr/bin/env python3
"""
代表账号导入模板生成脚本
运行此脚本生成Excel模板文件
"""

import openpyxl
from openpyxl.styles import Font, PatternFill
import os

def create_representative_template():
    """创建代表账号导入模板"""
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "代表账号导入模板"
    
    # 定义表头
    headers = [
        '用户名*', '密码*', '角色*', '账号状态',
        '代表姓名*', '代表层级*', '性别*', '民族', '所属片区',
        '出生日期*', '籍贯*', '党派*', '现任职务*', '手机号码*',
        '学历', '毕业院校', '所学专业'
    ]

    # 示例数据
    example_data = [
        'rep001', '123456', 'representative', 'TRUE',
        '张三', '县区人大代表', 'male', '汉族', '那洪片区',
        '1980-01-01', '广西南宁', '中国共产党', '某公司总经理', '13800138000',
        '本科', '广西大学', '计算机科学与技术'
    ]
    
    # 写入表头
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        # 设置表头样式
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
    
    # 写入示例数据
    for col, data in enumerate(example_data, 1):
        ws.cell(row=2, column=col, value=data)
    
    # 添加说明工作表
    ws_info = wb.create_sheet("导入说明")
    
    instructions = [
        "代表账号导入说明：",
        "",
        "1. 必填字段标记为 * ，不能为空",
        "2. 角色字段：固定填写 representative",
        "3. 账号状态：TRUE（启用）或 FALSE（禁用）",
        "4. 性别字段：male（男）或 female（女）",
        "5. 出生日期格式：YYYY-MM-DD（如：1980-01-01）",
        "6. 手机号码：11位数字",
        "7. 用户名必须唯一，不能重复",
        "",
        "代表层级选项：",
        "- 乡镇人大代表",
        "- 县区人大代表", 
        "- 市人大代表",
        "- 自治区人大代表",
        "- 全国人大代表",
        "",
        "所属片区选项：",
        "- 那洪片区",
        "- 那历片区",
        "- 沛鸿片区",
        "",
        "党派选项：",
        "- 中国共产党",
        "- 中国国民党革命委员会",
        "- 中国民主同盟",
        "- 中国民主建国会",
        "- 中国民主促进会",
        "- 中国农工民主党",
        "- 中国致公党",
        "- 九三学社",
        "- 台湾民主自治同盟",
        "- 群众",
        "",
        "注意事项：",
        "- 请勿修改表头",
        "- 删除示例数据后填入真实数据",
        "- 保存为Excel格式（.xlsx）",
        "- 单次导入建议不超过100条记录"
    ]
    
    for row, instruction in enumerate(instructions, 1):
        ws_info.cell(row=row, column=1, value=instruction)
    
    # 调整列宽
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 20)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    return wb

def create_staff_template():
    """创建工作人员账号导入模板"""
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "工作人员账号导入模板"
    
    # 定义表头
    headers = [
        '用户名*', '密码*', '角色*', '账号状态',
        '工作人员姓名*', '职位*', '工作人员手机号码*', '邮箱地址', '工作站点*'
    ]

    # 示例数据
    example_data = [
        'staff001', '123456', 'staff', 'TRUE',
        '李四', '联络员', '13900139000', '<EMAIL>', '江南区人大代表联络站'
    ]
    
    # 写入表头
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        # 设置表头样式
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
    
    # 写入示例数据
    for col, data in enumerate(example_data, 1):
        ws.cell(row=2, column=col, value=data)
    
    # 添加说明工作表
    ws_info = wb.create_sheet("导入说明")
    
    instructions = [
        "工作人员账号导入说明：",
        "",
        "1. 必填字段标记为 * ，不能为空",
        "2. 角色字段：固定填写 staff",
        "3. 账号状态：TRUE（启用）或 FALSE（禁用）",
        "4. 手机号码：11位数字",
        "5. 邮箱地址：标准邮箱格式（可选）",
        "6. 用户名必须唯一，不能重复",
        "",
        "职位示例：",
        "- 联络员",
        "- 工作人员",
        "- 管理员",
        "- 协调员",
        "",
        "工作站点示例：",
        "- 江南区人大代表联络站",
        "- 兴宁区人大代表联络站",
        "- 青秀区人大代表联络站",
        "",
        "注意事项：",
        "- 请勿修改表头",
        "- 删除示例数据后填入真实数据",
        "- 保存为Excel格式（.xlsx）",
        "- 单次导入建议不超过100条记录"
    ]
    
    for row, instruction in enumerate(instructions, 1):
        ws_info.cell(row=row, column=1, value=instruction)
    
    # 调整列宽
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 20)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    return wb

if __name__ == "__main__":
    # 创建模板目录
    template_dir = os.path.join(os.path.dirname(__file__), '..', 'static', 'templates')
    os.makedirs(template_dir, exist_ok=True)
    
    # 生成代表模板
    rep_wb = create_representative_template()
    rep_path = os.path.join(template_dir, '代表账号导入模板.xlsx')
    rep_wb.save(rep_path)
    print(f"代表模板已生成: {rep_path}")
    
    # 生成工作人员模板
    staff_wb = create_staff_template()
    staff_path = os.path.join(template_dir, '工作人员账号导入模板.xlsx')
    staff_wb.save(staff_path)
    print(f"工作人员模板已生成: {staff_path}")
    
    print("模板生成完成！")
