<template>
  <div class="knowledge-qa-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- AI助手介绍 - 仅在无对话时显示 -->
      <div v-if="conversationHistory.length === 0" class="ai-intro">
        <div class="ai-avatar-large">
          <el-icon><ChatDotRound /></el-icon>
        </div>
        <h1 class="ai-title">您好，我是法律政策互动AI问答助手</h1>
        <p class="ai-subtitle">基于专业法律政策知识库，为您提供智能问答服务</p>
      </div>
      <!-- <div v-for="(message, index) in  messages" :key="index">
                {{ message }}
       </div> -->
      <!-- 对话历史 -->
      <div v-if="conversationHistory.length > 0" class="conversation-history" ref="conversationArea">
        <div 
          v-for="(item, index) in conversationHistory" 
          :key="index"
          class="conversation-item"
        >
          <!-- 用户问题 -->
          <div v-if="item.type === 'user'" class="message user-message">
            <div class="message-content">{{ item.question }}</div>
          </div>

          <!-- AI回答 -->
          <div v-else-if="item.type === 'assistant'" class="message ai-message">
            <div class="ai-avatar-small">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="message-content ai-response-content">
              
              <!-- 加载状态 -->
              <div v-if="item.loading" class="ai-thinking">
                <el-icon class="is-loading"><Loading /></el-icon>
                正在思考...
              </div>
              
              <!-- AI回答内容 -->
              <div v-else-if="item.answer && item.answer.length > 0" class="ai-answer-wrapper">
                
                <!-- 主要回答内容 -->
                <div class="answer-content">
                  
                  <!-- 深度思考区域 - 内嵌在回答内容中 -->
                  <div v-if="item.thinking && item.thinking.length > 0" class="thinking-section">
                    <div class="thinking-header" @click="toggleThinking(index)">
                      <el-icon class="thinking-icon">
                        <ArrowDown v-if="item.showThinking" />
                        <ArrowRight v-else />
                      </el-icon>
                      <span class="thinking-label">
                        💭 深度思考
                        <span v-if="item.isInThinking" class="thinking-indicator">
                          <el-icon class="is-loading"><Loading /></el-icon>
                          思考中...
                        </span>
                      </span>
                      <span class="thinking-status">({{ item.showThinking ? '展开' : '收起' }})</span>
                    </div>
                    <div v-if="item.showThinking" class="thinking-content">
                      <div class="thinking-text" v-html="renderMarkdown(item.thinking.join(''))"></div>
                    </div>
                  </div>

                  <!-- 实际回答内容 -->
                  <div 
                    class="streaming-text markdown-content"
                    v-html="renderMarkdown(item.answer.join(''))"
                  ></div>
                  <div 
                    v-if="item.isStreaming" 
                    class="typing-cursor"
                  >|</div>
                </div>

                                <!-- 参考文件 - 折叠式显示 -->
                <div v-if="item.retrieverResources && item.retrieverResources.length > 0" class="references-section">
                  <!-- 折叠按钮 -->
                  <div class="references-toggle" @click="toggleReferences(index)">
                    <div class="toggle-content">
                      <el-icon class="toggle-icon">
                        <Document />
                      </el-icon>
                      <span class="toggle-text">查看参考文件 ({{ item.retrieverResources.length }}条)</span>
                      <el-icon class="expand-icon" :class="{ 'expanded': item.showReferences }">
                        <ArrowDown v-if="item.showReferences" />
                        <ArrowRight v-else />
                      </el-icon>
                    </div>
                  </div>
                  
                  <!-- 展开的参考文件列表 -->
                  <div v-if="item.showReferences" class="references-list">
                    <div 
                      v-for="(resource, resourceIndex) in item.retrieverResources" 
                      :key="resourceIndex"
                      class="reference-item"
                    >
                      <div class="reference-header">
                        <span class="reference-title">{{ resource.document_name }}</span>
                        <div class="reference-indicators">
                          <span class="reference-position">#{{ resource.position }}</span>
                          <span class="reference-score">{{ (resource.score * 100).toFixed(1) }}%</span>
                        </div>
                      </div>
                      <div class="reference-content">
                        {{ resource.content }}
                      </div>
                      <div class="reference-meta">
                        <span class="meta-item">
                          <el-icon><FolderOpened /></el-icon>
                          数据集: {{ resource.dataset_name }}
                        </span>
                        <span v-if="resource.segment_id" class="meta-item">
                          <el-icon><Document /></el-icon>
                          片段ID: {{ resource.segment_id.substring(0, 8) }}...
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="answer-actions">
                  <el-button 
                    text 
                    size="small"
                    @click="copyAnswer(item.answer)"
                    icon="CopyDocument"
                  >
                    复制
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部输入区域 -->
    <div class="input-container">
      <div class="input-wrapper">
        <el-input
          v-model="currentQuestion"
          type="textarea"
          :rows="1"
          :autosize="{ minRows: 1, maxRows: 4 }"
          placeholder="请输入您的法律政策相关问题..."
          @keydown.enter.exact.prevent="submitQuestion"
          :disabled="isAIThinking || isRecording"
          class="question-input"
          resize="none"
        />
        <!-- 语音转文字按钮 -->
        <el-button 
          :type="isRecording ? 'danger' : 'default'"
          @click="toggleRecording"
          :loading="isProcessingAudio"
          :disabled="isAIThinking"
          class="voice-button"
          circle
          :icon="isRecording ? 'VideoPlay' : 'Microphone'"
        >
          <template #loading>
            <el-icon class="is-loading"><Loading /></el-icon>
          </template>
        </el-button>
        <el-button 
          type="primary" 
          @click="submitQuestion"
          :loading="isAIThinking"
          :disabled="!currentQuestion.trim() || isRecording"
          class="send-button"
          circle
          icon="Position"
        />
      </div>
      
      
      <!-- 清空对话按钮 -->
      <div v-if="conversationHistory.length > 0" class="clear-button-container">
        <el-button 
          text 
          size="small"
          @click="clearMessages"
          :disabled="isAIThinking || isRecording"
          icon="Delete"
        >
          清空对话
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  ChatDotRound, Loading, CopyDocument, Position, Delete, Microphone, VideoPlay,
  ArrowDown, ArrowRight, Document, FolderOpened
} from '@element-plus/icons-vue'
import { useKnowledgeQAStore } from '@/stores/knowledgeQA'
import { aiKnowledgeAPI } from '@/api/modules/aiknowledge'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

const knowledgeStore = useKnowledgeQAStore()

// 本地状态
const currentQuestion = ref('')
const isAIThinking = ref(false)
const conversationHistory = ref([])
const conversationArea = ref(null)
const currentConversationId = ref(null)

// 语音录制相关状态
const isRecording = ref(false)
const isProcessingAudio = ref(false)
const mediaRecorder = ref(null)
const audioChunks = ref([])

// DOM观察器
const observer = ref(null)



// 配置markdown渲染器
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value
      } catch (__) {}
    }
    return hljs.highlightAuto(code).value
  },
  breaks: true,
  gfm: true
})

// 渲染Markdown
const renderMarkdown = (content) => {
  if (!content) return ''
  return marked(content)
}

// 解析思考内容
const parseThinkingContent = (content) => {
  if (!content) {
    return { thinking: '', answer: '', isThinking: false }
  }
  
  // 检查是否有完整的思考标签对
  const completeThinkMatch = content.match(/<think>([\s\S]*?)<\/think>/)
  
  if (completeThinkMatch) {
    // 找到完整的思考内容
    const thinkingContent = completeThinkMatch[1]
    const cleanAnswer = content.replace(/<think>[\s\S]*?<\/think>/g, '')
    
    return {
      thinking: thinkingContent,
      answer: cleanAnswer,
      isThinking: false // 思考已完成
    }
  }
  
  // 检查是否正在思考中（只有开始标签）
  const thinkStartMatch = content.match(/<think>([\s\S]*)$/)
  
  if (thinkStartMatch) {
    // 正在接收思考内容
    const thinkingContent = thinkStartMatch[1]
    const answerBeforeThink = content.substring(0, content.indexOf('<think>'))
    
    return {
      thinking: thinkingContent,
      answer: answerBeforeThink,
      isThinking: true // 正在思考中
    }
  }
  
  // 没有思考标签，全部作为回答内容
  return {
    thinking: '',
    answer: content,
    isThinking: false
  }
}

// 切换深度思考展示
const toggleThinking = (index) => {
  conversationHistory.value[index].showThinking = !conversationHistory.value[index].showThinking
}

// 切换参考文件展示
const toggleReferences = (index) => {
  conversationHistory.value[index].showReferences = !conversationHistory.value[index].showReferences
  
  // 如果展开参考文件，稍等一下再滚动（等动画完成）
  if (conversationHistory.value[index].showReferences) {
    setTimeout(() => {
      autoScroll()
    }, 350) // 动画时间 + 50ms
  }
}

// 提交问题
const submitQuestion = async () => {
  if (!currentQuestion.value.trim() || isAIThinking.value) {
    return
  }

  const question = currentQuestion.value.trim()
  currentQuestion.value = ''
  
  // 添加用户问题到对话历史
  const userMessage = {
    id: Date.now(),
    type: 'user',
    question,
    timestamp: new Date()
  }
  
  conversationHistory.value.push(userMessage)
  
  // 创建AI回答消息
  const aiMessage = {
    id: Date.now() + 1,
    type: 'assistant',
    question: '',
    answer: [],
    thinking: [],
    rawContent: [], // 存储原始内容，用于解析思考标签
    retrieverResources: [],
    loading: true,
    isStreaming: false,
    showThinking: true, // 默认展开深度思考
    isInThinking: false, // 是否正在接收思考内容
    showReferences: false, // 默认收起参考文件
    timestamp: new Date()
  }
  
  conversationHistory.value.push(aiMessage)
  isAIThinking.value = true

  // 强制滚动到底部（新消息）
  forceScrollToBottom()

  try {
    // 使用真实的SSE流式API
    const requestData = {
      query: question.trim()
    }
    
    // 只有当conversation_id存在且不为null时才添加
    if (currentConversationId.value) {
      requestData.conversation_id = currentConversationId.value
    }
    
    await aiKnowledgeAPI.chatSSE(
      requestData,
      (data) => {
        if (data.event === 'message' || data.answer) {
          const newContent = data.answer || data.text || ''
          if (newContent) {
            if (aiMessage.loading) {
              aiMessage.loading = false
              aiMessage.isStreaming = true
            }
            
            // 直接添加新内容到原始内容数组，用于解析
            if (!aiMessage.rawContent) {
              aiMessage.rawContent = []
            }
            aiMessage.rawContent.push(newContent)
            
            // 解析原始内容，分离思考和回答
            const fullRawContent = aiMessage.rawContent.join('')
            const parsedContent = parseThinkingContent(fullRawContent)
            
                         // 更新消息内容
             aiMessage.thinking = parsedContent.thinking ? [parsedContent.thinking] : []
             aiMessage.answer = [parsedContent.answer]
             aiMessage.isInThinking = parsedContent.isThinking
             
             // 控制思考区域显示
             if (parsedContent.thinking && parsedContent.thinking.length > 0) {
               aiMessage.showThinking = parsedContent.isThinking // 思考中时展开，思考完成后保持展开
             }
             
             console.log('🧠 解析结果:', {
               thinking: parsedContent.thinking,
               answer: parsedContent.answer,
               isThinking: parsedContent.isThinking
             })
             
             // 更精确的响应式更新：只更新当前消息
             const currentIndex = conversationHistory.value.length - 1
             conversationHistory.value[currentIndex] = { ...aiMessage }
             
             // 思考内容或回答内容有更新时都要滚动
             autoScroll()
          }
        } else if (data.event === 'message_end') {
          console.log('📋 收到message_end事件:', data)
          
          if (data.conversation_id && !currentConversationId.value) {
            currentConversationId.value = data.conversation_id
          }
          
          // 处理metadata中的参考文件
          if (data.metadata && data.metadata.retriever_resources) {
            console.log('📚 找到参考文件:', data.metadata.retriever_resources)
            aiMessage.retrieverResources = data.metadata.retriever_resources
            const currentIndex = conversationHistory.value.length - 1
            conversationHistory.value[currentIndex] = { ...aiMessage }
            console.log('✅ 参考文件已更新到aiMessage:', aiMessage.retrieverResources)
            
            // 参考文件加载后自动滚动
            autoScroll()
          } else {
            console.log('❌ 未找到参考文件数据，metadata:', data.metadata)
          }
        } else if (data.event === 'tts_message') {
          // TTS消息，这里可以处理音频播放
          console.log('收到TTS消息:', data.audio)
        } else if (data.event === 'tts_message_end') {
          // TTS消息结束
          console.log('TTS消息结束')
        }
      },
      (error) => {
        console.error('SSE流错误:', error)
        aiMessage.loading = false
        aiMessage.isStreaming = false
        aiMessage.answer = ['抱歉，AI服务暂时不可用，请稍后重试。']
        ElMessage.error('AI服务暂时不可用')
      },
      () => {
        console.log('[前端DEBUG] SSE流结束')
        aiMessage.isStreaming = false
        if (aiMessage.loading) {
            aiMessage.loading = false
            aiMessage.answer = ['抱歉，没有收到AI回复。']
        }
      }
    )
    
  } catch (error) {
    console.error('发送消息失败:', error)
    aiMessage.loading = false
    aiMessage.isStreaming = false
    aiMessage.answer = ['抱歉，AI服务暂时不可用，请稍后重试。']
    ElMessage.error('AI服务暂时不可用')
  } finally {
    isAIThinking.value = false
    // 最终确保滚动到底部
    forceScrollToBottom()
  }
}



// 获取滚动容器
const getScrollContainer = () => {
  return document.querySelector('.main-content')
}

// 通用的滚动到底部方法
const scrollToBottom = (smooth = false) => {
  // 多次尝试确保DOM更新完成
  const attemptScroll = (attempts = 0) => {
    const scrollContainer = getScrollContainer()
    if (scrollContainer && attempts < 3) {
      if (smooth) {
        scrollContainer.scrollTo({
          top: scrollContainer.scrollHeight,
          behavior: 'smooth'
        })
      } else {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
      }
    } else if (attempts < 3) {
      // 如果容器还没准备好，等一下再试
      setTimeout(() => attemptScroll(attempts + 1), 50)
    }
  }
  
  nextTick(() => attemptScroll())
}

// 自动滚动（带智能检测）
const autoScroll = () => {
  nextTick(() => {
    const scrollContainer = getScrollContainer()
    if (scrollContainer) {
      const scrollTop = scrollContainer.scrollTop
      const clientHeight = scrollContainer.clientHeight
      const scrollHeight = scrollContainer.scrollHeight
      const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100
      
      console.log('🔍 滚动检测:', {
        scrollTop,
        clientHeight,
        scrollHeight,
        isNearBottom,
        距离底部: scrollHeight - (scrollTop + clientHeight)
      })
      
      // 只有在接近底部时才自动滚动
      if (isNearBottom) {
        console.log('⬇️ 执行自动滚动')
        scrollContainer.scrollTo({
          top: scrollContainer.scrollHeight,
          behavior: 'smooth'
        })
      } else {
        console.log('🚫 用户不在底部，跳过自动滚动')
      }
    } else {
      console.warn('⚠️ 未找到滚动容器')
    }
  })
}

// 强制滚动到底部（不管用户位置）
const forceScrollToBottom = () => {
  scrollToBottom(true)
}

// 复制回答
const copyAnswer = async (answer) => {
  try {
    // answer.value 现在是数组，需要先 join
    const content = answer.join('')
    // 移除HTML标签以复制纯文本
    const textContent = content.replace(/<[^>]*>/g, '').replace(/\n\s*/g, '\n')
    await navigator.clipboard.writeText(textContent)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 清空对话
const clearMessages = () => {
  conversationHistory.value = []
  currentConversationId.value = null
  ElMessage.success('对话已清空')
}



// 切换录音状态
const toggleRecording = async () => {
  if (isRecording.value) {
    stopRecording()
  } else {
    await startRecording()
  }
}

// 开始录音
const startRecording = async () => {
  try {
    // 检查浏览器是否支持录音
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      ElMessage.error('您的浏览器不支持录音功能')
      return
    }

    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        sampleRate: 44100
      } 
    })

    // 创建MediaRecorder
    const mimeType = MediaRecorder.isTypeSupported('audio/webm;codecs=opus') 
      ? 'audio/webm;codecs=opus' 
      : 'audio/webm'
    
    mediaRecorder.value = new MediaRecorder(stream, {
      mimeType: mimeType
    })

    audioChunks.value = []

    // 监听数据可用事件
    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.value.push(event.data)
      }
    }

    // 监听录音停止事件
    mediaRecorder.value.onstop = async () => {
      const audioBlob = new Blob(audioChunks.value, { type: mimeType })
      await processAudio(audioBlob)
      
      // 停止所有音频轨道
      stream.getTracks().forEach(track => track.stop())
    }

    // 开始录音
    mediaRecorder.value.start()
    isRecording.value = true
    ElMessage.success('开始录音...')

  } catch (error) {
    console.error('开始录音失败:', error)
    if (error.name === 'NotAllowedError') {
      ElMessage.error('请允许访问麦克风权限')
    } else if (error.name === 'NotFoundError') {
      ElMessage.error('未找到麦克风设备')
    } else {
      ElMessage.error('录音失败，请检查设备权限')
    }
  }
}

// 停止录音
const stopRecording = () => {
  if (mediaRecorder.value && mediaRecorder.value.state === 'recording') {
    mediaRecorder.value.stop()
    isRecording.value = false
    ElMessage.info('录音结束，正在转换为文字...')
  }
}

// 处理音频文件
const processAudio = async (audioBlob) => {
  if (audioBlob.size === 0) {
    ElMessage.warning('录音时间太短，请重新录音')
    return
  }

  isProcessingAudio.value = true

  try {
    // 创建文件对象
    const audioFile = new File([audioBlob], 'recording.webm', {
      type: audioBlob.type
    })

    console.log('处理音频文件:', {
      size: audioFile.size,
      type: audioFile.type,
      name: audioFile.name
    })

    // 创建FormData
    const formData = new FormData()
    formData.append('file', audioFile)

    console.log('FormData内容检查:', {
      hasFile: formData.has('file'),
      formDataEntries: Array.from(formData.entries()).map(([key, value]) => ({
        key,
        value: value instanceof File ? `File: ${value.name} (${value.size} bytes)` : value
      }))
    })

    // 调用语音转文字API
    const response = await aiKnowledgeAPI.audioToText(formData)
    
    console.log('语音转文字响应:', response)
    console.log('响应数据结构:', {
      hasData: !!response.data,
      responseKeys: Object.keys(response),
      dataKeys: response.data ? Object.keys(response.data) : null
    })

    // 从response.data中获取实际数据
    const result = response.data || response
    
    if (result.success && result.text) {
      // 将转换的文字填入输入框
      currentQuestion.value = result.text.trim()
      ElMessage.success('语音转文字成功')
    } else {
      ElMessage.error(result.error || '语音转文字失败')
    }

  } catch (error) {
    console.error('语音转文字失败:', error)
    ElMessage.error('语音转文字失败: ' + error.message)
  } finally {
    isProcessingAudio.value = false
  }
}

// 初始化
onMounted(() => {
  knowledgeStore.fetchStats()
  
  // 设置DOM变化观察器，监听对话内容变化
  nextTick(() => {
    const targetNode = document.querySelector('.conversation-history')
    if (targetNode) {
      observer.value = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // 有新节点添加时自动滚动
            autoScroll()
          }
        })
      })
      
      observer.value.observe(targetNode, {
        childList: true,
        subtree: true
      })
    }
  })
})

// 组件卸载时清理资源
onBeforeUnmount(() => {
  if (mediaRecorder.value && mediaRecorder.value.state === 'recording') {
    mediaRecorder.value.stop()
  }
  
  // 清理DOM观察器
  if (observer.value) {
    observer.value.disconnect()
  }
})
</script>

<style scoped>
.knowledge-qa-container {
  height: calc(100vh - 60px);
  background: #ffffff;
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.main-content {
  flex: 1;
  padding: 40px 40px 20px;
  overflow-y: auto;
  min-height: 0;
}

/* AI介绍样式 */
.ai-intro {
  text-align: center;
  padding: 80px 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.ai-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--china-red), #e04545);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30px;
  color: white;
  font-size: 32px;
}

.ai-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.ai-subtitle {
  font-size: 16px;
  color: #666666;
  line-height: 1.6;
  margin: 0;
}

/* 对话历史样式 */
.conversation-history {
  max-height: none;
  overflow-y: visible;
  padding-bottom: 20px;
}

.conversation-item {
  margin-bottom: 32px;
}

.message {
  margin-bottom: 16px;
}

.user-message {
  display: flex;
  justify-content: flex-end;
}

.user-message .message-content {
  max-width: 70%;
  background: var(--china-red);
  color: white;
  padding: 12px 18px;
  border-radius: 18px;
  border-bottom-right-radius: 4px;
  font-size: 15px;
  line-height: 1.5;
}

.ai-message {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  width: 100%;
}

.ai-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--china-red), #e04545);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.ai-response-content {
  flex: 1;
  max-width: none;
  background: #f8f9fa;
  border-radius: 18px;
  border-bottom-left-radius: 4px;
  overflow: hidden;
}

.ai-thinking {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666666;
  padding: 12px 18px;
}

.ai-answer-wrapper {
  width: 100%;
}

/* 深度思考区域样式 - 内嵌在answer-content中 */
.thinking-section {
  margin-bottom: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f8f9fa;
  overflow: hidden;
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 14px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 13px;
  color: #6b7280;
  background: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
}

.thinking-header:hover {
  background: #e5e7eb;
}

.thinking-icon {
  font-size: 12px;
  transition: transform 0.2s;
}

.thinking-label {
  font-weight: 500;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #059669;
  font-weight: normal;
}

.thinking-status {
  font-size: 11px;
  opacity: 0.7;
}

.thinking-content {
  padding: 12px 14px;
  background: #ffffff;
}

.thinking-text {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 10px;
  font-size: 12px;
  line-height: 1.4;
  color: #4b5563;
  font-style: italic;
}

/* 主要回答内容样式 */
.answer-content {
  position: relative;
  padding: 12px 18px;
}

.streaming-text {
  display: inline;
}

.typing-cursor {
  display: inline-block;
  animation: blink 1s infinite;
  color: var(--china-red);
  font-weight: bold;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Markdown内容样式 */
.markdown-content {
  font-size: 15px;
  line-height: 1.6;
  color: #303133;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
  color: #1f2937;
}

.markdown-content :deep(p) {
  margin: 0 0 12px 0;
}

.markdown-content :deep(ol),
.markdown-content :deep(ul) {
  margin: 8px 0;
  padding-left: 20px;
}

.markdown-content :deep(li) {
  margin: 4px 0;
}

.markdown-content :deep(strong) {
  color: var(--china-red);
  font-weight: 600;
}

.markdown-content :deep(code) {
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 13px;
  color: #e11d48;
}

.markdown-content :deep(pre) {
  background: #1e293b;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 12px 0;
}

.markdown-content :deep(pre code) {
  background: none;
  padding: 0;
  color: inherit;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid var(--china-red);
  margin: 12px 0;
  padding: 8px 16px;
  background: #f8f9fa;
  color: #6b7280;
  font-style: italic;
}

.markdown-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  border: 1px solid #e5e7eb;
  padding: 8px 12px;
  text-align: left;
}

.markdown-content :deep(th) {
  background: #f9fafb;
  font-weight: 600;
}

/* 参考文件样式 */
.references-section {
  border-top: 1px solid #e5e7eb;
  background: #f8f9fa;
}

/* 折叠按钮样式 */
.references-toggle {
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid transparent;
}

.references-toggle:hover {
  background: #e5e7eb;
}

.toggle-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 18px;
  font-size: 13px;
  color: #6b7280;
}

.toggle-icon {
  font-size: 14px;
  color: #9ca3af;
}

.toggle-text {
  flex: 1;
  font-weight: 500;
}

.expand-icon {
  font-size: 12px;
  transition: transform 0.2s;
  color: #9ca3af;
}

.expand-icon.expanded {
  transform: rotate(0deg);
}

.references-list {
  padding: 0 18px 12px;
  border-top: 1px solid #e5e7eb;
  background: #ffffff;
  animation: expandReferences 0.3s ease-out;
}

@keyframes expandReferences {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.reference-item {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 8px;
  overflow: hidden;
}

.reference-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
}

.reference-title {
  font-weight: 500;
  color: #1f2937;
  font-size: 13px;
  flex: 1;
  margin-right: 12px;
}

.reference-indicators {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.reference-position {
  font-size: 11px;
  color: #6b7280;
  background: #e5e7eb;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.reference-score {
  font-size: 12px;
  color: #059669;
  font-weight: 500;
}

.reference-content {
  padding: 10px 12px;
  font-size: 13px;
  line-height: 1.4;
  color: #4b5563;
  max-height: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reference-meta {
  padding: 6px 12px;
  background: #f9fafb;
  font-size: 11px;
  color: #6b7280;
  border-top: 1px solid #f3f4f6;
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.meta-item {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.meta-item .el-icon {
  font-size: 11px;
  opacity: 0.7;
}

.answer-actions {
  margin-top: 12px;
  padding: 12px 18px 0;
  border-top: 1px solid #eeeeee;
}

/* 底部输入区域 */
.input-container {
  flex-shrink: 0;
  background: #ffffff;
  padding: 20px 40px 30px;
  border-top: 1px solid #e0e0e0;
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  background: #f8f9fa;
  border-radius: 24px;
  padding: 8px 8px 8px 20px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s;
  width: 100%;
}

.input-wrapper:focus-within {
  border-color: var(--china-red);
  box-shadow: 0 0 0 3px rgba(198, 45, 45, 0.1);
}

.question-input {
  flex: 1;
  border: none;
  background: transparent;
}

.question-input :deep(.el-textarea__inner) {
  border: none;
  background: transparent;
  box-shadow: none;
  padding: 8px 0;
  font-size: 15px;
  line-height: 1.5;
  resize: none;
}

.question-input :deep(.el-textarea__inner):focus {
  box-shadow: none;
}

.voice-button {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  margin-right: 8px;
}

.voice-button.is-recording {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.send-button {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}



.clear-button-container {
  display: flex;
  justify-content: center;
  margin-top: 12px;
}

/* 自定义滚动条 */
.conversation-history::-webkit-scrollbar,
.main-content::-webkit-scrollbar {
  width: 4px;
}

.conversation-history::-webkit-scrollbar-track,
.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.conversation-history::-webkit-scrollbar-thumb,
.main-content::-webkit-scrollbar-thumb {
  background: #dcdfe6;
  border-radius: 2px;
}

.conversation-history::-webkit-scrollbar-thumb:hover,
.main-content::-webkit-scrollbar-thumb:hover {
  background: #c0c4cc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-qa-container {
    height: calc(100vh - 60px);
  }
  
  .main-content {
    padding: 20px 20px 20px;
  }
  
  .ai-intro {
    padding: 40px 16px;
  }
  
  .ai-title {
    font-size: 24px;
  }
  
  .ai-subtitle {
    font-size: 14px;
  }
  
  .input-container {
    padding: 16px 20px 20px;
  }
  
  .user-message .message-content {
    max-width: 80%;
    font-size: 14px;
  }
  
  .ai-message .message-content {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 16px 16px 20px;
  }
  
  .ai-intro {
    padding: 20px 12px;
  }
  
  .ai-avatar-large {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
  
  .ai-title {
    font-size: 20px;
  }
  
  .input-container {
    padding: 12px 16px 16px;
  }
  
  .input-wrapper {
    padding: 6px 6px 6px 16px;
  }
  
  .send-button {
    width: 36px;
    height: 36px;
  }
  
  .user-message .message-content {
    max-width: 85%;
  }
}
</style> 