<template>
  <div class="network-error-container">
    <el-result
      icon="warning"
      title="网络连接失败"
      sub-title="后端服务暂时不可用，建议清除缓存后重新登录"
    >
      <template #extra>
        <el-button type="primary" @click="clearCacheAndLogin" :loading="clearing">
          {{ clearing ? '清除中...' : '清除缓存并重新登录' }}
        </el-button>
      </template>
    </el-result>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// 按钮状态
const clearing = ref(false)

/**
 * 清除缓存并重新登录
 */
const clearCacheAndLogin = () => {
  clearing.value = true

  try {
    console.log('🧹 开始清除浏览器缓存和认证信息...')

    // 1. 清除用户store状态
    userStore.clearAuthState()

    // 2. 清除所有localStorage数据
    localStorage.clear()

    // 3. 清除所有sessionStorage数据
    sessionStorage.clear()

    // 4. 显示成功消息
    ElMessage.success('缓存已清除，即将跳转到登录页')

    console.log('✅ 缓存清除完成')

    // 5. 延迟跳转到登录页，让用户看到成功消息
    setTimeout(() => {
      router.push('/login')
    }, 1500)

  } catch (error) {
    console.error('❌ 清除缓存失败:', error)
    ElMessage.error('清除缓存失败，请手动刷新页面')
  } finally {
    // 延迟重置按钮状态
    setTimeout(() => {
      clearing.value = false
    }, 2000)
  }
}
</script>

<style scoped>
.network-error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}
</style> 
