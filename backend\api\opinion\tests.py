"""
意见建议管理测试用例

包含以下测试：
1. 模型测试 - 数据模型功能测试
2. 序列化器测试 - 数据验证和序列化测试
3. 权限测试 - 权限控制测试
4. API视图测试 - 完整的API功能测试
5. 安全测试 - 安全性验证测试

uv run manage.py test api.opinion.tests -v 2
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from api.users.models import Representative, StaffMember
from .models import OpinionSuggestion, OpinionReview
from .serializers import (
    OpinionSuggestionCreateSerializer,
    OpinionReviewCreateSerializer,
    AIGenerateRequestSerializer
)

User = get_user_model()


class OpinionModelTest(TestCase):
    """意见建议模型测试"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建代表用户
        self.representative_user = User.objects.create_user(
            username='test_representative',
            password='testpass123',
            role='representative'
        )
        
        self.representative = Representative.objects.create(
            user=self.representative_user,
            level='区级',
            name='测试代表',
            gender='male',
            nationality='汉族',
            birth_date='1980-01-01',
            birthplace='测试市',
            party='中国共产党',
            current_position='测试职位',
            mobile_phone='13800138000',
            education='本科'
        )
        
        # 创建工作人员用户
        self.staff_user = User.objects.create_user(
            username='test_staff',
            password='testpass123',
            role='staff'
        )
        
        self.staff_member = StaffMember.objects.create(
            user=self.staff_user,
            name='测试工作人员',
            position='站点主任',
            mobile_phone='13900139000',
            email='<EMAIL>',
            station_name='测试站点'
        )
    
    def test_opinion_suggestion_creation(self):
        """测试意见建议创建"""
        opinion = OpinionSuggestion.objects.create(
            representative=self.representative,
            title='测试意见建议',
            category='urban_construction',
            reporter_name='张三',
            original_content='这是一个测试意见建议的内容',
            final_suggestion='这是最终的建议内容',
            ai_assisted=False
        )
        
        self.assertEqual(opinion.title, '测试意见建议')
        self.assertEqual(opinion.representative, self.representative)
        self.assertEqual(opinion.current_status, 'draft')  # 默认状态
        self.assertIsNotNone(opinion.created_at)
        self.assertIsNotNone(opinion.updated_at)
    
    def test_opinion_review_creation(self):
        """测试审核记录创建"""
        # 先创建意见建议
        opinion = OpinionSuggestion.objects.create(
            representative=self.representative,
            title='测试意见建议',
            category='urban_construction',
            reporter_name='张三',
            original_content='这是一个测试意见建议的内容'
        )
        
        # 创建审核记录
        review = OpinionReview.objects.create(
            opinion=opinion,
            reviewer=self.staff_member,
            action='approve',
            status='approved',
            review_comment='审核通过'
        )
        
        self.assertEqual(review.opinion, opinion)
        self.assertEqual(review.reviewer, self.staff_member)
        self.assertEqual(review.action, 'approve')
        self.assertEqual(review.status, 'approved')
        
        # 测试意见建议的当前状态
        self.assertEqual(opinion.current_status, 'approved')
    
    def test_opinion_status_flow(self):
        """测试意见建议状态流转"""
        # 创建意见建议
        opinion = OpinionSuggestion.objects.create(
            representative=self.representative,
            title='测试状态流转',
            category='urban_construction',
            reporter_name='张三',
            original_content='测试状态流转的内容'
        )
        
        # 1. 初始状态应该是draft（通过property获取）
        self.assertEqual(opinion.current_status, 'draft')
        
        # 2. 代表提交
        OpinionReview.objects.create(
            opinion=opinion,
            reviewer=None,  # 代表提交，无审核人
            action='submit',
            status='submitted',
            review_comment='代表提交意见建议'
        )
        self.assertEqual(opinion.current_status, 'submitted')
        
        # 3. 工作人员审核通过
        OpinionReview.objects.create(
            opinion=opinion,
            reviewer=self.staff_member,
            action='approve',
            status='approved',
            review_comment='审核通过'
        )
        self.assertEqual(opinion.current_status, 'approved')
        
        # 4. 转交部门
        OpinionReview.objects.create(
            opinion=opinion,
            reviewer=self.staff_member,
            action='transfer',
            status='transferred',
            transferred_department='城建局',
            review_comment='转交城建局处理'
        )
        self.assertEqual(opinion.current_status, 'transferred')
        
        # 5. 更新进展
        OpinionReview.objects.create(
            opinion=opinion,
            reviewer=self.staff_member,
            action='update_progress',
            status='in_progress',
            processing_result='正在处理中，已安排相关人员实地查看'
        )
        self.assertEqual(opinion.current_status, 'in_progress')
        
        # 6. 办结
        OpinionReview.objects.create(
            opinion=opinion,
            reviewer=self.staff_member,
            action='close',
            status='completed',
            processing_result='问题已解决，相关设施已完成修缮'
        )
        self.assertEqual(opinion.current_status, 'completed')


class OpinionSerializerTest(TestCase):
    """意见建议序列化器测试"""
    
    def setUp(self):
        """设置测试数据"""
        self.representative_user = User.objects.create_user(
            username='test_representative',
            password='testpass123',
            role='representative'
        )
        
        self.representative = Representative.objects.create(
            user=self.representative_user,
            level='区级',
            name='测试代表',
            gender='male',
            nationality='汉族',
            birth_date='1980-01-01',
            birthplace='测试市',
            party='中国共产党',
            current_position='测试职位',
            mobile_phone='13800138000',
            education='本科'
        )
    
    def test_opinion_create_serializer_valid_data(self):
        """测试意见建议创建序列化器的有效数据"""
        data = {
            'title': '测试意见建议标题',
            'category': 'urban_construction',
            'reporter_name': '张三',
            'original_content': '这是一个测试意见建议的详细内容，需要至少10个字符',
            'final_suggestion': '这是最终的建议内容',
            'ai_assisted': False
        }
        
        # 模拟请求上下文
        from unittest.mock import Mock
        request = Mock()
        request.user = self.representative_user
        
        serializer = OpinionSuggestionCreateSerializer(
            data=data,
            context={'request': request}
        )
        
        self.assertTrue(serializer.is_valid())
        opinion = serializer.save()
        
        self.assertEqual(opinion.title, data['title'])
        self.assertEqual(opinion.representative, self.representative)
        self.assertEqual(opinion.category, data['category'])
    
    def test_opinion_create_serializer_invalid_data(self):
        """测试意见建议创建序列化器的无效数据"""
        # 测试标题过短
        data = {
            'title': '短',  # 少于5个字符
            'category': 'urban_construction',
            'reporter_name': '张三',
            'original_content': '内容过短',  # 少于10个字符
            'ai_assisted': False
        }
        
        serializer = OpinionSuggestionCreateSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('title', serializer.errors)
        self.assertIn('original_content', serializer.errors)
    
    def test_ai_generate_serializer(self):
        """测试AI生成请求序列化器"""
        # 有效数据
        valid_data = {
            'original_content': '这是一个有效的原始意见内容，字符数足够',
            'category': 'urban_construction',
            'context': '补充背景信息'
        }
        
        serializer = AIGenerateRequestSerializer(data=valid_data)
        self.assertTrue(serializer.is_valid())
        
        # 无效数据 - 内容过短
        invalid_data = {
            'original_content': '太短',  # 少于10个字符
            'category': 'urban_construction'
        }
        
        serializer = AIGenerateRequestSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('original_content', serializer.errors)


class OpinionPermissionTest(APITestCase):
    """意见建议权限测试"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建代表用户
        self.representative_user = User.objects.create_user(
            username='test_representative',
            password='testpass123',
            role='representative'
        )
        
        self.representative = Representative.objects.create(
            user=self.representative_user,
            level='区级',
            name='测试代表',
            gender='male',
            nationality='汉族',
            birth_date='1980-01-01',
            birthplace='测试市',
            party='中国共产党',
            current_position='测试职位',
            mobile_phone='13800138000',
            education='本科'
        )
        
        # 创建另一个代表用户
        self.other_representative_user = User.objects.create_user(
            username='other_representative',
            password='testpass123',
            role='representative'
        )
        
        self.other_representative = Representative.objects.create(
            user=self.other_representative_user,
            level='区级',
            name='其他代表',
            gender='female',
            nationality='汉族',
            birth_date='1985-01-01',
            birthplace='测试市',
            party='中国共产党',
            current_position='测试职位',
            mobile_phone='13900139000',
            education='本科'
        )
        
        # 创建工作人员用户
        self.staff_user = User.objects.create_user(
            username='test_staff',
            password='testpass123',
            role='staff'
        )
        
        self.staff_member = StaffMember.objects.create(
            user=self.staff_user,
            name='测试工作人员',
            position='站点主任',
            mobile_phone='13700137000',
            email='<EMAIL>',
            station_name='测试站点'
        )
        
        # 创建测试意见建议
        self.opinion = OpinionSuggestion.objects.create(
            representative=self.representative,
            title='测试权限的意见建议',
            category='urban_construction',
            reporter_name='张三',
            original_content='这是一个测试权限的意见建议内容'
        )
    
    def get_jwt_token(self, user):
        """获取JWT令牌"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def test_representative_can_view_own_opinions(self):
        """测试代表可以查看自己的意见建议"""
        token = self.get_jwt_token(self.representative_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/v1/opinions/suggestions/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 应该能看到自己的意见建议
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(len(data['data']['results']), 1)
        self.assertEqual(data['data']['results'][0]['id'], self.opinion.id)
    
    def test_representative_cannot_view_others_opinions(self):
        """测试代表不能查看其他代表的意见建议"""
        token = self.get_jwt_token(self.other_representative_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/v1/opinions/suggestions/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 不应该能看到其他代表的意见建议
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(len(data['data']['results']), 0)
    
    def test_staff_can_view_all_opinions(self):
        """测试工作人员可以查看所有意见建议"""
        token = self.get_jwt_token(self.staff_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/v1/opinions/suggestions/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 应该能看到所有意见建议
        data = response.json()
        self.assertTrue(data['success'])
        self.assertGreaterEqual(len(data['data']['results']), 1)
    
    def test_representative_can_create_opinion(self):
        """测试代表可以创建意见建议"""
        token = self.get_jwt_token(self.representative_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        data = {
            'title': '新的测试意见建议',
            'category': 'transportation',
            'reporter_name': '李四',
            'original_content': '这是一个新的测试意见建议的详细内容，需要足够的字符数',
            'ai_assisted': False
        }
        
        response = self.client.post('/api/v1/opinions/suggestions/create/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        response_data = response.json()
        self.assertTrue(response_data['success'])
        self.assertEqual(response_data['data']['title'], data['title'])
    
    def test_staff_cannot_create_opinion(self):
        """测试工作人员不能创建意见建议"""
        token = self.get_jwt_token(self.staff_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        data = {
            'title': '工作人员不能创建的意见建议',
            'category': 'transportation',
            'reporter_name': '李四',
            'original_content': '这是一个测试内容',
            'ai_assisted': False
        }
        
        response = self.client.post('/api/v1/opinions/suggestions/create/', data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_representative_can_use_ai(self):
        """测试代表可以使用AI功能"""
        token = self.get_jwt_token(self.representative_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        data = {
            'original_content': '这是一个需要AI优化的原始意见内容，字符数足够',
            'category': 'urban_construction',
            'context': '补充背景信息'
        }
        
        response = self.client.post('/api/v1/opinions/ai/generate/', data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        self.assertTrue(response_data['success'])
        self.assertIn('generated_suggestion', response_data['data'])
    
    def test_staff_cannot_use_ai(self):
        """测试工作人员不能使用AI功能"""
        token = self.get_jwt_token(self.staff_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        data = {
            'original_content': '这是一个测试内容',
            'category': 'urban_construction'
        }
        
        response = self.client.post('/api/v1/opinions/ai/generate/', data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class OpinionAPITest(APITestCase):
    """意见建议API功能测试"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建代表用户
        self.representative_user = User.objects.create_user(
            username='test_representative',
            password='testpass123',
            role='representative'
        )
        
        self.representative = Representative.objects.create(
            user=self.representative_user,
            level='区级',
            name='测试代表',
            gender='male',
            nationality='汉族',
            birth_date='1980-01-01',
            birthplace='测试市',
            party='中国共产党',
            current_position='测试职位',
            mobile_phone='13800138000',
            education='本科'
        )
        
        # 创建工作人员用户
        self.staff_user = User.objects.create_user(
            username='test_staff',
            password='testpass123',
            role='staff'
        )
        
        self.staff_member = StaffMember.objects.create(
            user=self.staff_user,
            name='测试工作人员',
            position='站点主任',
            mobile_phone='13700137000',
            email='<EMAIL>',
            station_name='测试站点'
        )
        
        # 创建测试意见建议
        self.opinion = OpinionSuggestion.objects.create(
            representative=self.representative,
            title='测试API的意见建议',
            category='urban_construction',
            reporter_name='张三',
            original_content='这是一个测试API的意见建议内容'
        )
    
    def get_jwt_token(self, user):
        """获取JWT令牌"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def test_opinion_list_api(self):
        """测试意见建议列表API"""
        token = self.get_jwt_token(self.representative_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/v1/opinions/suggestions/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertTrue(data['success'])
        self.assertIn('data', data)
        self.assertIn('results', data['data'])
        self.assertIn('count', data['data'])
        self.assertIn('total_pages', data['data'])
    
    def test_opinion_detail_api(self):
        """测试意见建议详情API"""
        token = self.get_jwt_token(self.representative_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(f'/api/v1/opinions/suggestions/{self.opinion.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['data']['id'], self.opinion.id)
        self.assertEqual(data['data']['title'], self.opinion.title)
    
    def test_opinion_submit_api(self):
        """测试意见建议提交API"""
        token = self.get_jwt_token(self.representative_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.post(f'/api/v1/opinions/suggestions/{self.opinion.id}/submit/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertTrue(data['success'])
        
        # 验证状态已更新
        self.opinion.refresh_from_db()
        self.assertEqual(self.opinion.current_status, 'submitted')
    
    def test_opinion_review_api(self):
        """测试意见建议审核API"""
        # 先提交意见建议
        OpinionReview.objects.create(
            opinion=self.opinion,
            reviewer=None,
            action='submit',
            status='submitted',
            review_comment='代表提交'
        )
        
        token = self.get_jwt_token(self.staff_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        review_data = {
            'action': 'approve',
            'review_comment': '审核通过，建议合理'
        }
        
        response = self.client.post(
            f'/api/v1/opinions/suggestions/{self.opinion.id}/review/',
            review_data
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        data = response.json()
        self.assertTrue(data['success'])
        
        # 验证审核记录已创建
        review = OpinionReview.objects.filter(
            opinion=self.opinion,
            action='approve'
        ).first()
        self.assertIsNotNone(review)
        self.assertEqual(review.reviewer, self.staff_member)
    
    def test_opinion_statistics_api(self):
        """测试意见建议统计API"""
        token = self.get_jwt_token(self.representative_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/v1/opinions/statistics/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertTrue(data['success'])
        self.assertIn('total_opinions', data['data'])
        self.assertIn('pending_opinions', data['data'])
        self.assertIn('completed_opinions', data['data'])
        self.assertIn('monthly_opinions', data['data'])


class OpinionSecurityTest(APITestCase):
    """意见建议安全测试"""
    
    def setUp(self):
        """设置测试数据"""
        self.representative_user = User.objects.create_user(
            username='test_representative',
            password='testpass123',
            role='representative'
        )
        
        self.representative = Representative.objects.create(
            user=self.representative_user,
            level='区级',
            name='测试代表',
            gender='male',
            nationality='汉族',
            birth_date='1980-01-01',
            birthplace='测试市',
            party='中国共产党',
            current_position='测试职位',
            mobile_phone='13800138000',
            education='本科'
        )
    
    def test_unauthenticated_access_denied(self):
        """测试未认证用户访问被拒绝"""
        response = self.client.get('/api/v1/opinions/suggestions/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_sql_injection_protection(self):
        """测试SQL注入防护"""
        token = RefreshToken.for_user(self.representative_user).access_token
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 尝试SQL注入攻击
        malicious_search = "'; DROP TABLE opinion_suggestions; --"
        response = self.client.get(
            f'/api/v1/opinions/suggestions/?search={malicious_search}'
        )
        
        # 应该正常返回，不会执行恶意SQL
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_xss_protection(self):
        """测试XSS防护"""
        token = RefreshToken.for_user(self.representative_user).access_token
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 尝试XSS攻击
        xss_payload = {
            'title': '<script>alert("XSS")</script>恶意标题',
            'category': 'urban_construction',
            'reporter_name': '<img src=x onerror=alert("XSS")>',
            'original_content': '这是一个包含XSS的内容<script>alert("XSS")</script>',
            'ai_assisted': False
        }
        
        response = self.client.post('/api/v1/opinions/suggestions/create/', xss_payload)
        
        if response.status_code == status.HTTP_201_CREATED:
            # 如果创建成功，验证数据已被清理
            data = response.json()
            self.assertNotIn('<script>', data['data']['title'])
            self.assertNotIn('<img', data['data']['reporter_name'])
    
    def test_data_validation(self):
        """测试数据验证"""
        token = RefreshToken.for_user(self.representative_user).access_token
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 测试各种无效数据
        invalid_data_sets = [
            # 空标题
            {
                'title': '',
                'category': 'urban_construction',
                'reporter_name': '张三',
                'original_content': '有效的内容',
            },
            # 标题过短
            {
                'title': '短',
                'category': 'urban_construction',
                'reporter_name': '张三',
                'original_content': '有效的内容',
            },
            # 内容过短
            {
                'title': '有效的标题',
                'category': 'urban_construction',
                'reporter_name': '张三',
                'original_content': '短',
            },
            # 无效分类
            {
                'title': '有效的标题',
                'category': 'invalid_category',
                'reporter_name': '张三',
                'original_content': '有效的内容',
            },
        ]
        
        for invalid_data in invalid_data_sets:
            response = self.client.post('/api/v1/opinions/suggestions/create/', invalid_data)
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            
            data = response.json()
            self.assertFalse(data['success'])
            self.assertIn('errors', data)
