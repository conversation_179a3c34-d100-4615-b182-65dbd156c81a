"""
大屏展示数据序列化器

用于序列化大屏展示所需的各种数据结构
"""

from rest_framework import serializers


class Chart1DataSerializer(serializers.Serializer):
    """代表层级统计数据序列化器"""
    value = serializers.IntegerField()
    name = serializers.CharField()


class Chart2DataSerializer(serializers.Serializer):
    """代表结构组成数据序列化器"""
    value = serializers.IntegerField()
    name = serializers.CharField()


class Chart4DataSerializer(serializers.Serializer):
    """意见建议数据序列化器"""
    content = serializers.CharField()
    author = serializers.CharField()
    date = serializers.CharField()


class Chart5DistrictSerializer(serializers.Serializer):
    """片区统计数据序列化器"""
    name = serializers.CharField()
    count = serializers.IntegerField()


class Chart6DataSerializer(serializers.Serializer):
    """履职统计数据序列化器"""
    categories = serializers.ListField(child=serializers.CharField())
    values = serializers.ListField(child=serializers.IntegerField())


class Chart7RankingSerializer(serializers.Serializer):
    """代表排名数据序列化器"""
    avatar = serializers.CharField(allow_blank=True)
    name = serializers.CharField()
    total = serializers.IntegerField()


class Chart8DataSerializer(serializers.Serializer):
    """AI知识库访问统计序列化器"""
    month = serializers.CharField()
    value = serializers.IntegerField()


class Chart9DataSerializer(serializers.Serializer):
    """AI知识库内容序列化器"""
    content = serializers.CharField()
    time = serializers.CharField()


class BigScreenDataSerializer(serializers.Serializer):
    """大屏数据总序列化器"""
    chart1 = serializers.DictField()
    chart2 = serializers.DictField()
    chart4 = serializers.DictField()
    chart5 = serializers.DictField()
    chart6 = serializers.DictField()
    chart7 = serializers.DictField()
    chart8 = serializers.DictField()
    chart9 = serializers.DictField()
