<template>
  <div class="qr-code-container">
    <!-- 二维码标题 -->
    <!-- <div class="qr-title">
      <div class="qr-icon">📱</div>
      <span>扫码访问</span>
    </div> -->
    
    <!-- 二维码内容区域 -->
    <div class="qr-content">
      <!-- 二维码画布 -->
      <canvas 
        ref="qrCanvas" 
        class="qr-canvas"
        :width="qrSize" 
        :height="qrSize"
      ></canvas>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="qr-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">生成中...</div>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="error" class="qr-error">
        <div class="error-icon">⚠️</div>
        <div class="error-text">生成失败</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import QRCode from 'qrcode'

// Props定义
const props = defineProps({
  // 二维码链接
  url: {
    type: String,
    default: 'https://bing.com'
  },
  // 二维码尺寸
  size: {
    type: Number,
    default: 120
  },
  // 二维码颜色配置
  options: {
    type: Object,
    default: () => ({
      color: {
        dark: '#02a6b5',  // 蓝色科技
        light: '#FFFFFF'  // 白色背景
      },
      margin: 2,
      width: 120,
      errorCorrectionLevel: 'M'
    })
  }
})

// 响应式数据
const qrCanvas = ref(null)
const loading = ref(false)
const error = ref(false)
const qrSize = ref(props.size)

// 计算属性 - 显示的URL（截断过长的URL）
const displayUrl = ref('')

// 生成二维码
const generateQRCode = async () => {
  if (!qrCanvas.value || !props.url) {
    console.warn('⚠️ 二维码生成条件不满足')
    return
  }

  loading.value = true
  error.value = false

  try {
    console.log('🔄 开始生成二维码:', props.url)
    
    // 配置二维码选项
    const qrOptions = {
      ...props.options,
      width: qrSize.value,
      margin: 2,
      color: {
        dark: '#49bcf7',  // 亮蓝色
        light: '#001428'  // 深蓝色背景
      },
      errorCorrectionLevel: 'M'
    }

    // 生成二维码到canvas
    await QRCode.toCanvas(qrCanvas.value, props.url, qrOptions)
    
    // 更新显示URL
    displayUrl.value = props.url.length > 30 
      ? props.url.substring(0, 30) + '...' 
      : props.url

    console.log('✅ 二维码生成成功')
    loading.value = false
  } catch (err) {
    console.error('❌ 二维码生成失败:', err)
    error.value = true
    loading.value = false
  }
}

// 监听URL变化重新生成二维码
watch(() => props.url, () => {
  if (props.url) {
    generateQRCode()
  }
}, { immediate: false })

// 监听尺寸变化
watch(() => props.size, (newSize) => {
  qrSize.value = newSize
  nextTick(() => {
    generateQRCode()
  })
})

// 组件挂载后生成二维码
onMounted(async () => {
  await nextTick()
  if (qrCanvas.value) {
    generateQRCode()
  }
})

// 暴露方法供父组件调用
defineExpose({
  regenerate: generateQRCode
})
</script>

<style scoped>
/* 二维码容器 */
.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* padding: 16px; */
  transition: all 0.3s ease;
 
}

.qr-code-container:hover {
  background: rgba(0, 20, 40, 0.2);
  border-color: rgba(73, 188, 247, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(73, 188, 247, 0.2);
}

/* 二维码标题 */
.qr-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  position: relative;
  padding-bottom: 8px;
}

/* 标题分割线 */
.qr-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -20px;
  right: -20px;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(73, 188, 247, 0.3) 20%,
    rgba(73, 188, 247, 0.8) 50%,
    rgba(73, 188, 247, 0.3) 80%,
    transparent 100%
  );
  border-radius: 1px;
  box-shadow: 0 0 6px rgba(73, 188, 247, 0.3);
}

.qr-icon {
  font-size: 1.1rem;
  filter: drop-shadow(0 0 4px rgba(73, 188, 247, 0.6));
}

/* 二维码内容区域 */
.qr-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

/* 二维码画布 */
.qr-canvas {
  border-radius: 8px;
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 0 0 2px #49bcf7;;        /* 默认状态下的边框 */
  transition: all 0.3s ease;
  background: #001428;
}

.qr-canvas:hover {
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.4),
    0 0 0 3px rgba(73, 188, 247, 0.5);
  transform: scale(1.02);
}

/* 加载状态 */
.qr-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 20, 40, 0.95);
  border-radius: 8px;
  z-index: 10;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(73, 188, 247, 0.2);
  border-top: 2px solid rgba(73, 188, 247, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

.loading-text {
  font-size: 0.75rem;
  color: #49bcf7;
}

/* 错误状态 */
.qr-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 20, 40, 0.95);
  border-radius: 8px;
  z-index: 10;
}

.error-icon {
  font-size: 1.5rem;
  margin-bottom: 4px;
  filter: drop-shadow(0 0 4px rgba(2, 166, 181, 0.6));
}

.error-text {
  font-size: 0.75rem;
  color: #02a6b5;
}

/* 二维码描述 */
.qr-description {
  text-align: center;
}

.qr-url {
  font-size: 0.7rem;
  color: #49bcf7;
  font-weight: 500;
  margin-bottom: 4px;
  word-break: break-all;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.qr-hint {
  font-size: 0.65rem;
  color: rgba(73, 188, 247, 0.8);
  font-style: italic;
}

/* 旋转动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .qr-code-container {
    padding: 12px;
    margin-top: 12px;
  }
  
  .qr-title {
    font-size: 0.85rem;
    margin-bottom: 10px;
  }
  
  .qr-canvas {
    width: 100px !important;
    height: 100px !important;
  }
  
  .qr-url {
    font-size: 0.65rem;
  }
  
  .qr-hint {
    font-size: 0.6rem;
  }
}

@media (max-width: 768px) {
  .qr-code-container {
    padding: 10px;
    margin-top: 10px;
  }
  
  .qr-title {
    font-size: 0.8rem;
    margin-bottom: 8px;
  }
  
  .qr-canvas {
    width: 80px !important;
    height: 80px !important;
  }
  
  .qr-url {
    font-size: 0.6rem;
  }
  
  .qr-hint {
    font-size: 0.55rem;
  }
}
</style> 