<template>
  <div class="dashboard-layout">
    <!-- 头部 -->
    <el-header class="layout-header">
      <div class="header-left">
        <el-icon size="24" style="color: var(--china-red); margin-right: 10px;">
          <House />
        </el-icon>
        <span class="system-title">人大代表履职服务与管理平台</span>
      </div>
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-avatar
              :size="32"
              :src="userAvatar"
              style="background-color: var(--china-red);"
            >
              {{ userStore.userName.charAt(0) }}
            </el-avatar>
            <span class="user-name">{{ userStore.userName }}</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="logout" divided>
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主体内容 -->
    <el-main class="layout-main">
      <div class="dashboard-container">
        <!-- 欢迎卡片 -->
        <div class="welcome-card">
          <h2>欢迎使用人大代表履职服务与管理平台</h2>
          <p>{{ currentDate }}</p>
          <div class="role-info">
            <div class="info-item">
              <span class="label">当前用户：</span>
              <span class="value">{{ userStore.userName }}</span>
            </div>
            <div class="info-item">
              <span class="label">用户角色：</span>
              <span class="value">{{ userStore.roleText || '访客' }}</span>
            </div>
            <div class="info-item">
              <span class="label">登录时间：</span>
              <span class="value">{{ loginTime }}</span>
            </div>
          </div>
        </div>

        <!-- 角色导航 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-card class="nav-card" @click="goToRepresentative">
              <div class="nav-content">
                <el-icon size="48" style="color: var(--china-red);">
                  <User />
                </el-icon>
                <h3>人大代表工作台</h3>
                <p>履职记录、意见建议处理等</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="nav-card" @click="goToStaff">
              <div class="nav-content">
                <el-icon size="48" style="color: var(--china-red);">
                  <OfficeBuilding />
                </el-icon>
                <h3>站点工作人员工作台</h3>
                <p>意见审核、意见管理等</p>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 系统说明 -->
        <el-card style="margin-top: 20px;">
          <template #header>
            <span class="card-title">系统介绍</span>
          </template>
          <div class="system-intro">
            <p>人大代表履职服务与管理平台是为提升人大代表履职效率而构建的综合性服务平台。</p>
            <br>
            <h4>主要功能模块：</h4>
            <ul>
              <li><strong>代表履职记录：</strong>记录和管理代表的各项履职活动</li>
                              <li><strong>意见建议处理：</strong>收集、处理群众意见建议，支持AI辅助决策</li>
              <li><strong>意见审核管理：</strong>工作人员审核转交意见，跟踪处理进度</li>
              <li><strong>数据统计分析：</strong>提供各类履职数据的统计和分析</li>
            </ul>
            <br>
            <h4>系统特色：</h4>
            <ul>
              <li><strong>基于角色的权限管理：</strong>不同角色用户看到不同的功能界面</li>
              <li><strong>AI智能建议：</strong>集成AI系统为意见处理提供智能建议</li>
              <li><strong>完整的工作流程：</strong>从意见收集到处理反馈的全流程管理</li>
              <li><strong>现代化界面设计：</strong>采用中国红主题，界面简洁美观</li>
            </ul>
          </div>
        </el-card>

        <!-- 联系方式 -->
        <el-card style="margin-top: 20px;">
          <template #header>
            <span class="card-title">技术支持</span>
          </template>
          <div class="contact-info">
            <p>如遇到系统使用问题，请联系技术支持：</p>
            <p>📞 技术支持热线：400-123-4567</p>
            <p>📧 邮箱支持：<EMAIL></p>
            <p>🕒 服务时间：周一至周五 8:30-17:30</p>
          </div>
        </el-card>
      </div>
    </el-main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// 用户头像计算属性
const userAvatar = computed(() => {
  const repInfo = userStore.userInfo.representative_info
  return repInfo?.avatar || null
})

// 当前日期
const currentDate = computed(() => {
  const now = new Date()
  return now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

// 登录时间
const loginTime = ref('')

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'logout':
      handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 等待登出完成后再跳转
    await userStore.logout()
    router.push('/login')
  } catch {
    // 用户取消
  }
}

// 跳转到人大代表工作台
const goToRepresentative = () => {
  if (userStore.userInfo.role === 'representative') {
    router.push('/representative')
  } else {
    ElMessage.warning('您当前不是人大代表角色，无法访问该工作台')
  }
}

// 跳转到站点工作人员工作台
const goToStaff = () => {
  if (userStore.userInfo.role === 'staff') {
    router.push('/staff')
  } else {
    ElMessage.warning('您当前不是站点工作人员角色，无法访问该工作台')
  }
}

// 组件挂载时的初始化
onMounted(() => {
  loginTime.value = new Date().toLocaleString('zh-CN')
})
</script>

<style scoped>
.dashboard-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: white;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
}

.system-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--china-red);
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: var(--bg-color);
}

.user-name {
  font-weight: 500;
  color: var(--text-color);
}

.layout-main {
  background: var(--bg-color);
  padding: 0;
  overflow-y: auto;
  flex: 1;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: rgba(255, 255, 255, 0.8);
  margin-right: 8px;
  min-width: 80px;
}

.value {
  color: white;
  font-weight: 500;
}

.nav-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 200px;
}

.nav-card:hover {
  box-shadow: 0 4px 20px rgba(200, 16, 46, 0.3);
  transform: translateY(-2px);
}

.nav-content {
  text-align: center;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.nav-content h3 {
  color: var(--china-red);
  margin: 15px 0 10px 0;
  font-size: 18px;
}

.nav-content p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.card-title {
  font-weight: bold;
  color: var(--china-red);
  font-size: 16px;
}

.system-intro,
.contact-info {
  line-height: 1.8;
  color: var(--text-color);
}

.system-intro h4 {
  color: var(--china-red);
  margin: 10px 0;
}

.system-intro ul {
  margin: 10px 0;
  padding-left: 20px;
}

.system-intro li {
  margin-bottom: 5px;
}

.contact-info p {
  margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }
  
  .system-title {
    display: none;
  }
  
  .nav-card {
    height: 160px;
  }
  
  .nav-content {
    padding: 15px;
  }
  
  .nav-content h3 {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .layout-header {
    padding: 0 10px;
  }
  
  .user-name {
    display: none;
  }
}
</style> 