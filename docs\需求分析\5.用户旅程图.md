# 用户旅程图

## 用户旅程一：人大代表处理意见建议

*   **旅程名称**：人大代表收集、处理及反馈群众意见建议
*   **用户画像**：李明（人大代表）
*   **用户目标**：高效、规范地处理群众意见建议，并及时向群众反馈处理结果，提升履职效能和群众满意度。
*   **核心场景**：从收集群众原始意见到最终办结反馈的全过程。

| 阶段         | 用户行为                                                     | 用户想法/期望                                                                 | 用户情绪     | 接触点 (系统功能/线下活动)                                 | 痛点 (当前或无系统时)                                                                                             | 机会点 (系统如何帮助)                                                                                                                               |
| :----------- | :----------------------------------------------------------- | :---------------------------------------------------------------------------- | :----------- | :--------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------- |
| **0. 工作概览** | 登录系统后，查看工作台概览页面，了解近期履职情况和待办事项。     | "看看今天有什么重要工作要处理。" "最近的履职记录统计怎么样？"                       | 🙂 (开始工作) | 平台-工作台概览页面                                         | 缺乏统一的工作状态概览，需要分别查看各个模块才能了解整体情况。                                                              | 提供直观的dashboard展示，包括履职记录统计、待处理意见数量、近期活动等，帮助代表快速掌握工作状态。                                                                     |
| **1. 收集意见** | 线下走访或接待群众，记录群众反映的原始意见。                   | "这个意见很重要，得记下来。" "他说得比较口语化，我得抓住核心点。"                  | 😊 (积极收集) | 线下笔记、录音                                             | 手写记录可能不清晰、易丢失；口头描述可能重点不突出，后续整理费时。                                                              | 无 (系统外)                                                                                                                                       |
| **2. 录入系统** | 将收集到的意见建议信息录入到平台的"意见建议"模块，包括意见建议标题、分类、反映人、内容等。                 | "用系统记录更规范，方便查找。" "希望录入能快一点，选项清晰。"                       | 🙂 (期望便捷) | 平台-意见建议模块-新增意见建议功能                               | 手动录入文字工作量大；如果系统操作复杂，反而降低效率。                                                                          | 提供简洁的录入界面，支持语音转文字（远期），意见分类标签，减少手动输入。                                                                                    |
| **3. AI辅助**  | 针对已录入的意见建议内容，请求系统AI辅助生成高质量的意见建议。       | "这个内容比较零散，自己整理费神，看看AI能不能生成高质量的意见建议。" "AI生成的建议是否专业规范？" | 🤔 (期待高效) | 平台-意见建议模块-AI辅助生成高质量意见建议功能                             | AI可能无法完全理解复杂语境；AI建议可能过于模板化，不符合实际情况。                                                                | 优化AI算法，使其能生成更加专业、规范的高质量意见建议；提供多种建议模板供选择；允许用户对AI建议进行编辑。                                                              |
| **4. 审核修改** | 查看AI生成的高质量意见建议，结合自己的判断和经验进行修改、完善。         | "AI生成的建议很专业，但这里需要调整一下。" "这条建议的措辞要更严谨。"                       | 😐 (审慎处理) | 平台-意见建议模块-编辑最终意见建议功能                               | 若AI建议与自己想法差距大，修改工作量依然不小。                                                                              | 提供强大的文本编辑功能；AI建议应突出关键点和可修改部分，方便用户快速定位和调整。                                                                          |
| **4.5 法律知识查询 (可选)** | 在整理或审核建议时，如遇到法律政策疑问，使用平台的法律政策互动AI问答功能进行查询。 | "这条建议涉及到某个政策，我不确定具体规定，查一下系统里的知识库看怎么说。" "希望查询方便，答案准确。" | 🤔 (寻求确认) | 平台-法律政策互动AI问答模块                                     | 过去需要自己翻阅大量文件或咨询他人，费时费力，信息可能不准确或不及时。                                                              | 系统提供即时、便捷的法律政策查询，辅助代表更专业地形成建议。                                                                                             |
| **5. 提交站点** | 将确认无误的意见和建议通过平台提交给对应的站点工作人员。         | "我的意见整理好了，提交给工作站，让他们去对接办理部门。" "希望提交后能快点有反馈。"     | 🙂 (完成提交) | 平台-意见建议模块-提交至站点功能                               | 提交后如果石沉大海，不知道工作站是否收到、是否开始处理。                                                                        | 提交成功后有明确提示；系统自动通知站点工作人员；代表可以实时查看意见状态（如：待站点审核）。                                                                      |
| **6. 跟踪进度** | 定期登录平台，查看自己提交意见的办理状态和站点工作人员的反馈。 | "上次那个意见现在怎么样了？" "有没有转交给相关部门？部门怎么说？"                   | 🤔 (关注进展) | 平台-我的意见模块-意见状态查看、处理详情查看                       | 过去依赖电话或当面询问工作站，效率低，信息可能不及时或不准确。                                                                    | 系统实时更新意见状态（待审核、已转交[部门]、处理中、已办结）；站点工作人员录入的每一步处理过程和结果都对代表可见。                                                              |
| **7. 获取结果** | 收到意见已办结的通知，查看站点工作人员录入的最终处理结果和详情。 | "太好了，终于办结了！" "看看具体是怎么处理的，我好跟群众反馈。"                     | 😊 (结果满意) | 平台-我的意见模块-查看办结结果；站内/短信通知（可选）              | 若结果不理想或反馈不清晰，仍需进一步沟通。                                                                                    | 办结结果描述清晰、附件完整；提供便捷的反馈渠道（如果对结果有疑问）。                                                                                          |
| **8. 反馈群众** | 将系统记录的处理结果（经自己语言组织后）反馈给提出意见的群众。   | "要把政府的处理情况准确、及时地告诉大家。" "希望群众对我的工作满意。"               | 😊 (履职完成) | 系统外沟通 (电话、当面)                                      | 仅靠口头反馈，群众可能感受不深；若处理周期长，群众可能已遗忘。                                                                    | 代表可从系统导出规范的处理结果摘要（远期），辅助其向群众反馈；系统记录的完整流程和结果本身就是履职成果的体现。                                                                |

## 用户旅程二：站点工作人员处理人大代表提交的意见建议

*   **旅程名称**：站点工作人员接收、审核、转交并反馈人大代表意见建议
*   **用户画像**：张芳（站点工作人员）
*   **用户目标**：高效、准确地处理人大代表提交的各类意见建议，确保信息流转顺畅，并及时更新办理状态。
*   **核心场景**：从接收代表提交的意见建议到最终标记办结的全过程。

| 阶段           | 用户行为                                                                 | 用户想法/期望                                                                    | 用户情绪     | 接触点 (系统功能)                                         | 痛点 (当前或无系统时)                                                                                                | 机会点 (系统如何帮助)                                                                                                                                   |
| :------------- | :----------------------------------------------------------------------- | :------------------------------------------------------------------------------- | :----------- | :-------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **0. 工作概览**   | 登录系统后，查看工作台概览页面，了解工作计划执行情况和待办事项。                 | "今天有哪些工作计划要处理？" "有多少代表意见需要审核？" "各代表的活动情况怎么样？"     | 🙂 (开始工作) | 平台-工作台概览页面                                        | 缺乏统一的工作状态概览，需要分别查看各个模块才能了解整体工作情况。                                                               | 提供直观的dashboard展示，包括工作计划执行进度、待审核意见数量、代表活动概况等，帮助工作人员快速掌握工作状态。                                                                 |
| **1. 接收意见建议**   | 收到系统通知（或定期查看），有新的代表意见建议提交至本站点等待处理。                   | "又有新的代表意见建议了，得赶紧看看。" "希望代表提交的意见建议质量高一些，AI辅助生成的应该更规范。"       | 🙂 (任务接收) | 平台-意见建议审核模块-待审核列表；站内通知                               | 过去代表可能通过微信、邮件、纸质等多种方式提交，收集整理麻烦，易遗漏。                                                                 | 系统统一接收入口，自动归集待办；代表通过AI辅助生成高质量意见建议，提交的内容更加规范化。                                                                                       |
| **2. 审核意见建议**   | 查看代表提交的意见建议详情（包括原始内容、代表最终的意见建议），进行内容合规性、条理性审核。 | "这个意见建议的核心是什么？材料齐全吗？" "符不符合上报要求？"                               | 🤔 (审慎评估) | 平台-意见建议审核模块-审核意见建议功能                                   | 代表提交的意见建议质量参差不齐，有时需要反复沟通确认才能理解清楚；纸质审核效率低。                                                               | 系统清晰展示原始内容和代表的最终意见建议；审核操作（通过/驳回及理由）线上完成，记录可追溯。                                                                                 |
| **2.5 法律知识核实 (可选)** | 在审核代表提交的意见或准备转交材料时，如对涉及的法律政策有疑问，使用平台的法律政策互动AI问答功能进行查询核实。 | "代表这个建议引用的政策条款对不对？我最好确认一下。" "这个意见属于哪个部门的职责范围，相关的法律依据是什么？" | 🤔 (仔细核查) | 平台-法律政策互动AI问答模块                                     | 审核时遇到不熟悉的法律政策点，需要花费额外时间查证，影响审核效率。                                                              | 系统提供快速的法律政策查询，帮助工作人员提高审核的准确性和专业性。                                                                                       |
| **3. 外部转交** | 对于审核通过的意见，根据内容和性质，手动将意见材料（打印或电子版）转交给对应的政府职能部门。 | "这个意见应该交给交通局。" "希望能顺利交接，部门能重视。"                               | 😐 (执行转交) | 系统外行为 (线下送达、邮件发送等)                                    | 手动转交耗时，且与政府部门的对接系统是割裂的。                                                                                       | 系统内虽然不直接对接政府部门，但提供了清晰的指引（如果可以预设一些常见部门）。                                                                                     |
| **4. 系统标记** | 在平台中找到对应意见建议，将其状态标记为"已转交【具体部门】"，并可备注交接时间、联系人等信息。   | "转交完了，要在系统里记一笔，不然忘了。" "标记清楚，方便后续跟踪。"                             | 🙂 (记录完成) | 平台-意见建议审核模块-标记转交状态功能                               | 如果不及时在台账或系统中更新，后续可能忘记转交给了哪个部门，或者交接的细节。                                                               | 系统提供便捷的状态更新和备注功能，确保每条意见建议的流转都有记录。                                                                                               |
| **5. 跟踪与催办** | 定期与相关职能部门沟通，了解已转交意见建议的办理进度（系统外）。                        | "那个关于噪音扰民的意见建议，城管局处理得怎么样了？" "得催一下，时间有点长了。"                     | 🤔 (关注进展) | 系统外行为 (电话、会议)                                          | 依赖人工电话或会议催办，效率低，且信息同步不及时。                                                                                     | 系统内清晰展示每条意见建议的"已转交"状态和转交日期，方便工作人员识别哪些意见建议需要重点跟进。虽然催办在系统外，但系统提供了管理依据。                                                      |
| **6. 录入结果**   | 收到职能部门对意见建议的正式处理结果反馈后，在平台中找到对应意见建议，录入处理结果详情、附件等。 | "环保局回复了，要把具体内容录进系统。" "附件也要传上去，材料要完整。"                             | 🙂 (信息归档) | 平台-意见建议审核模块-录入办理结果功能                               | 手动整理和录入部门反馈材料工作量大，格式可能不统一；纸质存档不易查阅。                                                                 | 系统提供结构化的录入表单，支持附件上传，确保办理结果信息完整、规范，便于后续查询和代表查阅。                                                                               |
| **7. 标记办结**   | 确认意见建议已妥善处理完毕，所有相关信息已完整录入系统后，将该意见建议的状态标记为"已办结"。     | "这条意见建议终于处理完了，可以关闭了。" "办结后代表就能看到了。"                                   | 😊 (完成闭环) | 平台-意见建议审核模块-标记办结状态功能                               | 如果办结状态不明确，可能会导致重复跟进，或代表无法及时获知最终结果。                                                                   | "已办结"状态清晰明了，代表可及时查看最终结果；办结后的意见建议自动归档，方便统计和历史查阅。                                                                                 |

## 用户旅程三：站点工作人员管理工作计划

*   **旅程名称**：站点工作人员制定、管理和执行工作计划
*   **用户画像**：张芳（站点工作人员）
*   **用户目标**：系统化管理年度、季度、月度工作计划，确保重要工作按时完成，提升工作管理效率。
*   **核心场景**：从制定工作计划到收到提醒并执行工作的全过程。

| 阶段           | 用户行为                                                                 | 用户想法/期望                                                                    | 用户情绪     | 接触点 (系统功能)                                         | 痛点 (当前或无系统时)                                                                                                | 机会点 (系统如何帮助)                                                                                                                                   |
| :------------- | :----------------------------------------------------------------------- | :------------------------------------------------------------------------------- | :----------- | :-------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **1. 制定计划**   | 年度开始时，在系统中录入本年度的工作计划，包括季度和月度安排。                     | "新一年开始了，要把工作计划系统地整理一下。" "希望系统操作简单，分类清晰。"             | 🙂 (计划工作) | 平台-工作计划管理模块-新增计划功能                               | 过去用纸质记录或Word文档，容易遗忘或丢失；没有统一的管理和提醒机制。                                                               | 系统提供结构化的计划录入，支持年度/季度/月度分类，可设置开始结束时间和提醒时间。                                                                                       |
| **2. 设置提醒**   | 为重要的工作计划设置提醒时间，确保在计划开始前或结束前收到通知。                     | "这个工作很重要，要设置提醒，免得忘了。" "提醒时间要合理，不能太早也不能太晚。"           | 🤔 (谨慎设置) | 平台-工作计划管理模块-设置提醒功能                               | 依赖个人记忆或简单的日历提醒，容易因为其他事务干扰而遗忘重要工作。                                                                 | 系统自动在设定时间发送站内通知（可选短信），提醒内容明确具体。                                                                                               |
| **3. 日常查看**   | 定期查看工作台概览，了解当前工作计划的执行进度和即将到来的重要节点。                 | "看看这个月还有什么重要工作要做。" "哪些计划快到期了？"                                 | 🙂 (掌控工作) | 平台-工作台概览页面、工作计划管理模块                               | 缺乏直观的进度展示，需要手动查看各个计划文档才能了解整体情况。                                                                       | 工作台概览提供计划执行进度统计和即将到期的计划提醒，方便快速掌握工作状态。                                                                                         |
| **4. 收到提醒**   | 在设定的提醒时间，收到系统发送的站内通知或短信提醒。                               | "系统提醒很及时，不会漏掉重要工作了。" "提醒内容要具体，让我知道该做什么。"               | 😊 (感谢提醒) | 平台-站内通知；短信通知（可选）                                     | 过去容易因为工作繁忙而遗忘重要时间节点，影响工作进度。                                                                             | 定时任务自动触发提醒，确保重要工作不被遗漏。                                                                                                               |
| **5. 执行调整**   | 根据实际情况，对工作计划进行调整，如修改时间、内容或状态。                         | "这个计划需要调整一下时间。" "这项工作已经完成了，要标记一下。"                         | 😐 (灵活调整) | 平台-工作计划管理模块-编辑计划功能                               | 纸质或文档形式的计划修改不便，版本管理混乱。                                                                                     | 系统支持在线编辑，修改记录可追溯，确保计划的动态管理。                                                                                                     |
| **6. 分析总结**   | 定期查看AI生成的站点工作分析，了解工作计划执行情况和改进建议。                     | "看看AI分析的结果，我们的工作计划执行得怎么样？" "有什么需要改进的地方？"               | 🤔 (反思改进) | 平台-站点工作分析模块-站点工作总结                                 | 过去缺乏系统性的工作分析，主要靠个人经验总结，可能不够客观全面。                                                                   | AI分析提供客观的数据支持和改进建议，帮助优化未来的工作计划制定和执行。                                                                                         |

## 名词术语解释 (本旅程图相关)

*   **平台**：指本项目构建的"人大代表履职服务与管理平台"。
*   **AI辅助生成高质量意见建议**：指平台通过调用外部AI接口，针对群众原始意见内容生成的高质量、规范化的意见建议。
*   **站点工作人员**：指人大街道工作站等基层单位负责辅助代表工作、流转信息、审核群众意见等事务的工作人员。
*   **职能部门**：指负责具体处理群众意见的政府相关业务部门，如交通局、环保局等。本系统不直接对接，由站点工作人员手动转交并录入反馈。
*   **工作台概览**：类似dashboard的页面，为代表和站点工作人员提供工作概况展示。
*   **工作计划管理**：站点工作人员录入和管理年度、季度、月度工作计划，并设置提醒时间的功能。
*   **站点工作分析**：由AI分析生成的站点年度工作分析结果，包括工作亮点、问题分析、改进建议等。 