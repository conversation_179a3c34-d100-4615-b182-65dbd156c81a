# Generated by Django 5.2.3 on 2025-06-23 10:40

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='OpinionSuggestion',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='意见建议ID')),
                ('title', models.CharField(help_text='意见建议的标题，最多200个字符', max_length=200, verbose_name='意见建议标题')),
                ('category', models.CharField(choices=[('urban_construction', '城建环保'), ('transportation', '交通出行'), ('education', '教育文化'), ('healthcare', '医疗卫生'), ('social_security', '社会保障'), ('economic', '经济发展'), ('government_service', '政务服务'), ('other', '其他')], help_text='意见建议的分类类型', max_length=50, verbose_name='意见建议分类')),
                ('reporter_name', models.CharField(help_text='提出意见的群众姓名', max_length=100, verbose_name='反映人姓名')),
                ('original_content', models.TextField(help_text='群众反映的原始意见内容', verbose_name='原始意见内容')),
                ('final_suggestion', models.TextField(blank=True, help_text='代表最终确认的建议内容', null=True, verbose_name='最终建议内容')),
                ('ai_assisted', models.BooleanField(default=False, help_text='标识是否使用了AI辅助生成功能', verbose_name='是否使用AI辅助')),
                ('ai_generated_content', models.TextField(blank=True, help_text='AI辅助生成的建议内容', null=True, verbose_name='AI生成的建议内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('representative', models.ForeignKey(help_text='提交此意见建议的人大代表', on_delete=django.db.models.deletion.CASCADE, to='users.representative', verbose_name='提交代表')),
            ],
            options={
                'verbose_name': '意见建议',
                'verbose_name_plural': '意见建议',
                'db_table': 'opinion_suggestions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OpinionReview',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='审核记录ID')),
                ('action', models.CharField(choices=[('create', '创建草稿'), ('submit', '代表提交'), ('approve', '审核通过'), ('reject', '审核驳回'), ('transfer', '标记转交'), ('update_progress', '更新进展'), ('close', '标记办结')], help_text='执行的具体操作类型', max_length=50, verbose_name='操作动作')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('submitted', '已提交'), ('under_review', '待审核'), ('approved', '审核通过'), ('rejected', '审核驳回'), ('transferred', '已转交'), ('in_progress', '处理中'), ('completed', '已办结')], help_text='执行操作后的状态', max_length=50, verbose_name='操作后状态')),
                ('transferred_department', models.CharField(blank=True, help_text='转交的政府职能部门名称', max_length=100, null=True, verbose_name='转交部门名称')),
                ('review_comment', models.TextField(blank=True, help_text='审核备注或驳回理由', null=True, verbose_name='审核备注')),
                ('processing_result', models.TextField(blank=True, help_text='部门处理结果的详细描述', null=True, verbose_name='处理结果描述')),
                ('attachments', models.TextField(blank=True, help_text='相关附件的文件路径，JSON格式存储', null=True, verbose_name='附件路径')),
                ('action_time', models.DateTimeField(default=django.utils.timezone.now, help_text='执行此操作的时间', verbose_name='操作时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('reviewer', models.ForeignKey(blank=True, help_text='执行此操作的工作人员，为空表示系统操作', null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.staffmember', verbose_name='审核人')),
                ('opinion', models.ForeignKey(help_text='此审核记录对应的意见建议', on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='opinion.opinionsuggestion', verbose_name='关联意见建议')),
            ],
            options={
                'verbose_name': '意见审核记录',
                'verbose_name_plural': '意见审核记录',
                'db_table': 'opinion_reviews',
                'ordering': ['-action_time'],
            },
        ),
        migrations.AddIndex(
            model_name='opinionsuggestion',
            index=models.Index(fields=['representative'], name='opinion_sug_represe_668c0a_idx'),
        ),
        migrations.AddIndex(
            model_name='opinionsuggestion',
            index=models.Index(fields=['category'], name='opinion_sug_categor_ac3e84_idx'),
        ),
        migrations.AddIndex(
            model_name='opinionsuggestion',
            index=models.Index(fields=['created_at'], name='opinion_sug_created_a81a87_idx'),
        ),
        migrations.AddIndex(
            model_name='opinionsuggestion',
            index=models.Index(fields=['ai_assisted'], name='opinion_sug_ai_assi_2e1a16_idx'),
        ),
        migrations.AddIndex(
            model_name='opinionreview',
            index=models.Index(fields=['opinion'], name='opinion_rev_opinion_c4eb7c_idx'),
        ),
        migrations.AddIndex(
            model_name='opinionreview',
            index=models.Index(fields=['reviewer'], name='opinion_rev_reviewe_acc005_idx'),
        ),
        migrations.AddIndex(
            model_name='opinionreview',
            index=models.Index(fields=['status'], name='opinion_rev_status_91599b_idx'),
        ),
        migrations.AddIndex(
            model_name='opinionreview',
            index=models.Index(fields=['action_time'], name='opinion_rev_action__03ff01_idx'),
        ),
        migrations.AddIndex(
            model_name='opinionreview',
            index=models.Index(fields=['opinion', 'action_time'], name='opinion_rev_opinion_1b960f_idx'),
        ),
    ]
