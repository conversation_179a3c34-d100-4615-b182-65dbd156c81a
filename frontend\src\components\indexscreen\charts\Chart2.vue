<template>
  <div ref="myChart" style="width: 100%; height: 90%"></div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    this.initChart()
  },
  watch: {
    data: {
      handler() {
        this.initChart()
      },
      deep: true
    }
  },
  methods: {
    async initChart() {
      const myChart = this.$echarts.init(this.$refs.myChart)
      const option = {
        legend: {
          orient: 'vertical',
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 6,
          textStyle: {
            color: '#ffffff',
            fontSize: 15,
          },
          top: '0%',
          right: '5%',
          width: '35%',
          data: this.data.map((item) => item.name),
          formatter: function(name) {
            // 限制图例文字长度，避免过长
            return name.length > 11 ? name.substring(0, 11) + '...' : name;
          }
        },
        color: [
          '#37a2da',
          '#32c5e9',
          '#9fe6b8',
          '#ffdb5c',
          '#ff9f7f',
          '#fb7293',
          '#e7bcf3',
          '#8378ea',
          '#67c23a',
          '#e6a23c',
          '#f56c6c',
          '#909399',
          '#c71585',
          '#ff6347',
          '#4682b4'
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}人 ({d}%)',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 15
          }
        },

        calculable: true,
        series: [
          {
            type: 'pie',
            radius: ['25%','80%'],
            center: ['30%', '50%'],
            data: this.data,
            label: {
              show: true,
              position: 'outside',
              formatter: function(params) {
                // // 只显示百分比，避免文字过长
                // if (params.percent < 3) {
                //   // 小于3%的不显示标签，避免重叠
                //   return '';
                // }
                return params.percent + '%';
              },
              fontSize: 11,
              color: '#ffffff',
              distanceToLabelLine: 8,
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 25,
              lineStyle: {
                width: 1,
                color: 'rgba(255, 255, 255, 0.8)'
              },
              smooth: true
            },
            itemStyle: {
              normal: {
                shadowBlur: 20,
                shadowColor: 'rgba(0, 0, 0, 0.3)',
              },
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 30,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      }
      await myChart.setOption(option)
    },
  },
}
</script>

<style scoped></style>
