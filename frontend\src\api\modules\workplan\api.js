/**
 * 工作计划管理API模块
 * 对接后端Django工作计划接口
 */

import httpClient from '../../http/client'

const api = httpClient

/**
 * 获取工作计划列表
 * @param {Object} params - 查询参数
 * @param {string} params.keyword - 关键词搜索
 * @param {string} params.status - 状态筛选
 * @param {string} params.start_date - 开始日期
 * @param {string} params.end_date - 结束日期
 * @param {number} params.page - 页码
 * @param {number} params.page_size - 页大小
 * @param {string} params.order_by - 排序字段
 * @returns {Promise} 返回工作计划列表
 */
export const getWorkPlans = async (params = {}) => {
  const response = await api.get('/workplan/', { params })
  return response.data
}

/**
 * 获取工作计划详情
 * @param {number} planId - 计划ID
 * @returns {Promise} 返回工作计划详情
 */
export const getWorkPlan = async (planId) => {
  const response = await api.get(`/workplan/${planId}/`)
  return response.data
}

/**
 * 创建工作计划
 * @param {Object} planData - 计划数据
 * @param {string} planData.title - 计划标题
 * @param {string} planData.start_date - 开始时间 (YYYY-MM-DD)
 * @param {string} planData.end_date - 结束时间 (YYYY-MM-DD)
 * @param {string} planData.content - 计划内容
 * @param {string} planData.status - 计划状态
 * @param {number} planData.reminder_days - 提醒天数
 * @returns {Promise} 返回创建结果
 */
export const createWorkPlan = async (planData) => {
  const response = await api.post('/workplan/', planData)
  return response.data
}

/**
 * 更新工作计划
 * @param {number} planId - 计划ID
 * @param {Object} planData - 更新数据
 * @returns {Promise} 返回更新结果
 */
export const updateWorkPlan = async (planId, planData) => {
  const response = await api.put(`/workplan/${planId}/`, planData)
  return response.data
}

/**
 * 删除工作计划
 * @param {number} planId - 计划ID
 * @returns {Promise} 返回删除结果
 */
export const deleteWorkPlan = async (planId) => {
  const response = await api.delete(`/workplan/${planId}/`)
  return response.data
}

/**
 * 获取工作计划统计数据
 * @returns {Promise} 返回统计数据
 */
export const getWorkPlanStatistics = async () => {
  const response = await api.get('/workplan/statistics/')
  return response.data
}

/**
 * 获取工作计划提醒数据
 * @returns {Promise} 返回提醒数据
 */
export const getWorkPlanReminders = async () => {
  const response = await api.get('/workplan/reminders/')
  return response.data
}

// 导出工作计划API对象
export const workPlanAPI = {
  getWorkPlans,
  getWorkPlan,
  createWorkPlan,
  updateWorkPlan,
  deleteWorkPlan,
  getWorkPlanStatistics,
  getWorkPlanReminders
}