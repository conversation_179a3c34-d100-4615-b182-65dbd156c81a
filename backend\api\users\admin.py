"""
用户管理应用的Django管理界面配置

虽然系统不使用Django自带的admin，但为了开发和调试方便，
提供基本的管理界面配置
"""

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _

from .models import User, Representative, StaffMember


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """用户管理界面"""
    
    # 列表页显示的字段
    list_display = ('username', 'role', 'is_active', 'last_login_at', 'created_at')
    list_filter = ('role', 'is_active', 'created_at')
    search_fields = ('username',)
    ordering = ('-created_at',)
    
    # 详情页字段组织
    fieldsets = (
        (None, {
            'fields': ('username', 'password')
        }),
        (_('个人信息'), {
            'fields': ('role',)
        }),
        (_('权限'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        (_('重要日期'), {
            'fields': ('last_login', 'created_at', 'updated_at')
        }),
    )
    
    # 添加用户时的字段
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'password1', 'password2', 'role', 'is_active'),
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at', 'last_login')


@admin.register(Representative)
class RepresentativeAdmin(admin.ModelAdmin):
    """人大代表管理界面"""
    
    list_display = ('name', 'level', 'composition', 'gender', 'party', 'mobile_phone', 'created_at')
    list_filter = ('level', 'composition', 'gender', 'party', 'created_at')
    search_fields = ('name', 'mobile_phone')
    ordering = ('name',)
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('user', 'name', 'level', 'composition', 'gender', 'nationality')
        }),
        (_('个人详情'), {
            'fields': ('birth_date', 'birthplace', 'party', 'current_position')
        }),
        (_('联系方式'), {
            'fields': ('mobile_phone',)
        }),
        (_('教育背景'), {
            'fields': ('education', 'graduated_school', 'major')
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related('user')


@admin.register(StaffMember)
class StaffMemberAdmin(admin.ModelAdmin):
    """站点工作人员管理界面"""
    
    list_display = ('name', 'position', 'station_name', 'mobile_phone', 'email', 'created_at')
    list_filter = ('station_name', 'position', 'created_at')
    search_fields = ('name', 'mobile_phone', 'email')
    ordering = ('name',)
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('user', 'name', 'position', 'station_name')
        }),
        (_('联系方式'), {
            'fields': ('mobile_phone', 'email')
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related('user')


# 自定义管理界面标题
admin.site.site_header = '人大代表履职服务与管理平台'
admin.site.site_title = '管理后台'
admin.site.index_title = '欢迎使用管理后台' 