# Generated by Django 5.2.3 on 2025-07-13 17:20

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='RepresentativeAISummary',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False, verbose_name='AI总结ID')),
                ('analysis_year', models.PositiveIntegerField(help_text='生成AI总结的年份，范围：2020-2050', validators=[django.core.validators.MinValueValidator(2020), django.core.validators.MaxValueValidator(2050)], verbose_name='分析年份')),
                ('source_data_summary', models.JSONField(default=dict, help_text='记录用于AI分析的数据来源统计信息，JSON格式', verbose_name='数据来源概要')),
                ('ai_result', models.JSONField(default=dict, help_text='AI生成的分析结果，JSON格式，匹配前端需要的数据结构', verbose_name='AI分析结果')),
                ('status', models.CharField(choices=[('pending', '待生成'), ('generating', '生成中'), ('completed', '已完成'), ('failed', '生成失败')], default='pending', help_text='AI总结的生成状态', max_length=20, verbose_name='生成状态')),
                ('generation_duration', models.PositiveIntegerField(blank=True, help_text='AI生成总结的耗时，单位：秒', null=True, verbose_name='生成耗时')),
                ('error_message', models.TextField(blank=True, help_text='生成失败时的错误信息', null=True, verbose_name='错误信息')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('completed_at', models.DateTimeField(blank=True, help_text='AI总结生成完成的时间', null=True, verbose_name='完成时间')),
                ('representative', models.ForeignKey(help_text='生成AI总结的人大代表', on_delete=django.db.models.deletion.CASCADE, to='users.representative', verbose_name='关联代表')),
            ],
            options={
                'verbose_name': '代表AI总结',
                'verbose_name_plural': '代表AI总结',
                'db_table': 'representative_ai_summaries',
                'ordering': ['-analysis_year', '-created_at'],
                'indexes': [models.Index(fields=['representative', 'analysis_year'], name='representat_represe_40e789_idx'), models.Index(fields=['status'], name='representat_status_509186_idx'), models.Index(fields=['created_at'], name='representat_created_c12cc9_idx')],
                'unique_together': {('representative', 'analysis_year')},
            },
        ),
    ]
