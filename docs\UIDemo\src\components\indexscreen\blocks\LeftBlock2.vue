<template>
  <div class="left-block-2">
    <!-- 标题区域 -->
    <div class="chart-header">
      <div class="header-left">
        <!-- <div class="chart-icon">🥧</div> -->
        <div class="chart-title">{{ chartTitle }}</div>
      </div>
      <!-- <div class="chart-subtitle">{{ chartSubtitle }}</div> -->
    </div>
    
    <!-- 图表容器 -->
    <div class="chart-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">数据加载中...</div>
      </div>
      
      <!-- 图表内容 -->
      <div v-else class="chart-wrapper">
        <!-- 饼图 -->
        <div ref="chartRef" class="pie-chart"></div>
        
        <!-- 图例列表 -->
        <div class="legend-list">
          <div 
            v-for="(item, index) in chartData" 
            :key="index"
            class="legend-item"
            @click="handleLegendClick(item)"
          >
            <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
            <div class="legend-info">
              <div class="legend-name">{{ item.name }}</div>
              <div class="legend-value">{{ item.value }}人</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import { getRepresentativeComposition } from '@/api/indexscreen/representativeComposition.js'

// 响应式数据
const chartTitle = ref('代表组成')
const chartRef = ref(null)
const loading = ref(false)
const chartData = ref([])
const totalCount = ref(0)
let chartInstance = null


// 获取图表数据
const fetchChartData = async () => {
  try {
    loading.value = true
    const response = await getRepresentativeComposition()
    
    if (response.code === 200) {
      chartData.value = response.data
      totalCount.value = response.totalCount || 0
      console.log('🥧 代表组成统计数据加载成功:', chartData.value)
    } else {
      console.error('❌ 获取数据失败:', response.message)
    }
  } catch (error) {
    console.error('❌ 数据请求异常:', error)
  } finally {
    loading.value = false
  }
}

// 初始化饼图
const initChart = () => {
  if (!chartRef.value || chartData.value.length === 0) return
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'rgba(255, 255, 255, 0.2)',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      formatter: function(params) {
        return `
          <div style="padding: 8px;">
            <div style="color: ${params.color}; font-weight: bold; margin-bottom: 4px;">
              ${params.name}
            </div>
            <div style="margin-bottom: 2px;">
              人数: <span style="color: #00D4FF; font-weight: bold;">${params.value}</span>
            </div>
            <div>
              占比: <span style="color: #00D4FF; font-weight: bold;">${params.percent}%</span>
            </div>
          </div>
        `
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['30%', '60%'], // 环形饼图，稍微调小
        center: ['45%', '50%'], // 向左偏移，为标签留出空间
        data: chartData.value.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color,
            borderColor: 'rgba(255, 255, 255, 0.1)',
            borderWidth: 2,
            shadowColor: item.color,
            shadowBlur: 8,
            shadowOffsetY: 2
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 15,
              shadowOffsetX: 0,
              shadowColor: item.color
            }
          }
        })),
        label: {
          show: true,
          position: 'outside',
          formatter: function(params) {
            return `{name|${params.name}}\n{percent|${params.percent}%}`
          },
          rich: {
            name: {
              fontSize: 11,
              color: '#ffffff',
              fontWeight: 'normal',
              lineHeight: 16
            },
            percent: {
              fontSize: 12,
              color: '#00D4FF',
              fontWeight: 'bold',
              lineHeight: 16
            }
          },
          distanceToLabelLine: 5
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 8,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.6)',
            width: 1
          }
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200
        }
      }
    ]
  }
  
  chartInstance.setOption(option)
  
  // 添加点击事件
  chartInstance.on('click', (params) => {
    console.log('点击了饼图扇形:', params.name, '数值:', params.value)
    handleLegendClick(chartData.value.find(item => item.name === params.name))
  })
}

// 处理图例点击事件
const handleLegendClick = (item) => {
  console.log('点击了图例:', item.name, '详细信息:', item.description)
  // 这里可以添加更多交互逻辑，比如显示详细信息弹窗
}

// 响应式调整
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 组件挂载
onMounted(async () => {
  await nextTick()
  // 先获取数据，再初始化图表
  await fetchChartData()
  initChart()
  window.addEventListener('resize', resizeChart)
})

// 组件卸载
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
/* 主容器 - 使用flex布局 */
.left-block-2 {
  /* background: rgba(60, 24, 24, 0.6);
  backdrop-filter: blur(10px); */
  /* border: 1px solid rgba(2, 166, 181, 0.15); */
  border-radius: 12px;
  padding: 16px;
  color: #ffffff;
  transition: all 0.3s ease;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  min-height: 0;
}

.left-block-2:hover {
  background: rgba(0, 20, 40, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 13, 74, 0.2);
  border-color: rgba(2, 166, 181, 0.3);
}

/* 图表头部 - flex布局 */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  position: relative;
  flex-shrink: 0; /* 防止头部被压缩 */
}

/* 美观的分割线设计 */
.chart-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(73, 188, 247, 0.3) 10%,
    rgba(73, 188, 247, 0.8) 30%,
    rgba(73, 188, 247, 1) 50%,
    rgba(73, 188, 247, 0.8) 70%,
    rgba(73, 188, 247, 0.3) 90%,
    transparent 100%
  );
  border-radius: 2px;
  box-shadow: 
    0 0 8px rgba(73, 188, 247, 0.4),
    0 2px 4px rgba(73, 188, 247, 0.2);
  animation: glow-pulse 3s ease-in-out infinite alternate;
}

/* 添加一个细的上层装饰线 */
.chart-header::before {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  border-radius: 1px;
  animation: shimmer 2s ease-in-out infinite alternate;
}

/* 分割线发光动画 */
@keyframes glow-pulse {
  0% {
    box-shadow: 
      0 0 8px rgba(73, 188, 247, 0.4),
      0 2px 4px rgba(73, 188, 247, 0.2);
  }
  100% {
    box-shadow: 
      0 0 12px rgba(73, 188, 247, 0.6),
      0 2px 6px rgba(73, 188, 247, 0.3);
  }
}

/* 上层装饰线闪烁动画 */
@keyframes shimmer {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-icon {
  font-size: 1.2rem;
  filter: drop-shadow(0 0 4px rgba(73, 188, 247, 0.8));
}

.chart-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.chart-subtitle {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(2, 166, 181, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  border: 1px solid rgba(73, 188, 247, 0.3);
}

/* 图表容器 - flex自适应 */
.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  position: relative;
}

.chart-wrapper {
  flex: 1;
  display: flex;
  gap: 8px;
  min-height: 0;
  width: 95%;
}

/* 饼图区域 */
.pie-chart {
  flex: 1;
  min-height: 120px;
  min-width: 140px;
}

/* 图例列表 */
.legend-list {
  flex: 0 0 100px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding-left: 4px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 3px 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.75rem;
}

.legend-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(2px);
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 2px;
  flex-shrink: 0;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.legend-info {
  flex: 1;
  min-width: 0;
}

.legend-name {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 1px;
}

.legend-value {
  font-size: 0.65rem;
  color: #49bcf7;
  font-weight: bold;
}

/* 加载状态样式 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  min-height: 120px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(2, 166, 181, 0.2);
  border-top: 3px solid rgba(2, 166, 181, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-block-2 {
    padding: 12px;
  }
  
  .chart-wrapper {
    flex-direction: column;
    gap: 6px;
  }
  
  .legend-list {
    flex: 0 0 auto;
    flex-direction: row;
    flex-wrap: wrap;
    padding-left: 0;
    gap: 4px;
    justify-content: center;
  }
  
  .legend-item {
    flex: 0 0 auto;
    font-size: 0.65rem;
    padding: 2px 6px;
  }
  
  .legend-color {
    width: 8px;
    height: 8px;
  }
}

@media (max-width: 768px) {
  .left-block-2 {
    padding: 10px;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    margin-bottom: 8px;
  }
  
  .chart-subtitle {
    align-self: flex-end;
    font-size: 0.65rem;
  }
  
  .pie-chart {
    min-height: 100px;
  }
}

/* 加载动画效果 - 党建红主题 */
.left-block-2::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(2, 166, 181, 0.15),
    transparent
  );
  transition: left 0.5s ease;
}

.left-block-2:hover::after {
  left: 100%;
}

/* 确保图表在容器变化时正确响应 */
.pie-chart > div {
  width: 100% !important;
  height: 100% !important;
}
</style> 