<template>
  <div class="left-block-3">
    <!-- 标题区域 -->
    <div class="list-header">
      <div class="header-left">
        <!-- <div class="list-icon">📝</div> -->
        <div class="list-title">{{ listTitle }}</div>
      </div>
      
    </div>
    
    <!-- 列表容器 -->
    <div class="list-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">数据加载中...</div>
      </div>
      
      <!-- 意见列表 - 轮播容器 -->
      <div v-else class="opinion-carousel">
        <div 
          class="opinion-list"
          :style="{ transform: `translateY(-${currentIndex * itemHeight}px)` }"
        >
          <div 
            v-for="(item, index) in opinionList" 
            :key="item.id"
            class="opinion-item"
            :ref="el => setItemRef(el, index)"
          >
            <!-- 状态指示器 -->
            <div class="status-indicator" :class="getStatusClass(item.status)"></div>
            
            <!-- 意见内容 - 左侧 -->
            <div class="opinion-content">
              <div class="opinion-title">{{ item.opinionTitle }}</div>
            </div>
            
            <!-- 右侧信息 -->
            <div class="opinion-meta">
              <div class="submit-time">{{ formatTime(item.submitTime) }}</div>
              <div class="representative-name">{{ item.representativeName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { getRepresentativeOpinions } from '@/api/indexscreen/representativeOpinions.js'

// 响应式数据
const listTitle = ref('意见建议')
const loading = ref(false)
const opinionList = ref([])
const totalCount = ref(0)
const currentIndex = ref(0)
const itemHeight = ref(50) // 每个项目的高度
const carouselTimer = ref(null)
const itemRefs = ref([])
const containerHeight = ref(0) // 容器高度
const visibleItemCount = ref(0) // 可见项目数量

// 设置项目引用
const setItemRef = (el, index) => {
  if (el) {
    itemRefs.value[index] = el
  }
}

// 计算可见项目数量
const calculateVisibleItems = () => {
  const container = document.querySelector('.left-block-3 .opinion-carousel')
  if (container && itemRefs.value[0]) {
    containerHeight.value = container.offsetHeight
    // 获取实际的项目高度（包括margin和gap）
    const firstItem = itemRefs.value[0]
    const itemRect = firstItem.getBoundingClientRect()
    const computedStyle = window.getComputedStyle(firstItem)
    const marginTop = parseFloat(computedStyle.marginTop) || 0
    const marginBottom = parseFloat(computedStyle.marginBottom) || 0
    
    // 实际项目高度 = 元素高度 + margin + gap（6px）
    itemHeight.value = itemRect.height + marginTop + marginBottom + 6
    visibleItemCount.value = Math.floor(containerHeight.value / itemHeight.value)
    
    console.log('📝 容器高度:', containerHeight.value)
    console.log('📝 项目实际高度:', itemRect.height)
    console.log('📝 项目总高度(含gap):', itemHeight.value)
    console.log('📝 可见项目数:', visibleItemCount.value)
  }
}

// 获取列表数据
const fetchOpinionList = async () => {
  try {
    loading.value = true
    const response = await getRepresentativeOpinions({ 
      page: 1, 
      pageSize: 10 // 增加到10条用于轮播
    })
    
    if (response.code === 200) {
      opinionList.value = response.data.list
      totalCount.value = response.data.pagination.total
      console.log('📝 代表意见列表数据加载成功:', opinionList.value)
      
      // 数据加载完成后启动轮播
      await nextTick()
      // 延迟一点时间确保DOM完全渲染
      setTimeout(() => {
        calculateVisibleItems()
        startCarousel()
      }, 100)
    } else {
      console.error('❌ 获取数据失败:', response.message)
    }
  } catch (error) {
    console.error('❌ 数据请求异常:', error)
  } finally {
    loading.value = false
  }
}

// 启动轮播
const startCarousel = () => {
  // 如果总项目数小于等于可见项目数，不需要轮播
  if (opinionList.value.length <= visibleItemCount.value) {
    console.log('📝 项目数量不足，无需轮播. 总数:', opinionList.value.length, '可见数:', visibleItemCount.value)
    return
  }
  
  carouselTimer.value = setInterval(() => {
    // 逐项滚动，但要确保不会出现空白区域
    const nextIndex = currentIndex.value + 1
    
    // 如果下一个位置会导致底部空白，则重新开始
    // 改进判断条件：当滚动位置 + 可见项目数 >= 总项目数时重新开始
    if (nextIndex + visibleItemCount.value > opinionList.value.length) {
      currentIndex.value = 0
      console.log('📝 重新开始轮播. 下一个索引:', nextIndex, '可见数:', visibleItemCount.value, '总数:', opinionList.value.length)
    } else {
      currentIndex.value = nextIndex
      console.log('📝 轮播到索引:', currentIndex.value)
    }
  }, 3000) // 每3秒切换一次
}

// 停止轮播
const stopCarousel = () => {
  if (carouselTimer.value) {
    clearInterval(carouselTimer.value)
    carouselTimer.value = null
  }
}

// 格式化时间显示
const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now - date
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return '今天'
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return timeStr.split(' ')[0] // 返回日期部分
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    'pending': 'status-pending',
    'processing': 'status-processing', 
    'completed': 'status-completed'
  }
  return statusMap[status] || 'status-pending'
}

// 组件挂载
onMounted(async () => {
  await nextTick()
  await fetchOpinionList()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopCarousel()
})
</script>

<style scoped>
/* 主容器 - 使用flex布局 */
.left-block-3 {
  /* background: rgba(60, 24, 24, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(220, 20, 60, 0.15); */
  border-radius: 12px;
  padding: 16px;
  color: #ffffff;
  transition: all 0.3s ease;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  min-height: 0;
}

.left-block-3:hover {
  background: rgba(0, 20, 40, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 13, 74, 0.2);
  border-color: rgba(2, 166, 181, 0.3);
}

/* 列表头部 - flex布局 */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  position: relative;
  flex-shrink: 0;
}

/* 美观的分割线设计 */
.list-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(73, 188, 247, 0.3) 10%,
    rgba(73, 188, 247, 0.8) 30%,
    rgba(73, 188, 247, 1) 50%,
    rgba(73, 188, 247, 0.8) 70%,
    rgba(73, 188, 247, 0.3) 90%,
    transparent 100%
  );
  border-radius: 2px;
  box-shadow: 
    0 0 8px rgba(73, 188, 247, 0.4),
    0 2px 4px rgba(73, 188, 247, 0.2);
  animation: glow-pulse 3s ease-in-out infinite alternate;
}

/* 添加一个细的上层装饰线 */
.list-header::before {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  border-radius: 1px;
  animation: shimmer 2s ease-in-out infinite alternate;
}

/* 分割线发光动画 */
@keyframes glow-pulse {
  0% {
    box-shadow: 
      0 0 8px rgba(73, 188, 247, 0.4),
      0 2px 4px rgba(73, 188, 247, 0.2);
  }
  100% {
    box-shadow: 
      0 0 12px rgba(73, 188, 247, 0.6),
      0 2px 6px rgba(73, 188, 247, 0.3);
  }
}

/* 上层装饰线闪烁动画 */
@keyframes shimmer {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.list-icon {
  font-size: 1.2rem;
  filter: drop-shadow(0 0 4px rgba(73, 188, 247, 0.8));
}

.list-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.list-subtitle {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(2, 166, 181, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  border: 1px solid rgba(73, 188, 247, 0.3);
}

/* 列表容器 - flex自适应 */
.list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  position: relative;
}

/* 轮播容器 */
.opinion-carousel {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.opinion-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  transition: transform 0.8s ease-in-out;
}

/* 意见项目 - 左右布局 */
.opinion-item {
  display: flex;
  align-items: center;
  gap: 8px;
  /* padding: 6px 8px; */
  border-radius: 6px;
  /* border: 1px solid rgba(220, 20, 60, 0.08); */
  /* background: rgba(60, 24, 24, 0.3); */
  min-height: 24px;
  flex-shrink: 0;
}

/* 状态指示器 */
.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-pending {
  background: #FFA726;
  box-shadow: 0 0 8px rgba(255, 167, 38, 0.6);
}

.status-processing {
  background: #42A5F5;
  box-shadow: 0 0 8px rgba(66, 165, 245, 0.6);
}

.status-completed {
  background: #66BB6A;
  box-shadow: 0 0 8px rgba(102, 187, 106, 0.6);
}

/* 意见内容 - 左侧 */
.opinion-content {
  flex: 1;
  min-width: 0;
}

.opinion-title {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 右侧信息 */
.opinion-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  flex-shrink: 0;
  min-width: 80px;
}

.submit-time {
  font-size: 0.65rem;
  color: rgba(255, 255, 255, 0.6);
}

.representative-name {
  font-size: 0.7rem;
  color: #4FC3F7;
  font-weight: 500;
}

/* 加载状态样式 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  min-height: 120px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(2, 166, 181, 0.2);
  border-top: 3px solid rgba(2, 166, 181, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-block-3 {
    padding: 12px;
  }
  
  .opinion-item {
    padding: 5px 6px;
    gap: 6px;
    min-height: 32px;
  }
  
  .opinion-title {
    font-size: 0.75rem;
  }
  
  .representative-name {
    font-size: 0.65rem;
  }
  
  .submit-time {
    font-size: 0.6rem;
  }
}

@media (max-width: 768px) {
  .left-block-3 {
    padding: 10px;
  }
  
  .list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    margin-bottom: 8px;
  }
  
  .list-subtitle {
    align-self: flex-end;
    font-size: 0.65rem;
  }
  
  .opinion-item {
    padding: 4px 6px;
    gap: 6px;
    min-height: 30px;
  }
  
  .opinion-title {
    font-size: 0.7rem;
  }
  
  .opinion-meta {
    min-width: 70px;
  }
  
  .representative-name {
    font-size: 0.6rem;
  }
  
  .submit-time {
    font-size: 0.55rem;
  }
}

/* 轮播动画效果 - 党建红主题 */
.left-block-3::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(2, 166, 181, 0.15),
    transparent
  );
  transition: left 0.5s ease;
}

.left-block-3:hover::after {
  left: 100%;
}
</style> 