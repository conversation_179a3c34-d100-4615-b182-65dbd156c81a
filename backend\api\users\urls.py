"""
用户管理应用URL配置

定义用户相关的API端点路由
"""

from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView

from . import views

# 应用命名空间
app_name = 'users'

urlpatterns = [
    # 用户认证相关
    path('auth/login/', views.UserLoginView.as_view(), name='login'),
    path('auth/logout/', views.UserLogoutView.as_view(), name='logout'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # 用户个人信息
    path('profile/', views.UserProfileView.as_view(), name='profile'),
    path('password/change/', views.PasswordChangeView.as_view(), name='password_change'),
    
    # 用户管理（工作人员使用）
    path('manage/', views.UserListView.as_view(), name='user_list'),
    path('manage/<int:user_id>/', views.UserDetailView.as_view(), name='user_detail'),

    # 导入导出功能
    path('export/', views.UserExportView.as_view(), name='user_export'),
    path('import/', views.UserImportView.as_view(), name='user_import'),
    path('template/', views.UserTemplateDownloadView.as_view(), name='user_template'),
    
    # 人大代表相关
    path('representatives/', views.RepresentativeListView.as_view(), name='representative_list'),
    
    # 工作人员相关
    path('staff/', views.StaffMemberListView.as_view(), name='staff_list'),
] 