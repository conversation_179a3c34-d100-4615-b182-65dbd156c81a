# 人大代表履职服务与管理平台后端

基于Django 5.2 LTS + Django REST Framework构建的REST API后端服务。

## 功能特性

- **用户认证与管理**：基于JWT的用户认证系统
- **角色权限控制**：支持人大代表和工作人员两种角色
- **履职记录管理**：代表履职活动的完整记录和管理
- **意见建议管理**：群众意见建议的全流程管理和AI辅助
- **文件附件管理**：支持多种文件类型的安全上传和管理
- **API文档**：集成Swagger/OpenAPI文档
- **数据验证**：完整的数据验证和错误处理
- **日志记录**：详细的操作日志记录
- **缓存支持**：Redis缓存提升性能
- **异步任务**：Celery异步任务处理

## 技术栈

- **Python 3.10+**
- **Django 5.2 LTS** - Web框架
- **Django REST Framework** - REST API框架
- **PostgreSQL/SQLite** - 数据库
- **Redis** - 缓存和消息队列
- **Celery** - 异步任务处理
- **JWT** - 身份认证
- **Swagger/OpenAPI** - API文档

## 项目结构

```
backend/
├── npcsite/                 # Django项目配置
│   ├── settings.py          # 项目设置
│   ├── urls.py              # 主URL配置
│   └── wsgi.py              # WSGI配置
├── api/                     # API应用包
│   ├── users/               # 用户管理应用
│   │   ├── models.py        # 数据模型
│   │   ├── serializers.py   # 序列化器
│   │   ├── views.py         # API视图
│   │   ├── permissions.py   # 权限类
│   │   ├── urls.py          # URL配置
│   │   └── tests.py         # 测试文件
│   ├── performance/         # 履职管理应用
│   │   ├── models.py        # 履职记录和附件模型
│   │   ├── serializers.py   # 序列化器
│   │   ├── views.py         # API视图（APIView方式）
│   │   ├── permissions.py   # 权限类
│   │   ├── urls.py          # URL配置
│   │   ├── utils.py         # 工具函数（文件处理等）
│   │   └── tests.py         # 测试文件
│   ├── opinion/             # 意见建议管理应用
│   │   ├── models.py        # 意见建议和审核记录模型
│   │   ├── serializers.py   # 序列化器
│   │   ├── views.py         # API视图（APIView方式）
│   │   ├── permissions.py   # 权限类
│   │   ├── urls.py          # URL配置
│   │   └── tests.py         # 测试文件
│   └── aiknowledge/         # AI知识库应用
│       ├── models.py        # 空模型（无需数据库）
│       ├── serializers.py   # 序列化器
│       ├── views.py         # API视图（SSE流式响应、语音转文字、意见建议生成）
│       ├── services.py      # 核心服务类（Dify代理、语音转文字、意见建议AI生成）
│       ├── urls.py          # URL配置
│       ├── tests.py         # 测试文件
│       └── README.md        # AI知识库应用文档
├── logs/                    # 日志文件目录
├── media/                   # 用户上传文件目录
├── staticfiles/             # 静态文件目录
├── pyproject.toml           # 项目依赖配置
└── manage.py                # Django管理脚本
```

## 快速开始

### 1. 环境准备

确保已安装Python 3.10+和uv包管理工具：

```bash
# 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 克隆项目
git clone <repository-url>
cd backend
```

### 2. 安装依赖

根据pyproject.toml修改建议，手动更新依赖：

```toml
[project]
name = "backend"
version = "0.1.0"
description = "人大代表履职服务与管理平台后端"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "django>=5.2.3",                # Django框架
    "djangorestframework>=3.15.2",  # Django REST Framework - 构建REST API
    "djangorestframework-simplejwt>=5.3.0",  # JWT认证支持
    "django-cors-headers>=4.3.1",   # 处理跨域请求
    "drf-yasg>=1.21.7",             # Swagger文档生成
    "requests>=2.32.4",             # HTTP请求库
    "python-decouple>=3.8",         # 环境变量管理
    "celery>=5.3.4",                # 异步任务处理（用于通知等）
    "redis>=5.0.1",                 # Redis缓存和消息队列
]
```

然后安装依赖：

```bash
uv sync
```

### 3. 环境配置

创建.env文件（可选，使用默认配置也可以正常运行）：

```bash
# Django配置
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=127.0.0.1,localhost

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 其他配置...
```

### 4. 数据库初始化

```bash
# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
# 或 .venv\Scripts\activate  # Windows

# 创建数据库迁移
python manage.py makemigrations

# 应用迁移
python manage.py migrate

# 创建超级用户（可选）
python manage.py createsuperuser
```

### 5. 启动开发服务器

```bash
python manage.py runserver
```

服务器启动后，可以访问：

- **API文档**: http://127.0.0.1:8000/docs/
- **ReDoc文档**: http://127.0.0.1:8000/redoc/
- **管理后台**: http://127.0.0.1:8000/admin/

## API端点

### 用户认证

- `POST /api/v1/users/auth/login/` - 用户登录
- `POST /api/v1/users/auth/logout/` - 用户登出
- `POST /api/v1/users/auth/refresh/` - 刷新令牌

### 用户信息

- `GET /api/v1/users/profile/` - 获取个人信息
- `PUT /api/v1/users/profile/` - 更新个人信息
- `POST /api/v1/users/password/change/` - 修改密码

### 用户管理（工作人员）

- `GET /api/v1/users/manage/` - 获取用户列表
- `POST /api/v1/users/manage/` - 创建用户
- `GET /api/v1/users/manage/{id}/` - 获取用户详情
- `PATCH /api/v1/users/manage/{id}/` - 更新用户状态
- `PUT /api/v1/users/manage/{id}/` - 重置用户密码
- `DELETE /api/v1/users/manage/{id}/` - 删除用户账号

### 人大代表

- `GET /api/v1/users/representatives/` - 获取代表列表

### 工作人员

- `GET /api/v1/users/staff/` - 获取工作人员列表

### 意见建议管理

#### 代表端API
- `GET /api/v1/opinions/suggestions/` - 获取意见建议列表
- `POST /api/v1/opinions/suggestions/create/` - 创建意见建议
- `GET /api/v1/opinions/suggestions/{id}/` - 获取意见建议详情
- `PUT /api/v1/opinions/suggestions/{id}/update/` - 更新意见建议
- `DELETE /api/v1/opinions/suggestions/{id}/delete/` - 删除意见建议
- `POST /api/v1/opinions/suggestions/{id}/submit/` - 提交意见建议
- `POST /api/v1/opinions/ai/generate/` - AI辅助生成
- `GET /api/v1/opinions/statistics/` - 获取个人统计数据

#### 工作人员端API
- `GET /api/v1/opinions/suggestions/` - 获取所有意见建议
- `POST /api/v1/opinions/suggestions/{id}/review/` - 审核意见建议
- `GET /api/v1/opinions/statistics/` - 获取全局统计数据

### 履职管理（代表用户）

#### 履职记录

- `GET /api/v1/performance/records/` - 获取履职记录列表
- `POST /api/v1/performance/records/` - 创建履职记录
- `GET /api/v1/performance/records/{id}/` - 获取履职记录详情
- `PUT /api/v1/performance/records/{id}/` - 完整更新履职记录
- `PATCH /api/v1/performance/records/{id}/` - 部分更新履职记录
- `DELETE /api/v1/performance/records/{id}/` - 删除履职记录
- `GET /api/v1/performance/records/stats/` - 获取统计数据
- `GET /api/v1/performance/records/export/` - 导出数据

#### 附件管理

- `GET /api/v1/performance/attachments/?performance_record_id={id}` - 获取附件列表
- `GET /api/v1/performance/attachments/{id}/` - 获取附件详情
- `PATCH /api/v1/performance/attachments/{id}/` - 更新附件信息
- `DELETE /api/v1/performance/attachments/{id}/` - 删除附件

#### 文件操作

- `POST /api/v1/performance/upload/` - 上传文件
- `POST /api/v1/performance/cleanup/` - 清理孤立文件（管理员）

#### 选择项数据

- `GET /api/v1/performance/choices/types/` - 获取履职类型选项
- `GET /api/v1/performance/choices/status/` - 获取履职状态选项
- `GET /api/v1/performance/choices/file-limits/` - 获取文件类型限制

### AI知识库管理

#### AI聊天问答
- `POST /api/v1/aiknowledge/chat/sse/` - AI流式聊天接口（SSE）

#### 语音转文字
- `POST /api/v1/aiknowledge/audio-to-text/` - 语音转文字接口

## 权限说明

系统支持两种用户角色：

1. **人大代表 (representative)**
   - 可以查看和修改自己的个人信息
   - 可以修改自己的密码
   - 可以查看其他代表的基本信息
   - 可以管理自己的履职记录和附件
   - 可以创建、编辑、提交意见建议（仅自己的）
   - 可以使用AI辅助功能生成意见建议
   - 可以查看自己的统计数据

2. **工作人员 (staff)**
   - 拥有人大代表的所有权限
   - 可以管理所有用户账号
   - 可以创建、禁用、启用用户
   - 可以重置其他用户的密码
   - 可以查看和审核所有意见建议
   - 可以转交、更新进度、办结意见建议
   - 可以查看全局统计数据和报表

## 开发指南

### 代码规范

- 严格按照APIView方式编写API视图
- 所有API响应使用统一的JSON格式
- 详细的代码注释和文档字符串
- 完整的单元测试覆盖

### 测试

项目包含完整的测试套件，涵盖模型、API、权限等各个方面。

#### 快速运行测试

```bash
# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
# 或 .venv\Scripts\activate  # Windows

# 使用自定义测试脚本（推荐）
python run_tests.py                    # 默认运行performance应用测试
python run_tests.py --all              # 运行所有测试
python run_tests.py --app users        # 运行users应用测试
python run_tests.py --app performance  # 运行performance应用测试
python run_tests.py --verbose          # 详细输出
python run_tests.py --coverage         # 生成覆盖率报告

# 使用Django原生命令
python manage.py test                           # 运行所有测试
python manage.py test api.users                # 运行users应用测试
python manage.py test api.performance          # 运行performance应用测试
python manage.py test api.performance.tests.PerformanceRecordAPITest  # 运行特定测试类
```

#### 测试覆盖率

生成详细的测试覆盖率报告：

```bash
# 安装coverage包（如果还没安装）
pip install coverage

# 运行覆盖率测试
python run_tests.py --coverage

# 或者手动运行
coverage run --source='.' manage.py test api
coverage report
coverage html  # 生成HTML报告
```

#### 测试内容

**Performance模块测试覆盖：**

1. **模型测试** (`PerformanceRecordModelTest`, `PerformanceAttachmentModelTest`)
   - 数据模型创建和验证
   - 字符串表示方法
   - 模型方法功能
   - 附件状态更新

2. **API功能测试** (`PerformanceRecordAPITest`)
   - CRUD操作（创建、读取、更新、删除）
   - 列表筛选和搜索
   - 分页功能
   - URL路由正确性

3. **附件API测试** (`PerformanceAttachmentAPITest`)
   - 附件列表获取
   - 附件详情查看
   - 附件信息更新
   - 附件删除操作

4. **文件上传测试** (`FileUploadAPITest`)
   - 图片文件上传
   - 文件类型验证
   - 权限控制
   - 错误处理

5. **统计API测试** (`StatisticsAPITest`)
   - 统计数据计算
   - 导出功能
   - 数据格式验证

6. **选择项API测试** (`ChoicesAPITest`)
   - 履职类型选择项
   - 履职状态选择项
   - 文件类型限制

7. **权限控制测试** (`PermissionTest`)
   - 代表用户权限
   - 工作人员权限限制
   - 未认证用户访问控制
   - 对象级权限检查

#### 测试质量指标

- **测试用例总数**: 35+ 个测试方法
- **覆盖功能**: 所有主要API端点
- **权限测试**: 完整的权限控制验证
- **边界情况**: 异常和错误处理
- **数据验证**: 输入验证和业务逻辑

## 更新日志

### 2025年1月 - Performance模块APIView重构

**重大更改：将performance模块的所有ViewSet改为APIView实现**

#### 变更内容：

1. **视图重构**：
   - `PerformanceRecordViewSet` → `PerformanceRecordListCreateAPIView` + `PerformanceRecordDetailAPIView`
   - `PerformanceAttachmentViewSet` → `PerformanceAttachmentListAPIView` + `PerformanceAttachmentDetailAPIView`
   - 新增 `PerformanceRecordStatsAPIView`、`PerformanceRecordExportAPIView` 等独立视图
   - 保持原有的 `FileUploadAPIView` 等已经是APIView的视图

2. **URL配置重构**：
   - 移除 DRF Router，改为直接路径配置
   - 重新设计URL结构，保持RESTful规范
   - 附件列表接口改为查询参数方式：`/attachments/?performance_record_id={id}`

3. **权限处理优化**：
   - 手动实现对象级权限检查
   - 保持原有的权限逻辑不变
   - 增强错误提示信息

4. **分页处理**：
   - 使用Django Paginator手动实现分页
   - 返回完整的分页信息（总数、页码、是否有下一页等）

5. **响应格式统一**：
   - 所有APIView返回统一的JSON格式
   - 保持与原ViewSet相同的数据结构
   - 增强错误处理和日志记录

#### 接口兼容性：

- **URL路径保持兼容**：主要端点URL未发生变化
- **请求/响应格式保持一致**：客户端无需修改
- **功能完全保持**：所有原有功能均已实现
- **权限控制保持不变**：安全策略未改变

#### 技术优势：

- **代码更清晰**：每个端点有独立的处理逻辑
- **更好的控制**：可以更精细地控制每个端点的行为
- **易于维护**：减少了ViewSet的魔法方法，代码更直观
- **性能优化**：可以针对特定端点进行优化

#### 文件变更：

- `backend/api/performance/views.py` - 完全重写，改为APIView方式
- `backend/api/performance/urls.py` - URL配置重构
- `backend/README.md` - 更新项目文档

这次重构保证了向后兼容性，前端代码无需做任何修改即可正常工作。

# 生成测试覆盖率报告
coverage run --source='.' manage.py test
coverage report
```

### 数据库

项目支持SQLite（开发）和PostgreSQL（生产）：

```python
# 生产环境数据库配置示例
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'npc_db',
        'USER': 'npc_user',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

## 部署

### 生产环境配置

1. 设置环境变量
2. 配置PostgreSQL数据库
3. 配置Redis缓存
4. 收集静态文件：`python manage.py collectstatic`
5. 使用Gunicorn或uWSGI部署

### Docker部署（可选）

项目支持Docker容器化部署，详见Dockerfile和docker-compose.yml。

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 更新记录

### 2025-01-20 - 前端Records.vue完整实现
- ✅ **完成前端履职记录管理页面**：实现Records.vue的完整功能，包含CRUD操作
- ✅ **集成AttachmentUploader组件**：支持图片、音频、视频、文档等多媒体附件上传
- ✅ **实现搜索筛选功能**：支持按履职类型、状态、日期范围进行筛选
- ✅ **优化用户界面设计**：统一主题风格，采用中国红配色方案
- ✅ **添加响应式布局**：支持移动端访问，优化移动端显示效果
- ✅ **完善错误处理**：添加完整的错误提示和用户反馈机制
- ✅ **集成后端API**：与performance API完全对接，支持实时数据交互
- ✅ **表单验证优化**：添加前端表单验证，提升用户体验

#### 主要功能特性：
- **履职记录管理**：创建、编辑、查看、删除履职记录
- **附件管理**：支持多种文件类型上传，包含预览和下载功能
- **搜索筛选**：多维度搜索筛选，快速定位目标记录
- **分页展示**：支持大量数据的分页显示
- **详情查看**：完整的记录详情展示，包含附件信息
- **状态管理**：履职状态的可视化管理

#### 技术实现：
- **Vue 3 Composition API**：使用现代化的组合式API
- **Element Plus UI**：统一的UI组件库
- **响应式数据**：使用ref管理所有响应式数据
- **模块化API**：按照前端API模块化规范组织代码
- **TypeScript风格**：完整的JSDoc注释和类型提示

### 2025-01-XX - 前端API模块化重构
- **API模块化**: 重构前端API为模块化结构，符合Vue3最佳实践
- **目录结构优化**: 将 `performance.js` 移至 `modules/performance/` 目录
- **统一导入方式**: 所有组件改为使用 `@/api/modules/performance` 导入
- **移除硬编码URL**: 组件中的上传URL改为通过API方法获取
- **修正API路径**: 前端API配置从 `/api` 改为 `/api/v1`
- **更新文档**: 完善SETUP.md，添加API使用示例和故障排除指南

### 2024-12-19 - 账号管理功能优化

#### 新增功能
1. **重置用户密码功能**
   - 在`UserDetailView`中添加PUT方法支持重置用户密码
   - 包含密码强度验证（至少8位，包含字母和数字）
   - 前端添加重置密码对话框，包含密码确认和强度验证
   - 完整的错误处理和用户反馈

2. **删除用户账号功能**
   - 在`UserDetailView`中添加DELETE方法支持删除用户账号
   - 前端账号管理页面添加删除按钮，支持删除用户（防止删除自己）
   - 删除操作包含确认对话框，提高安全性

3. **分页功能完善**
   - 后端支持动态`page_size`参数（1-100范围）
   - 添加搜索功能支持用户名和姓名检索
   - 前端分页组件UI优化，支持防抖搜索
   - 分页信息显示和响应式设计

#### 安全改进
4. **自我保护机制**
   - 用户不能删除自己的账号
   - 用户不能禁用自己的账号
   - 相关按钮添加禁用状态和Tooltip提示
   - 操作前添加前端和后端双重验证

#### 改进
5. **用户名重复验证优化**
   - 在`AccountCreateSerializer`中添加`validate_username`方法验证用户名唯一性
   - 前端优化错误处理，特别显示用户名重复错误信息
   - 当用户名重复时，自动聚焦到用户名输入框

#### 文件修改
- `backend/api/users/views.py` - 添加重置密码、删除用户API和分页搜索功能
- `backend/api/users/serializers.py` - 添加用户名唯一性验证
- `frontend/src/views/staff/AccountManagement.vue` - 完整的分页UI优化和搜索功能
- `frontend/src/api/modules/auth/api.js` - 添加重置密码API方法

### 2025-06-23 v1.2.1 - 草稿状态筛选修复
- 🔧 **修复草稿状态筛选逻辑**：
  - 发现问题：创建意见建议时自动创建审核记录，导致所有意见建议都有审核记录
  - 原逻辑错误：`reviews__isnull=True` 永远不会匹配到记录
  - 修复方案：草稿状态也通过最新审核记录状态筛选，与其他状态保持一致
  - 技术实现：统一使用 `current_status_db=Subquery(latest_review.values('status')[:1])` 筛选逻辑

### 2025-06-23 v1.2.0 - 状态筛选和处理流程修复
- 🔧 **修复状态筛选功能**：
  - 解决了代表页面和工作人员页面筛选功能不一致的问题
  - 修复了views.py中筛选逻辑与模型current_status属性不一致的bug
  - 将筛选查询的排序从`order_by('-action_time')`改为`order_by('-id')`，与模型保持一致
  - 确保草稿状态和其他状态的筛选逻辑正确

- 🛠️ **优化工作人员审核流程显示**：
  - 修复了工作人员查看意见建议时处理流程显示问题
  - 工作人员查看详情时调用完整的详情API获取审核历史
  - 确保处理流程时间线显示完整的审核记录

- 🎯 **修复操作人显示逻辑**：
  - 后端序列化器新增`operator_name`字段
  - 创建草稿时正确显示代表姓名而不是"系统"
  - 工作人员操作时显示工作人员姓名

- 📋 **技术细节**：
  - 状态筛选逻辑与`OpinionSuggestion.current_status`属性保持完全一致
  - 使用ID排序确保获取真正的"最新"审核记录（ID是自增的）
  - 前端工作人员页面查看详情时调用`getOpinionDetail` API

### 2025-06-23 v1.2.2 - AI内容编辑保留和时间显示优化

**问题修复：**

1. **AI内容编辑丢失问题**：
   - 修复代表用AI生成建议意见后保存草稿，再次编辑时AI内容丢失的问题
   - 添加一键应用AI内容到最终意见建议的功能

2. **时间显示精度问题**：
   - 修复工作人员查看意见建议详情时，提交时间和最后更新时间只显示日期不显示时分的问题
   - 修复工作人员列表中所有时间列精确到分钟显示

3. **代表姓名显示问题**：
   - 修复工作人员页面代表显示为"未知代表"的问题
   - 将错误的`representative_info?.user?.real_name`改为正确的`representative_info?.name`

**技术实现：**

1. **AI内容编辑保留**：
   ```javascript
   // 修改前：直接使用列表数据
   const editOpinion = (row) => {
     Object.assign(opinionForm, row)  // ❌ 列表数据可能不完整
   }

   // 修改后：获取完整详情数据
   const editOpinion = async (row) => {
     const response = await opinionAPI.getOpinionDetail(row.id)
     if (response.data?.success) {
       Object.assign(opinionForm, {
         ai_generated_content: detailData.ai_generated_content || '',
         // ... 其他字段
       })
     }
   }
   ```

2. **一键应用AI内容功能**：
   ```vue
   <el-button 
     size="small" 
     type="success" 
     @click="applyAIContent"
     :disabled="!opinionForm.ai_generated_content"
   >
     <el-icon><Check /></el-icon>
     一键应用到最终意见建议
   </el-button>
   ```

3. **时间显示精度优化**：
   ```javascript
   // 修改前：只显示日期
   formatDate(currentOpinion.created_at)  // 2025-06-23

   // 修改后：显示完整时间
   formatDateTime(currentOpinion.created_at)  // 2025-06-23 14:30
   ```

4. **代表姓名显示修复**：
   ```javascript
   // 修改前：错误的字段路径
   submitterName: item.representative_info?.user?.real_name || '未知代表'

   // 修改后：正确的字段路径
   submitterName: item.representative_info?.name || '未知代表'
   ```

**用户体验改进：**
- 编辑意见时AI生成内容完整保留
- 提供便捷的一键应用功能，无需手动复制粘贴
- 时间显示更加精确，便于跟踪处理进度
- 代表姓名正确显示，便于工作人员识别提交人

**文件修改：**
- `frontend/src/views/representative/Opinions.vue` - AI内容编辑保留和一键应用功能
- `frontend/src/views/staff/Review.vue` - 时间显示精度优化

### 2025-06-23 v1.2.3 - 表格显示优化和统计数据修复

**问题修复：**
- 工作人员页面表格时间列显示换行，影响界面美观性
- 优化用户体验，时间列直接显示完整内容，标题列支持悬浮查看
- 工作人员页面统计卡片显示全为0，没有显示真实数据
- 移除"超期未办结"统计卡片
- 修复统计逻辑：基于`opinion_reviews`表中每个意见的最新审核状态进行统计

**技术实现：**

1. **表格列宽度优化**：
   - 提交时间、转交时间、最后更新时间列宽度从120px增加到160px
   - 移除时间列的`show-overflow-tooltip`，确保时间直接显示完整内容
   - 保留标题列的`show-overflow-tooltip`属性

2. **CSS样式优化**：
   ```css
   /* 防止表格内容换行 */
   :deep(.el-table .el-table__cell) {
     white-space: nowrap;
   }

   /* 标题列特殊处理 - 允许鼠标悬浮查看完整内容 */
   :deep(.el-table .el-table__cell:first-child .cell) {
     overflow: hidden;
     text-overflow: ellipsis;
   }

   /* 时间列确保能够完整显示 */
   :deep(.el-table .el-table__cell:nth-child(4) .cell),
   :deep(.el-table .el-table__cell:nth-child(5) .cell),
   :deep(.el-table .el-table__cell:nth-child(6) .cell) {
     white-space: nowrap;
   }
   ```

3. **统计数据修复**：
   - **后端修复**：修复工作人员统计API，使用正确的基于`opinion_reviews`最新状态的统计逻辑
   - **前端修复**：修复前端统计API调用逻辑，正确映射后端返回的字段
   - **算法优化**：使用子查询`Subquery`获取每个意见建议的最新审核记录状态
   - **排序保证**：使用`order_by('-id')`确保获取真正的最新记录（ID是自增的）
   - 移除"超期未办结"统计卡片，简化界面
   - 添加fallback机制：API失败时通过分析列表数据计算统计
   - **统计逻辑**：
     - 待审核：最新状态为'submitted'的意见数量
     - 已通过待转交：最新状态为'approved'的意见数量  
     - 处理中：最新状态为'transferred'或'in_progress'的意见数量
     - 已办结：最新状态为'completed'的意见数量

**用户体验改进：**
- 时间列直接显示完整的"年-月-日 时:分"格式，无需鼠标悬浮
- 意见建议标题列支持鼠标悬浮查看完整内容（因为标题可能很长）
- 所有表格内容保证在一行内显示，避免换行影响阅读
- 合理的列宽分配，确保关键信息清晰可见
- 统计卡片显示真实数据，便于工作人员掌握工作量和进度

**文件修改：**
- `backend/api/opinion/views.py` - 修复工作人员统计API，基于最新审核状态正确统计
- `frontend/src/views/staff/Review.vue` - 表格列宽度和CSS样式优化，修复统计卡片数据显示
- `frontend/src/views/representative/Opinions.vue` - 同步优化代表页面表格显示，修复操作按钮排列

### 2025-06-23 v1.2.4 - 状态流程文档完善和排序策略确认

**文档更新：**
- 📋 **完善意见建议模块文档**：
  - 在`backend/api/opinion/README.md`中明确统计分类说明
  - 详细说明基于`opinion_reviews`最新状态的统计逻辑
  - 明确"处理中"包含`transferred`和`in_progress`两个状态
  - 添加统计API实现细节和前后端字段映射说明
  - 完善状态流转图和各状态含义说明
  - 确保文档与代码实现完全一致

**排序策略确认：**
- 📊 **前端排序方案**：
  - 代表页面和工作人员页面都使用`ordering: '-created_at'`
  - 后端默认排序`'-created_at'`，支持`['created_at', '-created_at', 'updated_at', '-updated_at']`
  - 模型默认`ordering = ['-created_at']`
  - 策略：最新创建的意见建议排在前面，有索引支持性能良好

**技术说明：**
- 状态获取使用`order_by('-id')`确保获取真正最新的审核记录
- 列表排序使用`order_by('-created_at')`提供更好的用户体验
- ID排序用于状态准确性，时间排序用于用户界面逻辑

**处理中状态详细说明：**
- **transferred**: 意见建议已转交给相关政府部门，但部门尚未开始处理
- **in_progress**: 相关部门已开始处理意见建议，正在推进解决
- 在工作人员统计界面中，这两个状态合并显示为"处理中"

**文件修改：**
- `backend/api/opinion/README.md` - 完善状态流程文档和技术实现说明

### 2025-06-23 v1.2.5 - 统计卡片优化和搜索功能完善

**统计卡片拆分：**
- 🔄 **拆分处理中状态**：
  - 原来："处理中"包含transferred和in_progress两个状态
  - 现在：分别显示"已转交"和"处理中"两个独立卡片
  - 前端增加新的inProgress卡片，使用Loading图标
  - 后端统计API新增in_progress_count字段

**搜索功能增强：**
- 🔍 **完善提交人搜索**：
  - 修复工作人员页面无法搜索代表姓名的问题
  - 后端搜索逻辑新增representative__name__icontains条件
  - 现在支持按代表姓名、标题、反映人姓名、原始内容四个字段搜索

**技术实现：**
- 后端统计逻辑分离transferred_count和in_progress_count计算
- 序列化器OpinionStatisticsSerializer新增in_progress_count字段
- 前端stats数据结构增加inProgress字段
- 状态分布status_distribution新增in_progress状态项

**文件修改：**
- `backend/api/opinion/views.py` - 拆分统计逻辑，增强搜索功能
- `backend/api/opinion/serializers.py` - 新增in_progress_count字段
- `frontend/src/views/staff/Review.vue` - 添加处理中卡片，修改数据映射

## 🚀 当前系统状态 (截至2025-06-23)

### ✅ 已完成核心模块

1. **用户认证与管理模块** - 完全稳定
   - JWT身份认证系统
   - 代表和工作人员角色权限控制
   - 用户信息管理（创建、更新、删除、重置密码）
   - 完整的权限验证和安全机制

2. **履职记录管理模块** - 完全稳定
   - 代表履职活动的完整记录和管理
   - 多媒体附件支持（图片、文档、音频、视频）
   - 文件上传、缩略图生成、安全存储
   - 统计分析和数据导出功能

3. **意见建议管理模块** - 已完成前后端完整对接 ✨
   - 意见建议全流程管理（创建→提交→审核→转交→办结）
   - AI辅助生成高质量意见建议
   - 工作人员审核和处理流程
   - 状态流转控制和权限管理
   - 实时统计数据和报表功能

### 🔧 技术架构成熟度

- **后端架构**: Django 5.2 + DRF，APIView架构设计，代码清晰可维护
- **数据库设计**: 规范化设计，合理的索引和查询优化
- **权限系统**: 严格的基于角色的权限控制（RBAC）
- **API设计**: RESTful风格，统一响应格式，完整的错误处理
- **测试覆盖**: 35+测试用例，涵盖所有主要功能和边界情况
- **文档完整**: API文档、技术文档、部署文档齐全

### 📊 系统稳定性指标

- **意见建议模块**: 状态筛选、处理流程、统计数据已全面修复
- **数据一致性**: 基于审核记录的状态管理，确保数据完整性
- **前后端对接**: 完整的API对接，数据格式统一，错误处理完善
- **用户体验**: 时间显示精确、表格优化、操作流畅
- **AI功能**: 内容编辑保留、一键应用、智能生成等功能稳定

### 🎯 主要功能亮点

1. **AI辅助功能**: 智能生成高质量意见建议，提升代表工作效率
2. **全流程管理**: 从草稿创建到最终办结的完整业务流程
3. **实时统计**: 基于最新审核状态的准确统计数据
4. **权限隔离**: 代表只能访问自己的数据，工作人员可管理全部
5. **移动适配**: 响应式设计，支持PC和移动端访问
6. **文件管理**: 安全的文件上传、存储、缩略图生成

### 📈 性能与扩展性

- **查询优化**: 使用select_related和子查询优化数据库性能
- **分页支持**: 大数据量列表的高效分页处理
- **缓存策略**: Redis缓存提升系统响应速度
- **异步处理**: Celery支持异步任务处理
- **横向扩展**: 无状态设计，支持负载均衡部署

### 🚀 生产就绪

当前系统已具备生产环境部署条件：
- ✅ 核心功能完整且稳定
- ✅ 完整的错误处理和日志记录
- ✅ 严格的权限控制和数据验证
- ✅ 完善的测试覆盖和文档支持
- ✅ 规范的代码结构和部署指南

## 更新记录

### 2025-06-29 v1.3.2 - 修复AI功能问题和超时处理

**问题修复：**
- 🔧 **修复法律AI问答功能**：重新实现`chatSSE`方法的SSE流式响应
- 🔧 **优化超时处理**：AI生成请求超时时间延长至60秒，避免页面跳转
- 🔧 **精准错误处理**：只对意见建议生成的超时错误进行特殊处理，不影响其他功能

**技术修复：**

1. **SSE流式实现修复**：
   ```javascript
   // 重新实现chatSSE方法支持回调函数
   async chatSSE(chatData, onMessage, onError, onComplete) {
     const response = await fetch(url, {
       method: 'POST',
       headers: { 'Accept': 'text/event-stream' },
       body: JSON.stringify(chatData)
     })
     
     const reader = response.body.getReader()
     // 处理SSE数据流...
   }
   ```

2. **超时时间分层配置**：
   ```javascript
   // config.js新增AI专用超时配置
   export const API_CONFIG = {
     TIMEOUT: 15000,        // 普通请求15秒
     AI_TIMEOUT: 60000,     // AI生成请求60秒
   }
   ```

3. **精准错误处理**：
   ```javascript
   // 只对意见建议生成的超时进行特殊处理
   if (error.code === 'ECONNABORTED' && error.message?.includes('timeout')) {
     const isOpinionGenerate = error.config?.url?.includes('/aiknowledge/opinion/generate/')
     if (isOpinionGenerate) {
       ElMessage.error('AI生成请求超时，请稍后重试')
       return  // 不跳转页面
     }
   }
   ```

**修复效果：**
- ✅ **法律AI问答恢复正常**：SSE流式响应正确处理消息回调
- ✅ **语音转文字功能正常**：音频文件上传和转换功能稳定
- ✅ **意见建议AI生成不再超时跳转**：60秒超时时间充足，错误处理精准
- ✅ **其他网络错误处理机制保持不变**：真正的网络连接问题仍会跳转错误页面

**用户体验改善：**
- 🎯 **AI问答流畅**：消息流式显示，参考文件正常展示
- ⏰ **超时时间合理**：AI生成有充足处理时间，避免误报超时
- 🚫 **错误处理友好**：超时错误显示提示消息，不会误跳转页面
- 🔄 **功能稳定性**：所有AI相关功能恢复正常工作状态
- 🎤 **语音转文字恢复**：修复FormData传递和Content-Type处理问题

**文件修改：**
- `frontend/src/api/modules/aiknowledge/api.js` - 重新实现chatSSE的SSE流式处理，修复audioToText的Content-Type问题
- `frontend/src/api/http/config.js` - 新增AI_TIMEOUT配置
- `frontend/src/api/http/interceptors.js` - 精准化超时错误处理逻辑，修复FormData请求的Content-Type处理
- `frontend/src/views/KnowledgeQA.vue` - 修复语音转文字FormData传递问题，添加调试信息
- `backend/api/aiknowledge/services.py` - 修复audio_to_text文件指针重置问题

### 2025-06-24 v1.3.0 - AI语音转文字功能

**新增功能：**
- 🎤 **语音转文字功能**：
  - 前端语音录制：支持实时录音，麦克风权限管理
  - 语音转文字API：透明代理第三方语音识别服务
  - 音频格式支持：mp3, mp4, mpeg, mpga, m4a, wav, webm
  - 文件大小限制：最大50MB
  - 自动填充输入框：转换结果直接填入问答输入框

**技术实现：**

1. **后端API实现**：
   ```python
   # 新增语音转文字API端点
   POST /api/v1/aiknowledge/audio-to-text/
   
   # services.py新增方法
   AIKnowledgeService.audio_to_text(audio_file)
   ```

2. **前端录音功能**：
   ```javascript
   // 使用MediaRecorder API实现录音
   const mediaRecorder = new MediaRecorder(stream, {
     mimeType: 'audio/webm;codecs=opus'
   })
   
   // 语音转文字API调用
   aiKnowledgeAPI.audioToText(audioFile)
   ```

3. **UI组件更新**：
   - 在发送按钮左侧添加语音按钮
   - 录音中显示红色危险样式，带有脉动动画
   - 停止时显示播放图标，正常时显示麦克风图标
   - 录音期间禁用输入框和其他交互按钮

4. **第三方服务集成**：
   - 配置语音转文字API地址：`http://*************/v1/audio-to-text`
   - 使用与AI聊天相同的API密钥进行身份验证
   - 支持多种音频格式的文件上传

**用户体验：**
- 🎯 **一键录音**: 点击麦克风图标开始录音，再次点击停止
- 🔄 **实时反馈**: 录音状态清晰显示，处理过程有loading提示
- 📝 **自动填充**: 转换的文字自动填入输入框，等待用户确认发送
- 🚫 **状态控制**: 录音时禁用其他操作，避免冲突
- ⚡ **快速转换**: 停止录音后立即开始转换，无需额外操作

**安全性：**
- 🔐 **权限验证**: 需要登录认证才能使用语音转文字功能
- 📏 **文件限制**: 严格的文件大小和格式验证
- 🛡️ **错误处理**: 完整的异常处理和用户友好的错误提示

**性能优化：**
- 🎵 **音频压缩**: 使用opus编码器压缩音频文件
- ⚡ **流式处理**: 不在内存中保存大文件，直接转发
- 🧹 **资源清理**: 组件卸载时自动停止录音和清理资源

**文件修改：**
- `backend/api/aiknowledge/services.py` - 新增audio_to_text方法
- `backend/api/aiknowledge/views.py` - 新增AudioToTextAPIView
- `backend/api/aiknowledge/urls.py` - 新增语音转文字路由
- `frontend/src/api/modules/aiknowledge/api.js` - 新增audioToText方法
- `frontend/src/views/KnowledgeQA.vue` - 集成语音录制和转换功能
- `backend/test_audio_to_text.py` - 语音转文字功能测试脚本

### 2025-06-27 v1.3.1 - AI意见建议生成功能优化

**新增功能：**
- 🤖 **AI意见建议生成服务**：
  - 在aiknowledge应用中新增独立的意见建议生成功能
  - 使用专门的Dify应用配置，不影响现有法律知识库功能
  - 支持基于原始内容、分类和背景信息生成高质量意见建议
  - 自动清理AI生成内容中的思考标签（<think>标签）

**前端体验优化：**
- 🎯 **智能按钮控制**：
  - AI生成按钮根据内容长度（10-1000字）和分类选择智能启用/禁用
  - 不满足条件时显示友好提示信息
  - 按钮状态实时响应用户输入变化

- 📝 **文本框优化**：
  - 最终意见建议文本框从4行扩展到8行
  - 字符限制从500字增加到2000字
  - 支持自适应高度（8-15行）和手动调整
  - 优化字体和行高，提升可读性

**技术实现：**

1. **后端服务架构**：
   ```python
   # 新增独立的意见建议AI服务
   class OpinionAIService:
       def generate_suggestion(original_content, category, context)
       def _clean_generated_content(content)  # 清理<think>标签
       def _build_prompt(original_content, category, context)
   
   # 新增API端点
   POST /api/v1/aiknowledge/opinion/generate/
   ```

2. **前端配置规范化**：
   ```javascript
   // 统一API端点配置
   export const AIKNOWLEDGE_ENDPOINTS = {
     CHAT_SSE: '/aiknowledge/chat/sse/',
     AUDIO_TO_TEXT: '/aiknowledge/audio-to-text/',
     OPINION_GENERATE: '/aiknowledge/opinion/generate/'
   }
   
   // 使用配置文件而非硬编码路径
   return httpClient.post(OPINION_ENDPOINTS.AI_GENERATE, aiData)
   ```

3. **内容清理机制**：
   ```python
   # 正则表达式清理AI思考标签
   cleaned = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
   # 清理多余空行并格式化
   cleaned = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned).strip()
   ```

4. **前端智能控制**：
   ```javascript
   // 计算属性控制按钮状态
   const isAIButtonEnabled = computed(() => {
     const content = opinionForm.original_content?.trim() || ''
     const hasCategory = !!opinionForm.category
     const validLength = content.length >= 10 && content.length <= 1000
     return hasCategory && validLength
   })
   ```

**配置管理：**
- 🔧 **独立配置**：新的Dify应用使用独立的API密钥配置
- 📋 **统一管理**：前端API端点统一在config.js中管理
- 🎨 **风格一致**：遵循项目现有的API调用风格和错误处理模式

**测试覆盖：**
- ✅ **完整测试用例**：6个测试方法覆盖成功生成、参数验证、API错误、权限检查等场景
- 🧪 **Mock测试**：使用unittest.mock模拟Dify API响应
- 🔍 **内容验证**：测试<think>标签清理功能和提示词构建逻辑

**用户体验：**
- 🚀 **即时反馈**：按钮状态和提示信息实时更新
- 📏 **合适尺寸**：文本框大小适配AI生成的长内容
- 🎯 **一键应用**：生成的内容可一键应用到最终意见建议
- 🧹 **内容清理**：自动去除AI思考过程，只保留实际建议内容

**文件修改：**
- `backend/api/aiknowledge/services.py` - 新增OpinionAIService类和内容清理方法
- `backend/api/aiknowledge/views.py` - 新增OpinionGenerateAPIView
- `backend/api/aiknowledge/urls.py` - 新增意见建议生成路由
- `backend/api/aiknowledge/tests.py` - 新增OpinionGenerateAPITest测试类
- `backend/api/aiknowledge/README.md` - 创建详细的功能文档
- `frontend/src/api/http/config.js` - 新增AIKNOWLEDGE_ENDPOINTS配置
- `frontend/src/api/modules/aiknowledge/api.js` - 完善aiknowledge API模块
- `frontend/src/api/modules/opinion/api.js` - 恢复使用配置文件端点
- `frontend/src/views/representative/Opinions.vue` - 优化AI按钮控制和文本框尺寸

### 2025-06-23 v1.1.0 - 认证机制优化