#!/usr/bin/env python
"""
简单的工作计划提醒测试数据生成脚本
"""

import os
import django
from datetime import date, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'npcsite.settings')
django.setup()

from django.contrib.auth import get_user_model
from api.workplan.models import WorkPlan

User = get_user_model()

def main():
    print("🚀 开始创建工作计划提醒测试数据...")
    
    # 获取工作人员用户
    staff_user = User.objects.filter(role='staff').first()
    if not staff_user:
        print("❌ 没有找到工作人员用户，请先创建工作人员账号")
        print("可以运行: python manage.py shell -c \"from django.contrib.auth import get_user_model; User=get_user_model(); User.objects.create_user('staff', '<EMAIL>', 'password', role='staff')\"")
        return
    
    print(f"✅ 找到工作人员用户: {staff_user.username}")
    
    # 清除现有工作计划
    WorkPlan.objects.all().delete()
    print("🗑️ 已清除现有工作计划数据")
    
    today = date.today()
    
    # 创建即将到期的计划（明天到期，提前3天提醒）
    plan1 = WorkPlan.objects.create(
        staff_member=staff_user,
        title='紧急：社区调研报告整理',
        start_date=today - timedelta(days=5),
        end_date=today + timedelta(days=1),  # 明天到期
        content='需要整理本月社区调研的所有资料，准备向上级汇报。包括民意收集、问题分析和解决方案建议。',
        status='in_progress',
        reminder_days=3
    )
    
    # 创建已逾期的计划（昨天到期）
    plan2 = WorkPlan.objects.create(
        staff_member=staff_user,
        title='代表联络站会议纪要',
        start_date=today - timedelta(days=7),
        end_date=today - timedelta(days=1),  # 昨天到期
        content='整理上周代表联络站会议的讨论要点，形成正式纪要文档，分发给相关代表。',
        status='in_progress',
        reminder_days=2
    )
    
    # 创建另一个即将到期的计划（3天后到期，提前5天提醒）
    plan3 = WorkPlan.objects.create(
        staff_member=staff_user,
        title='月度工作总结撰写',
        start_date=today,
        end_date=today + timedelta(days=3),  # 3天后到期
        content='撰写本月度工作总结，包括代表履职情况统计、意见建议处理情况等。',
        status='planned',
        reminder_days=5
    )
    
    print(f"✅ 成功创建 {WorkPlan.objects.count()} 个测试工作计划")
    
    # 测试提醒查询
    reminder_plans = WorkPlan.get_reminder_plans()
    overdue_plans = WorkPlan.get_overdue_plans()
    
    print(f"📊 提醒统计:")
    print(f"  - 需要提醒的计划: {len(reminder_plans)} 个")
    print(f"  - 已逾期的计划: {len(overdue_plans)} 个")
    print(f"  - 总计需要关注: {len(reminder_plans) + len(overdue_plans)} 个")
    
    if reminder_plans:
        print("\n🔔 即将到期的计划:")
        for plan in reminder_plans:
            print(f"  - {plan.title} (还有 {plan.days_until_end} 天)")
    
    if overdue_plans:
        print("\n⚠️ 已逾期的计划:")
        for plan in overdue_plans:
            print(f"  - {plan.title} (逾期 {abs(plan.days_until_end)} 天)")
    
    print("\n🎉 测试数据创建完成！现在可以用工作人员账号登录测试提醒功能了。")

if __name__ == '__main__':
    main() 