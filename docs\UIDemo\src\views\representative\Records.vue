<template>
  <div class="records-container">
    <div class="page-header">
      <h2>履职记录管理</h2>
      <p>记录和管理您的履职活动</p>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showAddDialog">
        <el-icon><Plus /></el-icon>
        新增记录
      </el-button>
    </div>

    <!-- 履职记录列表 -->
    <el-card>
      <el-table :data="recordsList" v-loading="loading">
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="title" label="履职内容" min-width="200" />
        <el-table-column prop="type" label="履职类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)">{{ row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="地点" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === '已完成' ? 'success' : 'warning'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewRecord(row)">
              查看
            </el-button>
            <el-button size="small" type="primary" @click="editRecord(row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '新增履职记录' : '编辑履职记录'"
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="recordForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="履职日期" prop="date">
          <el-date-picker
            v-model="recordForm.date"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="履职类型" prop="type">
          <el-select v-model="recordForm.type" placeholder="请选择履职类型" style="width: 100%">
            <el-option label="会议参与" value="会议参与" />
            <el-option label="实地调研" value="实地调研" />
            <el-option label="走访群众" value="走访群众" />
            <el-option label="意见建议" value="意见建议" />
            <el-option label="监督检查" value="监督检查" />
            <el-option label="其他活动" value="其他活动" />
          </el-select>
        </el-form-item>

        <el-form-item label="履职内容" prop="title">
          <el-input
            v-model="recordForm.title"
            placeholder="请输入履职内容"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="活动地点" prop="location">
          <el-input
            v-model="recordForm.location"
            placeholder="请输入活动地点"
          />
        </el-form-item>

        <el-form-item label="详细描述" prop="description">
          <el-input
            v-model="recordForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述履职活动内容、收获等"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select v-model="recordForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="已完成" value="已完成" />
            <el-option label="进行中" value="进行中" />
            <el-option label="已计划" value="已计划" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveRecord">保存</el-button>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="履职记录详情"
      width="600px"
    >
      <div v-if="currentRecord" class="record-detail">
        <div class="detail-item">
          <span class="label">履职日期：</span>
          <span class="value">{{ currentRecord.date }}</span>
        </div>
        <div class="detail-item">
          <span class="label">履职类型：</span>
          <el-tag :type="getTypeColor(currentRecord.type)">{{ currentRecord.type }}</el-tag>
        </div>
        <div class="detail-item">
          <span class="label">履职内容：</span>
          <span class="value">{{ currentRecord.title }}</span>
        </div>
        <div class="detail-item">
          <span class="label">活动地点：</span>
          <span class="value">{{ currentRecord.location }}</span>
        </div>
        <div class="detail-item">
          <span class="label">状态：</span>
          <el-tag :type="currentRecord.status === '已完成' ? 'success' : 'warning'">
            {{ currentRecord.status }}
          </el-tag>
        </div>
        <div class="detail-item">
          <span class="label">详细描述：</span>
          <div class="description">{{ currentRecord.description || '暂无详细描述' }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 数据状态
const loading = ref(false)
const recordsList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 对话框状态
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit'

// 表单引用
const formRef = ref()

// 当前记录
const currentRecord = ref(null)

// 表单数据
const recordForm = reactive({
  id: null,
  date: '',
  type: '',
  title: '',
  location: '',
  description: '',
  status: '已完成'
})

// 表单验证规则
const formRules = {
  date: [
    { required: true, message: '请选择履职日期', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择履职类型', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入履职内容', trigger: 'blur' },
    { min: 2, max: 100, message: '履职内容长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请输入活动地点', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    '会议参与': 'primary',
    '实地调研': 'success',
    '走访群众': 'warning',
    '意见建议': 'info',
    '监督检查': 'danger',
    '其他活动': ''
  }
  return colorMap[type] || ''
}

// 显示新增对话框
const showAddDialog = () => {
  dialogMode.value = 'add'
  resetForm()
  dialogVisible.value = true
}

// 编辑记录
const editRecord = (row) => {
  dialogMode.value = 'edit'
  Object.assign(recordForm, {
    ...row,
    date: new Date(row.date)
  })
  dialogVisible.value = true
}

// 查看记录
const viewRecord = (row) => {
  currentRecord.value = row
  viewDialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(recordForm, {
    id: null,
    date: '',
    type: '',
    title: '',
    location: '',
    description: '',
    status: '已完成'
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 保存记录
const saveRecord = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  try {
    loading.value = true
    
    // 模拟保存操作
    const recordData = {
      ...recordForm,
      date: recordForm.date.toISOString().split('T')[0]
    }

    if (dialogMode.value === 'add') {
      recordData.id = Date.now()
      recordsList.value.unshift(recordData)
      total.value++
      ElMessage.success('履职记录添加成功')
    } else {
      const index = recordsList.value.findIndex(item => item.id === recordData.id)
      if (index !== -1) {
        recordsList.value[index] = recordData
      }
      ElMessage.success('履职记录更新成功')
    }

    dialogVisible.value = false
    resetForm()
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  loadRecords()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadRecords()
}

// 加载履职记录
const loadRecords = () => {
  loading.value = true
  
  // 模拟数据
  setTimeout(() => {
    recordsList.value = [
      {
        id: 1,
        date: '2024-01-15',
        title: '参加街道民情恳谈会',
        type: '会议参与',
        location: '街道办事处',
        status: '已完成',
        description: '参与讨论社区治理相关议题，收集群众意见建议。'
      },
      {
        id: 2,
        date: '2024-01-12',
        title: '走访困难群众家庭',
        type: '走访群众',
        location: '某某小区',
        status: '已完成',
        description: '了解困难群众生活状况，收集民生需求。'
      },
      {
        id: 3,
        date: '2024-01-10',
        title: '参与议案讨论',
        type: '会议参与',
        location: '人大常委会',
        status: '已完成',
        description: '参与重要议案的审议讨论。'
      }
    ]
    total.value = 15
    loading.value = false
  }, 500)
}

// 初始化
onMounted(() => {
  loadRecords()
})
</script>

<style scoped>
.records-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: var(--china-red);
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  margin: 0;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.record-detail {
  line-height: 2;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.label {
  font-weight: bold;
  color: var(--china-red);
  min-width: 80px;
  margin-right: 10px;
}

.value {
  flex: 1;
  color: var(--text-color);
}

.description {
  flex: 1;
  color: var(--text-color);
  line-height: 1.6;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  white-space: pre-wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .records-container {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  /* 移动端隐藏部分列 */
  :deep(.el-table .el-table__cell:nth-child(4)),
  :deep(.el-table .el-table__cell:nth-child(5)) {
    display: none;
  }
}
</style> 