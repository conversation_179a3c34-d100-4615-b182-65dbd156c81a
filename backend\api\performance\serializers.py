"""
履职管理序列化器

包含以下序列化器：
1. PerformanceAttachmentSerializer - 履职记录附件序列化器
2. PerformanceRecordSerializer - 履职记录完整序列化器  
3. PerformanceRecordCreateSerializer - 履职记录创建序列化器
4. PerformanceRecordListSerializer - 履职记录列表序列化器
5. PerformanceRecordUpdateSerializer - 履职记录更新序列化器

设计原则：
- 数据验证严格，确保数据质量
- 权限控制，只能操作自己的数据
- 字段完整性检查
- 错误信息友好，便于老年用户理解
"""

import re
from rest_framework import serializers
from django.utils import timezone
from django.core.validators import FileExtensionValidator
from .models import PerformanceRecord, PerformanceAttachment
from api.users.models import Representative


class PerformanceAttachmentSerializer(serializers.ModelSerializer):
    """
    履职记录附件序列化器
    用于附件信息的序列化和反序列化
    """
    
    # 只读字段，用于前端展示
    file_url = serializers.SerializerMethodField(
        help_text='文件访问URL，用于前端下载和预览'
    )
    thumbnail_url = serializers.SerializerMethodField(
        help_text='缩略图URL，用于图片和视频预览'
    )
    file_size_display = serializers.SerializerMethodField(
        help_text='人类可读的文件大小格式'
    )
    duration_display = serializers.SerializerMethodField(
        help_text='人类可读的时长格式（音视频文件）'
    )
    
    # 选择字段的显示名称
    file_type_display = serializers.CharField(
        source='get_file_type_display',
        read_only=True,
        help_text='文件类型的中文显示名称'
    )
    upload_status_display = serializers.CharField(
        source='get_upload_status_display',
        read_only=True,
        help_text='上传状态的中文显示名称'
    )
    
    class Meta:
        model = PerformanceAttachment
        fields = [
            'id', 'file_type', 'file_type_display', 'original_filename', 
            'stored_filename', 'file_path', 'file_size', 'file_size_display', 
            'mime_type', 'file_hash', 'duration', 'duration_display', 
            'width', 'height', 'upload_status', 'upload_status_display', 
            'sort_order', 'file_url', 'thumbnail_url', 'thumbnail_path',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'stored_filename', 'file_path', 'file_hash', 
            'duration', 'width', 'height', 'thumbnail_path',
            'created_at', 'updated_at'
        ]
    
    def get_file_url(self, obj):
        """获取文件访问URL"""
        return obj.get_file_url()
    
    def get_thumbnail_url(self, obj):
        """获取缩略图URL"""
        return obj.get_thumbnail_url()
    
    def get_file_size_display(self, obj):
        """获取人类可读的文件大小"""
        return obj.get_file_size_display()
    
    def get_duration_display(self, obj):
        """获取人类可读的时长"""
        return obj.get_duration_display()


class PerformanceRecordSerializer(serializers.ModelSerializer):
    """
    履职记录完整序列化器
    用于详情查看，包含所有字段和关联数据
    """
    
    # 关联数据（只读）
    representative_name = serializers.CharField(
        source='representative.name',
        read_only=True,
        help_text='代表姓名'
    )
    representative_level = serializers.CharField(
        source='representative.level',
        read_only=True,
        help_text='代表层级'
    )
    
    # 选择字段的显示名称
    performance_type_display = serializers.CharField(
        source='get_performance_type_display',
        read_only=True,
        help_text='履职类型的中文显示名称'
    )
    performance_status_display = serializers.CharField(
        source='get_performance_status_display',
        read_only=True,
        help_text='履职状态的中文显示名称'
    )
    
    # 附件信息（嵌套序列化）
    attachments = PerformanceAttachmentSerializer(
        many=True,
        read_only=True,
        help_text='关联的附件列表'
    )
    
    # 统计字段
    attachment_count = serializers.SerializerMethodField(
        help_text='附件总数'
    )
    attachment_stats = serializers.SerializerMethodField(
        help_text='附件统计信息（按类型分组）'
    )
    
    class Meta:
        model = PerformanceRecord
        fields = [
            'id', 'representative', 'representative_name', 'representative_level',
            'performance_date', 'performance_type', 'performance_type_display',
            'performance_content', 'activity_location', 'detailed_description',
            'performance_status', 'performance_status_display', 'has_attachments',
            'attachment_count', 'attachment_stats', 'attachments',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'representative', 'has_attachments', 'created_at', 'updated_at'
        ]
    
    def get_attachment_count(self, obj):
        """获取附件总数"""
        return obj.get_attachment_count()
    
    def get_attachment_stats(self, obj):
        """获取附件统计信息"""
        return obj.get_attachment_stats()
    
    def validate_performance_date(self, value):
        """
        验证履职日期
        - 不能超过今天
        - 不能太久远（10年内）
        """
        today = timezone.now().date()
        
        if value > today:
            raise serializers.ValidationError('履职日期不能超过今天')
        
        # 检查日期不能太久远（防止误输入）
        one_year_ago = today.replace(year=today.year - 1)
        if value < one_year_ago:
            raise serializers.ValidationError('履职日期不能超过一年前，如有特殊情况请联系管理员')
        
        return value
    
    def validate_performance_content(self, value):
        """
        验证履职内容
        - 去除首尾空格
        - 长度检查
        - 内容质量检查
        """
        content = value.strip()

        if len(content) < 2:
            raise serializers.ValidationError('履职内容至少需要2个字符')

        if len(content) > 500:
            raise serializers.ValidationError('履职内容不能超过500个字符，请精简描述')

        # 检查是否只有重复字符或无意义内容（只有当内容长度大于2时才检查）
        if len(content) > 2 and len(set(content.replace(' ', ''))) < 2:
            raise serializers.ValidationError('请填写有意义的履职内容')

        return content
    
    def validate_activity_location(self, value):
        """验证活动地点"""
        location = value.strip()
        
        if len(location) < 2:
            raise serializers.ValidationError('活动地点至少需要2个字符')
        
        if len(location) > 200:
            raise serializers.ValidationError('活动地点不能超过200个字符')
        
        return location
    
    def validate_detailed_description(self, value):
        """验证详细描述（可选字段）"""
        if value:
            description = value.strip()
            if len(description) > 1000:
                raise serializers.ValidationError('详细描述不能超过1000个字符')
            return description
        return value


class PerformanceRecordCreateSerializer(serializers.ModelSerializer):
    """
    履职记录创建序列化器
    用于新建履职记录，自动关联当前用户的代表信息
    """
    
    class Meta:
        model = PerformanceRecord
        fields = [
            'performance_date', 'performance_type', 'performance_content',
            'activity_location', 'detailed_description', 'performance_status'
        ]
    
    def validate_performance_date(self, value):
        """验证履职日期（与完整序列化器相同的验证逻辑）"""
        today = timezone.now().date()
        
        if value > today:
            raise serializers.ValidationError('履职日期不能超过今天')
        
        ten_year_ago = today.replace(year=today.year - 10)
        if value < ten_year_ago:
            raise serializers.ValidationError('履职日期不能超过10年前，如有特殊情况请联系管理员')
        
        return value
    
    def validate_performance_content(self, value):
        """验证履职内容（与完整序列化器相同的验证逻辑）"""
        content = value.strip()

        if len(content) < 2:
            raise serializers.ValidationError('履职内容至少需要2个字符')

        if len(content) > 500:
            raise serializers.ValidationError('履职内容不能超过500个字符，请精简描述')

        # 检查是否只有重复字符或无意义内容（只有当内容长度大于2时才检查）
        if len(content) > 2 and len(set(content.replace(' ', ''))) < 2:
            raise serializers.ValidationError('请填写有意义的履职内容')

        return content
    
    def validate_activity_location(self, value):
        """验证活动地点"""
        location = value.strip()
        
        if len(location) < 2:
            raise serializers.ValidationError('活动地点至少需要2个字符')
        
        if len(location) > 200:
            raise serializers.ValidationError('活动地点不能超过200个字符')
        
        return location
    
    def create(self, validated_data):
        """
        创建履职记录
        自动关联当前用户的代表信息
        """
        request = self.context.get('request')
        if not request or not hasattr(request.user, 'representative'):
            raise serializers.ValidationError('当前用户不是代表，无法创建履职记录')
        
        # 自动设置代表字段
        validated_data['representative'] = request.user.representative
        return super().create(validated_data)


class PerformanceRecordUpdateSerializer(serializers.ModelSerializer):
    """
    履职记录更新序列化器
    用于更新履职记录，不包含代表字段（不允许修改）
    """
    
    class Meta:
        model = PerformanceRecord
        fields = [
            'performance_date', 'performance_type', 'performance_content',
            'activity_location', 'detailed_description', 'performance_status'
        ]
    
    def validate_performance_date(self, value):
        """验证履职日期"""
        today = timezone.now().date()
        
        if value > today:
            raise serializers.ValidationError('履职日期不能超过今天')
        
        ten_year_ago = today.replace(year=today.year - 10)
        if value < ten_year_ago:
            raise serializers.ValidationError('履职日期不能超过10年前，如有特殊情况请联系管理员')
        
        return value
    
    def validate_performance_content(self, value):
        """验证履职内容"""
        content = value.strip()

        if len(content) < 2:
            raise serializers.ValidationError('履职内容至少需要2个字符')

        if len(content) > 500:
            raise serializers.ValidationError('履职内容不能超过500个字符，请精简描述')

        # 检查是否只有重复字符或无意义内容（只有当内容长度大于2时才检查）
        if len(content) > 2 and len(set(content.replace(' ', ''))) < 2:
            raise serializers.ValidationError('请填写有意义的履职内容')

        return content
    
    def validate(self, attrs):
        """
        整体验证
        检查用户是否有权限修改此记录
        """
        request = self.context.get('request')
        instance = self.instance
        
        if (request and hasattr(request.user, 'representative') and 
            instance and instance.representative != request.user.representative):
            raise serializers.ValidationError('您只能修改自己的履职记录')
        
        return attrs


class PerformanceRecordListSerializer(serializers.ModelSerializer):
    """
    履职记录列表序列化器
    用于列表显示，字段精简，提高性能
    """
    
    # 关联数据
    representative_name = serializers.CharField(
        source='representative.name',
        read_only=True,
        help_text='代表姓名'
    )
    
    # 选择字段的显示名称
    performance_type_display = serializers.CharField(
        source='get_performance_type_display',
        read_only=True,
        help_text='履职类型显示名称'
    )
    performance_status_display = serializers.CharField(
        source='get_performance_status_display',
        read_only=True,
        help_text='履职状态显示名称'
    )
    
    # 截断内容用于列表显示
    content_preview = serializers.SerializerMethodField(
        help_text='内容预览（截取前50个字符）'
    )
    
    # 统计信息
    attachment_count = serializers.SerializerMethodField(
        help_text='附件总数'
    )
    
    class Meta:
        model = PerformanceRecord
        fields = [
            'id', 'representative_name', 'performance_date', 'performance_type',
            'performance_type_display', 'content_preview', 'activity_location',
            'performance_status', 'performance_status_display', 'has_attachments',
            'attachment_count', 'created_at', 'updated_at'
        ]
    
    def get_content_preview(self, obj):
        """
        获取内容预览
        截取前50个字符，适合列表显示
        """
        content = obj.performance_content
        if len(content) > 50:
            return content[:50] + '...'
        return content
    
    def get_attachment_count(self, obj):
        """获取附件总数"""
        return obj.get_attachment_count()


class PerformanceRecordStatsSerializer(serializers.Serializer):
    """
    履职记录统计序列化器
    用于工作台统计数据
    """
    
    # 时间维度统计
    monthly_count = serializers.IntegerField(
        help_text='本月履职记录数量'
    )
    yearly_count = serializers.IntegerField(
        help_text='本年履职记录数量'
    )
    total_count = serializers.IntegerField(
        help_text='总履职记录数量'
    )
    
    # 类型统计
    type_stats = serializers.DictField(
        help_text='按类型分组的统计数据'
    )
    
    # 状态统计
    status_stats = serializers.DictField(
        help_text='按状态分组的统计数据'
    )
    
    # 趋势数据
    monthly_trend = serializers.ListField(
        child=serializers.DictField(),
        help_text='近12个月的履职记录趋势数据'
    )
    
    # 附件统计
    attachment_stats = serializers.DictField(
        help_text='附件统计信息'
    )
    
    # 最近记录
    recent_records = serializers.ListField(
        child=serializers.DictField(),
        help_text='最近5条履职记录',
        required=False
    )


# 文件上传相关序列化器
class FileUploadSerializer(serializers.Serializer):
    """
    文件上传序列化器
    用于验证上传的文件
    """
    
    file = serializers.FileField(
        required=True,
        help_text='要上传的文件'
    )
    
    file_type = serializers.ChoiceField(
        choices=PerformanceAttachment.FILE_TYPE_CHOICES,
        required=True,
        help_text='文件类型'
    )
    
    performance_record_id = serializers.IntegerField(
        required=True,
        help_text='关联的履职记录ID'
    )
    
    def validate_file(self, value):
        """
        验证上传的文件
        - 文件大小检查
        - 文件类型检查
        - 文件扩展名检查
        """
        if not value:
            raise serializers.ValidationError('请选择要上传的文件')
        
        # 获取文件类型
        file_type = self.initial_data.get('file_type')
        if not file_type:
            raise serializers.ValidationError('请指定文件类型')
        
        # 检查文件大小
        size_limits = PerformanceAttachment.get_file_size_limits()
        max_size = size_limits.get(file_type, 0)
        
        if value.size > max_size:
            max_size_mb = max_size // (1024 * 1024)
            raise serializers.ValidationError(f'文件大小不能超过 {max_size_mb}MB')
        
        # 检查文件扩展名
        if hasattr(value, 'name') and value.name:
            file_ext = value.name.split('.')[-1].lower()
            allowed_extensions = PerformanceAttachment.get_allowed_extensions()
            
            if file_ext not in allowed_extensions.get(file_type, []):
                allowed_exts = ', '.join(allowed_extensions.get(file_type, []))
                raise serializers.ValidationError(f'不支持的文件格式，请选择 {allowed_exts} 格式的文件')
        
        return value
    
    def validate_performance_record_id(self, value):
        """验证履职记录ID"""
        try:
            record = PerformanceRecord.objects.get(id=value)
        except PerformanceRecord.DoesNotExist:
            raise serializers.ValidationError('履职记录不存在')
        
        # 检查权限：只能上传到自己的履职记录
        request = self.context.get('request')
        if (request and hasattr(request.user, 'representative') and 
            record.representative != request.user.representative):
            raise serializers.ValidationError('您只能向自己的履职记录上传附件')
        
        return value
    
    def validate(self, attrs):
        """
        整体验证
        检查文件数量限制
        """
        performance_record_id = attrs.get('performance_record_id')
        file_type = attrs.get('file_type')
        
        if performance_record_id and file_type:
            try:
                record = PerformanceRecord.objects.get(id=performance_record_id)
                if not record.can_add_attachment(file_type):
                    limits = PerformanceAttachment.get_file_type_limits()
                    max_count = limits.get(file_type, 0)
                    raise serializers.ValidationError(
                        f'{file_type}类型的文件已达到上限（{max_count}个）'
                    )
            except PerformanceRecord.DoesNotExist:
                pass  # 在字段验证中已处理
        
        return attrs 