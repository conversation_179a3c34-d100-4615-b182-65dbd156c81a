"""
代表AI总结Django Admin配置

仅用于开发和调试，生产环境不使用Django Admin
"""

from django.contrib import admin
from .models import RepresentativeAISummary


@admin.register(RepresentativeAISummary)
class RepresentativeAISummaryAdmin(admin.ModelAdmin):
    """代表AI总结管理界面"""
    
    list_display = [
        'id', 'representative_name', 'analysis_year', 'status',
        'generation_duration_display', 'has_ai_result', 'created_at', 'completed_at'
    ]
    
    list_filter = [
        'status', 'analysis_year', 'created_at', 'completed_at'
    ]
    
    search_fields = [
        'representative__name', 'representative__user__username'
    ]
    
    readonly_fields = [
        'id', 'representative_name', 'has_ai_result', 'generation_duration_display',
        'created_at', 'updated_at', 'completed_at'
    ]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'representative', 'representative_name', 'analysis_year')
        }),
        ('生成状态', {
            'fields': ('status', 'generation_duration', 'generation_duration_display', 'error_message')
        }),
        ('数据信息', {
            'fields': ('has_ai_result', 'source_data_summary'),
            'classes': ('collapse',)
        }),
        ('AI结果', {
            'fields': ('ai_result',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )
    
    # 排序
    ordering = ['-analysis_year', '-created_at']
    
    # 每页显示数量
    list_per_page = 20
    
    def representative_name(self, obj):
        """显示代表姓名"""
        return obj.representative.name if obj.representative else '-'
    representative_name.short_description = '代表姓名'
    representative_name.admin_order_field = 'representative__name'
    
    def generation_duration_display(self, obj):
        """显示生成耗时"""
        if obj.generation_duration is None:
            return '-'
        
        duration = obj.generation_duration
        if duration < 60:
            return f"{duration}秒"
        elif duration < 3600:
            minutes = duration // 60
            seconds = duration % 60
            return f"{minutes}分{seconds}秒"
        else:
            hours = duration // 3600
            minutes = (duration % 3600) // 60
            return f"{hours}小时{minutes}分钟"
    generation_duration_display.short_description = '生成耗时'
    generation_duration_display.admin_order_field = 'generation_duration'
    
    def has_ai_result(self, obj):
        """显示是否有AI结果"""
        return obj.has_ai_result
    has_ai_result.short_description = '有AI结果'
    has_ai_result.boolean = True
    
    def has_add_permission(self, request):
        """禁止通过admin添加记录"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """只允许查看，不允许修改"""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """禁止删除"""
        return False
