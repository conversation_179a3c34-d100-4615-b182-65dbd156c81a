/**
 * 代表组成数据相关API
 * 包含人大代表组成结构饼图所需的数据接口
 */

import { get, post } from './request.js'

/**
 * 获取代表组成统计数据
 * @returns {Promise} 返回代表组成统计数据
 */
export const getRepresentativeComposition = async () => {
  try {
    // 模拟数据 - 后期替换为真实API调用
    const mockData = {
      code: 200,
      message: 'success',
      data: [
        { 
          name: '中共党员', 
          value: 1156, 
          percentage: 38,
          color: '#02a6b5',
          description: '中国共产党党员代表'
        },
        { 
          name: '群众', 
          value: 856, 
          percentage: 28,
          color: '#1a5490',
          description: '群众代表'
        },
        { 
          name: '农工党党员', 
          value: 702, 
          percentage: 23,
          color: '#337ab7',
          description: '中国农工民主党党员代表'
        },
        { 
          name: '民建会员', 
          value: 334, 
          percentage: 11,
          color: '#49bcf7',
          description: '中国民主建国会会员代表'
        }
      ],
      totalCount: 3048, // 总代表人数
      updateTime: new Date().toLocaleString(),
      timestamp: Date.now()
    }

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 350))
    
    console.log('🥧 获取代表组成统计数据成功:', mockData.data)
    return mockData
    
    // 后期替换为真实API调用：
    // return await get('/statistics/representative-composition')
    
  } catch (error) {
    console.error('❌ 获取代表组成统计数据失败:', error)
    throw error
  }
}

/**
 * 获取代表组成详细信息
 * @param {string} type - 代表类型
 * @returns {Promise} 返回指定类型代表的详细信息
 */
export const getRepresentativeCompositionDetail = async (type) => {
  try {
    // 模拟详细数据
    const detailData = {
      '中共党员': {
        totalCount: 1156,
        ageDistribution: [
          { range: '30-40岁', count: 234 },
          { range: '40-50岁', count: 456 },
          { range: '50-60岁', count: 346 },
          { range: '60岁以上', count: 120 }
        ],
        genderDistribution: [
          { gender: '男', count: 693 },
          { gender: '女', count: 463 }
        ]
      },
      '群众': {
        totalCount: 856,
        ageDistribution: [
          { range: '30-40岁', count: 186 },
          { range: '40-50岁', count: 342 },
          { range: '50-60岁', count: 256 },
          { range: '60岁以上', count: 72 }
        ],
        genderDistribution: [
          { gender: '男', count: 514 },
          { gender: '女', count: 342 }
        ]
      },
      '农工党党员': {
        totalCount: 702,
        ageDistribution: [
          { range: '30-40岁', count: 152 },
          { range: '40-50岁', count: 281 },
          { range: '50-60岁', count: 210 },
          { range: '60岁以上', count: 59 }
        ],
        genderDistribution: [
          { gender: '男', count: 421 },
          { gender: '女', count: 281 }
        ]
      },
      '民建会员': {
        totalCount: 334,
        ageDistribution: [
          { range: '30-40岁', count: 72 },
          { range: '40-50岁', count: 134 },
          { range: '50-60岁', count: 100 },
          { range: '60岁以上', count: 28 }
        ],
        genderDistribution: [
          { gender: '男', count: 200 },
          { gender: '女', count: 134 }
        ]
      }
    }

    const mockData = {
      code: 200,
      message: 'success',
      data: detailData[type] || null,
      timestamp: Date.now()
    }

    await new Promise(resolve => setTimeout(resolve, 200))
    
    console.log(`📋 获取${type}详细信息成功:`, mockData.data)
    return mockData
    
    // 后期替换为：
    // return await get(`/statistics/representative-composition/${encodeURIComponent(type)}`)
    
  } catch (error) {
    console.error(`❌ 获取${type}详细信息失败:`, error)
    throw error
  }
} 