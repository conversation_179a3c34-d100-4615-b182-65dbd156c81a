# 代表履职AI分析设计文档

## 1. 文档说明

本文档详细设计了人大代表履职AI分析功能的技术架构、数据流程和实现方案。该功能旨在通过AI技术自动分析代表的年度履职数据，生成图文并茂的精美年度总结报告。

## 2. 功能概述

### 2.1 业务目标

- **智能分析**：基于代表的履职记录和意见建议数据，生成AI驱动的年度履职分析
- **可视化展示**：生成包含核心指标、活动分布、时间投入、履职亮点等的可视化报告
- **成果总结**：提供AI智能评价和改进建议，帮助代表了解履职成效
- **数据洞察**：通过关键词云、趋势分析等方式提供深度数据洞察

### 2.2 功能特性

- ✅ **年度分析**：支持按年度生成AI分析报告
- ✅ **数据整合**：自动收集履职记录和意见建议数据
- ✅ **AI生成**：调用第三方AI API生成分析内容
- ✅ **可视化**：丰富的图表和数据展示
- ✅ **导出功能**：支持PDF导出和分享
- ✅ **重新生成**：支持重新生成最新分析

## 3. 技术架构

### 3.1 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A[年度成就页面] --> B[数据可视化组件]
        A --> C[报告展示组件]
        A --> D[导出分享组件]
    end
    
    subgraph "后端API层"
        E[AI分析API] --> F[数据收集服务]
        E --> G[AI调用服务]
        E --> H[结果存储服务]
    end
    
    subgraph "数据层"
        I[(履职记录表)] --> F
        J[(意见建议表)] --> F
        K[(AI总结表)] --> H
    end
    
    subgraph "第三方服务"
        L[AI API Provider]
    end
    
    A --> E
    G --> L
    F --> I
    F --> J
    H --> K
```

### 3.2 核心组件

#### 3.2.1 前端组件
- **AnnualAchievements.vue**：年度成就展示页面
- **数据可视化组件**：图表、指标卡片、时间线等
- **报告操作组件**：生成、重新生成、导出、分享

#### 3.2.2 后端服务
- **AISummaryService**：AI分析核心服务
- **DataCollectionService**：数据收集和预处理
- **AIProviderService**：第三方AI API调用
- **ReportService**：报告生成和管理

#### 3.2.3 数据存储
- **representative_ai_summaries**：AI分析结果存储表
- **performance_records**：履职记录数据源
- **opinion_suggestions**：意见建议数据源

## 4. 数据模型设计

### 4.1 AI总结表结构

```sql
CREATE TABLE representative_ai_summaries (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'AI总结主键ID',
    representative_id BIGINT NOT NULL COMMENT '关联代表表',
    analysis_year INT NOT NULL COMMENT '分析年份',
    source_data_summary JSON NOT NULL DEFAULT ('{}') COMMENT '数据来源概要',
    ai_result JSON NULL COMMENT 'AI分析结果',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '生成状态',
    generation_duration FLOAT NULL COMMENT '生成耗时(秒)',
    error_message TEXT NULL COMMENT '错误信息',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    completed_at DATETIME NULL COMMENT '完成时间',
    
    INDEX idx_representative_id (representative_id),
    INDEX idx_analysis_year (analysis_year),
    INDEX idx_status (status),
    INDEX idx_rep_year (representative_id, analysis_year),
    
    FOREIGN KEY (representative_id) REFERENCES representatives(id) ON DELETE CASCADE,
    CONSTRAINT chk_status CHECK (status IN ('pending', 'generating', 'completed', 'failed'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 4.2 数据来源概要格式

```json
{
  "performance_records_count": 45,
  "opinion_suggestions_count": 12,
  "total_attachments": 67,
  "date_range": {
    "start": "2024-01-01",
    "end": "2024-12-31"
  },
  "performance_types": {
    "会议参与": 15,
    "实地调研": 10,
    "走访群众": 12,
    "其他活动": 8
  },
  "opinion_categories": {
    "城建环保": 4,
    "教育文化": 3,
    "医疗卫生": 2,
    "其他": 3
  }
}
```

### 4.3 AI分析结果格式

根据前端展示需求，AI API需要返回以下格式的JSON数据：

```json
{
  "overview": {
    "subtitle": "履职年度总结：45项重要成就，280天积极履职"
  },
  "coreMetrics": [
    {
      "label": "履职活动",
      "value": "52次",
      "icon": "Trophy",
      "color": "#c62d2d",
      "trend": "+12.5%",
      "trendType": "positive"
    },
    {
      "label": "建议提案",
      "value": "18份",
      "icon": "DataLine", 
      "color": "#52c41a",
      "trend": "+8.3%",
      "trendType": "positive"
    },
    {
      "label": "采纳率",
      "value": "87.2%",
      "icon": "Star",
      "color": "#fa8c16",
      "trend": "+5.1%",
      "trendType": "positive"
    },
    {
      "label": "投入时间",
      "value": "280天",
      "icon": "Timer",
      "color": "#1890ff",
      "trend": "+15.2%",
      "trendType": "positive"
    }
  ],
  "activityDistribution": [
    {
      "type": "会议参与",
      "count": 20,
      "percentage": 35,
      "color": "#c62d2d"
    },
    {
      "type": "建议提交",
      "count": 12,
      "percentage": 20,
      "color": "#52c41a"
    }
  ],
  "timeInvestment": {
    "total": 280,
    "monthly": 23,
    "activity": 92,
    "monthlyData": [
      {"month": 1, "percentage": 85},
      {"month": 2, "percentage": 78}
    ]
  },
  "highlights": [
    {
      "title": "重点民生提案",
      "description": "围绕教育、医疗、住房等民生热点，提交高质量提案8份，其中6份被政府部门采纳实施。",
      "metrics": [
        {"label": "提案数量", "value": "8份"},
        {"label": "采纳率", "value": "75.0%"}
      ]
    }
  ],
  "keywords": [
    {"word": "民生保障", "weight": 0.95},
    {"word": "教育公平", "weight": 0.88}
  ],
  "aiSummary": {
    "evaluation": "2024年度，您在人大代表履职方面表现卓越...",
    "achievements": [
      "积极参与人大会议24次，出席率达到96.8%",
      "提交高质量建议提案18份，其中13份被相关部门采纳"
    ],
    "suggestions": [
      "继续加强对新兴产业发展的关注",
      "进一步深化与其他代表的协作"
    ]
  }
}
```

## 5. API设计

### 5.1 RESTful API端点

```python
# backend/api/aisummary/urls.py
urlpatterns = [
    # 代表AI总结相关API
    path('ai-summaries/', views.AISummaryListView.as_view(), name='ai-summary-list'),
    path('ai-summaries/generate/', views.AISummaryGenerateView.as_view(), name='ai-summary-generate'),
    path('ai-summaries/<int:year>/', views.AISummaryDetailView.as_view(), name='ai-summary-detail'),
    path('ai-summaries/<int:year>/regenerate/', views.AISummaryRegenerateView.as_view(), name='ai-summary-regenerate'),
    path('ai-summaries/<int:year>/check/', views.AISummaryCheckView.as_view(), name='ai-summary-check'),
]
```

### 5.2 API详细设计

#### 5.2.1 生成AI分析

**POST** `/api/v1/ai-summaries/generate/`

**请求参数：**
```json
{
  "year": 2024
}
```

**响应格式：**
```json
{
  "success": true,
  "message": "AI分析生成完成",
  "data": {
    "id": 1,
    "representative": {
      "id": 1,
      "name": "张三",
      "level": "区级"
    },
    "analysis_year": 2024,
    "status": "completed",
    "ai_result": {
      "overview": {...},
      "coreMetrics": [...],
      "activityDistribution": [...],
      "timeInvestment": {...},
      "highlights": [...],
      "keywords": [...],
      "aiSummary": {...}
    },
    "source_data_summary": {
      "performance_records_count": 45,
      "opinion_suggestions_count": 12
    },
    "generation_duration": 12.5,
    "created_at": "2024-12-01T10:00:00Z",
    "completed_at": "2024-12-01T10:00:12Z"
  }
}
```

#### 5.2.2 获取AI分析结果

**GET** `/api/v1/ai-summaries/2024/`

**响应格式：** 同上

#### 5.2.3 检查分析数据

**GET** `/api/v1/ai-summaries/2024/check/`

**响应格式：**
```json
{
  "success": true,
  "data": {
    "has_data": true,
    "has_completed_summary": true,
    "summary_id": 1,
    "data_summary": {
      "performance_records_count": 45,
      "opinion_suggestions_count": 12
    }
  }
}
```

#### 5.2.4 重新生成分析

**POST** `/api/v1/ai-summaries/2024/regenerate/`

**响应格式：** 同生成AI分析

## 6. 后端实现

### 6.1 Django模型定义

```python
# backend/api/aisummary/models.py
from django.db import models
from django.utils import timezone
from api.users.models import Representative


class RepresentativeAISummary(models.Model):
    """代表年度AI总结结果存储表"""
    
    STATUS_CHOICES = [
        ('pending', '等待生成'),
        ('generating', '生成中'), 
        ('completed', '生成完成'),
        ('failed', '生成失败'),
    ]
    
    id = models.BigAutoField(primary_key=True, verbose_name='AI总结ID')
    representative = models.ForeignKey(
        Representative,
        on_delete=models.CASCADE,
        related_name='ai_summaries',
        verbose_name='关联代表'
    )
    analysis_year = models.IntegerField(verbose_name='分析年份')
    source_data_summary = models.JSONField(
        default=dict,
        verbose_name='数据来源概要'
    )
    ai_result = models.JSONField(
        null=True,
        blank=True,
        verbose_name='AI分析结果'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='生成状态'
    )
    generation_duration = models.FloatField(
        null=True,
        blank=True,
        verbose_name='生成耗时'
    )
    error_message = models.TextField(
        null=True,
        blank=True,
        verbose_name='错误信息'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='完成时间'
    )
    
    class Meta:
        db_table = 'representative_ai_summaries'
        verbose_name = '代表AI总结'
        verbose_name_plural = '代表AI总结'
        ordering = ['-analysis_year', '-created_at']
        indexes = [
            models.Index(fields=['representative']),
            models.Index(fields=['analysis_year']),
            models.Index(fields=['status']),
            models.Index(fields=['representative', 'analysis_year']),
        ]
        constraints = [
            # 每个代表每年只能有一个完成的AI总结
            models.UniqueConstraint(
                fields=['representative', 'analysis_year'],
                condition=models.Q(status='completed'),
                name='unique_completed_summary_per_representative_year'
            ),
        ]
    
    def __str__(self):
        return f"{self.representative.name} - {self.analysis_year}年度AI总结"
    
    @property
    def is_ready(self):
        """检查总结是否可用"""
        return self.status == 'completed' and self.ai_result is not None
```

### 6.2 序列化器设计

```python
# backend/api/aisummary/serializers.py
from rest_framework import serializers
from api.users.serializers import RepresentativeSerializer
from .models import RepresentativeAISummary


class AISummarySerializer(serializers.ModelSerializer):
    """AI总结序列化器"""
    
    representative_info = RepresentativeSerializer(
        source='representative',
        read_only=True
    )
    
    status_display = serializers.CharField(
        source='get_status_display',
        read_only=True
    )
    
    is_ready = serializers.ReadOnlyField()
    
    class Meta:
        model = RepresentativeAISummary
        fields = [
            'id', 'analysis_year', 'status', 'status_display',
            'source_data_summary', 'ai_result', 'generation_duration',
            'error_message', 'is_ready', 'representative_info',
            'created_at', 'updated_at', 'completed_at'
        ]
        read_only_fields = [
            'id', 'status', 'generation_duration', 'error_message',
            'created_at', 'updated_at', 'completed_at'
        ]


class AISummaryCreateSerializer(serializers.Serializer):
    """AI总结创建请求序列化器"""
    
    year = serializers.IntegerField(
        min_value=2020,
        max_value=2030,
        help_text='分析年份'
    )
    
    def validate_year(self, value):
        """验证年份"""
        current_year = timezone.now().year
        if value > current_year:
            raise serializers.ValidationError('不能分析未来年份的数据')
        return value
```

### 6.3 核心服务实现

```python
# backend/api/aisummary/services.py
import time
import logging
from datetime import datetime, date
from django.db import transaction
from django.utils import timezone
from api.performance.models import PerformanceRecord
from api.opinion.models import OpinionSuggestion
from .models import RepresentativeAISummary
from .ai_providers import get_ai_provider


logger = logging.getLogger(__name__)


class AISummaryService:
    """AI总结服务"""
    
    @staticmethod
    def generate_summary(representative, year):
        """生成AI总结"""
        # 检查是否已存在完成的总结
        existing = RepresentativeAISummary.objects.filter(
            representative=representative,
            analysis_year=year,
            status='completed'
        ).first()
        
        if existing:
            return existing
        
        # 创建或更新总结记录
        summary, created = RepresentativeAISummary.objects.get_or_create(
            representative=representative,
            analysis_year=year,
            defaults={'status': 'pending'}
        )
        
        # 如果正在生成中，直接返回
        if summary.status == 'generating':
            return summary
        
        try:
            with transaction.atomic():
                # 更新状态为生成中
                summary.status = 'generating'
                summary.save()
                
                # 收集源数据
                source_data = AISummaryService._collect_source_data(representative, year)
                summary.source_data_summary = source_data
                summary.save()
                
                # 如果没有数据，标记失败
                if not source_data.get('has_data', False):
                    summary.status = 'failed'
                    summary.error_message = '该年度暂无履职数据'
                    summary.save()
                    return summary
                
                # 调用AI生成分析
                start_time = time.time()
                ai_result = AISummaryService._call_ai_api(source_data, representative, year)
                generation_duration = time.time() - start_time
                
                # 保存结果
                summary.ai_result = ai_result
                summary.status = 'completed'
                summary.generation_duration = generation_duration
                summary.completed_at = timezone.now()
                summary.error_message = None
                summary.save()
                
                logger.info(f"AI总结生成成功: {representative.name} - {year}年")
                return summary
                
        except Exception as e:
            logger.error(f"AI总结生成失败: {representative.name} - {year}年, 错误: {str(e)}")
            summary.status = 'failed'
            summary.error_message = str(e)
            summary.save()
            raise e
    
    @staticmethod
    def _collect_source_data(representative, year):
        """收集源数据"""
        start_date = date(year, 1, 1)
        end_date = date(year, 12, 31)
        
        # 获取履职记录
        performance_records = PerformanceRecord.objects.filter(
            representative=representative,
            performance_date__range=[start_date, end_date]
        )
        
        # 获取意见建议
        opinion_suggestions = OpinionSuggestion.objects.filter(
            representative=representative,
            created_at__year=year
        )
        
        # 统计履职类型分布
        performance_types = {}
        for record in performance_records:
            type_name = record.performance_type
            performance_types[type_name] = performance_types.get(type_name, 0) + 1
        
        # 统计意见分类分布
        opinion_categories = {}
        for opinion in opinion_suggestions:
            category_name = opinion.get_category_display()
            opinion_categories[category_name] = opinion_categories.get(category_name, 0) + 1
        
        # 统计附件数量
        total_attachments = sum(
            record.get_attachment_count() for record in performance_records
        )
        
        has_data = performance_records.exists() or opinion_suggestions.exists()
        
        return {
            'has_data': has_data,
            'performance_records_count': performance_records.count(),
            'opinion_suggestions_count': opinion_suggestions.count(),
            'total_attachments': total_attachments,
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat()
            },
            'performance_types': performance_types,
            'opinion_categories': opinion_categories,
            'performance_records': [
                {
                    'date': record.performance_date.isoformat(),
                    'type': record.performance_type,
                    'content': record.performance_content[:200],  # 截取前200字符
                    'location': record.activity_location
                }
                for record in performance_records.order_by('-performance_date')[:50]  # 最多50条
            ],
            'opinion_suggestions': [
                {
                    'title': opinion.title,
                    'category': opinion.get_category_display(),
                    'content': opinion.original_content[:200],  # 截取前200字符
                    'created_at': opinion.created_at.isoformat()
                }
                for opinion in opinion_suggestions.order_by('-created_at')[:20]  # 最多20条
            ]
        }
    
    @staticmethod
    def _call_ai_api(source_data, representative, year):
        """调用AI API生成分析"""
        ai_provider = get_ai_provider()
        
        # 构造AI提示词
        prompt = AISummaryService._build_ai_prompt(source_data, representative, year)
        
        # 调用AI API
        return ai_provider.generate_summary(prompt, source_data)
    
    @staticmethod
    def _build_ai_prompt(source_data, representative, year):
        """构造AI提示词"""
        performance_count = source_data.get('performance_records_count', 0)
        opinion_count = source_data.get('opinion_suggestions_count', 0)
        
        prompt = f"""
请基于以下数据为人大代表{representative.name}生成{year}年度履职AI分析报告。

代表基本信息：
- 姓名：{representative.name}
- 级别：{representative.level}

年度履职数据概况：
- 履职记录总数：{performance_count}条
- 意见建议总数：{opinion_count}份
- 履职类型分布：{source_data.get('performance_types', {})}
- 意见分类分布：{source_data.get('opinion_categories', {})}

请生成包含以下内容的JSON格式分析报告：
1. overview: 概览信息，包含副标题总结
2. coreMetrics: 核心指标数组，包含履职活动次数、建议提案数、采纳率、投入时间等
3. activityDistribution: 活动类型分布数组
4. timeInvestment: 时间投入分析对象，包含总计、月均、活跃度、月度数据
5. highlights: 履职亮点数组，突出重要成就
6. keywords: 关键词云数组，体现关注领域
7. aiSummary: AI智能总结，包含评价、成就列表、建议列表

要求：
- 数据要基于实际输入进行合理推算和分析
- 语言要正面积极，体现履职成效
- 数字要合理真实，避免夸大
- 突出代表履职的亮点和特色
- 提供有建设性的改进建议

详细履职记录和意见建议数据：
{source_data}
"""
        return prompt


class DataCheckService:
    """数据检查服务"""
    
    @staticmethod
    def check_analysis_data(representative, year):
        """检查指定年份是否有分析数据"""
        start_date = date(year, 1, 1)
        end_date = date(year, 12, 31)
        
        # 检查履职记录
        has_performance_data = PerformanceRecord.objects.filter(
            representative=representative,
            performance_date__range=[start_date, end_date]
        ).exists()
        
        # 检查意见建议
        has_opinion_data = OpinionSuggestion.objects.filter(
            representative=representative,
            created_at__year=year
        ).exists()
        
        # 检查是否有完成的AI总结
        completed_summary = RepresentativeAISummary.objects.filter(
            representative=representative,
            analysis_year=year,
            status='completed'
        ).first()
        
        has_data = has_performance_data or has_opinion_data
        
        result = {
            'has_data': has_data,
            'has_completed_summary': completed_summary is not None,
            'summary_id': completed_summary.id if completed_summary else None
        }
        
        if has_data:
            # 获取数据概要
            performance_count = PerformanceRecord.objects.filter(
                representative=representative,
                performance_date__range=[start_date, end_date]
            ).count()
            
            opinion_count = OpinionSuggestion.objects.filter(
                representative=representative,
                created_at__year=year
            ).count()
            
            result['data_summary'] = {
                'performance_records_count': performance_count,
                'opinion_suggestions_count': opinion_count
            }
        
        return result
```

### 6.4 AI Provider接口

```python
# backend/api/aisummary/ai_providers/__init__.py
from django.conf import settings
from .openai_provider import OpenAIProvider
# from .claude_provider import ClaudeProvider


def get_ai_provider():
    """获取AI提供商实例"""
    provider_name = getattr(settings, 'AI_PROVIDER', 'openai')
    
    if provider_name == 'openai':
        return OpenAIProvider()
    # elif provider_name == 'claude':
    #     return ClaudeProvider()
    else:
        raise ValueError(f"Unsupported AI provider: {provider_name}")


# backend/api/aisummary/ai_providers/base.py
from abc import ABC, abstractmethod


class BaseAIProvider(ABC):
    """AI提供商基类"""
    
    @abstractmethod
    def generate_summary(self, prompt, source_data):
        """生成AI总结"""
        pass


# backend/api/aisummary/ai_providers/openai_provider.py
import json
import openai
from django.conf import settings
from .base import BaseAIProvider


class OpenAIProvider(BaseAIProvider):
    """OpenAI提供商"""
    
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=settings.OPENAI_API_KEY,
            base_url=getattr(settings, 'OPENAI_BASE_URL', None)
        )
        self.model = getattr(settings, 'OPENAI_MODEL', 'gpt-3.5-turbo')
    
    def generate_summary(self, prompt, source_data):
        """调用OpenAI API生成总结"""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一个专业的人大代表履职分析专家，能够基于履职数据生成专业的年度总结报告。请严格按照要求的JSON格式返回结果。"
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=0.7,
                max_tokens=4000
            )
            
            result_text = response.choices[0].message.content.strip()
            
            # 解析JSON结果
            try:
                # 如果结果包含markdown代码块，提取JSON部分
                if result_text.startswith('```json'):
                    result_text = result_text.replace('```json', '').replace('```', '').strip()
                elif result_text.startswith('```'):
                    result_text = result_text.replace('```', '').strip()
                
                ai_result = json.loads(result_text)
                return ai_result
                
            except json.JSONDecodeError as e:
                # 如果解析失败，返回默认结果
                return self._get_fallback_result(source_data)
                
        except Exception as e:
            # API调用失败，返回默认结果
            return self._get_fallback_result(source_data)
    
    def _get_fallback_result(self, source_data):
        """获取默认结果"""
        performance_count = source_data.get('performance_records_count', 0)
        opinion_count = source_data.get('opinion_suggestions_count', 0)
        
        return {
            "overview": {
                "subtitle": f"履职年度总结：{performance_count}项履职活动，{opinion_count}份意见建议"
            },
            "coreMetrics": [
                {
                    "label": "履职活动",
                    "value": f"{performance_count}次",
                    "icon": "Trophy",
                    "color": "#c62d2d",
                    "trend": "+5.0%",
                    "trendType": "positive"
                },
                {
                    "label": "建议提案",
                    "value": f"{opinion_count}份",
                    "icon": "DataLine",
                    "color": "#52c41a",
                    "trend": "+3.0%",
                    "trendType": "positive"
                }
            ],
            "activityDistribution": [
                {
                    "type": "履职活动",
                    "count": performance_count,
                    "percentage": 100,
                    "color": "#c62d2d"
                }
            ],
            "timeInvestment": {
                "total": performance_count * 2,
                "monthly": round(performance_count / 12, 1),
                "activity": 85,
                "monthlyData": [
                    {"month": i, "percentage": 60 + (i % 3) * 15}
                    for i in range(1, 13)
                ]
            },
            "highlights": [
                {
                    "title": "积极履职",
                    "description": f"全年完成履职活动{performance_count}次，提交意见建议{opinion_count}份，展现了良好的履职精神。",
                    "metrics": [
                        {"label": "活动数量", "value": f"{performance_count}次"},
                        {"label": "建议数量", "value": f"{opinion_count}份"}
                    ]
                }
            ],
            "keywords": [
                {"word": "履职尽责", "weight": 0.9},
                {"word": "民生关注", "weight": 0.8},
                {"word": "建言献策", "weight": 0.7}
            ],
            "aiSummary": {
                "evaluation": "本年度履职表现良好，积极参与各类履职活动，认真履行代表职责。",
                "achievements": [
                    f"完成履职活动{performance_count}次，履职积极性较高",
                    f"提交意见建议{opinion_count}份，关注民生热点"
                ],
                "suggestions": [
                    "继续保持履职积极性，深入了解民意",
                    "加强与其他代表的交流合作，形成履职合力"
                ]
            }
        }
```

### 6.5 视图实现

```python
# backend/api/aisummary/views.py
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from api.users.permissions import IsRepresentative
from .models import RepresentativeAISummary
from .serializers import AISummarySerializer, AISummaryCreateSerializer
from .services import AISummaryService, DataCheckService


class AISummaryGenerateView(APIView):
    """AI总结生成视图"""
    
    permission_classes = [IsAuthenticated, IsRepresentative]
    
    def post(self, request):
        """生成AI总结"""
        serializer = AISummaryCreateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'message': '参数错误',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        year = serializer.validated_data['year']
        representative = request.user.representative
        
        try:
            # 生成AI总结
            summary = AISummaryService.generate_summary(representative, year)
            
            # 序列化返回
            result_serializer = AISummarySerializer(summary)
            
            return Response({
                'success': True,
                'message': 'AI分析生成完成' if summary.status == 'completed' else 'AI分析生成中',
                'data': result_serializer.data
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'生成失败：{str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AISummaryDetailView(APIView):
    """AI总结详情视图"""
    
    permission_classes = [IsAuthenticated, IsRepresentative]
    
    def get(self, request, year):
        """获取指定年度的AI总结"""
        representative = request.user.representative
        
        try:
            summary = RepresentativeAISummary.objects.get(
                representative=representative,
                analysis_year=year,
                status='completed'
            )
            
            serializer = AISummarySerializer(summary)
            return Response({
                'success': True,
                'data': serializer.data
            })
            
        except RepresentativeAISummary.DoesNotExist:
            return Response({
                'success': False,
                'message': '未找到该年度的AI分析结果'
            }, status=status.HTTP_404_NOT_FOUND)


class AISummaryRegenerateView(APIView):
    """AI总结重新生成视图"""
    
    permission_classes = [IsAuthenticated, IsRepresentative]
    
    def post(self, request, year):
        """重新生成AI总结"""
        representative = request.user.representative
        
        try:
            # 删除现有的总结
            RepresentativeAISummary.objects.filter(
                representative=representative,
                analysis_year=year
            ).delete()
            
            # 重新生成
            summary = AISummaryService.generate_summary(representative, year)
            
            serializer = AISummarySerializer(summary)
            return Response({
                'success': True,
                'message': 'AI分析重新生成完成' if summary.status == 'completed' else 'AI分析重新生成中',
                'data': serializer.data
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'重新生成失败：{str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AISummaryCheckView(APIView):
    """AI总结数据检查视图"""
    
    permission_classes = [IsAuthenticated, IsRepresentative]
    
    def get(self, request, year):
        """检查指定年度是否有分析数据"""
        representative = request.user.representative
        
        result = DataCheckService.check_analysis_data(representative, year)
        
        return Response({
            'success': True,
            'data': result
        })


class AISummaryListView(APIView):
    """AI总结列表视图"""
    
    permission_classes = [IsAuthenticated, IsRepresentative]
    
    def get(self, request):
        """获取代表的所有AI总结"""
        representative = request.user.representative
        
        summaries = RepresentativeAISummary.objects.filter(
            representative=representative,
            status='completed'
        ).order_by('-analysis_year')
        
        serializer = AISummarySerializer(summaries, many=True)
        return Response({
            'success': True,
            'data': serializer.data
        })
```

## 7. 前端实现

### 7.1 API客户端

```javascript
// frontend/src/api/modules/aisummary/api.js
import { httpClient } from '@/api/http/client'

export const aiSummaryAPI = {
  /**
   * 生成AI分析
   * @param {number} year - 分析年份
   */
  async generateSummary(year) {
    const response = await httpClient.post('/ai-summaries/generate/', { year })
    return response.data
  },

  /**
   * 获取AI分析结果
   * @param {number} year - 分析年份
   */
  async getSummary(year) {
    const response = await httpClient.get(`/ai-summaries/${year}/`)
    return response.data
  },

  /**
   * 重新生成AI分析
   * @param {number} year - 分析年份
   */
  async regenerateSummary(year) {
    const response = await httpClient.post(`/ai-summaries/${year}/regenerate/`)
    return response.data
  },

  /**
   * 检查分析数据
   * @param {number} year - 分析年份
   */
  async checkAnalysisData(year) {
    const response = await httpClient.get(`/ai-summaries/${year}/check/`)
    return response.data
  },

  /**
   * 获取AI分析列表
   */
  async getSummaryList() {
    const response = await httpClient.get('/ai-summaries/')
    return response.data
  }
}
```

### 7.2 页面组件优化

在现有的 `AnnualAchievements.vue` 基础上，需要做以下调整：

```vue
<!-- frontend/src/views/representative/AnnualAchievements.vue -->
<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { aiSummaryAPI } from '@/api/modules/aisummary/api'

// 替换原有的achievementAPI导入
// import achievementAPI from '@/api/achievement'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const selectedYear = ref('')
const loadingAchievements = ref(false)
const loadingProgress = ref(0)
const loadingText = ref('正在分析数据...')
const achievements = ref(null)
const hasAnalysisData = ref(true)

// 可选年份
const availableYears = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = 0; i < 6; i++) {
    years.push(currentYear - i)
  }
  return years
})

// 监听年度变化
watch(selectedYear, async (newYear) => {
  if (newYear) {
    await checkAnalysisData(newYear)
  }
})

// 检查分析数据是否存在
const checkAnalysisData = async (year) => {
  try {
    const response = await aiSummaryAPI.checkAnalysisData(year)
    if (response.success) {
      hasAnalysisData.value = response.data.has_data
      
      // 如果已有完成的AI总结，直接加载
      if (response.data.has_completed_summary) {
        await loadExistingSummary(year)
      }
    }
  } catch (error) {
    console.error('检查分析数据失败:', error)
    hasAnalysisData.value = false
  }
}

// 加载已存在的AI总结
const loadExistingSummary = async (year) => {
  try {
    const response = await aiSummaryAPI.getSummary(year)
    if (response.success) {
      achievements.value = response.data.ai_result
    }
  } catch (error) {
    console.error('加载AI总结失败:', error)
  }
}

// 年度变化处理
const onYearChange = (year) => {
  achievements.value = null
}

// 生成成果展示
const generateAchievements = async () => {
  if (!selectedYear.value) {
    ElMessage.warning('请选择年度')
    return
  }

  if (!hasAnalysisData.value) {
    ElMessage.warning('请先完成该年度的履职记录，系统将自动进行AI分析')
    return
  }

  loadingAchievements.value = true
  loadingProgress.value = 0
  
  // 模拟生成进度
  const progressSteps = [
    { progress: 20, text: '正在收集履职数据...' },
    { progress: 40, text: '正在分析履职记录...' },
    { progress: 60, text: '正在生成AI分析...' },
    { progress: 80, text: '正在优化分析结果...' },
    { progress: 100, text: '生成完成！' }
  ]

  const updateProgress = async () => {
    for (const step of progressSteps) {
      await new Promise(resolve => setTimeout(resolve, 800))
      loadingProgress.value = step.progress
      loadingText.value = step.text
    }
  }

  // 启动进度更新
  updateProgress()

  try {
    const response = await aiSummaryAPI.generateSummary(selectedYear.value)
    
    if (response.success) {
      achievements.value = response.data.ai_result
      ElMessage.success('AI分析生成完成！')
    } else {
      ElMessage.error(response.message || '生成失败，请稍后重试')
    }
  } catch (error) {
    console.error('生成成果展示失败:', error)
    ElMessage.error(error.message || '生成失败，请稍后重试')
  } finally {
    loadingAchievements.value = false
    loadingProgress.value = 0
  }
}

// 重新生成
const regenerateAchievements = async () => {
  const confirm = await ElMessageBox.confirm(
    '重新生成将覆盖当前内容，确定继续吗？',
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).catch(() => false)

  if (confirm) {
    achievements.value = null
    
    try {
      const response = await aiSummaryAPI.regenerateSummary(selectedYear.value)
      
      if (response.success) {
        achievements.value = response.data.ai_result
        ElMessage.success('AI分析重新生成完成！')
      } else {
        ElMessage.error(response.message || '重新生成失败')
      }
    } catch (error) {
      console.error('重新生成失败:', error)
      ElMessage.error('重新生成失败，请稍后重试')
    }
  }
}

// 其他方法保持不变...
</script>
```

## 8. 配置和部署

### 8.1 Django配置

```python
# backend/npcsite/settings.py

# AI相关配置
AI_PROVIDER = 'openai'  # 可选: 'openai', 'claude'

# OpenAI配置（在环境变量中设置）
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
OPENAI_BASE_URL = os.getenv('OPENAI_BASE_URL', '')  # 可选，用于代理
OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')

# 添加新应用
INSTALLED_APPS = [
    # ... 其他应用
    'api.aisummary',
]
```

### 8.2 环境变量

```bash
# .env 文件
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1  # 可选
OPENAI_MODEL=gpt-3.5-turbo  # 可选
```

### 8.3 数据库迁移

```bash
# 创建迁移文件
python manage.py makemigrations aisummary

# 应用迁移
python manage.py migrate
```

## 9. 测试策略

### 9.1 单元测试

```python
# backend/api/aisummary/tests.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from api.users.models import Representative
from .models import RepresentativeAISummary
from .services import AISummaryService, DataCheckService

User = get_user_model()


class AISummaryServiceTest(TestCase):
    """AI总结服务测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='test_rep',
            password='testpass123',
            role='representative'
        )
        self.representative = Representative.objects.create(
            user=self.user,
            name='测试代表',
            level='区级',
            # ... 其他必填字段
        )
    
    def test_data_check_service(self):
        """测试数据检查服务"""
        result = DataCheckService.check_analysis_data(self.representative, 2024)
        
        self.assertIn('has_data', result)
        self.assertIn('has_completed_summary', result)
        self.assertIn('summary_id', result)
    
    def test_ai_summary_creation(self):
        """测试AI总结创建"""
        summary = RepresentativeAISummary.objects.create(
            representative=self.representative,
            analysis_year=2024,
            status='completed',
            ai_result={'test': 'data'}
        )
        
        self.assertTrue(summary.is_ready)
        self.assertEqual(summary.representative, self.representative)
```

### 9.2 API测试

```python
# backend/api/aisummary/tests_api.py
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from api.users.models import Representative

User = get_user_model()


class AISummaryAPITest(APITestCase):
    """AI总结API测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='test_rep',
            password='testpass123',
            role='representative'
        )
        self.representative = Representative.objects.create(
            user=self.user,
            name='测试代表',
            level='区级',
            # ... 其他必填字段
        )
        self.client.force_authenticate(user=self.user)
    
    def test_check_analysis_data(self):
        """测试数据检查API"""
        url = '/api/v1/ai-summaries/2024/check/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('has_data', response.data['data'])
    
    def test_generate_summary_invalid_year(self):
        """测试无效年份生成总结"""
        url = '/api/v1/ai-summaries/generate/'
        data = {'year': 2050}  # 未来年份
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
```

## 10. 性能优化

### 10.1 数据库优化

- 合理使用索引加速查询
- 使用JSON字段存储复杂数据结构
- 限制AI结果的大小（建议不超过1MB）

### 10.2 缓存策略

```python
# 可以考虑对完成的AI总结进行缓存
from django.core.cache import cache

def get_cached_summary(representative_id, year):
    cache_key = f"ai_summary:{representative_id}:{year}"
    return cache.get(cache_key)

def set_cached_summary(representative_id, year, data, timeout=3600*24):
    cache_key = f"ai_summary:{representative_id}:{year}"
    cache.set(cache_key, data, timeout)
```

### 10.3 异步处理

对于耗时较长的AI API调用，可以考虑使用Celery异步处理：

```python
# backend/api/aisummary/tasks.py
from celery import shared_task
from .services import AISummaryService

@shared_task
def generate_ai_summary_async(representative_id, year):
    """异步生成AI总结"""
    from api.users.models import Representative
    
    representative = Representative.objects.get(id=representative_id)
    return AISummaryService.generate_summary(representative, year)
```

## 11. 监控和日志

### 11.1 日志记录

```python
# backend/api/aisummary/services.py
import logging

logger = logging.getLogger('aisummary')

class AISummaryService:
    @staticmethod
    def generate_summary(representative, year):
        logger.info(f"开始生成AI总结: {representative.name} - {year}年")
        
        try:
            # ... 生成逻辑
            logger.info(f"AI总结生成成功: {representative.name} - {year}年")
        except Exception as e:
            logger.error(f"AI总结生成失败: {representative.name} - {year}年, 错误: {str(e)}")
            raise
```

### 11.2 性能监控

- 监控AI API调用时间和成功率
- 记录生成失败的原因和频率
- 监控数据库查询性能

## 12. 安全考虑

### 12.1 权限控制

- 代表只能查看和生成自己的AI总结
- 工作人员不能访问AI总结功能
- 严格验证年份参数，防止SQL注入

### 12.2 数据保护

- AI API密钥安全存储在环境变量中
- 敏感数据不传输给第三方AI API
- 定期清理失败的生成记录

## 13. 扩展规划

### 13.1 功能扩展

- 支持季度和月度分析
- 增加多代表对比分析
- 支持自定义分析模板
- 增加更多AI提供商支持

### 13.2 技术优化

- 引入流式响应提升用户体验
- 使用WebSocket实时更新生成进度
- 支持批量生成多个代表的AI总结
- 增加AI结果质量评估机制

---

**文档版本**: V1.0  
**创建日期**: 2024-12-20  
**最后更新**: 2024-12-20  
**维护人员**: AI助手 