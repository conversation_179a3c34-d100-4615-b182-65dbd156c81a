# AI知识库模块

## 概述
AI知识库模块提供基于Dify的智能法律政策问答服务，集成SSE流式对话和语音转文字功能。

## 功能特性
- 🤖 AI智能问答
- 🎙️ 语音转文字
- 📡 SSE流式响应
- 🔒 JWT认证保护
- 💬 对话上下文管理
- 🧠 深度思考展示
- 📚 参考文件折叠显示
- ⚡ 实时数据流处理

## 核心功能

### 1. 文字聊天 (SSE流式)

#### aiKnowledgeAPI.chatSSE()
创建SSE连接进行AI聊天，支持实时流式响应

**参数：**
- `data`: 聊天请求数据
  - `query`: 用户问题（必填）
  - `conversation_id`: 对话ID（可选，支持上下文）
  - `inputs`: 变量输入（可选）
  - `user`: 用户标识（可选，默认使用用户ID）
- `onMessage`: 消息回调函数
- `onError`: 错误回调函数
- `onComplete`: 完成回调函数

**支持的事件类型：**
- `message`: 流式回答内容
- `message_end`: 消息结束，包含参考文件数据
- `tts_message`: TTS音频消息
- `tts_message_end`: TTS消息结束
- `error`: 错误事件

**使用示例：**
```javascript
import { aiKnowledgeAPI } from '@/api/modules/aiknowledge'

await aiKnowledgeAPI.chatSSE(
  { 
    query: '人大代表的主要职责是什么？',
    conversation_id: null // 新对话
  },
  (data) => {
    if (data.event === 'message' || data.answer) {
      // 处理流式回答内容
      console.log('AI回答片段:', data.answer)
    } else if (data.event === 'message_end') {
      // 处理参考文件和元数据
      console.log('对话ID:', data.conversation_id)
      if (data.metadata?.retriever_resources) {
        console.log('参考文件:', data.metadata.retriever_resources)
      }
    }
  },
  (error) => {
    console.error('SSE流错误:', error)
  },
  () => {
    console.log('对话流结束')
  }
)
```

### 2. 语音转文字

#### aiKnowledgeAPI.audioToText()
将音频文件转换为文字，支持多种音频格式

**参数：**
- `audioFile`: 音频文件对象 (File)

**支持的音频格式：**
- mp3, mp4, mpeg, mpga, m4a, wav, webm

**文件限制：**
- 最大文件大小：50MB
- 自动权限检查和错误处理

**使用示例：**
```javascript
import { aiKnowledgeAPI } from '@/api/modules/aiknowledge'

// 处理音频文件
const handleAudioFile = async (audioFile) => {
  try {
    const result = await aiKnowledgeAPI.audioToText(audioFile)
    
    if (result.success) {
      console.log('转换成功:', result.text)
      // 将文字填入输入框
      currentQuestion.value = result.text
    } else {
      console.error('转换失败:', result.error)
    }
  } catch (error) {
    console.error('语音转文字异常:', error.message)
  }
}

// 录音示例
const recordAndConvert = async () => {
  // 1. 请求麦克风权限
  const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
  
  // 2. 创建录音器
  const mediaRecorder = new MediaRecorder(stream, {
    mimeType: 'audio/webm;codecs=opus'
  })
  
  // 3. 收集音频数据
  const audioChunks = []
  mediaRecorder.ondataavailable = (event) => {
    audioChunks.push(event.data)
  }
  
  // 4. 录音结束处理
  mediaRecorder.onstop = async () => {
    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' })
    const audioFile = new File([audioBlob], 'recording.webm', {
      type: 'audio/webm'
    })
    
    // 转换为文字
    await handleAudioFile(audioFile)
  }
  
  // 开始/停止录音
  mediaRecorder.start()
  // ... 用户操作
  mediaRecorder.stop()
}
```

## 后端接口

### 聊天接口
- `POST /api/v1/aiknowledge/chat/sse/` - SSE流式聊天

**请求体：**
```json
{
  "query": "用户问题",
  "conversation_id": "对话ID（可选）",
  "inputs": {},
  "user": "用户标识"
}
```

**响应格式（SSE）：**
```
data: {"event": "message", "answer": "回答片段", "conversation_id": "xxx"}
data: {"event": "message_end", "conversation_id": "xxx", "metadata": {...}}
```

### 语音转文字接口
- `POST /api/v1/aiknowledge/audio-to-text/` - 语音转文字

**请求体：** FormData
- `file`: 音频文件

**响应格式：**
```json
{
  "success": true,
  "text": "转换后的文字内容",
  "message": "语音转文字成功"
}
```

## 特色功能

### 深度思考解析
自动识别和解析AI回答中的`<think></think>`标签：
- 分离思考过程和最终回答
- 可折叠的思考内容展示
- 实时思考状态指示

### 参考文件管理
智能显示AI回答的参考资料：
- 折叠式设计，不干扰主要内容
- 显示文档名称、相关度评分、位置排序
- 包含数据集信息和片段ID

### 自动滚动优化
智能的页面滚动管理：
- 用户在底部时自动滚动到新内容
- 用户滚动查看历史时不干扰
- DOM变化监听，确保及时滚动

## 技术特点

### 认证机制
- 统一JWT令牌管理
- 自动token刷新
- 401错误自动处理

### 错误处理
- 全局错误拦截
- 用户友好的错误提示
- 网络异常自动重试

### 性能优化
- 局部Loading状态（避免全局Loading）
- 流式数据的高效处理
- 内存优化的音频处理

## 使用注意事项

### 权限要求
- 需要有效的JWT令牌
- 语音功能需要麦克风权限
- 支持跨域CORS配置

### 浏览器兼容性
- 支持现代浏览器的MediaRecorder API
- SSE流式响应支持
- WebAudio API音频处理

### 安全性
- 音频文件大小限制（50MB）
- 文件类型验证
- 用户输入验证和清理

## 开发调试

### 调试日志
```javascript
// 启用详细日志
console.log('🔄 收到SSE数据:', eventData)
console.log('🧠 解析思考内容:', parsedContent)
console.log('📚 参考文件数据:', retrieverResources)
```

### 常见问题
1. **SSE连接失败**：检查网络和token有效性
2. **语音录制失败**：确认麦克风权限
3. **音频转换失败**：检查文件格式和大小
4. **参考文件不显示**：确认后端metadata转发 