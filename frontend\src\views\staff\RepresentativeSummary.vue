<template>
  <div class="representative-summary-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>代表工作总结</h2>
      <p>查看本站点各个代表的年度履职AI分析展示</p>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <div class="filter-section">
        <div class="filter-item">
          <label>分析年度：</label>
          <el-date-picker
            v-model="selectedYear"
            type="year"
            placeholder="选择年度"
            value-format="YYYY"
            style="margin-left: 10px;"
            @change="loadRepresentatives"
          />
        </div>
        <div class="filter-item">
          <label>代表状态：</label>
          <el-select v-model="statusFilter" placeholder="全部状态" style="margin-left: 10px;" @change="filterRepresentatives">
            <el-option label="全部状态" value="" />
            <el-option label="已生成分析" value="analyzed" />
            <el-option label="未生成分析" value="not_analyzed" />
          </el-select>
        </div>

      </div>
    </el-card>

    <!-- 代表列表 -->
    <el-card class="representatives-card">
      <template #header>
        <div class="card-header">
          <span>代表履职分析列表</span>
        </div>
      </template>

      <div class="representatives-list">
        <div v-if="!filteredRepresentatives.length" class="empty-state">
          <el-empty description="暂无代表数据" />
        </div>
        
        <div v-else>
          <div 
            v-for="representative in filteredRepresentatives" 
            :key="representative.id"
            class="representative-item"
          >
            <div class="representative-header">
              <div class="representative-info">
                <div class="representative-name">
                  <el-avatar
                    :size="40"
                    :src="representative.avatar"
                    style="background-color: #c62d2d;"
                  >
                    {{ representative.name.charAt(0) }}
                  </el-avatar>
                  <div class="name-section">
                    <h4>{{ representative.name }}</h4>
                    <span class="representative-level">{{ representative.level }}</span>
                  </div>
                </div>
                <div class="representative-meta">
                  <el-tag size="small" type="info">{{ representative.department }}</el-tag>
                  <span class="contact">{{ representative.phone }}</span>
                </div>
              </div>
            </div>

            <div class="representative-stats">
              <div class="stat-item">
                <span class="stat-label">履职记录</span>
                <span class="stat-value">{{ representative.recordCount }}条</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">提交意见</span>
                <span class="stat-value">{{ representative.opinionCount }}条</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">分析状态</span>
                <el-tag 
                  :type="representative.analysisStatus === 'analyzed' ? 'success' : 'warning'"
                  size="small"
                >
                  {{ representative.analysisStatus === 'analyzed' ? '已生成' : '未生成' }}
                </el-tag>
              </div>
            </div>

            <div class="representative-actions">
              <el-button 
                v-if="representative.analysisStatus === 'analyzed'"
                type="primary" 
                size="small"
                @click="viewAnalysis(representative)"
              >
                <el-icon><View /></el-icon>
                查看分析
              </el-button>
              <el-button 
                type="success" 
                size="small"
                @click="generateAnalysis(representative)"
                :loading="generatingIds.includes(representative.id)"
              >
                <el-icon><Refresh /></el-icon>
                {{ representative.analysisStatus === 'analyzed' ? '重新生成' : '生成分析' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>



    <!-- 分析结果查看弹窗 -->
    <el-dialog 
      v-model="analysisDialogVisible" 
      :title="`${currentAnalysis?.representative_name} - ${currentAnalysis?.analysis_year}年度履职分析报告`"
      width="90%"
      class="analysis-dialog"
      :close-on-click-modal="false"
    >
      <div v-if="currentAnalysis && currentAnalysis.ai_result_data" class="analysis-content">
        <!-- 使用AIReportRenderer组件展示报告 -->
        <AIReportRenderer 
          :data="currentAnalysis.ai_result_data" 
          :representative-info="currentAnalysis.representative_info || currentRepresentative"
          :generated-time="currentAnalysis.completed_at"
        />
      </div>
      <div v-else class="no-analysis">
        <el-empty description="暂无分析数据" />
      </div>
      
      <template #footer>
        <div class="actions-section">
          <div class="action-buttons">
            <el-button 
              type="primary" 
              :loading="isExporting" 
              @click="exportPDFReport"
            >
              <el-icon><Download /></el-icon>
              导出PDF报告
            </el-button>
            <el-button @click="analysisDialogVisible = false">关闭</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Document, View, Refresh, Download,
  DataLine, TrendCharts, Star, Trophy, User
} from '@element-plus/icons-vue'

import { 
  getRepresentativesList, 
  generateRepresentativeAnalysis,
  getRepresentativeAnalysis
} from '@/api/workAnalysis'
import AIReportRenderer from '@/components/performance/AIReportRenderer.vue'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

export default {
  name: 'RepresentativeSummary',
  components: {
    Document, View, Refresh, Download,
    DataLine, TrendCharts, Star, Trophy, User,
    AIReportRenderer
  },
  setup() {
    // 响应式数据
    const selectedYear = ref(new Date().getFullYear().toString())
    const statusFilter = ref('')
    const isLoading = ref(false)
    const isGenerating = ref(false)
    const representatives = ref([])
    const generatingIds = ref([])
    

    
    // 分析查看相关
    const analysisDialogVisible = ref(false)
    const currentRepresentative = ref(null)
    const currentAnalysis = ref(null)
    const isExporting = ref(false)

    // 计算属性 - 根据状态筛选代表列表
    const filteredRepresentatives = computed(() => {
      if (!statusFilter.value) {
        return representatives.value
      }
      
      return representatives.value.filter(rep => {
        if (statusFilter.value === 'analyzed') {
          return rep.analysisStatus === 'analyzed'
        } else if (statusFilter.value === 'not_analyzed') {
          return rep.analysisStatus === 'not_analyzed'
        }
        return true
      })
    })

    // 加载代表列表
    const loadRepresentatives = async () => {
      if (!selectedYear.value) return
      
      isLoading.value = true
      try {
        const response = await getRepresentativesList(selectedYear.value)
        if (response.success) {
          representatives.value = response.data
        } else {
          throw new Error(response.message || '加载失败')
        }
      } catch (error) {
        console.error('加载代表列表失败：', error)
        ElMessage.error('加载代表列表失败')
      } finally {
        isLoading.value = false
      }
    }

    // 筛选代表
    const filterRepresentatives = () => {
      // 筛选后重新加载数据
      loadRepresentatives()
    }

    // 为单个代表生成分析
    const generateAnalysis = async (representative) => {
      generatingIds.value.push(representative.id)
      
      try {
        ElMessage.info(`正在为${representative.name}生成分析报告...`)
        
        const response = await generateRepresentativeAnalysis(representative.id, selectedYear.value)
        
        if (response.success) {
          // 更新代表状态
          const index = representatives.value.findIndex(rep => rep.id === representative.id)
          if (index !== -1) {
            representatives.value[index].analysisStatus = 'analyzed'
            representatives.value[index].lastAnalysisTime = new Date().toLocaleString()
          }
          
          // 处理不同的响应状态
          if (response.status === 'completed') {
            ElMessage.success(`${representative.name}的分析报告生成成功！`)
          } else if (response.status === 'generating') {
            ElMessage.info(`${representative.name}的分析报告正在生成中，请稍后查看结果`)
          } else {
            ElMessage.success(`${representative.name}的分析报告已开始生成`)
          }
        } else {
          throw new Error(response.message || '生成失败')
        }
      } catch (error) {
        console.error('生成分析失败：', error)
        
        // 根据错误类型显示不同的友好提示
        if (error.details && error.details.error === 'data_insufficient') {
          // 数据不足的友好提示
          ElMessage({
            type: 'warning',
            message: `${representative.name}在${selectedYear.value}年暂无履职数据`,
            description: '该代表在选定年份没有履职记录和意见建议，无法生成AI分析报告。请选择其他年份或等待履职数据产生后再试。',
            duration: 6000,
            showClose: true
          })
        } else {
          // 其他错误的通用提示
          const errorMsg = error.message || '网络错误或服务器异常，请稍后重试'
          ElMessage.error(`${representative.name}的分析报告生成失败：${errorMsg}`)
        }
      } finally {
        const index = generatingIds.value.indexOf(representative.id)
        if (index > -1) {
          generatingIds.value.splice(index, 1)
        }
      }
    }



    // 查看分析结果
    const viewAnalysis = async (representative) => {
      currentRepresentative.value = representative
      
      try {
        const response = await getRepresentativeAnalysis(representative.id, selectedYear.value)
        if (response.success) {
          currentAnalysis.value = response.data
          analysisDialogVisible.value = true
        } else {
          throw new Error(response.message || '获取分析结果失败')
        }
      } catch (error) {
        console.error('获取分析结果失败：', error)
        ElMessage.error('获取分析结果失败')
      }
    }

    // 导出PDF报告
    const exportPDFReport = async () => {
      if (!currentRepresentative.value || !currentAnalysis.value) {
        ElMessage.error('没有可导出的报告数据')
        return
      }

      isExporting.value = true
      try {
        ElMessage.info('正在生成PDF，请稍候...')
        
        // 查找报告渲染器组件
        const reportElement = document.querySelector('.ai-report-renderer')
        if (!reportElement) {
          ElMessage.error('未找到报告内容')
          return
        }

        // 临时设置样式以优化PDF输出
        const originalOverflow = reportElement.style.overflow
        const originalHeight = reportElement.style.height
        const originalMaxWidth = reportElement.style.maxWidth
        const originalWidth = reportElement.style.width
        const originalPadding = reportElement.style.padding
        
        reportElement.style.overflow = 'visible'
        reportElement.style.height = 'auto'
        reportElement.style.maxWidth = '1000px' // 设置更宽的最大宽度
        reportElement.style.width = '1000px'    // 固定宽度以确保一致性
        reportElement.style.padding = '20px'    // 适当的内边距

        // 使用html2canvas截图
        const canvas = await html2canvas(reportElement, {
          scale: 2, // 提高清晰度
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          width: reportElement.scrollWidth,
          height: reportElement.scrollHeight,
          scrollX: 0,
          scrollY: 0,
          // 优化PDF输出的选项
          removeContainer: true,
          imageTimeout: 15000,
          logging: false
        })

        // 恢复原始样式
        reportElement.style.overflow = originalOverflow
        reportElement.style.height = originalHeight
        reportElement.style.maxWidth = originalMaxWidth
        reportElement.style.width = originalWidth
        reportElement.style.padding = originalPadding

        // 创建PDF
        const imgData = canvas.toDataURL('image/jpeg', 0.8)
        const pdf = new jsPDF({
          orientation: 'portrait',
          unit: 'mm',
          format: 'a4'
        })

        // A4页面尺寸 (210 x 297 mm)
        const pageWidth = 210
        const pageHeight = 297
        const margin = 5 // 减小边距，充分利用纸张空间
        const contentWidth = pageWidth - (margin * 2)
        const contentHeight = pageHeight - (margin * 2)

        // 计算图片在PDF中的尺寸 - 优化宽度适配
        const imgWidth = canvas.width
        const imgHeight = canvas.height
        
        // 优先按宽度适配，充分利用纸张宽度
        const widthRatio = contentWidth / (imgWidth * 0.264583)
        const heightRatio = contentHeight / (imgHeight * 0.264583)
        
        // 使用宽度比例，除非高度超出太多
        let ratio = widthRatio
        const projectedHeight = imgHeight * 0.264583 * widthRatio
        
        // 如果按宽度缩放后高度过高，则适当调整
        if (projectedHeight > contentHeight * 3) { // 允许3页的高度
          ratio = heightRatio
        }
        
        const pdfImgWidth = Math.min(imgWidth * 0.264583 * ratio, contentWidth)
        const pdfImgHeight = imgHeight * 0.264583 * ratio

        // 如果内容高度超过一页，需要分页
        if (pdfImgHeight > contentHeight) {
          // 计算需要多少页
          const totalPages = Math.ceil(pdfImgHeight / contentHeight)
          
          for (let page = 0; page < totalPages; page++) {
            if (page > 0) {
              pdf.addPage()
            }
            
            // 计算当前页要显示的部分
            const sourceY = (imgHeight / totalPages) * page
            const sourceHeight = imgHeight / totalPages
            
            // 创建当前页的canvas
            const pageCanvas = document.createElement('canvas')
            pageCanvas.width = imgWidth
            pageCanvas.height = sourceHeight
            const pageCtx = pageCanvas.getContext('2d')
            
            pageCtx.drawImage(canvas, 0, sourceY, imgWidth, sourceHeight, 0, 0, imgWidth, sourceHeight)
            
            const pageImgData = pageCanvas.toDataURL('image/jpeg', 0.8)
            const pageImgHeight = sourceHeight * 0.264583 * ratio
            
            pdf.addImage(pageImgData, 'JPEG', margin, margin, pdfImgWidth, pageImgHeight)
          }
        } else {
          // 单页内容 - 居中显示，如果宽度已经充满则左对齐
          const x = pdfImgWidth >= contentWidth ? margin : margin + (contentWidth - pdfImgWidth) / 2
          const y = margin
          pdf.addImage(imgData, 'JPEG', x, y, pdfImgWidth, pdfImgHeight)
        }

        // 生成文件名
        const representativeName = currentRepresentative.value.name
        const year = selectedYear.value
        const timestamp = new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')
        const filename = `${representativeName}-${year}年度履职AI分析报告-${timestamp}.pdf`

        // 下载PDF
        pdf.save(filename)
        ElMessage.success('PDF导出成功！')
        
      } catch (error) {
        console.error('PDF导出失败:', error)
        ElMessage.error('PDF导出失败，请重试')
      } finally {
        isExporting.value = false
      }
    }

    onMounted(() => {
      loadRepresentatives()
    })

    return {
      selectedYear,
      statusFilter,
      isLoading,
      isGenerating,
      representatives,
      filteredRepresentatives,
      generatingIds,
      analysisDialogVisible,
      currentRepresentative,
      currentAnalysis,
      isExporting,
      loadRepresentatives,
      filterRepresentatives,
      generateAnalysis,
      viewAnalysis,
      exportPDFReport
    }
  }
}
</script>

<style scoped>
.representative-summary-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  text-align: center;
}

.page-header h2 {
  color: #c62d2d;
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: bold;
}

.page-header p {
  color: #666;
  margin: 0;
  font-size: 16px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  padding: 20px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-item label {
  font-weight: bold;
  color: #333;
  white-space: nowrap;
}

.representatives-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.representatives-list {
  min-height: 200px;
}

.representative-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 15px;
  padding: 20px;
  background: white;
  transition: all 0.3s;
}

.representative-item:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}



.representative-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.representative-info {
  flex: 1;
}

.representative-name {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.name-section h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.representative-level {
  color: #666;
  font-size: 14px;
}

.representative-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.contact {
  color: #666;
  font-size: 14px;
}

.representative-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.representative-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}



.analysis-content {
  max-height: 80vh;
  overflow-y: auto;
}

/* 操作按钮区域 */
.actions-section {
  text-align: center;
  padding: 20px 0;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  padding: 10px 20px;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 50px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .representative-summary-container {
    padding: 10px;
  }

  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .filter-item {
    justify-content: space-between;
  }

  .header-actions {
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
  }

  .representative-stats {
    flex-direction: column;
    gap: 15px;
  }

  .representative-actions {
    flex-direction: column;
  }

  .analysis-dialog {
    width: 95% !important;
  }
}
</style> 