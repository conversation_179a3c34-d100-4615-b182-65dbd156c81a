@font-face {
  font-family: "iconfont"; /* Project id 4978134 */
  src: url('iconfont.woff2?t=1752806216271') format('woff2'),
       url('iconfont.woff?t=1752806216271') format('woff'),
       url('iconfont.ttf?t=1752806216271') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-mti-nan:before {
  content: "\e8cd";
}

.icon-mti-wode:before {
  content: "\e8ce";
}

.icon-mti-nv:before {
  content: "\e8cf";
}

.icon-mti-tuandui:before {
  content: "\e8d0";
}

.icon-mti-renyuanjuji:before {
  content: "\e8d1";
}

