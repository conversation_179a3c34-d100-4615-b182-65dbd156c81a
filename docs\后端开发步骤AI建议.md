# 后端开发步骤AI建议

## 概述

基于数据库设计和Django REST Framework最佳实践，本文档提供了NPC履职服务与管理平台后端的详细开发步骤建议。

## 技术栈

- **Django 5.2 LTS** - Web框架
- **Django REST Framework** - API框架
- **MySQL** - 数据库
- **uv** - 包管理工具
- **drf-spectacular** - API文档生成

## 第一阶段：项目基础设施搭建 (1-2天)

### 1. 项目初始化

```bash
# 创建项目目录结构
cd backend/
uv init --name npc_backend
uv add django==5.2
uv add djangorestframework
uv add django-cors-headers
uv add drf-spectacular  # Swagger文档
uv add PyMySQL
uv add python-decouple
uv add django-filter
uv add celery  # 异步任务
```

### 2. 项目结构设计

```
backend/
├── apps/
│   ├── users/           # 用户认证系统
│   ├── representatives/ # 代表信息管理  
│   ├── opinions/        # 意见建议核心
│   ├── work_plans/      # 工作计划管理
│   ├── notifications/   # 通知系统
│   └── dashboard/       # 工作台概览
├── config/              # 项目配置
│   ├── settings/
│   │   ├── base.py
│   │   ├── development.py
│   │   └── production.py
│   ├── urls.py
│   ├── wsgi.py
│   └── asgi.py
├── utils/               # 通用工具
├── requirements/        # 依赖管理
└── docs/               # 文档
```

### 3. 基础配置

#### config/settings/base.py
```python
from decouple import config
import os
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent.parent

SECRET_KEY = config('SECRET_KEY', default='your-secret-key-here')
DEBUG = config('DEBUG', default=False, cast=bool)

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    
    # Third party apps
    'rest_framework',
    'rest_framework.authtoken',
    'corsheaders',
    'drf_spectacular',
    'django_filters',
    
    # Local apps
    'apps.users',
    'apps.representatives',
    'apps.opinions',
    'apps.work_plans',
    'apps.notifications',
    'apps.dashboard',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'config.urls'

# 自定义用户模型
AUTH_USER_MODEL = 'users.User'

# DRF配置
REST_FRAMEWORK = {
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.TokenAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.OrderingFilter',
        'rest_framework.filters.SearchFilter',
    ],
}

# Swagger配置
SPECTACULAR_SETTINGS = {
    'TITLE': 'NPC履职服务与管理平台 API',
    'DESCRIPTION': '人大代表履职服务与管理平台后端API文档',
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    'COMPONENT_SPLIT_REQUEST': True,
}

# 数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DB_NAME', default='npc_db'),
        'USER': config('DB_USER', default='root'),
        'PASSWORD': config('DB_PASSWORD', default=''),
        'HOST': config('DB_HOST', default='localhost'),
        'PORT': config('DB_PORT', default='3306'),
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
    }
}

# 国际化
LANGUAGE_CODE = 'zh-hans'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_TZ = True
```

## 第二阶段：核心模型实现 (2-3天)

### 开发优先级顺序：

1. **用户认证系统** (`apps/users/`)
2. **人大代表信息** (`apps/representatives/`)  
3. **意见建议核心** (`apps/opinions/`)
4. **通知系统** (`apps/notifications/`)

### 1. 用户模型

#### apps/users/models.py
```python
from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    """扩展的用户模型"""
    ROLE_CHOICES = [
        ('representative', '人大代表'),
        ('staff', '站点工作人员'),
    ]
    
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, verbose_name='用户角色')
    is_active = models.BooleanField(default=True, verbose_name='账号是否激活')
    mobile_phone = models.CharField(max_length=11, blank=True, verbose_name='手机号码')
    last_login_at = models.DateTimeField(null=True, blank=True, verbose_name='最后登录时间')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'

    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"
```

### 2. 代表信息模型

#### apps/representatives/models.py
```python
from django.db import models
from django.conf import settings

class Representative(models.Model):
    """人大代表信息"""
    GENDER_CHOICES = [
        ('male', '男'),
        ('female', '女'),
    ]
    
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE,
        verbose_name='关联用户'
    )
    level = models.CharField(max_length=50, verbose_name='代表层级')
    name = models.CharField(max_length=50, verbose_name='姓名')
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, verbose_name='性别')
    nationality = models.CharField(max_length=50, verbose_name='民族')
    birth_date = models.DateField(verbose_name='出生日期')
    birthplace = models.CharField(max_length=100, verbose_name='籍贯')
    party = models.CharField(max_length=50, verbose_name='党派')
    current_position = models.CharField(max_length=100, verbose_name='现任职务')
    mobile_phone = models.CharField(max_length=11, verbose_name='移动电话号码')
    education = models.CharField(max_length=50, verbose_name='学历')
    graduated_school = models.CharField(max_length=100, blank=True, verbose_name='毕业院校')
    major = models.CharField(max_length=100, blank=True, verbose_name='所学专业')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'representatives'
        verbose_name = '人大代表'
        verbose_name_plural = '人大代表'

    def __str__(self):
        return f"{self.name} - {self.level}"

class PerformanceRecord(models.Model):
    """履职记录"""
    PERFORMANCE_TYPE_CHOICES = [
        ('meeting', '会议参与'),
        ('proposal', '议案建议'),
        ('research', '调研走访'),
        ('supervision', '监督检查'),
        ('training', '学习培训'),
        ('other', '其他'),
    ]
    
    STATUS_CHOICES = [
        ('in_progress', '进行中'),
        ('completed', '已完成'),
        ('paused', '已暂停'),
    ]
    
    representative = models.ForeignKey(
        Representative, 
        on_delete=models.CASCADE,
        related_name='performance_records',
        verbose_name='关联代表'
    )
    performance_date = models.DateField(verbose_name='履职日期')
    performance_type = models.CharField(
        max_length=50, 
        choices=PERFORMANCE_TYPE_CHOICES,
        verbose_name='履职类型'
    )
    performance_content = models.TextField(verbose_name='履职内容')
    activity_location = models.CharField(max_length=200, verbose_name='活动地点')
    detailed_description = models.TextField(blank=True, verbose_name='详细描述')
    performance_status = models.CharField(
        max_length=50,
        choices=STATUS_CHOICES,
        default='completed',
        verbose_name='履职状态'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'performance_records'
        verbose_name = '履职记录'
        verbose_name_plural = '履职记录'
        ordering = ['-performance_date']

    def __str__(self):
        return f"{self.representative.name} - {self.performance_content[:20]}"
```

## 第三阶段：MVP核心功能API (3-4天)

### 开发顺序建议：

#### 1. 用户认证API (高优先级)

#### apps/users/serializers.py
```python
from rest_framework import serializers
from django.contrib.auth import authenticate
from .models import User

class LoginSerializer(serializers.Serializer):
    """登录序列化器"""
    username = serializers.CharField(max_length=150)
    password = serializers.CharField(max_length=128, write_only=True)
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError('用户名或密码错误')
            if not user.is_active:
                raise serializers.ValidationError('用户账号已被禁用')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('用户名和密码不能为空')
        
        return attrs

class UserSerializer(serializers.ModelSerializer):
    """用户信息序列化器"""
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    
    class Meta:
        model = User
        fields = ['id', 'username', 'role', 'role_display', 'mobile_phone', 
                 'is_active', 'last_login_at', 'created_at']
        read_only_fields = ['id', 'created_at']

class PasswordChangeSerializer(serializers.Serializer):
    """密码修改序列化器"""
    old_password = serializers.CharField(max_length=128, write_only=True)
    new_password = serializers.CharField(max_length=128, write_only=True)
    confirm_password = serializers.CharField(max_length=128, write_only=True)
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('原密码错误')
        return value
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError('新密码与确认密码不一致')
        return attrs
```

#### apps/users/views.py
```python
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authtoken.models import Token
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.contrib.auth import login, logout
from django.utils import timezone
from drf_spectacular.utils import extend_schema, OpenApiResponse

from .serializers import LoginSerializer, UserSerializer, PasswordChangeSerializer
from .models import User

class LoginAPIView(APIView):
    """用户登录API"""
    permission_classes = [AllowAny]
    
    @extend_schema(
        summary="用户登录",
        description="通过用户名和密码进行登录认证",
        request=LoginSerializer,
        responses={
            200: OpenApiResponse(description="登录成功"),
            400: OpenApiResponse(description="登录失败"),
        }
    )
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # 更新最后登录时间
            user.last_login_at = timezone.now()
            user.save(update_fields=['last_login_at'])
            
            # 获取或创建token
            token, created = Token.objects.get_or_create(user=user)
            
            # 序列化用户信息
            user_data = UserSerializer(user).data
            
            return Response({
                'success': True,
                'message': '登录成功',
                'data': {
                    'user': user_data,
                    'token': token.key
                }
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': '登录失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

class LogoutAPIView(APIView):
    """用户登出API"""
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        summary="用户登出",
        description="用户登出，删除token",
        responses={
            200: OpenApiResponse(description="登出成功"),
        }
    )
    def post(self, request):
        try:
            # 删除用户的token
            Token.objects.filter(user=request.user).delete()
            return Response({
                'success': True,
                'message': '登出成功'
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'success': False,
                'message': '登出失败'
            }, status=status.HTTP_400_BAD_REQUEST)

class UserProfileAPIView(APIView):
    """用户个人信息API"""
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        summary="获取用户信息",
        description="获取当前登录用户的个人信息",
        responses={
            200: UserSerializer,
        }
    )
    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)

class PasswordChangeAPIView(APIView):
    """密码修改API"""
    permission_classes = [IsAuthenticated]
    
    @extend_schema(
        summary="修改密码",
        description="修改当前用户的登录密码",
        request=PasswordChangeSerializer,
        responses={
            200: OpenApiResponse(description="密码修改成功"),
            400: OpenApiResponse(description="密码修改失败"),
        }
    )
    def post(self, request):
        serializer = PasswordChangeSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            user = request.user
            user.set_password(serializer.validated_data['new_password'])
            user.save()
            
            # 删除所有token，强制重新登录
            Token.objects.filter(user=user).delete()
            
            return Response({
                'success': True,
                'message': '密码修改成功，请重新登录'
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': '密码修改失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
```

#### apps/users/urls.py
```python
from django.urls import path
from .views import (
    LoginAPIView, LogoutAPIView, 
    UserProfileAPIView, PasswordChangeAPIView
)

urlpatterns = [
    path('login/', LoginAPIView.as_view(), name='login'),
    path('logout/', LogoutAPIView.as_view(), name='logout'),
    path('profile/', UserProfileAPIView.as_view(), name='user-profile'),
    path('change-password/', PasswordChangeAPIView.as_view(), name='change-password'),
]
```

## 第四阶段：业务逻辑完善 (2-3天)

### 1. 权限控制系统

#### utils/permissions.py
```python
from rest_framework.permissions import BasePermission

class IsRepresentativeOnly(BasePermission):
    """只允许人大代表访问"""
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and 
            request.user.role == 'representative'
        )

class IsStaffOnly(BasePermission):
    """只允许站点工作人员访问"""
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and 
            request.user.role == 'staff'
        )

class IsRepresentativeOrStaff(BasePermission):
    """允许代表或工作人员访问"""
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and 
            request.user.role in ['representative', 'staff']
        )

class IsOwnerOrStaff(BasePermission):
    """只允许对象拥有者或工作人员访问"""
    def has_object_permission(self, request, view, obj):
        # 工作人员可以访问所有对象
        if request.user.role == 'staff':
            return True
        
        # 检查对象是否属于当前用户
        if hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'representative'):
            return obj.representative.user == request.user
        
        return False
```

### 2. 状态管理服务

#### apps/opinions/services.py
```python
from django.db import transaction
from .models import OpinionSuggestion, OpinionReview

class OpinionStatusService:
    """意见建议状态管理服务"""
    
    ACTION_STATUS_MAP = {
        'submit': 'submitted',           # 代表提交意见
        'approve': 'approved',           # 工作人员审核通过
        'reject': 'rejected',            # 工作人员审核驳回
        'transfer': 'transferred',       # 工作人员标记转交
        'update_progress': 'in_progress', # 工作人员更新处理进展
        'close': 'completed'             # 工作人员标记办结
    }
    
    @classmethod
    @transaction.atomic
    def create_review(cls, opinion_id, reviewer_id, action, **kwargs):
        """创建审核记录，自动设置对应状态"""
        status = cls.ACTION_STATUS_MAP.get(action)
        if not status:
            raise ValueError(f"Invalid action: {action}")
        
        # 验证必填字段
        cls._validate_required_fields(action, kwargs)
        
        # 创建审核记录
        review = OpinionReview.objects.create(
            opinion_id=opinion_id,
            reviewer_id=reviewer_id,
            action=action,
            status=status,
            **kwargs
        )
        
        return review
    
    @classmethod
    def _validate_required_fields(cls, action, kwargs):
        """验证必填字段"""
        if action == 'transfer' and not kwargs.get('transferred_department'):
            raise ValueError("transferred_department is required for transfer action")
        
        if action == 'reject' and not kwargs.get('review_comment'):
            raise ValueError("review_comment is required for reject action")
        
        if action in ['update_progress', 'close'] and not kwargs.get('processing_result'):
            raise ValueError("processing_result is required for update_progress/close action")
    
    @classmethod
    def get_current_status(cls, opinion_id):
        """获取意见建议当前状态"""
        latest_review = OpinionReview.objects.filter(
            opinion_id=opinion_id
        ).order_by('-action_time').first()
        
        return latest_review.status if latest_review else 'draft'
    
    @classmethod
    def get_status_history(cls, opinion_id):
        """获取状态变更历史"""
        return OpinionReview.objects.filter(
            opinion_id=opinion_id
        ).order_by('action_time')
```

## 第五阶段：工作台与统计功能 (2天)

### 工作台概览API

#### apps/dashboard/views.py
```python
from rest_framework.views import APIView
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Count, Q
from drf_spectacular.utils import extend_schema

from utils.permissions import IsRepresentativeOnly, IsStaffOnly
from apps.representatives.models import PerformanceRecord
from apps.opinions.models import OpinionSuggestion, OpinionReview

class RepresentativeDashboardAPIView(APIView):
    """人大代表工作台概览"""
    permission_classes = [IsRepresentativeOnly]
    
    @extend_schema(
        summary="代表工作台概览",
        description="获取代表的工作台统计数据",
    )
    def get(self, request):
        user = request.user
        representative = user.representative
        current_month = timezone.now().month
        current_year = timezone.now().year
        
        # 本月履职记录数量
        monthly_performance = PerformanceRecord.objects.filter(
            representative=representative,
            performance_date__year=current_year,
            performance_date__month=current_month
        ).count()
        
        # 待处理意见数量（未办结的意见）
        pending_opinions = self._get_pending_opinions_count(representative)
        
        # 本年度履职记录数量
        yearly_performance = PerformanceRecord.objects.filter(
            representative=representative,
            performance_date__year=current_year
        ).count()
        
        # 近期履职活动
        recent_activities = PerformanceRecord.objects.filter(
            representative=representative
        ).order_by('-performance_date')[:5]
        
        from apps.representatives.serializers import PerformanceRecordSerializer
        recent_activities_data = PerformanceRecordSerializer(
            recent_activities, many=True
        ).data
        
        return Response({
            'success': True,
            'data': {
                'monthly_performance_count': monthly_performance,
                'yearly_performance_count': yearly_performance,
                'pending_opinions_count': pending_opinions,
                'recent_activities': recent_activities_data,
            }
        })
    
    def _get_pending_opinions_count(self, representative):
        """获取待处理意见数量"""
        # 获取所有意见的最新状态
        opinions = OpinionSuggestion.objects.filter(representative=representative)
        pending_count = 0
        
        for opinion in opinions:
            latest_review = OpinionReview.objects.filter(
                opinion=opinion
            ).order_by('-action_time').first()
            
            if not latest_review or latest_review.status != 'completed':
                pending_count += 1
        
        return pending_count

class StaffDashboardAPIView(APIView):
    """站点工作人员工作台概览"""
    permission_classes = [IsStaffOnly]
    
    @extend_schema(
        summary="工作人员工作台概览",
        description="获取工作人员的工作台统计数据",
    )
    def get(self, request):
        current_month = timezone.now().month
        current_year = timezone.now().year
        
        # 待审核意见数量
        pending_review_count = self._get_pending_review_count()
        
        # 本月已办结意见数量
        monthly_completed_count = self._get_monthly_completed_count(current_year, current_month)
        
        # 本月代表履职统计
        monthly_representative_stats = self._get_monthly_representative_stats(current_year, current_month)
        
        return Response({
            'success': True,
            'data': {
                'pending_review_count': pending_review_count,
                'monthly_completed_count': monthly_completed_count,
                'monthly_representative_stats': monthly_representative_stats,
            }
        })
    
    def _get_pending_review_count(self):
        """获取待审核意见数量"""
        # 查找状态为已提交的意见
        pending_opinions = []
        all_opinions = OpinionSuggestion.objects.all()
        
        for opinion in all_opinions:
            latest_review = OpinionReview.objects.filter(
                opinion=opinion
            ).order_by('-action_time').first()
            
            if latest_review and latest_review.status == 'submitted':
                pending_opinions.append(opinion)
        
        return len(pending_opinions)
    
    def _get_monthly_completed_count(self, year, month):
        """获取本月已办结意见数量"""
        completed_reviews = OpinionReview.objects.filter(
            status='completed',
            action_time__year=year,
            action_time__month=month
        )
        return completed_reviews.count()
    
    def _get_monthly_representative_stats(self, year, month):
        """获取本月代表履职统计"""
        from apps.representatives.models import Representative
        
        stats = []
        representatives = Representative.objects.all()
        
        for rep in representatives:
            monthly_count = PerformanceRecord.objects.filter(
                representative=rep,
                performance_date__year=year,
                performance_date__month=month
            ).count()
            
            stats.append({
                'representative_name': rep.name,
                'monthly_performance_count': monthly_count
            })
        
        return stats
```

## 第六阶段：外部服务集成 (1-2天)

### AI服务集成

#### utils/ai_service.py
```python
import requests
import logging
from django.conf import settings
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class AIService:
    """AI服务接口封装"""
    
    def __init__(self):
        self.base_url = getattr(settings, 'AI_API_BASE_URL', '')
        self.api_key = getattr(settings, 'AI_API_KEY', '')
        self.timeout = getattr(settings, 'AI_API_TIMEOUT', 30)
    
    def generate_suggestion(self, original_content: str) -> Optional[Dict[str, Any]]:
        """调用外部AI生成意见建议"""
        try:
            response = requests.post(
                f"{self.base_url}/generate-suggestion",
                json={
                    'content': original_content,
                    'api_key': self.api_key
                },
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            response.raise_for_status()
            result = response.json()
            
            logger.info(f"AI suggestion generated successfully for content length: {len(original_content)}")
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"AI service request failed: {str(e)}")
            return None
        except ValueError as e:
            logger.error(f"AI service response parsing failed: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in AI service: {str(e)}")
            return None
    
    def analyze_performance(self, performance_data: list) -> Optional[Dict[str, Any]]:
        """调用外部AI分析履职记录"""
        try:
            response = requests.post(
                f"{self.base_url}/analyze-performance",
                json={
                    'performance_data': performance_data,
                    'api_key': self.api_key
                },
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            response.raise_for_status()
            result = response.json()
            
            logger.info(f"AI performance analysis completed for {len(performance_data)} records")
            return result
            
        except Exception as e:
            logger.error(f"AI performance analysis failed: {str(e)}")
            return None
    
    def query_knowledge(self, query: str) -> Optional[Dict[str, Any]]:
        """调用外部AI知识问答"""
        try:
            response = requests.post(
                f"{self.base_url}/query-knowledge",
                json={
                    'query': query,
                    'api_key': self.api_key
                },
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            response.raise_for_status()
            result = response.json()
            
            logger.info(f"AI knowledge query completed for: {query[:50]}...")
            return result
            
        except Exception as e:
            logger.error(f"AI knowledge query failed: {str(e)}")
            return None

# 创建全局实例
ai_service = AIService()
```

## 第七阶段：测试与文档 (1-2天)

### 1. 单元测试示例

#### apps/users/tests.py
```python
from django.test import TestCase
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token

User = get_user_model()

class UserAuthTestCase(APITestCase):
    """用户认证相关测试"""
    
    def setUp(self):
        self.user_data = {
            'username': 'testuser',
            'password': 'testpass123',
            'role': 'representative'
        }
        self.user = User.objects.create_user(**self.user_data)
    
    def test_login_success(self):
        """测试登录成功"""
        response = self.client.post('/api/v1/auth/login/', {
            'username': 'testuser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('token', response.data['data'])
        self.assertIn('user', response.data['data'])
    
    def test_login_failure(self):
        """测试登录失败"""
        response = self.client.post('/api/v1/auth/login/', {
            'username': 'testuser',
            'password': 'wrongpassword'
        })
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
    
    def test_logout_success(self):
        """测试登出成功"""
        token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + token.key)
        
        response = self.client.post('/api/v1/auth/logout/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # 验证token已被删除
        self.assertFalse(Token.objects.filter(user=self.user).exists())
    
    def test_change_password_success(self):
        """测试密码修改成功"""
        token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + token.key)
        
        response = self.client.post('/api/v1/auth/change-password/', {
            'old_password': 'testpass123',
            'new_password': 'newpass123',
            'confirm_password': 'newpass123'
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # 验证密码已修改
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('newpass123'))
```

### 2. API文档配置

#### config/urls.py
```python
from django.contrib import admin
from django.urls import path, include
from drf_spectacular.views import (
    SpectacularAPIView, 
    SpectacularSwaggerView,
    SpectacularRedocView
)

urlpatterns = [
    path('admin/', admin.site.urls),
    
    # API路由
    path('api/v1/auth/', include('apps.users.urls')),
    path('api/v1/representatives/', include('apps.representatives.urls')),
    path('api/v1/opinions/', include('apps.opinions.urls')),
    path('api/v1/dashboard/', include('apps.dashboard.urls')),
    
    # API文档
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
]
```

## 开发建议与最佳实践

### 1. 错误处理

#### utils/exceptions.py
```python
from rest_framework.views import exception_handler
from rest_framework.response import Response
import logging

logger = logging.getLogger(__name__)

def custom_exception_handler(exc, context):
    """自定义异常处理器"""
    response = exception_handler(exc, context)
    
    if response is not None:
        # 记录错误日志
        logger.error(f"API Error: {exc}", exc_info=True)
        
        custom_response_data = {
            'success': False,
            'message': '请求处理失败',
            'errors': response.data,
            'status_code': response.status_code
        }
        response.data = custom_response_data
    
    return response
```

### 2. 统一响应格式

#### utils/response.py
```python
from rest_framework.response import Response
from rest_framework import status

class APIResponse:
    """统一API响应格式"""
    
    @staticmethod
    def success(data=None, message="操作成功", status_code=status.HTTP_200_OK):
        return Response({
            'success': True,
            'message': message,
            'data': data
        }, status=status_code)
    
    @staticmethod
    def error(message="操作失败", errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            'success': False,
            'message': message,
            'errors': errors
        }, status=status_code)
    
    @staticmethod
    def paginated_success(data, message="获取成功"):
        return Response({
            'success': True,
            'message': message,
            'data': data
        }, status=status.HTTP_200_OK)
```

### 3. 开发节奏建议

#### 第1-2周：基础建设
- 完成项目初始化和基础配置
- 实现用户认证系统  
- 搭建基础API框架
- 完成核心模型设计

#### 第3周：核心业务
- 意见建议完整流程API
- 代表信息管理API
- 基础权限控制系统
- 工作台概览功能

#### 第4周：完善优化
- 外部服务集成（AI接口）
- 性能优化和索引调优
- 单元测试和集成测试
- API文档完善

### 4. 部署准备

#### requirements/production.txt
```
Django==5.2
djangorestframework
django-cors-headers
drf-spectacular
PyMySQL
python-decouple
gunicorn
redis
celery
django-redis
```

#### 生产环境配置
```python
# config/settings/production.py
from .base import *

DEBUG = False
ALLOWED_HOSTS = ['your-domain.com']

# 生产数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DB_NAME'),
        'USER': config('DB_USER'),
        'PASSWORD': config('DB_PASSWORD'),
        'HOST': config('DB_HOST'),
        'PORT': config('DB_PORT'),
    }
}

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': config('REDIS_URL'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/npc_backend/django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## 总结

这个开发步骤建议确保了：

1. **渐进式开发**：从基础设施到核心业务逐步推进
2. **风险可控**：关键功能优先实现，及早验证
3. **代码质量**：遵循Django和DRF最佳实践
4. **可维护性**：清晰的代码结构和完善的文档
5. **可扩展性**：为后续功能迭代预留空间

按照这个步骤实施，可以在4周内完成一个功能完整、代码规范的MVP版本。 