/**
 * 地图数据API - 江南区街道代表分布
 * 提供地图边界数据和街道代表数量统计
 */

/**
 * 获取街道代表数量统计数据
 * @returns {Promise<Object>} 街道代表数量数据
 */
export const getStreetRepresentativeCount = async () => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 800))
  
  try {
    // 模拟江南区街道代表数量数据 - 简化版本，只包含街道名称和代表人数
    const mockData = {
      code: 200,
      message: '数据获取成功',
      data: {
        list: [
          {
            streetName: '吴圩镇',
            representativeCount: 42
          },
          {
            streetName: '延安镇',
            representativeCount: 38
          },
          {
            streetName: '江西镇',
            representativeCount: 35
          },
          {
            streetName: '苏圩镇',
            representativeCount: 28
          },
          {
            streetName: '金凯街道',
            representativeCount: 32
          },
          {
            streetName: '江南街道',
            representativeCount: 45
          },
          {
            streetName: '福建园街道',
            representativeCount: 41
          },
          {
            streetName: '沙井街道',
            representativeCount: 36
          },
          {
            streetName: '那洪街道',
            representativeCount: 29
          },
          {
            streetName: '明阳工业园区',
            representativeCount: 18
          },
          {
            streetName: '经济技术开发区',
            representativeCount: 22
          }
        ]
      }
    }
    
    console.log('🔄 模拟API返回数据:', mockData)
    return mockData
  } catch (error) {
    console.error('❌ 模拟API异常:', error)
    return {
      code: 500,
      message: '服务器内部错误',
      data: null
    }
  }
}

/**
 * 获取街道详细信息
 * @param {string} streetCode - 街道代码
 * @returns {Promise<Object>} 街道详细信息
 */
export const getStreetDetail = async (streetCode) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  
  try {
    // 根据街道代码返回详细信息
    const streetDetails = {
      'JN001': {
        streetCode: 'JN001',
        streetName: '吴圩镇',
        representativeCount: 42,
        totalPopulation: 85000,
        coverage: '98.5%',
        area: '323.5平方公里',
        committees: 15,
        villages: 8,
        description: '吴圩镇位于江南区南部，是重要的工业和农业基地。',
        keyProjects: ['南宁国际机场', '吴圩工业园', '现代农业示范区'],
        lastUpdate: '2024-01-15 14:30:00'
      },
      'JN002': {
        streetCode: 'JN002',
        streetName: '延安镇',
        representativeCount: 38,
        totalPopulation: 72000,
        coverage: '96.8%',
        area: '155.5平方公里',
        committees: 12,
        villages: 6,
        description: '延安镇历史悠久，文化底蕴深厚，是江南区的重要组成部分。',
        keyProjects: ['延安文化园', '生态农业基地', '乡村振兴示范点'],
        lastUpdate: '2024-01-15 14:25:00'
      }
      // 可以继续添加其他街道的详细信息...
    }
    
    const detail = streetDetails[streetCode]
    if (detail) {
      return {
        code: 200,
        message: '获取成功',
        data: detail
      }
    } else {
      return {
        code: 404,
        message: '街道信息未找到',
        data: null
      }
    }
    
  } catch (error) {
    console.error('❌ 获取街道详细信息失败:', error)
    return {
      code: 500,
      message: '获取失败',
      data: null
    }
  }
} 