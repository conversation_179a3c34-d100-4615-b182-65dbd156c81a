// 密码管理API模块

/**
 * 修改密码
 * @param {Object} passwordData - 密码数据
 * @param {string} passwordData.currentPassword - 当前密码
 * @param {string} passwordData.newPassword - 新密码
 * @returns {Promise} 返回修改结果
 */
export const changePassword = async (passwordData) => {
  try {
    // 模拟API调用
    return await mockChangePasswordAPI(passwordData)
  } catch (error) {
    throw error
  }
}

/**
 * 验证当前密码
 * @param {string} currentPassword - 当前密码
 * @returns {Promise} 返回验证结果
 */
export const verifyCurrentPassword = async (currentPassword) => {
  try {
    // 模拟API调用
    return await mockVerifyPasswordAPI(currentPassword)
  } catch (error) {
    throw error
  }
}

/**
 * 获取密码策略配置
 * @returns {Promise} 返回密码策略
 */
export const getPasswordPolicy = async () => {
  try {
    // 模拟返回密码策略
    return {
      success: true,
      data: {
        minLength: 6,
        maxLength: 20,
        requireNumbers: true,
        requireLetters: true,
        requireSpecialChars: false,
        forbidCommonPasswords: true
      }
    }
  } catch (error) {
    throw error
  }
}

// ================== 模拟API函数 ==================

/**
 * 模拟密码修改API
 * @param {Object} passwordData - 密码数据
 * @returns {Promise} 返回修改结果
 */
const mockChangePasswordAPI = (passwordData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 模拟当前密码验证
      if (passwordData.currentPassword !== '123456') {
        reject({
          response: {
            data: {
              success: false,
              code: 'PWD_ERR_001',
              message: '当前密码输入错误'
            }
          }
        })
        return
      }
      
      // 模拟新密码强度检查
      if (passwordData.newPassword.length < 6) {
        reject({
          response: {
            data: {
              success: false,
              code: 'PWD_ERR_003',
              message: '密码强度不够，请重新设置'
            }
          }
        })
        return
      }
      
      // 模拟成功响应
      resolve({
        success: true,
        message: '密码修改成功',
        data: {
          timestamp: new Date().toISOString(),
          userId: 'user_123'
        }
      })
    }, 1500)
  })
}

/**
 * 模拟密码验证API
 * @param {string} currentPassword - 当前密码
 * @returns {Promise} 返回验证结果
 */
const mockVerifyPasswordAPI = (currentPassword) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (currentPassword === '123456') {
        resolve({
          success: true,
          message: '密码验证成功'
        })
      } else {
        reject({
          response: {
            data: {
              success: false,
              code: 'PWD_ERR_001',
              message: '密码验证失败'
            }
          }
        })
      }
    }, 500)
  })
}

export default {
  changePassword,
  verifyCurrentPassword,
  getPasswordPolicy
} 