/**
 * AI总结模块配置
 * 统一管理AI相关的超时时间和配置参数
 */

// AI总结相关的超时配置
export const AI_SUMMARY_CONFIG = {
  // 请求超时时间（毫秒）
  REQUEST_TIMEOUT: 120000, // 2分钟
  
  // 状态轮询配置
  POLLING: {
    MAX_ATTEMPTS: 60, // 最大轮询次数
    INTERVAL: 2000,   // 轮询间隔（毫秒）
    TIMEOUT: 120000   // 总轮询超时时间（毫秒）
  },
  
  // 进度模拟配置
  PROGRESS_SIMULATION: {
    STEPS: [
      { progress: 20, text: '正在收集履职数据...' },
      { progress: 40, text: '分析履职类型分布...' },
      { progress: 60, text: '提取履职亮点...' },
      { progress: 80, text: 'AI智能总结生成中...' },
      { progress: 95, text: '完善可视化图表...' }
    ],
    INTERVAL: 1500 // 进度更新间隔（毫秒）
  },
  
  // 错误消息配置
  ERROR_MESSAGES: {
    TIMEOUT: 'AI分析生成超时，Dify服务响应时间较长。您可以稍后刷新页面查看结果，或者重新生成。',
    GENERATION_FAILED: 'AI分析生成失败，请稍后重试',
    POLLING_TIMEOUT: 'AI总结生成超时，请稍后手动刷新查看结果',
    NO_DATA: '该年度暂无履职数据，无法生成AI分析',
    NETWORK_ERROR: '网络连接失败，请检查网络状态后重试'
  },
  
  // 成功消息配置
  SUCCESS_MESSAGES: {
    GENERATION_COMPLETED: 'AI履职分析生成完成！',
    REGENERATION_COMPLETED: 'AI履职分析重新生成完成！',
    DATA_LOADED: 'AI分析数据加载完成！'
  }
}

// AI总结状态常量
export const AI_SUMMARY_STATUS = {
  PENDING: 'pending',
  GENERATING: 'generating', 
  COMPLETED: 'completed',
  FAILED: 'failed'
}

// 导出所有配置
export default {
  AI_SUMMARY_CONFIG,
  AI_SUMMARY_STATUS
} 