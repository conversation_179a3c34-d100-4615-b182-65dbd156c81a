# 意见建议管理设计文档

## 1. 文档概述

### 1.1 文档目的
本文档基于用户故事清单、用户旅程图和数据库设计，详细阐述意见建议管理模块的设计思路、业务流程、技术架构和实现方案。

### 1.2 设计范围
- **核心流程**: 代表意见提交 → AI辅助优化 → 站点审核 → 部门转交 → 进展跟踪 → 办结反馈
- **涉及角色**: 人大代表、站点工作人员
- **技术特色**: AI辅助生成、状态流转管理、全程可追溯

## 2. 业务需求分析

### 2.1 核心用户故事映射

#### 代表端用户故事 (US-IM系列)
```
US-IM-001: 录入意见建议 + AI辅助生成
US-IM-002: 审核修改并提交到站点
US-IM-006: 查看处理状态和最终结果
```

#### 工作人员端用户故事
```
US-IM-003: 审核代表提交的意见建议
US-IM-004: 更新办理进展和结果
US-IM-005: 标记意见办结
```

### 2.2 用户旅程关键触点

#### 代表旅程设计亮点
- **降低门槛**: 简化录入流程，支持AI辅助
- **质量把控**: 代表可审核修改AI内容
- **闭环反馈**: 全程状态可跟踪

#### 工作人员旅程设计亮点  
- **高效审核**: 集中展示待审核意见
- **流程标准化**: 明确的状态流转规则
- **信息完整性**: 处理结果结构化记录

## 3. 系统架构设计

### 3.1 核心实体关系

```mermaid
erDiagram
    Representative ||--o{ OpinionSuggestion : "提交"
    OpinionSuggestion ||--o{ OpinionReview : "审核记录"
    StaffMember ||--o{ OpinionReview : "审核操作"
    
    OpinionSuggestion {
        bigint id PK
        bigint representative_id FK
        varchar(200) title "标题"
        varchar(50) category "分类"
        varchar(100) reporter_name "反映人"
        text original_content "原始内容"
        text final_suggestion "最终建议"
        boolean ai_assisted "AI辅助标识"
        text ai_generated_content "AI生成内容"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    OpinionReview {
        bigint id PK
        bigint opinion_id FK
        bigint reviewer_id FK
        varchar(50) action "操作动作"
        varchar(50) status "操作后状态"
        varchar(100) transferred_department "转交部门"
        text review_comment "审核备注"
        text processing_result "处理结果"
        text attachments "附件路径JSON"
        datetime action_time "操作时间"
        datetime created_at "创建时间"
    }
```

### 3.2 状态管理设计

#### 核心设计理念
基于数据库设计的记忆，意见建议的状态管理完全移到`OpinionReview`实体中：
- `OpinionSuggestion`只保存基础信息，不含状态字段
- `OpinionReview`记录每次状态变更和审核动作
- 当前状态通过查询最新审核记录获得

#### 状态流转图
```mermaid
stateDiagram-v2
    [*] --> draft: 代表创建草稿
    draft --> submitted: 代表提交(submit)
    submitted --> approved: 工作人员审核通过(approve)
    submitted --> rejected: 工作人员驳回(reject)
    rejected --> submitted: 代表重新提交
    approved --> transferred: 工作人员转交部门(transfer)
    transferred --> in_progress: 更新处理进展(update_progress)
    in_progress --> in_progress: 持续更新进展
    in_progress --> completed: 标记办结(close)
    transferred --> completed: 直接办结
```

#### 状态-操作映射
```python
ACTION_STATUS_MAP = {
    'submit': 'submitted',           # 代表提交意见
    'approve': 'approved',           # 工作人员审核通过
    'reject': 'rejected',            # 工作人员审核驳回
    'transfer': 'transferred',       # 工作人员标记转交
    'update_progress': 'in_progress', # 工作人员更新处理进展
    'close': 'completed'             # 工作人员标记办结
}
```

## 4. AI功能设计

### 4.1 AI需求分析

#### 问题识别
- 群众原始意见表达不规范
- 代表整理意见耗时费力
- 意见建议专业性不足
- 处理效率有待提升

#### 解决方案
- 理解原始意见核心诉求
- 生成规范化专业建议
- 提供可操作解决方案
- 保持代表最终决定权

### 4.2 AI功能架构

#### 核心API设计
```python
# AI意见建议生成
POST /api/v1/ai/generate-suggestion/
{
    "original_content": "群众原始意见内容",
    "category": "意见分类",
    "context": "相关背景信息"
}

# 响应示例
{
    "success": true,
    "data": {
        "generated_suggestion": "AI生成的规范化建议",
        "key_points": ["要点1", "要点2", "要点3"],
        "suggested_actions": ["建议措施1", "建议措施2"],
        "confidence_score": 0.85
    }
}
```

#### AI能力层次设计
```
核心需求（Must Have）:
├── 意见建议生成
├── 内容规范化
└── 关键点提取

增强需求（Should Have）:
├── 智能分类建议
├── 相似案例推荐
└── 处理建议生成

扩展需求（Could Have）:
├── 多轮对话优化
├── 个性化建议
└── 批量处理
```

## 5. 技术实现方案

### 5.1 后端API设计

#### 5.1.1 意见建议基础操作
```python
# 创建意见建议
POST /api/v1/opinions/
{
    "title": "意见建议标题",
    "category": "urban_construction",
    "reporter_name": "张三",
    "original_content": "原始意见内容",
    "ai_assisted": false
}

# 获取意见建议列表
GET /api/v1/opinions/?status=submitted&page=1&size=10

# 获取意见建议详情
GET /api/v1/opinions/{id}/

# 更新意见建议
PUT /api/v1/opinions/{id}/
{
    "final_suggestion": "最终建议内容"
}
```

#### 5.1.2 AI辅助功能
```python
# 请求AI生成建议
POST /api/v1/opinions/{id}/ai-generate/
{
    "context": "补充背景信息"
}

# 保存AI生成内容
POST /api/v1/opinions/{id}/save-ai-content/
{
    "ai_generated_content": "AI生成的建议内容",
    "use_as_final": true
}
```

#### 5.1.3 审核流程操作
```python
# 提交审核
POST /api/v1/opinions/{id}/submit/

# 审核操作
POST /api/v1/opinions/{id}/review/
{
    "action": "approve",
    "comment": "审核通过",
    "transferred_department": "市交通局"
}

# 更新处理进展
POST /api/v1/opinions/{id}/update-progress/
{
    "processing_result": "部门已受理，正在调研中",
    "attachments": ["file1.pdf", "file2.jpg"]
}
```

### 5.2 前端组件设计

#### 5.2.1 代表端组件结构
```
OpinionManagement/
├── OpinionList.vue           # 意见列表页
├── OpinionForm.vue           # 意见录入表单
├── OpinionDetail.vue         # 意见详情页
├── AIAssistant.vue           # AI辅助组件
└── StatusTracker.vue         # 状态跟踪组件
```

#### 5.2.2 工作人员端组件结构
```
OpinionReview/
├── ReviewList.vue            # 待审核列表
├── ReviewDetail.vue          # 审核详情页
├── ReviewForm.vue            # 审核操作表单
├── ProgressUpdate.vue        # 进展更新组件
└── StatisticsDashboard.vue   # 统计看板
```

### 5.3 数据库查询优化

#### 5.3.1 获取意见当前状态
```sql
-- 获取意见建议的当前状态（通过最新审核记录）
SELECT 
    os.*,
    or_latest.status as current_status,
    or_latest.action_time as last_action_time,
    or_latest.transferred_department,
    sm.name as last_reviewer_name
FROM opinion_suggestions os
LEFT JOIN (
    SELECT 
        opinion_id,
        status,
        action_time,
        transferred_department,
        reviewer_id,
        ROW_NUMBER() OVER (PARTITION BY opinion_id ORDER BY action_time DESC) as rn
    FROM opinion_reviews
) or_latest ON os.id = or_latest.opinion_id AND or_latest.rn = 1
LEFT JOIN staff_members sm ON or_latest.reviewer_id = sm.id
WHERE os.representative_id = ?;
```

#### 5.3.2 获取待处理的意见建议
```sql
-- 站点工作人员待审核的意见建议
SELECT 
    os.*,
    r.name as representative_name,
    or_latest.status as current_status
FROM opinion_suggestions os
JOIN representatives r ON os.representative_id = r.id
JOIN (
    SELECT 
        opinion_id,
        status,
        ROW_NUMBER() OVER (PARTITION BY opinion_id ORDER BY action_time DESC) as rn
    FROM opinion_reviews
) or_latest ON os.id = or_latest.opinion_id AND or_latest.rn = 1
WHERE or_latest.status = 'submitted'
ORDER BY os.created_at DESC;
```

#### 5.3.3 统计查询
```sql
-- 人大代表工作台统计
SELECT 
    (SELECT COUNT(*) FROM performance_records 
     WHERE representative_id = ? AND MONTH(performance_date) = MONTH(CURRENT_DATE())) as monthly_performance_count,
    (SELECT COUNT(*) FROM opinion_suggestions os
     JOIN (
         SELECT opinion_id, status, ROW_NUMBER() OVER (PARTITION BY opinion_id ORDER BY action_time DESC) as rn
         FROM opinion_reviews
     ) or_latest ON os.id = or_latest.opinion_id AND or_latest.rn = 1
     WHERE os.representative_id = ? AND or_latest.status NOT IN ('completed')) as pending_opinions_count;

-- 站点工作人员工作台统计
SELECT 
    (SELECT COUNT(*) FROM opinion_suggestions os
     JOIN (
         SELECT opinion_id, status, ROW_NUMBER() OVER (PARTITION BY opinion_id ORDER BY action_time DESC) as rn
         FROM opinion_reviews
     ) or_latest ON os.id = or_latest.opinion_id AND or_latest.rn = 1
     WHERE or_latest.status = 'submitted') as pending_review_count,
    (SELECT COUNT(*) FROM opinion_suggestions os
     JOIN (
         SELECT opinion_id, status, action_time, ROW_NUMBER() OVER (PARTITION BY opinion_id ORDER BY action_time DESC) as rn
         FROM opinion_reviews
     ) or_latest ON os.id = or_latest.opinion_id AND or_latest.rn = 1
     WHERE or_latest.status = 'completed' AND MONTH(or_latest.action_time) = MONTH(CURRENT_DATE())) as monthly_completed_count;
```

## 6. 用户体验设计

### 6.1 代表端UX设计

#### 6.1.1 意见录入流程优化
```
录入优化策略:
├── 智能表单预填充
├── 分类标签快速选择
├── 语音转文字输入（远期）
└── 实时保存草稿
```

#### 6.1.2 AI辅助交互设计
```
AI交互流程:
录入原始内容 → 点击AI辅助 → 展示生成结果 → 对比原文修改 → 确认使用
```

#### 6.1.3 状态跟踪可视化
```
状态展示设计:
├── 时间轴展示处理进程
├── 状态标签颜色区分
├── 关键节点消息提醒
└── 处理结果文档下载
```

### 6.2 工作人员端UX设计

#### 6.2.1 审核工作台设计
```
工作台布局:
├── 待办事项卡片
├── 紧急程度标识
├── 批量操作功能
└── 快速筛选条件
```

#### 6.2.2 审核操作流程
```
审核流程:
查看详情 → 评估内容 → 选择操作 → 填写备注 → 确认提交
```

## 7. 权限控制设计

### 7.1 角色权限矩阵

| 操作 | 人大代表 | 站点工作人员 |
|------|----------|-------------|
| 创建意见建议 | ✅ | ❌ |
| 查看自己的意见 | ✅ | ❌ |
| 使用AI辅助 | ✅ | ❌ |
| 审核意见建议 | ❌ | ✅ |
| 查看所有意见 | ❌ | ✅ |
| 更新处理进展 | ❌ | ✅ |
| 标记办结 | ❌ | ✅ |

### 7.2 数据访问控制
```python
# 代表只能访问自己提交的意见
class RepresentativeOpinionPermission(BasePermission):
    def has_object_permission(self, request, view, obj):
        return obj.representative.user == request.user

# 工作人员可以访问所有意见
class StaffOpinionPermission(BasePermission):
    def has_permission(self, request, view):
        return request.user.role == 'staff'
```

## 8. 通知机制设计

### 8.1 通知触发节点
```
关键通知节点:
├── 代表提交意见 → 通知工作人员
├── 工作人员审核 → 通知代表
├── 状态变更 → 通知相关人员
└── 长期未处理 → 催办提醒
```

### 8.2 通知实现方案
```python
# 通知服务设计
class NotificationService:
    def notify_opinion_submitted(self, opinion):
        # 通知工作人员有新意见待审核
        pass
    
    def notify_opinion_reviewed(self, opinion, action):
        # 通知代表审核结果
        pass
    
    def notify_progress_updated(self, opinion):
        # 通知代表处理进展
        pass
```

## 9. 测试策略

### 9.1 单元测试
```python
# 状态流转测试
def test_opinion_status_flow():
    # 测试从草稿到办结的完整流程
    pass

# AI功能测试  
def test_ai_suggestion_generation():
    # 测试AI建议生成功能
    pass

# 权限控制测试
def test_permission_control():
    # 测试不同角色的访问权限
    pass
```

### 9.2 集成测试
```python
# 端到端流程测试
def test_complete_opinion_workflow():
    # 测试从提交到办结的完整业务流程
    pass

# API接口测试
def test_opinion_api_endpoints():
    # 测试所有API接口的正确性
    pass
```

## 10. 性能优化

### 10.1 数据库优化
```sql
-- 关键索引
CREATE INDEX idx_opinion_suggestions_representative_id ON opinion_suggestions(representative_id);
CREATE INDEX idx_opinion_reviews_opinion_id ON opinion_reviews(opinion_id);
CREATE INDEX idx_opinion_reviews_action_time ON opinion_reviews(action_time);

-- 复合索引
CREATE INDEX idx_opinion_reviews_status_time ON opinion_reviews(status, action_time);
```

### 10.2 缓存策略
```python
# 意见状态缓存
@cache_result(timeout=300)
def get_opinion_current_status(opinion_id):
    # 缓存意见当前状态，减少复杂查询
    pass

# 统计数据缓存
@cache_result(timeout=3600)  
def get_opinion_statistics():
    # 缓存统计数据，提高仪表板性能
    pass
```

## 11. 部署和运维

### 11.1 部署清单
```
部署组件:
├── 后端API服务
├── 前端静态资源
├── AI服务接口
├── 数据库迁移脚本
└── 初始化数据脚本
```

### 11.2 监控指标
```
关键监控指标:
├── API响应时间
├── AI服务可用性
├── 数据库查询性能
├── 用户操作统计
└── 错误率监控
```

## 12. 总结

意见建议管理模块作为系统的核心业务模块，通过以下设计亮点实现了高效的群众意见处理流程：

### 12.1 设计亮点
1. **AI赋能**: 通过AI辅助提升意见建议质量
2. **状态分离**: 创新的状态管理设计确保数据一致性
3. **全程追溯**: 完整的审核记录支持全流程跟踪
4. **用户友好**: 优化的交互流程提升用户体验

### 12.2 技术创新
1. **状态管理**: OpinionReview独立管理状态变更
2. **AI集成**: 无缝集成AI服务提升工作效率
3. **权限控制**: 精细化的角色权限管理
4. **性能优化**: 合理的索引和缓存策略

通过这套设计方案，意见建议管理模块将为人大代表履职和站点工作管理提供强有力的技术支撑。

## 13. 数据库设计一致性确认

### 13.1 表结构修正记录
本文档已与数据库设计文档完全同步，修正内容包括：

#### OpinionSuggestion 表修正
- ✅ 补充 `created_at` (DATETIME) 字段
- ✅ 补充 `updated_at` (DATETIME) 字段  
- ✅ 明确字段长度：`title` VARCHAR(200)、`category` VARCHAR(50)、`reporter_name` VARCHAR(100)

#### OpinionReview 表修正
- ✅ 补充 `attachments` (TEXT) 字段 - JSON格式存储附件路径
- ✅ 补充 `created_at` (DATETIME) 字段
- ✅ 明确字段长度：`action` VARCHAR(50)、`status` VARCHAR(50)、`transferred_department` VARCHAR(100)

#### 数据库查询修正
- ✅ 统一字段名称：`last_action_time`、`last_reviewer_name`
- ✅ 补充待处理意见建议查询语句
- ✅ 补充人大代表工作台统计查询

### 13.2 一致性保证
- 表结构定义与 `docs/需求分析/数据库设计.md` 完全一致
- 查询语句与数据库设计文档中的示例查询保持同步
- 字段类型、长度限制、约束条件完全匹配
- 状态管理逻辑与数据库设计理念一致 