<template>
  <div class="password-change-container">
    <div class="page-header">
      <h2>账号密码修改</h2>
      <p>为了您的账号安全，请定期更新密码</p>
    </div>

    <el-card class="change-form-card">
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="formRules"
        label-width="120px"
        size="large"
        @submit.prevent="submitForm"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
            :disabled="loading"
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
            :disabled="loading"
          />
          <div class="password-tips">
            <p>密码要求：</p>
            <ul>
              <li>长度至少8位</li>
              <li>建议包含字母和数字</li>
              <li>不建议使用纯数字或纯字母</li>
            </ul>
          </div>
        </el-form-item>

        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
            :disabled="loading"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="submitForm"
            style="width: 200px;"
          >
            {{ loading ? '修改中...' : '修改密码' }}
          </el-button>
          <el-button @click="resetForm" :disabled="loading" style="margin-left: 20px;">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 密码修改记录（可选） -->
    <el-card class="change-history-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>安全提示</span>
        </div>
      </template>
      <div class="security-tips">
        <el-alert
          title="安全建议"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul>
              <li>定期更换密码，建议每3-6个月更换一次</li>
              <li>不要在多个系统中使用相同密码</li>
              <li>密码修改后，系统将自动退出，需要重新登录</li>
              <li>如遇密码遗忘，请联系站点工作人员重置</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { authAPI } from '@/api'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const passwordFormRef = ref()

// 加载状态
const loading = ref(false)

// 表单数据
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 自定义验证函数
const validateNewPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入新密码'))
  } else if (value.length < 6) {
    callback(new Error('密码长度至少6位'))
  } else if (value === passwordForm.currentPassword) {
    callback(new Error('新密码不能与当前密码相同'))
  } else {
    // 触发确认密码的重新验证
    if (passwordForm.confirmPassword) {
      passwordFormRef.value.validateField('confirmPassword', () => {})
    }
    callback()
  }
}

const validateConfirmPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请确认新密码'))
  } else if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const formRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { validator: validateNewPassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 提交表单
const submitForm = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    loading.value = true

    // 调用密码修改API - 使用后端期望的字段名
    const response = await authAPI.changePassword({
      old_password: passwordForm.currentPassword,
      new_password: passwordForm.newPassword,
      confirm_password: passwordForm.confirmPassword
    })

    // 解析响应数据
    const responseData = response.data
    
    if (responseData.success) {
      ElMessage.success(responseData.message || '密码修改成功！系统将自动退出，请重新登录')
      
      // 清空表单
      resetForm()
      
      // 延迟退出登录
      setTimeout(async () => {
        await userStore.logout()
        router.push('/login')
      }, 2000)
    } else {
      ElMessage.error(responseData.message || '密码修改失败，请重试')
    }

  } catch (error) {
    console.error('密码修改失败:', error)
    
    // 处理API错误响应
    if (error.response?.data) {
      const errorData = error.response.data
      
      // 处理字段级别的验证错误
      if (errorData.errors) {
        const errorMessages = []
        
        // 处理具体字段错误
        if (errorData.errors.old_password) {
          errorMessages.push(Array.isArray(errorData.errors.old_password) ? 
            errorData.errors.old_password[0] : errorData.errors.old_password)
        }
        if (errorData.errors.new_password) {
          errorMessages.push(Array.isArray(errorData.errors.new_password) ? 
            errorData.errors.new_password[0] : errorData.errors.new_password)
        }
        if (errorData.errors.confirm_password) {
          errorMessages.push(Array.isArray(errorData.errors.confirm_password) ? 
            errorData.errors.confirm_password[0] : errorData.errors.confirm_password)
        }
        
        // 处理非字段错误
        if (errorData.errors.non_field_errors) {
          errorMessages.push(...errorData.errors.non_field_errors)
        }
        
        ElMessage.error(errorMessages.join('；') || '密码修改失败，请重试')
      } else {
        ElMessage.error(errorData.message || '密码修改失败，请重试')
      }
    } else {
      //ElMessage.error('网络错误，请检查网络连接')
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
}


</script>

<style scoped>
.password-change-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  color: var(--china-red);
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.change-form-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.password-tips {
  margin-top: 8px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid var(--china-red);
}

.password-tips p {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.password-tips ul {
  margin: 0;
  padding-left: 20px;
}

.password-tips li {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 4px;
}

.change-history-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.security-tips {
  padding: 0;
}

.security-tips :deep(.el-alert__content) {
  padding-left: 8px;
}

.security-tips ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.security-tips li {
  font-size: 13px;
  line-height: 1.6;
  margin-bottom: 6px;
  color: #666;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-button--primary) {
  background-color: var(--china-red);
  border-color: var(--china-red);
}

:deep(.el-button--primary:hover) {
  background-color: #a52525;
  border-color: #a52525;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .password-change-container {
    padding: 16px;
  }
  
  :deep(.el-form-item__label) {
    width: 100px !important;
  }
  
  .page-header h2 {
    font-size: 20px;
  }
}
</style> 