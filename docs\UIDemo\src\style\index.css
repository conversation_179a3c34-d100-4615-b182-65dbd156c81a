/* 中国红主题色彩定义 */
:root {
  --china-red: #C8102E; /* 主要红色 */
  --china-red-light: #E53935; /* 亮红色 */
  --china-red-dark: #B71C1C; /* 深红色 */
  --gold: #FFD700; /* 金色 */
  --bg-color: #f5f5f5; /* 背景色 */
  --text-color: #333; /* 文字颜色 */
  --border-color: #e4e7ed; /* 边框颜色 */
}

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
}

/* Element Plus 主题定制 */
.el-button--primary {
  background-color: var(--china-red) !important;
  border-color: var(--china-red) !important;
}

.el-button--primary:hover {
  background-color: var(--china-red-light) !important;
  border-color: var(--china-red-light) !important;
}

.el-menu--horizontal .el-menu-item.is-active {
  border-bottom-color: var(--china-red) !important;
  color: var(--china-red) !important;
}

.el-menu-item:hover {
  background-color: rgba(200, 16, 46, 0.1) !important;
  color: var(--china-red) !important;
}

/* 登录页面样式 */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #C8102E 0%, #8B0000 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 400px;
  max-width: 90vw;
}

.login-title {
  text-align: center;
  margin-bottom: 30px;
  color: var(--china-red);
  font-size: 24px;
  font-weight: bold;
}

.login-subtitle {
  text-align: center;
  margin-bottom: 30px;
  color: #666;
  font-size: 14px;
}

/* 仪表盘样式 */
.dashboard-container {
  padding: 20px;
}

.welcome-card {
  background: linear-gradient(135deg, var(--china-red) 0%, var(--china-red-dark) 100%);
  color: white;
  border-radius: 10px;
  padding: 30px;
  margin-bottom: 20px;
}

.role-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  margin-top: 15px;
}

/* 统计卡片样式 */
.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: var(--china-red);
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

/* 表格样式优化 */
.el-table th.el-table__cell {
  background-color: var(--china-red) !important;
  color: white !important;
}

.el-table tr:hover > td {
  background-color: rgba(200, 16, 46, 0.05) !important;
}

/* 卡片阴影优化 */
.el-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1) !important;
}

/* 菜单样式 */
.el-menu {
  border-right: none !important;
}

.el-menu-item.is-active {
  background-color: rgba(200, 16, 46, 0.1) !important;
  color: var(--china-red) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-form {
    margin: 20px;
    padding: 30px 20px;
  }
  
  .dashboard-container {
    padding: 10px;
  }
  
  .welcome-card {
    padding: 20px;
  }
} 