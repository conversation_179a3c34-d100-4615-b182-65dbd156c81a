// 短信通知API接口 (P2功能预留)
import { ref } from 'vue'

// 模拟短信发送记录
const mockSmsRecords = ref([
  {
    id: 1,
    phone: '138****1234',
    content: '您有一条新的待审核意见，请及时处理。',
    status: 'success',
    sentAt: new Date('2024-01-15 10:30:00'),
    userId: 2,
    notificationId: 1
  },
  {
    id: 2,
    phone: '139****5678',
    content: '您的意见已审核通过，已转交至相关部门处理。',
    status: 'success',
    sentAt: new Date('2024-01-15 14:20:00'),
    userId: 1,
    notificationId: 2
  }
])

// 短信服务配置
const smsConfig = {
  provider: 'aliyun', // 短信服务商：aliyun, tencent, huawei等
  apiKey: 'mock_api_key',
  secretKey: 'mock_secret_key',
  signName: '人大代表平台',
  templateId: 'SMS_123456789'
}

/**
 * 发送短信通知
 * @param {Object} params - 短信参数
 * @param {string} params.phone - 手机号码
 * @param {string} params.content - 短信内容
 * @param {number} params.userId - 用户ID
 * @param {number} params.notificationId - 关联的通知ID
 * @param {string} params.templateCode - 短信模板代码
 * @param {Object} params.templateParams - 模板参数
 */
export const sendSms = async (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const { phone, content, userId, notificationId } = params
      
      // 模拟发送成功/失败
      const isSuccess = Math.random() > 0.1 // 90%成功率
      
      const smsRecord = {
        id: mockSmsRecords.value.length + 1,
        phone: phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'), // 脱敏处理
        content,
        status: isSuccess ? 'success' : 'failed',
        sentAt: new Date(),
        userId,
        notificationId,
        errorMessage: isSuccess ? null : '短信发送失败，请稍后重试'
      }
      
      mockSmsRecords.value.unshift(smsRecord)
      
      resolve({
        success: isSuccess,
        data: smsRecord,
        message: isSuccess ? '短信发送成功' : '短信发送失败'
      })
    }, 1000) // 模拟网络延迟
  })
}

/**
 * 批量发送短信
 * @param {Array} phoneList - 手机号列表
 * @param {string} content - 短信内容
 * @param {string} templateCode - 短信模板代码
 * @param {Object} templateParams - 模板参数
 */
export const batchSendSms = async (phoneList, content, templateCode, templateParams) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const results = phoneList.map(phone => {
        const isSuccess = Math.random() > 0.1
        return {
          phone: phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'),
          status: isSuccess ? 'success' : 'failed',
          errorMessage: isSuccess ? null : '发送失败'
        }
      })
      
      const successCount = results.filter(r => r.status === 'success').length
      const failedCount = results.length - successCount
      
      resolve({
        success: true,
        data: {
          total: results.length,
          successCount,
          failedCount,
          results
        },
        message: `批量发送完成，成功${successCount}条，失败${failedCount}条`
      })
    }, 2000)
  })
}

/**
 * 获取短信发送记录
 * @param {Object} params - 查询参数
 * @param {number} params.userId - 用户ID
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @param {string} params.status - 发送状态筛选
 */
export const getSmsRecords = async (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const { userId, page = 1, size = 10, status } = params
      
      let records = mockSmsRecords.value
      
      // 用户筛选
      if (userId) {
        records = records.filter(r => r.userId === userId)
      }
      
      // 状态筛选
      if (status) {
        records = records.filter(r => r.status === status)
      }
      
      // 按发送时间倒序排列
      records.sort((a, b) => new Date(b.sentAt) - new Date(a.sentAt))
      
      // 分页
      const total = records.length
      const start = (page - 1) * size
      const end = start + size
      const list = records.slice(start, end)
      
      resolve({
        success: true,
        data: {
          list,
          total,
          page,
          size,
          totalPages: Math.ceil(total / size)
        }
      })
    }, 300)
  })
}

/**
 * 获取短信发送统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 */
export const getSmsStatistics = async (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟统计数据
      const stats = {
        totalSent: 156,
        successCount: 142,
        failedCount: 14,
        successRate: 91.0,
        todaySent: 23,
        thisWeekSent: 89,
        thisMonthSent: 156
      }
      
      resolve({
        success: true,
        data: stats
      })
    }, 500)
  })
}

/**
 * 验证手机号格式
 * @param {string} phone - 手机号
 */
export const validatePhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 获取短信模板列表
 */
export const getSmsTemplates = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const templates = [
        {
          id: 1,
          code: 'OPINION_SUBMIT',
          name: '意见提交通知',
          content: '您有一条新的待审核意见"${title}"，请及时处理。',
          status: 'active'
        },
        {
          id: 2,
          code: 'OPINION_AUDIT',
          name: '意见审核通知',
          content: '您的意见"${title}"已审核${result}，${detail}。',
          status: 'active'
        },
        {
          id: 3,
          code: 'OPINION_RESULT',
          name: '意见办理结果通知',
          content: '您的意见"${title}"已处理完成，处理结果：${result}。',
          status: 'active'
        },
        {
          id: 4,
          code: 'SYSTEM_NOTICE',
          name: '系统通知',
          content: '${content}',
          status: 'active'
        }
      ]
      
      resolve({
        success: true,
        data: templates
      })
    }, 300)
  })
}

/**
 * 检查短信余额（模拟）
 */
export const checkSmsBalance = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        data: {
          balance: 1250,
          unit: '条',
          lowBalanceThreshold: 100,
          isLowBalance: false
        }
      })
    }, 200)
  })
}

/**
 * 业务触发短信通知的工具函数
 */

// 意见提交时发送短信通知
export const sendOpinionSubmitSms = async (opinionData, staffPhone) => {
  if (!validatePhone(staffPhone)) {
    return { success: false, message: '手机号格式不正确' }
  }
  
  const content = `您有一条新的待审核意见"${opinionData.title}"，请及时处理。`
  
  return await sendSms({
    phone: staffPhone,
    content,
    userId: opinionData.staffUserId,
    notificationId: opinionData.notificationId,
    templateCode: 'OPINION_SUBMIT',
    templateParams: {
      title: opinionData.title
    }
  })
}

// 意见审核时发送短信通知
export const sendOpinionAuditSms = async (opinionData, representativePhone, auditResult) => {
  if (!validatePhone(representativePhone)) {
    return { success: false, message: '手机号格式不正确' }
  }
  
  const isApproved = auditResult.status === 'approved'
  const result = isApproved ? '通过' : '未通过'
  const detail = isApproved 
    ? `已转交至${auditResult.department}处理`
    : `原因：${auditResult.reason}`
  
  const content = `您的意见"${opinionData.title}"已审核${result}，${detail}。`
  
  return await sendSms({
    phone: representativePhone,
    content,
    userId: opinionData.representativeUserId,
    notificationId: opinionData.notificationId,
    templateCode: 'OPINION_AUDIT',
    templateParams: {
      title: opinionData.title,
      result,
      detail
    }
  })
}

// 意见办理结果更新时发送短信通知
export const sendOpinionResultSms = async (opinionData, representativePhone, resultData) => {
  if (!validatePhone(representativePhone)) {
    return { success: false, message: '手机号格式不正确' }
  }
  
  const content = `您的意见"${opinionData.title}"已处理完成，处理结果：${resultData.description}。`
  
  return await sendSms({
    phone: representativePhone,
    content,
    userId: opinionData.representativeUserId,
    notificationId: opinionData.notificationId,
    templateCode: 'OPINION_RESULT',
    templateParams: {
      title: opinionData.title,
      result: resultData.description
    }
  })
}

// 系统通知短信
export const sendSystemNoticeSms = async (phoneList, content) => {
  return await batchSendSms(phoneList, content, 'SYSTEM_NOTICE', { content })
}

export default {
  sendSms,
  batchSendSms,
  getSmsRecords,
  getSmsStatistics,
  validatePhone,
  getSmsTemplates,
  checkSmsBalance,
  sendOpinionSubmitSms,
  sendOpinionAuditSms,
  sendOpinionResultSms,
  sendSystemNoticeSms
} 