"""
群众意见功能测试

运行测试：
uv run manage.py test api.peopleopnion.tests -v 2

测试覆盖：
1. 模型测试
2. API功能测试
3. 权限安全测试
4. 数据验证测试
5. 边界条件测试
"""

import json
from datetime import datetime, timedelta
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from rest_framework import status
from rest_framework.test import APITestCase, APIClient

from .models import PeopleOpinion
from .serializers import PeopleOpinionSerializer, PeopleOpinionCreateSerializer


User = get_user_model()


class PeopleOpinionModelTest(TestCase):
    """群众意见模型测试"""
    
    def setUp(self):
        """测试数据准备"""
        self.valid_data = {
            'title': '测试意见标题',
            'content': '这是一个测试意见的详细内容。',
            'name': '张三',
            'contact_info': '13800138000'
        }
    
    def test_create_opinion_with_valid_data(self):
        """测试使用有效数据创建意见"""
        opinion = PeopleOpinion.objects.create(**self.valid_data)
        
        self.assertEqual(opinion.title, self.valid_data['title'])
        self.assertEqual(opinion.content, self.valid_data['content'])
        self.assertEqual(opinion.name, self.valid_data['name'])
        self.assertEqual(opinion.contact_info, self.valid_data['contact_info'])
        self.assertIsNotNone(opinion.created_at)
        self.assertEqual(str(opinion), f"{opinion.title} - {opinion.name}")
    
    def test_create_opinion_without_optional_fields(self):
        """测试不包含可选字段创建意见"""
        minimal_data = {
            'title': '最小测试意见',
            'content': '最小内容'
        }
        opinion = PeopleOpinion.objects.create(**minimal_data)
        
        self.assertEqual(opinion.title, minimal_data['title'])
        self.assertEqual(opinion.content, minimal_data['content'])
        self.assertIsNone(opinion.name)
        self.assertIsNone(opinion.contact_info)
    
    def test_short_content_property(self):
        """测试内容简短版本属性"""
        # 短内容
        short_opinion = PeopleOpinion.objects.create(
            title='短内容测试',
            content='短内容'
        )
        self.assertEqual(short_opinion.short_content, '短内容')
        
        # 长内容
        long_content = 'a' * 150  # 超过100字符
        long_opinion = PeopleOpinion.objects.create(
            title='长内容测试',
            content=long_content
        )
        self.assertEqual(len(long_opinion.short_content), 103)  # 100 + "..."
        self.assertTrue(long_opinion.short_content.endswith('...'))
    
    def test_model_ordering(self):
        """测试模型默认排序"""
        from django.utils import timezone
        from datetime import timedelta
        
        # 创建多个意见，手动设置时间差
        now = timezone.now()
        opinion1 = PeopleOpinion.objects.create(title='第一个', content='内容1')
        opinion1.created_at = now - timedelta(seconds=10)  # 更早的时间
        opinion1.save()
        
        opinion2 = PeopleOpinion.objects.create(title='第二个', content='内容2')
        opinion2.created_at = now  # 更晚的时间
        opinion2.save()
        
        # 获取所有意见，应该按created_at倒序
        opinions = list(PeopleOpinion.objects.all())
        self.assertEqual(opinions[0].title, '第二个')  # 最新的在前
        self.assertEqual(opinions[1].title, '第一个')


class PeopleOpinionSerializerTest(TestCase):
    """序列化器测试"""
    
    def setUp(self):
        """测试数据准备"""
        self.valid_data = {
            'title': '序列化器测试意见',
            'content': '这是序列化器测试的内容',
            'name': '李四',
            'contact_info': '<EMAIL>'
        }
    
    def test_valid_serializer_create(self):
        """测试有效数据的序列化器创建"""
        serializer = PeopleOpinionCreateSerializer(data=self.valid_data)
        self.assertTrue(serializer.is_valid())
        
        opinion = serializer.save()
        self.assertEqual(opinion.title, self.valid_data['title'])
    
    def test_serializer_validation_title_required(self):
        """测试标题必填验证"""
        data = self.valid_data.copy()
        del data['title']
        
        serializer = PeopleOpinionCreateSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('title', serializer.errors)
    
    def test_serializer_validation_content_required(self):
        """测试内容必填验证"""
        data = self.valid_data.copy()
        del data['content']
        
        serializer = PeopleOpinionCreateSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('content', serializer.errors)
    
    def test_serializer_validation_title_max_length(self):
        """测试标题最大长度验证"""
        data = self.valid_data.copy()
        data['title'] = 'a' * 201  # 超过200字符
        
        serializer = PeopleOpinionCreateSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('title', serializer.errors)
    
    def test_serializer_validation_name_max_length(self):
        """测试姓名最大长度验证"""
        data = self.valid_data.copy()
        data['name'] = 'a' * 51  # 超过50字符
        
        serializer = PeopleOpinionCreateSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('name', serializer.errors)
    
    def test_serializer_validation_contact_info_max_length(self):
        """测试联系方式最大长度验证"""
        data = self.valid_data.copy()
        data['contact_info'] = 'a' * 101  # 超过100字符
        
        serializer = PeopleOpinionCreateSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('contact_info', serializer.errors)


class PeopleOpinionAPITest(APITestCase):
    """API功能测试"""
    
    def setUp(self):
        """测试环境设置"""
        self.client = APIClient()
        
        # 创建测试用户
        self.staff_user = User.objects.create_user(
            username='staff_test',
            password='testpass123',
            role='staff'
        )
        
        self.representative_user = User.objects.create_user(
            username='rep_test',
            password='testpass123',
            role='representative'
        )
        
        # 测试数据
        self.valid_opinion_data = {
            'title': 'API测试意见标题',
            'content': '这是通过API提交的测试意见内容，内容足够长',
            'name': '王五',
            'contact_info': '13900139000'
        }
        
        # 创建测试意见
        self.test_opinion = PeopleOpinion.objects.create(
            title='已存在的测试意见标题',
            content='已存在的测试内容，这里是详细的意见描述',
            name='已存在用户',
            contact_info='<EMAIL>'
        )
    
    def test_submit_opinion_anonymous_success(self):
        """测试匿名提交意见成功"""
        url = '/api/v1/people-opinions/submit/'
        response = self.client.post(url, self.valid_opinion_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('message', response.data)
        self.assertEqual(response.data['message'], '感谢您的宝贵意见！我们已收到您的反馈，将认真处理。')
        
        # 验证数据库中确实创建了记录
        self.assertTrue(
            PeopleOpinion.objects.filter(title=self.valid_opinion_data['title']).exists()
        )
    
    def test_submit_opinion_invalid_data(self):
        """测试提交无效数据"""
        url = '/api/v1/people-opinions/submit/'
        invalid_data = {'title': ''}  # 缺少内容
        
        response = self.client.post(url, invalid_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_get_opinion_list_staff_access(self):
        """测试工作人员访问意见列表"""
        self.client.force_authenticate(user=self.staff_user)
        
        url = '/api/v1/people-opinions/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertIn('opinions', response.data['data'])
        self.assertIn('pagination', response.data['data'])
    
    def test_get_opinion_list_with_search(self):
        """测试带搜索条件的意见列表"""
        self.client.force_authenticate(user=self.staff_user)
        
        url = '/api/v1/people-opinions/'
        response = self.client.get(url, {'search': '已存在'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(len(response.data['data']['opinions']) >= 1)
    
    def test_get_opinion_detail_staff_access(self):
        """测试工作人员访问意见详情"""
        self.client.force_authenticate(user=self.staff_user)
        
        url = f'/api/v1/people-opinions/{self.test_opinion.id}/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['title'], self.test_opinion.title)
    
    def test_update_opinion_staff_access(self):
        """测试工作人员更新意见"""
        self.client.force_authenticate(user=self.staff_user)
        
        url = f'/api/v1/people-opinions/{self.test_opinion.id}/'
        update_data = {
            'title': '更新后的标题内容',
            'content': self.test_opinion.content,
            'name': self.test_opinion.name,
            'contact_info': self.test_opinion.contact_info
        }
        
        response = self.client.put(url, update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证更新生效
        updated_opinion = PeopleOpinion.objects.get(id=self.test_opinion.id)
        self.assertEqual(updated_opinion.title, '更新后的标题内容')
    
    def test_delete_opinion_staff_access(self):
        """测试工作人员删除意见"""
        self.client.force_authenticate(user=self.staff_user)
        
        url = f'/api/v1/people-opinions/{self.test_opinion.id}/'
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证删除生效
        self.assertFalse(
            PeopleOpinion.objects.filter(id=self.test_opinion.id).exists()
        )
    



class PeopleOpinionSecurityTest(APITestCase):
    """安全测试"""
    
    def setUp(self):
        """安全测试环境设置"""
        self.client = APIClient()
        
        # 创建不同角色用户
        self.staff_user = User.objects.create_user(
            username='staff_security',
            password='testpass123',
            role='staff'
        )
        
        self.representative_user = User.objects.create_user(
            username='rep_security',
            password='testpass123',
            role='representative'
        )
        
        self.test_opinion = PeopleOpinion.objects.create(
            title='安全测试意见标题',
            content='安全测试内容，这里是详细的安全测试描述',
            name='安全测试用户'
        )
    
    def test_submit_opinion_no_authentication_required(self):
        """测试提交意见不需要认证（匿名访问）"""
        url = '/api/v1/people-opinions/submit/'
        data = {
            'title': '匿名意见标题',
            'content': '匿名提交的详细内容描述'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
    
    def test_list_opinions_requires_staff_permission(self):
        """测试列表查看需要工作人员权限"""
        url = '/api/v1/people-opinions/'
        
        # 未认证访问
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # 代表用户访问（权限不足）
        self.client.force_authenticate(user=self.representative_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # 工作人员访问（有权限）
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_detail_operations_require_staff_permission(self):
        """测试详情操作需要工作人员权限"""
        url = f'/api/v1/people-opinions/{self.test_opinion.id}/'
        
        # 测试GET详情
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # 测试PUT更新
        update_data = {'title': '尝试更新', 'content': '尝试更新内容'}
        response = self.client.put(url, update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # 测试DELETE删除
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    

    
    def test_sql_injection_protection(self):
        """测试SQL注入防护"""
        self.client.force_authenticate(user=self.staff_user)
        
        # 尝试在搜索参数中注入SQL
        malicious_search = "'; DROP TABLE people_opinion; --"
        url = '/api/v1/people-opinions/'
        
        response = self.client.get(url, {'search': malicious_search})
        
        # 应该正常返回，不会执行恶意SQL
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证表仍然存在
        self.assertTrue(PeopleOpinion.objects.filter(id=self.test_opinion.id).exists())
    
    def test_xss_protection_in_content(self):
        """测试XSS攻击防护"""
        url = '/api/v1/people-opinions/submit/'
        
        # 尝试提交包含脚本的内容
        xss_data = {
            'title': '测试XSS防护功能标题',  # 使用正常标题
            'content': '这是一个包含HTML的内容：<script>alert("xss")</script>恶意内容的详细描述',  # 内容包含脚本
        }
        
        response = self.client.post(url, xss_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        
        # 验证脚本标签被处理（这里我们简单检查是否成功创建）
        self.assertIn('data', response.data)
    
    def test_rate_limiting_simulation(self):
        """测试频率限制模拟"""
        url = '/api/v1/people-opinions/submit/'
        data = {
            'title': '频率测试标题',
            'content': '频率测试的详细内容描述'
        }
        
        # 快速提交多次请求
        responses = []
        for i in range(10):
            response = self.client.post(url, {**data, 'title': f'频率测试{i}'}, format='json')
            responses.append(response.status_code)
        
        # 所有请求都应该成功（实际环境中可能会有频率限制）
        self.assertTrue(all(status_code == 201 for status_code in responses))
    
    def test_large_content_handling(self):
        """测试大内容处理"""
        url = '/api/v1/people-opinions/submit/'
        
        # 创建超大内容
        large_content = 'A' * 10000  # 10KB内容
        data = {
            'title': '大内容测试标题',
            'content': large_content
        }
        
        response = self.client.post(url, data, format='json')
        
        # 应该能正常处理大内容
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证内容完整存储
        opinion = PeopleOpinion.objects.get(title='大内容测试标题')
        self.assertEqual(len(opinion.content), 10000)


class PeopleOpinionEdgeCaseTest(APITestCase):
    """边界条件测试"""
    
    def setUp(self):
        """边界测试环境设置"""
        self.client = APIClient()
        self.staff_user = User.objects.create_user(
            username='staff_edge',
            password='testpass123',
            role='staff'
        )
    
    def test_empty_database_list(self):
        """测试空数据库的意见列表"""
        self.client.force_authenticate(user=self.staff_user)
        
        # 清空所有数据
        PeopleOpinion.objects.all().delete()
        
        url = '/api/v1/people-opinions/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['pagination']['total_count'], 0)
        self.assertEqual(len(response.data['data']['opinions']), 0)
    
    def test_nonexistent_opinion_access(self):
        """测试访问不存在的意见"""
        self.client.force_authenticate(user=self.staff_user)
        
        # 使用不存在的ID
        url = '/api/v1/people-opinions/99999/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_malformed_opinion_id(self):
        """测试格式错误的意见ID"""
        self.client.force_authenticate(user=self.staff_user)
        
        # 使用非数字ID
        url = '/api/v1/people-opinions/invalid_id/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_special_characters_in_search(self):
        """测试搜索中的特殊字符"""
        self.client.force_authenticate(user=self.staff_user)
        
        # 创建包含特殊字符的测试数据
        PeopleOpinion.objects.create(
            title='特殊字符测试@#$%^&*()',
            content='包含各种符号的详细内容！@#￥%……&*（）测试描述'
        )
        
        url = '/api/v1/people-opinions/'
        response = self.client.get(url, {'search': '@#$%'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_unicode_content_handling(self):
        """测试Unicode内容处理"""
        url = '/api/v1/people-opinions/submit/'
        
        # 包含各种Unicode字符
        unicode_data = {
            'title': '🏛️ 政府建议标题 📝',
            'content': '这里有emoji 😊 和其他Unicode字符的详细内容描述 ñáéíóú αβγδε 中文 日本語 한국어',
            'name': '测试用户 👤',
            'contact_info': 'test@测试.com'
        }
        
        response = self.client.post(url, unicode_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证Unicode字符正确存储
        opinion = PeopleOpinion.objects.get(title__contains='政府建议')
        self.assertIn('😊', opinion.content)
        self.assertIn('👤', opinion.name)


# 运行特定测试的示例命令（在注释中提供）
"""
运行所有测试：
uv run manage.py test api.peopleopnion.tests -v 2

运行特定测试类：
uv run manage.py test api.peopleopnion.tests.PeopleOpinionAPITest -v 2

运行特定测试方法：
uv run manage.py test api.peopleopnion.tests.PeopleOpinionSecurityTest.test_sql_injection_protection -v 2

运行并生成覆盖率报告：
uv run coverage run --source='.' manage.py test api.peopleopnion.tests
uv run coverage report
uv run coverage html
"""
