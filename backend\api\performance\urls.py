"""
履职管理URL配置

提供以下API路由：
1. 履职记录CRUD接口
2. 附件管理接口
3. 文件上传接口
4. 统计数据接口
5. 选择项数据接口

URL设计遵循RESTful规范，使用APIView方式实现
"""

from django.urls import path
from . import views

app_name = 'performance'

urlpatterns = [
    # 履职记录接口
    path('records/', views.PerformanceRecordListCreateAPIView.as_view(), name='record-list-create'),
    path('records/<int:pk>/', views.PerformanceRecordDetailAPIView.as_view(), name='record-detail'),
    path('records/stats/', views.PerformanceRecordStatsAPIView.as_view(), name='record-stats'),
    path('records/export/', views.PerformanceRecordExportAPIView.as_view(), name='record-export'),
    
    # 附件管理接口
    path('attachments/', views.PerformanceAttachmentListAPIView.as_view(), name='attachment-list'),
    path('attachments/<int:pk>/', views.PerformanceAttachmentDetailAPIView.as_view(), name='attachment-detail'),
    
    # 文件上传接口
    path('upload/', views.FileUploadAPIView.as_view(), name='file-upload'),
    
    # 安全文件访问接口
    path('secure-file/<int:attachment_id>/', views.SecureFileAccessAPIView.as_view(), name='secure-file-access'),
    
    # 文件清理接口（管理员专用）
    path('cleanup/', views.FileCleanupAPIView.as_view(), name='file-cleanup'),
    
    # 选择项数据接口
    path('choices/types/', views.PerformanceTypeChoicesAPIView.as_view(), name='type-choices'),
    path('choices/status/', views.PerformanceStatusChoicesAPIView.as_view(), name='status-choices'),
    path('choices/file-limits/', views.FileTypeLimitsAPIView.as_view(), name='file-limits'),
]

"""
API端点说明：

履职记录接口：
- GET /api/v1/performance/records/ - 获取履职记录列表（支持分页、筛选、排序）
- POST /api/v1/performance/records/ - 创建履职记录
- GET /api/v1/performance/records/{id}/ - 获取履职记录详情
- PUT /api/v1/performance/records/{id}/ - 完整更新履职记录
- PATCH /api/v1/performance/records/{id}/ - 部分更新履职记录
- DELETE /api/v1/performance/records/{id}/ - 删除履职记录
- GET /api/v1/performance/records/stats/ - 获取统计数据
- GET /api/v1/performance/records/export/ - 导出数据

附件管理接口：
- GET /api/v1/performance/attachments/?performance_record_id={id} - 获取指定履职记录的附件列表
- GET /api/v1/performance/attachments/{id}/ - 获取附件详情
- PATCH /api/v1/performance/attachments/{id}/ - 更新附件信息（如排序）
- DELETE /api/v1/performance/attachments/{id}/ - 删除附件

文件操作接口：
- POST /api/v1/performance/upload/ - 上传文件
- GET /api/v1/performance/secure-file/{attachment_id}/ - 安全文件访问（JWT认证）
- POST /api/v1/performance/cleanup/ - 清理孤立文件（管理员）

选择项接口：
- GET /api/v1/performance/choices/types/ - 获取履职类型选项
- GET /api/v1/performance/choices/status/ - 获取履职状态选项
- GET /api/v1/performance/choices/file-limits/ - 获取文件类型限制

查询参数说明（适用于列表接口）：
- start_date: 开始日期 (YYYY-MM-DD)
- end_date: 结束日期 (YYYY-MM-DD)
- performance_type: 履职类型筛选
- performance_status: 履职状态筛选
- has_attachments: 是否有附件 (true/false)
- search: 关键词搜索
- ordering: 排序字段 (-performance_date, performance_type, etc.)
- page: 页码
- page_size: 每页数量

安全文件访问说明：
- 仅支持JWT认证头访问：Authorization: Bearer {token}
- 权限控制：只能访问自己的附件文件
- 使用Fetch API + Blob方式在前端安全展示

附件列表接口必需参数：
- performance_record_id: 履职记录ID
""" 