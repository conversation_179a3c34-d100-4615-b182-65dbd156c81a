"""
初始化测试数据的Django管理命令

用于在开发和测试环境中创建基础的用户和相关数据
"""

from datetime import date
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from api.users.models import Representative, StaffMember

User = get_user_model()


class Command(BaseCommand):
    """初始化测试数据命令"""
    
    help = '创建测试用户和相关数据，用于开发和测试'
    
    def add_arguments(self, parser):
        """添加命令行参数"""
        parser.add_argument(
            '--clear',
            action='store_true',
            help='清除现有数据后重新创建',
        )
    
    def handle(self, *args, **options):
        """命令执行主逻辑"""
        
        # 是否清除现有数据
        if options['clear']:
            self.stdout.write('正在清除现有数据...')
            Representative.objects.all().delete()
            StaffMember.objects.all().delete()
            User.objects.filter(is_superuser=False).delete()
            self.stdout.write(self.style.SUCCESS('现有数据已清除'))
        
        # 创建工作人员用户
        self.stdout.write('正在创建工作人员用户...')
        self.create_staff_users()
        
        # 创建人大代表用户
        self.stdout.write('正在创建人大代表用户...')
        self.create_representative_users()
        
        self.stdout.write(
            self.style.SUCCESS('测试数据初始化完成！')
        )
        
        # 显示登录信息
        self.stdout.write('\n=== 测试账号信息 ===')
        self.stdout.write('已创建50个测试账号：')
        self.stdout.write('工作人员账号：staff001 - staff025')
        self.stdout.write('人大代表账号：rep001 - rep025')
        self.stdout.write('统一密码：test123456')
    
    def create_staff_users(self):
        """创建工作人员用户和相关信息"""
        
        # 创建25个工作人员账号
        for i in range(1, 26):
            username = f'staff{i:03d}'  # staff001, staff002, ...
            
            # 检查用户是否已存在
            if User.objects.filter(username=username).exists():
                self.stdout.write(f'  用户 {username} 已存在，跳过创建')
                continue
            
            # 创建用户
            user = User.objects.create_user(
                username=username,
                password='test123456',
                role='staff'
            )
            
            # 创建工作人员信息
            StaffMember.objects.create(
                user=user,
                name=f'工作人员{i:02d}',
                position=f'职位{i}',
                mobile_phone=f'138{i:08d}',
                email=f'{username}@example.com',
                station_name='江南区人大代表联络站',
            )
            
            self.stdout.write(f'  创建工作人员: {username} (工作人员{i:02d})')
    
    def create_representative_users(self):
        """创建人大代表用户和相关信息"""
        
        # 创建25个人大代表账号
        for i in range(1, 26):
            username = f'rep{i:03d}'  # rep001, rep002, ...
            
            # 检查用户是否已存在
            if User.objects.filter(username=username).exists():
                self.stdout.write(f'  用户 {username} 已存在，跳过创建')
                continue
            
            # 创建用户
            user = User.objects.create_user(
                username=username,
                password='test123456',
                role='representative'
            )
            
            # 创建人大代表信息
            Representative.objects.create(
                user=user,
                name=f'人大代表{i:02d}',
                level='区级',
                gender='male' if i % 2 == 1 else 'female',
                nationality='汉族',
                birth_date=date(1970 + (i % 30), (i % 12) + 1, (i % 28) + 1),
                birthplace='广西南宁',
                party='中国共产党',
                current_position=f'职务{i}',
                mobile_phone=f'139{i:08d}',
                education='本科',
                graduated_school='广西大学',
                major='管理学',
            )
            
            self.stdout.write(f'  创建人大代表: {username} (人大代表{i:02d})')
    
    def create_superuser_if_not_exists(self):
        """如果不存在超级用户则创建一个"""
        if not User.objects.filter(is_superuser=True).exists():
            User.objects.create_superuser(
                username='admin',
                password='admin123456',
                role='staff'
            )
            self.stdout.write('创建超级用户: admin/admin123456') 