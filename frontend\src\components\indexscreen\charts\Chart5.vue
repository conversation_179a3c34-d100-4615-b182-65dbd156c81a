<template>
  <div class="chart5-container">
    <!-- 地图区域 -->
    <div ref="myChart" class="map-area"></div>

    <!-- 片区统计区域 -->
    <div
      class="district-stats"
      @mouseleave="clearHighlight()"
    >
      <h3 class="stats-title">代表履职分布</h3>

      <div class="stats-list">
        <div
          v-for="district in districtData"
          :key="district.name"
          class="stats-item clickable-district"
          :class="{
            'active': hoveredDistrict === district.name,
            'selected': selectedDistrict === district.name
          }"
          @mouseenter="highlightDistrict(district.name)"
          @mouseleave="clearHighlight()"
          @click="showDistrictDetail(district)"
        >
          <div class="district-name">{{ district.name }}</div>
          <div class="district-count">{{ district.count }}人</div>
        </div>
      </div>

      <!-- 二维码区域 - 放在最后一行文字下方 -->
      <div class="qrcode-container">
        <canvas ref="qrCanvas" class="qr-canvas" @click="showLargeQR"></canvas>
        <div class="qr-tip">扫码提交意见建议(点击放大)</div>
      </div>

      <!-- 点击时显示的大二维码 -->
      <div v-if="showLargeQRCode" class="large-qr-overlay" @click="hideLargeQR">
        <div class="large-qr-container" @click.stop>
          <canvas ref="largeQrCanvas" class="large-qr-canvas"></canvas>
          <div class="large-qr-tip">扫码提交意见建议</div>
          <div class="close-btn" @click="hideLargeQR">×</div>
        </div>
      </div>
    </div>
  </div>

  <!-- 保留原有的弹窗功能 -->
  <div v-if="activeRegion" class="popup" :style="popupStyle">
    <div class="popup-title">{{ activeRegion }}</div>
    <div class="popup-row popup-circle-row">
      <div class="popup-circle">
        <div class="popup-circle-num">{{ regions[activeRegion].city }}</div>
        <div class="popup-circle-label">市代表</div>
      </div>
      <div class="popup-circle">
        <div class="popup-circle-num">{{ regions[activeRegion].town }}</div>
        <div class="popup-circle-label">镇代表</div>
      </div>
    </div>
    <div class="popup-row popup-gender-label">男女比例</div>
    <div class="popup-row popup-gender">
      <span
        v-for="n in regions[activeRegion].male"
        :key="'m' + n"
        class="iconfont icon-mti-nan male"
      ></span>
      <span
        v-for="n in regions[activeRegion].female"
        :key="'f' + n"
        class="iconfont icon-mti-nv female"
      ></span>
    </div>
    <div class="popup-row popup-bar-row">
      <div class="popup-bar-label">党员占比</div>
      <div class="popup-bar">
        <div
          class="popup-bar-inner party"
          :style="{ width: regions[activeRegion].party + '%' }"
        ></div>
      </div>
      <div class="popup-bar-value">{{ regions[activeRegion].party }}%</div>
    </div>
    <div class="popup-row popup-bar-row">
      <div class="popup-bar-label">建议落实率</div>
      <div class="popup-bar">
        <div
          class="popup-bar-inner advice"
          :style="{ width: regions[activeRegion].advice + '%' }"
        ></div>
      </div>
      <div class="popup-bar-value">{{ regions[activeRegion].advice }}%</div>
    </div>
    <div class="popup-row popup-bar-row">
      <div class="popup-bar-label">履职平台参与率</div>
      <div class="popup-bar">
        <div
          class="popup-bar-inner platform"
          :style="{ width: regions[activeRegion].platform + '%' }"
        ></div>
      </div>
      <div class="popup-bar-value">{{ regions[activeRegion].platform }}%</div>
    </div>
    <span class="popup-close" @click="activeRegion = null">×</span>
  </div>

  <!-- 片区人员名单弹窗 -->
  <ModalDialog
    v-if="showDistrictModal"
    :visible="showDistrictModal"
    :title="`${selectedDistrictInfo?.name || ''}人员名单`"
    @close="closeDistrictModal"
  >
    <div class="district-members">
      <div v-if="districtMembers.length === 0" class="no-data">
        暂无人员数据
      </div>
      <div v-else class="members-grid">
        <div
          v-for="member in districtMembers"
          :key="member.id"
          class="member-card"
        >
          <div class="member-avatar">
            <img v-if="member.avatar" :src="member.avatar" :alt="member.name" @error="handleAvatarError" />
            <div v-else class="avatar-placeholder">
              {{ member.name.charAt(0) }}
            </div>
          </div>
          <div class="member-info">
            <div class="member-name">{{ member.name }}</div>
            <div class="member-level">{{ member.level }}</div>
            <div class="member-party">{{ member.party || '群众' }}</div>
          </div>
        </div>
      </div>
    </div>
  </ModalDialog>
</template>

<script>
// 从public目录动态加载地图数据
import qrcode from 'qrcode-generator'
import ModalDialog from '../common/ModalDialog.vue'

export default {
  components: {
    ModalDialog
  },
  props: {
    regions: {
      type: Object,
      default: () => ({})
    },
    // 新增districts prop来接收片区数据
    districts: {
      type: Array,
      default: () => []
    }
  },
  async mounted() {
    console.log('🔧 Chart5 mounted - 初始props districts:', this.districts)
    // 设置二维码URL
    this.setQRCodeUrl()
    await this.loadMapData()
    await this.loadDistrictData()
    this.initChart()
    this.generateQRCode()
    console.log('🔧 Chart5 mounted完成 - 最终districtData:', this.districtData)
  },
  // 添加districts的watch，当API数据更新时重新渲染
  watch: {
    districts: {
      handler(newDistricts) {
        console.log('🔄 Chart5 districts数据更新:', newDistricts)
        if (newDistricts && newDistricts.length > 0) {
          this.districtData = newDistricts
          console.log('✅ Chart5 districtData已更新:', this.districtData)
          // 强制Vue重新渲染组件
          this.$forceUpdate()
        }
      },
      deep: true,
      immediate: false // 改为false，避免在组件初始化时执行
    }
  },
  data() {
    return {
      activeRegion: null,
      popupPosition: { left: 0, top: 0 },
      chart: null, // 存储图表实例
      nahongGeoJson: null, // 存储地图数据
      districtData: [], // 片区统计数据
      hoveredDistrict: null, // 当前悬浮的片区
      selectedDistrict: null, // 当前选中的片区
      showLargeQRCode: false, // 控制大二维码显示
      showDistrictModal: false, // 控制片区人员弹窗显示
      selectedDistrictInfo: null, // 选中的片区信息
      districtMembers: [], // 片区人员列表
      // 🔧 二维码链接配置 - 硬编码固定链接
      qrCodeUrl: 'https://jnrd.gxaigc.cn/people-opinion' // 硬编码的二维码链接
    }
  },
  computed: {
    popupStyle() {
      // 居中于地图区域
      return {
        position: 'absolute',
        left: '60%',
        top: '40%',
        transform: 'translate(-50%, -50%)',
        zIndex: 10,
      }
    },
  },
  methods: {
    // 设置二维码URL为硬编码链接
    setQRCodeUrl() {
      // 硬编码的二维码链接，不再动态获取
      this.qrCodeUrl = 'https://jnrd.gxaigc.cn/people-opinion'
      console.log('🔧 Chart5 二维码URL已硬编码为:', this.qrCodeUrl)
    },

    async loadMapData() {
      try {
        const response = await fetch('/nahong.geojson')
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        this.nahongGeoJson = await response.json()
        console.log('✅ 地图数据加载成功:', this.nahongGeoJson)
      } catch (error) {
        console.error('❌ 地图数据加载失败:', error)
        // 如果加载失败，使用简化的默认数据
        this.nahongGeoJson = {
          "type": "FeatureCollection",
          "features": [
            {
              "type": "Feature",
              "properties": { "name": "友谊社区", "layer": "沛鸿片区" },
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[108.2, 22.8], [108.3, 22.8], [108.3, 22.9], [108.2, 22.9], [108.2, 22.8]]]
              }
            },
            {
              "type": "Feature",
              "properties": { "name": "沛鸿社区", "layer": "沛鸿片区" },
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[108.3, 22.8], [108.4, 22.8], [108.4, 22.9], [108.3, 22.9], [108.3, 22.8]]]
              }
            },
            {
              "type": "Feature",
              "properties": { "name": "银凯社区", "layer": "那历片区" },
              "geometry": {
                "type": "Polygon",
                "coordinates": [[[108.2, 22.7], [108.3, 22.7], [108.3, 22.8], [108.2, 22.8], [108.2, 22.7]]]
              }
            }
          ]
        }
        console.log('🔄 使用默认地图数据')
      }
    },

    async loadDistrictData() {
      console.log('🔍 Chart5 loadDistrictData - props districts:', this.districts)

      // 如果props中有districts数据，优先使用props数据
      if (this.districts && this.districts.length > 0) {
        this.districtData = this.districts
        console.log('✅ 使用props片区数据:', this.districtData)
        return
      }

      // 否则从静态文件加载（兜底方案）
      try {
        const response = await fetch('/data.json')
        const data = await response.json()
        this.districtData = data.chart5.districts || []
        console.log('✅ 片区数据从静态文件加载成功:', this.districtData)
      } catch (error) {
        console.error('❌ 片区数据加载失败:', error)
        // 使用默认数据
        this.districtData = [
          { name: '沛鸿片区', count: 10 },
          { name: '那历片区', count: 20 },
          { name: '那洪片区', count: 30 }
        ]
      }
    },

    async initChart() {
      if (!this.nahongGeoJson) {
        console.error('地图数据未加载')
        return
      }

      const myChart = this.$echarts.init(this.$refs.myChart)
      this.$echarts.registerMap('nahong', this.nahongGeoJson)

      let option = {
        geo: {
          map: 'nahong',
          roam: true,
          zoom: 1.2,              // 缩放比例
          center: [1400, -1400],  // 根据实际地图数据调整，中心点
          label: {
            show: true,
            color: '#fff',
            fontSize: 12,
            formatter: function(params) {
              // 不显示"轮廓"标签，只显示其他区域的名称
              return params.name === '轮廓' ? '' : params.name;
            }
          },
          itemStyle: {
            areaColor: '#013163',
            borderColor: '#3a769e',
            borderWidth: 1,
          },
          emphasis: {
            itemStyle: {
              areaColor: '#3498db',
            },
          },
          regions: this.getRegionStyles(), // 设置不同片区的颜色
        },
      }

      await myChart.setOption(option)
      this.chart = myChart

      // 移除地图鼠标悬浮事件，只保留右侧面板的交互
      // this.addMapEventListeners()
    },

    getRegionStyles() {
      if (!this.nahongGeoJson) return []

      // 使用参考文件的默认底色
      const defaultColor = '#013163'  // 深蓝色底色

      const regions = []
      this.nahongGeoJson.features.forEach((feature) => {
        const { name } = feature.properties

        // 所有区域都使用统一颜色，不需要特殊处理

        // 所有区域（包括轮廓）都使用统一的默认颜色
        regions.push({
          name: name,
          itemStyle: {
            areaColor: defaultColor,
            borderColor: '#3a769e',
            borderWidth: 2
          }
        })
      })

      return regions
    },

    // 已移除地图鼠标事件监听，提升性能，只保留右侧面板交互
    // addMapEventListeners() {
    //   if (!this.chart) return
    //   // 鼠标悬浮事件
    //   this.chart.on('mouseover', (params) => {
    //     // 忽略轮廓和非片区区域
    //     if (params.name === '轮廓') return
    //     const hoveredLayer = this.getLayerByName(params.name)
    //     // 只有属于有效片区的区域才高亮
    //     if (hoveredLayer && this.isValidLayer(hoveredLayer)) {
    //       this.highlightLayer(hoveredLayer)
    //     }
    //   })
    //   // 鼠标离开事件
    //   this.chart.on('mouseout', () => {
    //     this.clearHighlight()
    //   })
    //   // 点击事件
    //   this.chart.on('click', (params) => {
    //     if (params.name && params.name !== '轮廓' && this.regions[params.name]) {
    //       this.activeRegion = params.name
    //     }
    //   })
    // },

    getLayerByName(name) {
      // 从nahong.geojson数据中找到对应的layer
      if (!this.nahongGeoJson) return null
      const feature = this.nahongGeoJson.features.find(f => f.properties.name === name)
      if (!feature) return null

      let layer = feature.properties.layer

      return layer
    },

    isValidLayer(layer) {
      // 检查是否是有效的片区（排除轮廓等非片区区域）
      const validLayers = ['沛鸿片区shape', '那历片区shape', '那洪片区shape']
      return validLayers.includes(layer)
    },

    highlightLayer(layer) {
      if (!this.chart || !this.nahongGeoJson) return

      // 使用参考文件的统一高亮颜色
      // const highlightColor = '#3498db'  // 统一的高亮蓝色
      const highlightColor = '#fb7293'  // 统一的高亮

      // 获取同一片区的所有村/社区（包括修正后的留村）
      const sameLayerFeatures = this.nahongGeoJson.features.filter(f => {
        if (f.properties.name === '轮廓') return false

        let featureLayer = f.properties.layer

        return featureLayer === layer
      })

      // 获取基础的区域样式（统一颜色）
      const baseRegions = this.getRegionStyles()

      // 创建高亮配置，只高亮同一片区的区域，排除轮廓
      const highlightRegions = baseRegions.map(region => {
        const isInSameLayer = sameLayerFeatures.some(f => f.properties.name === region.name)

        // 如果是轮廓区域，永远不高亮
        if (region.name === '轮廓') {
          return region
        }

        if (isInSameLayer) {
          return {
            ...region,
            itemStyle: {
              ...region.itemStyle,
              // 使用统一的高亮颜色
              areaColor: highlightColor,
              borderColor: '#3a769e',
              borderWidth: 1
            }
          }
        }
        return region
      })

      // 更新地图配置
      this.chart.setOption({
        geo: {
          regions: highlightRegions
        }
      })
    },

    clearHighlight() {
      if (!this.chart) return

      // 恢复到原始的片区颜色
      this.chart.setOption({
        geo: {
          regions: this.getRegionStyles()
        }
      })
      // 只有在没有选中状态时才清除悬浮状态
      if (!this.selectedDistrict) {
        this.hoveredDistrict = null
      }
    },

    highlightDistrict(districtName) {
      this.hoveredDistrict = districtName

      // 根据片区名称找到对应的layer
      const layerMap = {
        '沛鸿片区': '沛鸿片区shape',
        '那历片区': '那历片区shape',
        '那洪片区': '那洪片区shape'
      }

      const layer = layerMap[districtName]
      if (layer) {
        this.highlightLayer(layer)
      }
    },

    generateQRCode() {
      // 使用qrcode-generator库生成二维码
      const canvas = this.$refs.qrCanvas
      if (!canvas) return

      try {
        // 创建二维码实例
        const qr = qrcode(0, 'M') // 0表示自动选择类型，'M'表示中等纠错级别
        qr.addData(this.qrCodeUrl)
        qr.make()

        // 获取二维码模块数据
        const moduleCount = qr.getModuleCount()

        // 动态计算容器可用宽度 - 让二维码占满容器
        const qrContainer = document.querySelector('.qrcode-container')
        const containerWidth = qrContainer ? qrContainer.clientWidth - 30 : 150 // 减去padding
        const containerHeight = qrContainer ? qrContainer.clientHeight - 60 : 150 // 减去padding和文字高度

        // 使用容器的较小尺寸，确保二维码是正方形且完全显示
        const availableSize = Math.min(containerWidth, containerHeight, 120) // 最大120px
        const margin = 4 // 减少边距
        const qrSize = availableSize - margin * 2

        // 计算每个模块的大小
        const cellSize = Math.floor(qrSize / moduleCount)
        const actualQrSize = moduleCount * cellSize
        const size = actualQrSize + margin * 2

        // 设置canvas尺寸
        canvas.width = size
        canvas.height = size

        const ctx = canvas.getContext('2d')

        // 绘制白色背景
        ctx.fillStyle = '#ffffff'
        ctx.fillRect(0, 0, size, size)

        // 绘制二维码，居中显示
        ctx.fillStyle = '#000000'
        const startX = (size - actualQrSize) / 2
        const startY = (size - actualQrSize) / 2

        for (let row = 0; row < moduleCount; row++) {
          for (let col = 0; col < moduleCount; col++) {
            if (qr.isDark(row, col)) {
              ctx.fillRect(
                startX + col * cellSize,
                startY + row * cellSize,
                cellSize,
                cellSize
              )
            }
          }
        }
      } catch (error) {
        console.error('生成二维码失败:', error)
        // 如果生成失败，绘制一个简单的占位符
        const size = 200 // 与容器宽度一致
        canvas.width = size
        canvas.height = size
        const ctx = canvas.getContext('2d')

        ctx.fillStyle = '#ffffff'
        ctx.fillRect(0, 0, size, size)
        ctx.fillStyle = '#013163'
        ctx.font = '16px Arial' // 增加字体大小
        ctx.textAlign = 'center'
        ctx.fillText('QR', size/2, size/2 - 8)
        ctx.fillText('CODE', size/2, size/2 + 12)
      }
    },

    // 显示大二维码（点击触发）
    showLargeQR() {
      this.showLargeQRCode = true
      this.$nextTick(() => {
        this.generateLargeQRCode()
      })
    },

    // 隐藏大二维码
    hideLargeQR() {
      this.showLargeQRCode = false
    },

    // 生成大二维码
    generateLargeQRCode() {
      const canvas = this.$refs.largeQrCanvas
      if (!canvas) return

      try {
        // 创建二维码实例
        const qr = qrcode(0, 'M')
        qr.addData(this.qrCodeUrl)
        qr.make()

        // 获取二维码模块数据
        const moduleCount = qr.getModuleCount()

        // 计算大二维码尺寸 - 占屏幕1/3
        const screenWidth = window.innerWidth
        const screenHeight = window.innerHeight
        const targetSize = Math.min(screenWidth, screenHeight) / 3
        const margin = 20
        const qrSize = targetSize - margin * 2

        // 计算每个模块的大小
        const cellSize = Math.floor(qrSize / moduleCount)
        const actualQrSize = moduleCount * cellSize
        const size = actualQrSize + margin * 2

        // 设置canvas尺寸
        canvas.width = size
        canvas.height = size

        const ctx = canvas.getContext('2d')

        // 绘制白色背景
        ctx.fillStyle = '#ffffff'
        ctx.fillRect(0, 0, size, size)

        // 绘制二维码，居中显示
        ctx.fillStyle = '#000000'
        const startX = (size - actualQrSize) / 2
        const startY = (size - actualQrSize) / 2

        for (let row = 0; row < moduleCount; row++) {
          for (let col = 0; col < moduleCount; col++) {
            if (qr.isDark(row, col)) {
              ctx.fillRect(
                startX + col * cellSize,
                startY + row * cellSize,
                cellSize,
                cellSize
              )
            }
          }
        }
      } catch (error) {
        console.error('生成大二维码失败:', error)
      }
    },

    async showDistrictDetail(district) {
      // 设置选中状态
      this.selectedDistrict = district.name
      this.selectedDistrictInfo = district

      // 获取该片区的人员列表
      await this.fetchDistrictMembers(district.name)

      // 显示弹窗
      this.showDistrictModal = true
    },

    closeDistrictModal() {
      this.showDistrictModal = false
      this.selectedDistrictInfo = null
      this.districtMembers = []
      // 保持选中状态，不清除 selectedDistrict
      this.selectedDistrict = null
    },

    async fetchDistrictMembers(districtName) {
      try {
        console.log(`获取${districtName}的人员列表`)

        // 调用API获取片区人员数据
        const response = await fetch(`/api/v1/bigscreen/district-members/?district=${encodeURIComponent(districtName)}`)

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        this.districtMembers = data.members || []

        console.log(`成功获取${districtName}人员列表:`, this.districtMembers)
      } catch (error) {
        console.error('获取片区人员失败:', error)
        // 如果API调用失败，使用模拟数据作为兜底
        const mockMembers = [
          {
            id: 1,
            name: '张三',
            level: '市级代表',
            party: '中国共产党',
            avatar: ''
          },
          {
            id: 2,
            name: '李四',
            level: '县级代表',
            party: '群众',
            avatar: ''
          },
          {
            id: 3,
            name: '王五',
            level: '镇级代表',
            party: '中国共产党',
            avatar: ''
          }
        ]
        this.districtMembers = mockMembers
      }
    },

    handleAvatarError(event) {
      event.target.style.display = 'none'
    }

  },
}
</script>

<style scoped>
.popup {
  z-index: 999;
  background: rgba(10, 32, 64, 0.68);
  color: #fff;
  border: 1.5px solid #3a7bd5;
  border-radius: 14px;
  padding: 24px 15px 18px 15px;
  min-width: 260px;
  min-height: 320px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.45);
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.popup-title {
  color: #ffd700;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 18px;
  letter-spacing: 2px;
}
.popup-row {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.popup-circle-row {
  justify-content: space-around;
  margin-bottom: 18px;
}
.popup-circle {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(145deg, #1e3c72 60%, #2a5298 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(58, 123, 213, 0.18);
}
.popup-circle-num {
  font-size: 22px;
  font-weight: bold;
  color: #fff;
}
.popup-circle-label {
  font-size: 13px;
  color: #b0cfff;
  margin-top: 2px;
}
.popup-gender-label {
  font-size: 15px;
  color: #b0cfff;
  margin-bottom: 2px;
  justify-content: flex-start;
}
.popup-gender {
  justify-content: flex-start;
  margin-bottom: 14px;
}
.iconfont {
  font-size: 20px;
  margin-right: 2px;
}
.male {
  color: #4fc3f7;
}
.female {
  color: #f06292;
}
.popup-bar-row {
  justify-content: flex-start;
  margin-bottom: 8px;
}
.popup-bar-label {
  width: 100px;
  font-size: 13px;
  color: #b0cfff;
}
.popup-bar {
  flex: 1;
  height: 10px;
  background: #183a5a;
  border-radius: 5px;
  margin: 0 8px;
  overflow: hidden;
}
.popup-bar-inner {
  height: 100%;
  border-radius: 5px;
}
.popup-bar-inner.party {
  background: linear-gradient(90deg, #3a7bd5, #00d2ff);
}
.popup-bar-inner.advice {
  background: linear-gradient(90deg, #f7971e, #ffd200);
}
.popup-bar-inner.platform {
  background: linear-gradient(90deg, #43e97b, #38f9d7);
}
.popup-bar-value {
  width: 38px;
  text-align: right;
  font-size: 13px;
  color: #fff;
}
.popup-close {
  position: absolute;
  top: 10px;
  right: 16px;
  font-size: 22px;
  color: #b0cfff;
  cursor: pointer;
  font-weight: bold;
  transition: color 0.2s;
}
.popup-close:hover {
  color: #fff;
}

/* 片区统计样式 - 已移动到响应式部分 */

.stats-title {
  text-align: center;
  font-size: 16px; /* 减少字体大小 */
  font-weight: bold;
  margin-bottom: 12px; /* 减少底部间距 */
  color: #00d2ff;
  border-bottom: 1px solid #3a7bd5;
  padding-bottom: 6px; /* 减少底部padding */
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 10px; /* 减少间距 */
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px; /* 减少padding */
  background: transparent;
  border: 1px solid rgba(58, 123, 213, 0.3);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}



.stats-item:hover,
.stats-item.active {
  background: rgba(58, 123, 213, 0.3);
  border-color: #3a7bd5;
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(58, 123, 213, 0.4);
}

.district-name {
  font-size: 16px;
  font-weight: bold;
  color: #b0cfff;
}

.district-count {
  font-size: 18px;
  font-weight: bold;
  color: #00d2ff;
}

.stats-item.active .district-name {
  color: #fff;
}

.stats-item.active .district-count {
  color: #ffd200;
}

.clickable-district {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable-district:hover {
  background: rgba(64, 128, 255, 0.2) !important;
  transform: translateX(2px);
}

.stats-item.selected {
  background: rgba(255, 215, 0, 0.2) !important;
  border-color: #ffd700 !important;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.stats-item.selected .district-name {
  color: #ffd700 !important;
}

.stats-item.selected .district-count {
  color: #fff !important;
}

/* 二维码样式 - 优化为更紧凑的布局 */
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px 0 5px 0; /* 减少margin */
  padding: 15px; /* 增加padding，为悬停放大留出空间 */
  background: transparent;
  border: 1px solid rgba(58, 123, 213, 0.3);
  border-radius: 8px;
  overflow: visible; /* 确保放大的二维码不被裁剪 */
}

.qr-canvas {
  border: 2px solid #3a7bd5;
  border-radius: 4px;
  background: #fff;
  image-rendering: pixelated; /* 保持二维码的像素清晰度 */
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
  transition: all 0.3s ease; /* 添加transform过渡 */
  cursor: pointer; /* 鼠标指针样式 */
  width: 100%; /* 占满容器宽度 */
  height: auto; /* 保持宽高比 */
}

/* 点击提示效果 */
.qr-canvas:hover {
  cursor: pointer;
  opacity: 0.8;
  transform: scale(1.05); /* 轻微放大提示可点击 */
}

/* 大二维码弹出层 */
.large-qr-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;
  cursor: pointer; /* 点击背景关闭 */
}

.large-qr-container {
  background: #ffffff;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: scaleIn 0.3s ease;
  cursor: default; /* 防止继承父元素的pointer */
  pointer-events: auto; /* 确保容器可以接收鼠标事件 */
  position: relative; /* 为关闭按钮定位 */
}

.large-qr-canvas {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.large-qr-tip {
  margin-top: 20px;
  font-size: 16px;
  color: #333;
  text-align: center;
  font-weight: 500;
}

/* 关闭按钮样式 */
.close-btn {
  position: absolute;
  top: 10px;
  right: 15px;
  width: 30px;
  height: 30px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 20px;
  font-weight: bold;
  color: #666;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.2);
  color: #333;
  transform: scale(1.1);
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.qr-tip {
  margin-top: 5px; /* 减少顶部间距 */
  font-size: 11px; /* 减少字体大小 */
  color: #b0cfff;
  text-align: center;
  font-weight: 500;
}

/* Chart5 适应新的标题在线框上的样式 */
.chart5-container {
  width: 100%;
  height: 100%;
  display: flex;
  box-sizing: border-box;
  position: relative;
  background: transparent;
  border: none;
  overflow: visible; /* 允许标题文字显示 */
  padding: 0; /* 移除内部padding */
}

.map-area {
  flex: 1;
  height: 100%;
  min-width: 0;
  position: relative;
}

.district-stats {
  flex: 0 0 clamp(180px, 22vw, 280px); /* 减少宽度，为地图留出更多空间 */
  height: 100%;
  padding: 8px; /* 统一改为8px */
  background: transparent;
  color: #fff;
  box-sizing: border-box;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* 响应式适配 */
@media screen and (max-width: 1400px) {
  .district-stats {
    padding: 8px; /* 统一改为8px */
    min-width: 160px; /* 减少最小宽度 */
  }

  .qrcode-container {
    padding: 12px; /* 适中的padding，为悬停效果留空间 */
    margin: 8px 0 4px 0; /* 减少margin */
  }

  .stats-item {
    padding: 8px; /* 减少padding */
  }
}

@media screen and (max-width: 1200px) {
  .map-area {
    width: 65%;
  }

  .district-stats {
    width: 35%;
    padding: 8px;
    max-width: 250px;
  }

  .stats-title {
    font-size: 16px;
    margin-bottom: 15px;
  }

  .district-name {
    font-size: 14px;
  }

  .district-count {
    font-size: 16px;
  }

  .qrcode-container {
    padding: 12px;
    margin: 12px 0 6px 0;
  }

  .qr-tip {
    font-size: 11px;
  }
}

@media screen and (max-width: 1000px) {
  .map-area {
    width: 60%;
  }

  .district-stats {
    width: 40%;
    padding: 8px;
    max-width: 200px;
  }

  .stats-title {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .stats-item {
    padding: 10px;
  }

  .district-name {
    font-size: 13px;
  }

  .district-count {
    font-size: 14px;
  }

  .qrcode-container {
    padding: 10px;
    margin: 10px 0 5px 0;
  }
}

@media screen and (max-width: 800px) {
  .chart5-container {
    flex-direction: column; /* 小屏幕改为垂直布局 */
  }

  .map-area {
    width: 100%;
    height: 70%;
  }

  .district-stats {
    width: 100%;
    height: 30%;
    max-width: none;
    padding: 8px;
  }

  .stats-list {
    display: flex;
    flex-direction: row;
    gap: 10px;
    overflow-x: auto;
  }

  .stats-item {
    flex-shrink: 0;
    min-width: 120px;
    padding: 8px;
  }

  .qrcode-container {
    display: none; /* 小屏幕隐藏二维码 */
  }
}

@media screen and (max-width: 600px) {
  .district-stats {
    padding: 8px;
  }

  .stats-title {
    font-size: 12px;
    margin-bottom: 8px;
  }

  .stats-item {
    min-width: 100px;
    padding: 6px;
  }

  .district-name {
    font-size: 11px;
  }

  .district-count {
    font-size: 12px;
  }
}

/* 高DPI屏幕适配 */
@media screen and (-webkit-min-device-pixel-ratio: 1.5),
       screen and (min-resolution: 144dpi) {
  .district-stats {
    border-width: 1px; /* 确保边框在高DPI下清晰 */
  }

  .qr-canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: pixelated;
  }
}

/* 片区人员弹窗样式 */
.district-members {
  max-height: 60vh;
  overflow-y: auto;
}

.district-members::-webkit-scrollbar {
  width: 8px;
}

.district-members::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.district-members::-webkit-scrollbar-thumb {
  background: #4a90e2;
  border-radius: 4px;
}

.district-members::-webkit-scrollbar-thumb:hover {
  background: #357abd;
}

.no-data {
  text-align: center;
  color: #888;
  padding: 40px 20px;
  font-size: 16px;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 16px;
  padding: 10px 0;
}

.member-card {
  display: flex;
  align-items: center;
  padding: 14px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(74, 144, 226, 0.3);
  transition: all 0.3s ease;
}

.member-card:hover {
  background: rgba(74, 144, 226, 0.2);
  border-color: #4a90e2;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.3);
}

.member-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 14px;
  flex-shrink: 0;
  border: 2px solid #4a90e2;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
}

.member-info {
  flex: 1;
  min-width: 0;
}

.member-name {
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 6px;
  font-size: 15px;
}

.member-level {
  color: #4a90e2;
  font-size: 13px;
  margin-bottom: 3px;
  font-weight: 500;
}

.member-party {
  color: #aaaaaa;
  font-size: 12px;
}

</style>
