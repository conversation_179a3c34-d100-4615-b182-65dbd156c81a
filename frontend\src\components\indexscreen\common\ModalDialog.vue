<template>
  <Teleport to="body">
    <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
      <div class="modal-container" @click.stop>
        <!-- 弹窗头部 -->
        <div class="modal-header">
          <h3 class="modal-title">{{ title }}</h3>
          <button class="modal-close" @click="$emit('close')">&times;</button>
        </div>

        <!-- 弹窗内容 -->
        <div class="modal-body">
          <slot></slot>
        </div>

        <!-- 弹窗底部（可选） -->
        <div v-if="$slots.footer" class="modal-footer">
          <slot name="footer"></slot>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script>
export default {
  name: 'ModalDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '详情'
    },
    closeOnOverlay: {
      type: Boolean,
      default: true
    }
  },
  emits: ['close'],
  methods: {
    handleOverlayClick() {
      if (this.closeOnOverlay) {
        this.$emit('close')
      }
    }
  },
  mounted() {
    // 阻止背景滚动
    if (this.visible) {
      document.body.style.overflow = 'hidden'
    }
  },
  beforeUnmount() {
    // 恢复背景滚动
    document.body.style.overflow = ''
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        document.body.style.overflow = 'hidden'
      } else {
        document.body.style.overflow = ''
      }
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.8);
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  z-index: 999999 !important;
  backdrop-filter: blur(3px);
  margin: 0 !important;
  padding: 0 !important;
  transform: none !important;
}

.modal-container {
  background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
  border: 2px solid #4a90e2;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5), 0 0 20px rgba(74, 144, 226, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  min-width: 400px;
  min-height: 200px;
  overflow: visible;
  animation: modalFadeIn 0.3s ease-out;
  position: relative !important;
  transform: none !important;
  margin: 0 !important;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 2px solid #4a90e2;
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
  color: #ffffff;
}

.modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #4a90e2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.modal-close {
  background: none;
  border: 2px solid #4a90e2;
  font-size: 20px;
  color: #4a90e2;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #4a90e2;
  color: white;
  box-shadow: 0 0 10px rgba(74, 144, 226, 0.5);
}

.modal-body {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
  background: #1a2332;
  color: #ffffff;
  min-height: 100px;
  width: 100%;
  box-sizing: border-box;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 2px solid #4a90e2;
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-container {
    min-width: 320px;
    margin: 20px;
  }
  
  .modal-header {
    padding: 16px 20px;
  }
  
  .modal-title {
    font-size: 16px;
  }
  
  .modal-body {
    padding: 20px;
  }
  
  .modal-footer {
    padding: 12px 20px;
  }
}
</style>
