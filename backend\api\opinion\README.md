# 意见建议管理模块

## 模块概述

意见建议管理模块是人大代表履职服务与管理平台的核心功能之一，负责处理群众意见建议的完整生命周期管理。

## 功能特性

### 🎯 核心功能

1. **意见建议录入** - 代表录入群众反映的意见建议
2. **AI辅助优化** - 使用AI技术优化和规范化意见建议内容
3. **审核流程管理** - 工作人员审核、转交、跟踪处理进展
4. **状态流转控制** - 完整的状态流转和权限控制
5. **统计分析** - 多维度的数据统计和分析

### 🔐 安全特性

- **严格权限控制** - 基于角色的细粒度权限管理
- **数据隔离** - 代表只能访问自己的数据
- **操作审计** - 完整的操作日志记录
- **数据验证** - 多层次的数据验证机制

## 技术架构

### 数据模型设计

#### OpinionSuggestion（意见建议表）
- 存储意见建议的基础信息
- 不包含状态字段，状态通过审核记录动态获取
- 支持AI辅助生成内容的存储

#### OpinionReview（审核记录表）
- 记录所有审核操作和状态变更
- 支持附件管理（JSON格式存储路径）
- 完整的审核历史追溯

### API设计

#### 代表端API
```
GET    /api/v1/opinions/suggestions/           # 获取意见建议列表
POST   /api/v1/opinions/suggestions/create/    # 创建意见建议
GET    /api/v1/opinions/suggestions/{id}/      # 获取意见建议详情
PUT    /api/v1/opinions/suggestions/{id}/update/ # 更新意见建议
POST   /api/v1/opinions/suggestions/{id}/submit/ # 提交意见建议
POST   /api/v1/opinions/ai/generate/           # AI辅助生成
GET    /api/v1/opinions/statistics/            # 个人统计数据
```

#### 工作人员端API
```
GET    /api/v1/opinions/suggestions/           # 获取所有意见建议
POST   /api/v1/opinions/suggestions/{id}/review/ # 审核意见建议
GET    /api/v1/opinions/statistics/            # 全局统计数据
```

### 权限矩阵

| 操作 | 代表 | 工作人员 | 说明 |
|------|------|----------|------|
| 创建意见建议 | ✅ | ❌ | 只有代表可以创建 |
| 查看意见建议 | ✅(自己的) | ✅(所有) | 数据隔离 |
| 修改意见建议 | ✅(草稿/驳回) | ❌ | 限制状态 |
| 提交意见建议 | ✅(自己的) | ❌ | 状态流转 |
| 审核意见建议 | ❌ | ✅ | 工作人员专属 |
| 使用AI功能 | ✅ | ❌ | 代表专属 |
| 删除意见建议 | ✅(草稿) | ✅(任意) | 权限分级 |

## 状态流转图

```
草稿(draft) 
    ↓ [代表提交]
已提交(submitted)
    ↓ [工作人员审核]
审核通过(approved) / 审核驳回(rejected)
    ↓ [工作人员转交]
已转交(transferred)
    ↓ [部门开始处理]
处理中(in_progress)
    ↓ [标记办结]
已办结(completed)
```

### 状态说明

- **draft**: 草稿状态，代表创建但未提交的意见建议
- **submitted**: 已提交状态，代表提交到站点等待审核
- **under_review**: 待审核状态，工作人员正在审核中
- **approved**: 审核通过状态，工作人员审核通过
- **rejected**: 审核驳回状态，需要代表修改后重新提交
- **transferred**: 已转交状态，已转交给相关政府部门
- **in_progress**: 处理中状态，相关部门正在处理中
- **completed**: 已办结状态，意见建议处理完成

### 统计分类说明

基于`opinion_reviews`表中每个意见建议的最新审核状态进行统计：

- **待审核**: 最新状态为 `submitted` 的意见建议数量
- **已通过待转交**: 最新状态为 `approved` 的意见建议数量  
- **处理中**: 最新状态为 `transferred` 或 `in_progress` 的意见建议数量（包含两个状态）
- **已办结**: 最新状态为 `completed` 的意见建议数量

#### 处理中状态详细说明

"处理中"统计包含两个具体状态：
1. **transferred**: 意见建议已转交给相关政府部门，但部门尚未开始处理
2. **in_progress**: 相关部门已开始处理意见建议，正在推进解决

这两个状态都表示意见建议正在政府部门的处理流程中，因此在工作人员统计界面中合并显示为"处理中"。

## 使用示例

### 代表创建意见建议

```python
# POST /api/v1/opinions/suggestions/create/
{
    "title": "关于某路段路灯损坏的建议",
    "category": "urban_construction",
    "reporter_name": "张三",
    "original_content": "某路段多盏路灯损坏，影响夜间通行安全",
    "final_suggestion": "建议相关部门尽快修复损坏路灯，确保道路照明",
    "ai_assisted": false
}
```

### AI辅助生成

```python
# POST /api/v1/opinions/ai/generate/
{
    "original_content": "某路段多盏路灯损坏，影响夜间通行安全",
    "category": "urban_construction",
    "context": "该路段为主要通行道路，夜间车流量较大"
}

# 响应
{
    "success": true,
    "data": {
        "generated_suggestion": "建议市政部门立即组织专业队伍对损坏路灯进行全面检修...",
        "key_points": ["道路安全", "照明设施", "夜间通行"],
        "suggested_actions": ["立即检修", "建立巡查机制", "设置临时照明"],
        "confidence_score": 0.85
    }
}
```

### 工作人员审核

```python
# POST /api/v1/opinions/suggestions/{id}/review/
{
    "action": "approve",
    "review_comment": "建议合理，同意转交相关部门处理",
    "transferred_department": "市政管理局"
}
```

## 部署说明

### 环境要求
- Python 3.8+
- Django 5.2 LTS
- Django REST Framework
- PostgreSQL/MySQL（生产环境）

### 安装步骤

1. **添加应用到设置**
```python
# settings.py
INSTALLED_APPS = [
    # ...
    'api.opinion',
    # ...
]
```

2. **配置URL路由**
```python
# urls.py
urlpatterns = [
    # ...
    path('api/v1/opinions/', include('api.opinion.urls')),
    # ...
]
```

3. **执行数据库迁移**
```bash
python manage.py makemigrations opinion
python manage.py migrate
```

4. **创建测试数据**
```bash
python manage.py shell
# 在shell中创建测试用户和代表数据
```

## 测试

### 运行测试
```bash
# 运行所有测试
python manage.py test api.opinion

# 运行特定测试类
python manage.py test api.opinion.tests.OpinionModelTest

# 运行特定测试方法
python manage.py test api.opinion.tests.OpinionModelTest.test_opinion_creation
```

### 测试覆盖率
```bash
# 安装coverage
pip install coverage

# 运行覆盖率测试
coverage run --source='.' manage.py test api.opinion
coverage report
coverage html
```

## 监控和维护

### 日志监控
- 所有API操作都有详细的日志记录
- 关键操作（创建、审核、状态变更）会记录操作用户和时间
- 建议配置日志聚合和告警系统

### 性能优化
- 使用`select_related`和`prefetch_related`优化数据库查询
- 对常用查询字段建立索引
- 实现查询结果缓存（Redis）

### 安全建议
- 定期更新依赖包
- 启用HTTPS
- 配置CORS白名单
- 实施API限流
- 定期进行安全审计

## 扩展功能

### 计划功能
- [ ] 意见建议批量导入/导出
- [ ] 邮件/短信通知功能
- [ ] 高级搜索和过滤
- [ ] 数据可视化报表
- [ ] 移动端适配

### 集成建议
- 与通知系统集成，实现状态变更通知
- 与工作计划系统集成，关联处理任务
- 与统计分析系统集成，生成综合报表

## 🔗 前后端对接状态

### ✅ 已完成对接功能

#### 前端API模块 (`frontend/src/api/modules/opinion/`)
- **API配置**: 已在 `frontend/src/api/http/config.js` 中添加 `OPINION_ENDPOINTS` 配置
- **API接口**: 完整实现 `opinionAPI`、`opinionAIAPI`、`opinionUtils` 模块
- **页面组件**: 已更新 `frontend/src/views/representative/Opinions.vue` 使用真实API

#### 核心功能对接
1. **意见建议CRUD操作**
   - ✅ 创建意见建议 (`POST /api/v1/opinions/suggestions/create/`)
   - ✅ 获取意见建议列表 (`GET /api/v1/opinions/suggestions/`)
   - ✅ 获取意见建议详情 (`GET /api/v1/opinions/suggestions/:id/`)
   - ✅ 更新意见建议 (`PUT /api/v1/opinions/suggestions/:id/update/`)
   - ✅ 删除意见建议 (`DELETE /api/v1/opinions/suggestions/:id/delete/`)

2. **意见建议提交流程**
   - ✅ 提交意见建议 (`POST /api/v1/opinions/suggestions/:id/submit/`)
   - ✅ 状态流转管理（草稿→已提交→审核→转交→办结）

3. **AI辅助功能**
   - ✅ AI辅助生成高质量意见建议 (`POST /api/v1/opinions/ai/generate/`)
   - ✅ 前端集成AI生成功能，支持基于原始内容和分类生成建议

4. **数据统计功能**
   - ✅ 获取意见建议统计数据 (`GET /api/v1/opinions/statistics/`)

#### 前端功能特性
- **响应式设计**: 支持PC和移动端访问
- **实时数据**: 使用真实API数据，支持分页、筛选、搜索
- **用户体验**: 
  - 表单验证和错误提示
  - Loading状态管理
  - 操作成功/失败反馈
  - AI生成过程的用户友好提示

#### 数据字段映射
前端组件已完全适配后端API数据结构：
- `title` → 意见建议标题
- `category` → 意见建议分类（使用枚举值）
- `reporter_name` → 反映人姓名
- `original_content` → 原始意见内容
- `final_suggestion` → 最终意见建议
- `ai_assisted` → 是否使用AI辅助
- `ai_generated_content` → AI生成的建议内容
- `created_at/updated_at` → 创建/更新时间

#### 权限控制
- ✅ 代表只能操作自己创建的意见建议
- ✅ 前端API调用包含身份验证token
- ✅ 错误处理包含权限验证失败的情况

### 🔧 测试和调试
- **API测试文件**: `frontend/src/api/modules/opinion/test.js`
- **浏览器控制台测试**: 可运行 `testOpinionAPI.runAllTests()` 进行完整API测试
- **后端测试**: 完整的测试套件覆盖所有API端点

### 📊 统计API实现细节

#### 后端统计逻辑 (`OpinionStatisticsView._get_staff_statistics`)
```python
# 使用子查询获取每个意见建议的最新审核状态
latest_status = OpinionReview.objects.filter(
    opinion=OuterRef('pk')
).order_by('-id').values('status')[:1]

opinions_with_status = opinions.annotate(
    current_status_db=Subquery(latest_status)
)

# 按最新状态统计
submitted_count = opinions_with_status.filter(
    current_status_db='submitted'
).count()

transferred_count = opinions_with_status.filter(
    current_status_db__in=['transferred', 'in_progress']  # 处理中包含两个状态
).count()
```

#### 前端统计字段映射
```javascript
stats.value = {
  submitted: data.pending_review_count,     // 待审核：submitted状态
  approved: data.approved_count,            // 已通过待转交：approved状态
  transferred: data.transferred_count,      // 处理中：transferred + in_progress状态
  completed: data.completed_opinions        // 已办结：completed状态
}
```

### 📝 更新记录
- **2025-06-23 v1.2.4**: 完善状态流程文档
  - 📋 **明确统计分类说明**: 详细说明基于`opinion_reviews`最新状态的统计逻辑
    - 明确"处理中"包含`transferred`和`in_progress`两个状态
    - 添加统计API实现细节和前后端字段映射说明
    - 完善状态流转图和各状态含义说明
    - 确保文档与代码实现完全一致
- **2024-12-22 v1.1.10**: 优化时间线显示标签
  - 🎨 **优化评论标签显示**: 根据操作类型显示合适的标签文字
    - 新增 `getCommentLabel` 函数，为不同操作类型提供专业标签：
      - 创建草稿：显示"说明"
      - 代表提交：显示"提交说明"
      - 审核通过：显示"审核意见"
      - 审核驳回：显示"驳回理由"
      - 转交部门：显示"转交说明"
      - 更新进度：显示"处理进度"
      - 标记办结：显示"办结结果"
    - 避免所有操作都显示"审核意见"的不专业表述
- **2024-12-22 v1.1.9**: 修复代表页面处理结果显示问题
  - 🐛 **修复处理结果显示不完整**: 解决代表无法看到处理进度和办结结果的问题
    - 发现"更新进度"和"办结"操作使用 `processing_result` 字段，而代表页面只显示 `review_comment`
    - 新增 `getReviewComment` 函数，智能选择显示内容：
      - 更新进度/办结操作：优先显示 `processing_result`，回退到 `review_comment`
      - 其他操作：优先显示 `review_comment`，回退到 `processing_result`
    - 确保代表能够看到完整的处理进展和最终结果
- **2024-12-22 v1.1.8**: 修复状态获取逻辑根本问题
  - 🐛 **修复最新审核记录获取错误**: 解决API返回错误的最新审核记录问题
    - 发现数据库中最新记录ID=101，但API返回的是ID=76的问题
    - 修改为使用 `order_by('-id')` 获取真正的最新记录（ID自增，最大ID即最新）
    - 放弃复杂的时间排序，直接使用数据库主键排序确保准确性
    - 确保 `current_status` 和 `latest_review` 返回真正最新的审核记录
- **2024-12-22 v1.1.6**: 优化审核功能
  - 🔧 **移除建议转交功能**: 审核对话框中移除"建议转交部门"选择框
    - 审核专注于内容审核，只处理通过/驳回决定
    - 转交部门作为审核通过后的独立操作
    - 简化审核流程，避免功能混淆
    - 保持现有转交对话框和相关功能完整性
- **2024-12-22 v1.1.5**: 修复审核流程和UI显示问题
  - 🐛 **修复审核状态未更新问题**: 解决审核通过后状态仍显示"待审核"的问题
    - 分离审核和转交操作逻辑，确保状态正确更新
    - 审核通过后如果选择了转交部门，会立即执行转交操作
    - 增强错误处理，提供更清晰的操作反馈
  - 🎨 **修复操作按钮对齐问题**: 确保所有操作按钮在同一水平线上
    - 为表格操作列添加 `table-action-buttons` 样式类
    - 使用 flexbox 布局确保按钮水平对齐
    - 调整操作列宽度为 240px，提供更多空间
  - 📊 **审核流程优化**: 
    - 第一步执行审核（通过/驳回）
    - 第二步如果审核通过且选择了部门，自动执行转交
    - 提供清晰的操作状态反馈和错误提示
- **2024-12-22 v1.1.4**: 修复工作人员审核页面问题
  - 🐛 **修复状态更新问题**: 解决审核意见写入数据库但页面状态未更新的问题
    - 优化数据转换逻辑，保留原始分类值用于颜色映射
    - 增强分类颜色映射函数，支持显示名称和原始值两种格式
    - 添加调试日志，便于排查API调用和数据更新问题
  - 🔧 **移除无效字段**: 移除转交对话框中的"期望回复时间"字段
    - 前端移除 `expectedDate` 字段和相关输入框
    - 后端数据库模型中不存在此字段，避免数据不匹配
    - 简化转交表单，只保留必要的部门和说明字段
- **2024-12-22 v1.1.3**: 修复处理流程时间线排序问题
  - 🐛 **修复时间线顺序错乱**: 解决同一分钟内多个操作显示顺序不正确的问题
    - 后端序列化器优化审核历史排序：`order_by('action_time', 'id')`
    - 确保相同时间的操作按数据库ID顺序显示
    - 前端移除重复排序逻辑，直接使用后端排序结果
  - 📈 **时间线显示优化**: 处理流程现在严格按照实际操作顺序显示
    - 创建草稿 → 代表提交 → 工作人员审核 → 转交部门 → 办结
    - 即使操作发生在同一分钟内，也能保证正确的时序
- **2024-12-22 v1.1.2**: 优化代表页面用户体验
  - 🔧 **审核驳回后可编辑**: 修复代表页面审核驳回后无法编辑的问题
    - 编辑和提交按钮现在对 `rejected` 状态也显示
    - 重新提交按钮显示"重新提交"文字，优化用户体验
    - 区分首次提交和重新提交的确认对话框文本
  - 📈 **完善处理流程时间线**: 修复时间线只显示两个节点的问题
    - 查看详情时调用详情API获取完整审核历史 (`review_history`)
    - 时间线显示所有审核记录，包含操作人和审核评论
    - 优化操作描述映射，显示更友好的中文描述
    - 转交操作显示具体的转交部门信息
    - 增强时间线样式，提升可读性
- **2024-12-22 v1.1.1**: 修复工作人员审核页面显示问题
  - 🐛 **修复代表名称显示**: 修正前端数据映射，正确显示提交代表姓名
  - 🐛 **完善状态管理**: 更新状态映射和筛选选项，支持完整状态流转
  - 📝 **状态说明**: 添加详细的状态说明文档，明确 `in_progress` 状态含义
  - 🎨 **分类对齐**: 统一前后端分类值，使用英文标识符
- **2024-12-22 v1.1**: 完成工作人员审核功能前后端对接
  - ✅ 更新 `frontend/src/api/modules/opinion/api.js` 添加审核相关API方法
  - ✅ 完善 `frontend/src/views/staff/Review.vue` 实现真实API调用
  - ✅ 支持审核、转交、进度更新、办结等完整流程
  - ✅ 集成统计数据展示和实时数据更新
- **2024-12-22 v1.0**: 完成代表页面意见建议功能前后端对接
  - ✅ 代表端意见建议管理功能全面可用
  - ✅ 支持完整的意见建议生命周期管理

## 工作人员审核功能详细说明

### 新增API方法
```javascript
// 审核意见建议
opinionAPI.reviewOpinion(opinionId, reviewData)

// 获取审核列表（支持筛选）
opinionAPI.getReviewList(params)
```

### 审核操作类型
- **approve**: 审核通过
- **reject**: 审核驳回
- **transfer**: 转交部门
- **update_progress**: 更新处理进度
- **close**: 办结意见建议

### 前端功能特性
- **实时数据**: 所有操作都与后端API同步
- **状态管理**: 支持完整的状态流转
- **筛选功能**: 按状态、分类、提交人筛选
- **统计展示**: 实时统计数据展示
- **用户体验**: 完善的加载状态和错误处理

## 技术支持

如有问题或建议，请联系开发团队或提交Issue。 