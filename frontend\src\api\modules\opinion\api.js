/**
 * 意见建议管理API请求方法
 * 包含意见建议的创建、查看、编辑、提交、AI辅助生成等接口
 */
import httpClient from '../../http/client'
import { OPINION_ENDPOINTS, API_CONFIG } from '../../http/config'

/**
 * 意见建议API
 */
export const opinionAPI = {
  /**
   * 获取意见建议列表
   * @param {Object} params 查询参数
   * @param {string} params.status 状态筛选 (draft|submitted|approved|rejected|transferred|in_progress|completed)
   * @param {string} params.category 分类筛选
   * @param {string} params.search 搜索关键词（标题、反映人姓名）
   * @param {string} params.ordering 排序字段 (created_at|-created_at|updated_at|-updated_at)
   * @param {number} params.page 页码
   * @param {number} params.page_size 每页数量
   * @returns {Promise} 意见建议列表
   */
  getOpinionsList(params = {}) {
    return httpClient.get(OPINION_ENDPOINTS.LIST, { params })
  },

  /**
   * 获取意见建议详情
   * @param {number} opinionId 意见建议ID
   * @returns {Promise} 意见建议详情
   */
  getOpinionDetail(opinionId) {
    const url = OPINION_ENDPOINTS.DETAIL.replace(':id', opinionId)
    return httpClient.get(url)
  },

  /**
   * 创建意见建议
   * @param {Object} opinionData 意见建议数据
   * @param {string} opinionData.title 意见建议标题
   * @param {string} opinionData.category 意见建议分类
   * @param {string} opinionData.reporter_name 反映人姓名
   * @param {string} opinionData.original_content 原始意见内容
   * @param {string} opinionData.final_suggestion 最终建议内容
   * @param {boolean} opinionData.ai_assisted 是否使用AI辅助
   * @param {string} opinionData.ai_generated_content AI生成的建议内容
   * @returns {Promise} 创建结果
   */
  createOpinion(opinionData) {
    return httpClient.post(OPINION_ENDPOINTS.CREATE, opinionData)
  },

  /**
   * 更新意见建议
   * @param {number} opinionId 意见建议ID
   * @param {Object} opinionData 更新的意见建议数据
   * @returns {Promise} 更新结果
   */
  updateOpinion(opinionId, opinionData) {
    const url = OPINION_ENDPOINTS.UPDATE.replace(':id', opinionId)
    return httpClient.put(url, opinionData)
  },

  /**
   * 删除意见建议
   * @param {number} opinionId 意见建议ID
   * @returns {Promise} 删除结果
   */
  deleteOpinion(opinionId) {
    const url = OPINION_ENDPOINTS.DELETE.replace(':id', opinionId)
    return httpClient.delete(url)
  },

  /**
   * 提交意见建议到站点
   * @param {number} opinionId 意见建议ID
   * @returns {Promise} 提交结果
   */
  submitOpinion(opinionId) {
    const url = OPINION_ENDPOINTS.SUBMIT.replace(':id', opinionId)
    return httpClient.post(url)
  },

  /**
   * 获取意见建议统计数据
   * @returns {Promise} 统计数据
   */
  getOpinionStatistics() {
    return httpClient.get(OPINION_ENDPOINTS.STATISTICS)
  },

  /**
   * 审核意见建议
   * @param {number} opinionId 意见建议ID
   * @param {Object} reviewData 审核数据
   * @param {string} reviewData.action 操作动作 (approve|reject|transfer|update_progress|close)
   * @param {string} reviewData.review_comment 审核备注
   * @param {string} reviewData.transferred_department 转交部门（transfer时必填）
   * @param {string} reviewData.processing_result 处理结果（update_progress/close时必填）
   * @param {Array} reviewData.attachment_files 附件文件列表（可选）
   * @returns {Promise} 审核结果
   */
  reviewOpinion(opinionId, reviewData) {
    const url = OPINION_ENDPOINTS.REVIEW.replace(':id', opinionId)
    return httpClient.post(url, reviewData)
  },

  /**
   * 获取工作人员审核列表（带筛选）
   * @param {Object} params 查询参数
   * @param {string} params.status 状态筛选 (submitted|approved|rejected|transferred|in_progress|completed)
   * @param {string} params.category 分类筛选
   * @param {string} params.submitter 提交代表筛选
   * @param {string} params.search 搜索关键词
   * @param {string} params.ordering 排序字段
   * @param {number} params.page 页码
   * @param {number} params.page_size 每页数量
   * @returns {Promise} 审核列表
   */
  getReviewList(params = {}) {
    return httpClient.get(OPINION_ENDPOINTS.LIST, { params })
  }
}

/**
 * AI辅助功能API
 */
export const opinionAIAPI = {
  /**
   * AI辅助生成高质量意见建议
   * @param {Object} aiData AI生成请求数据
   * @param {string} aiData.original_content 原始意见内容
   * @param {string} aiData.category 意见分类
   * @param {string} aiData.context 补充背景信息（可选）
   * @returns {Promise} AI生成结果
   */
  generateSuggestion(aiData) {
    // 调用aiknowledge应用的意见建议生成API
    return httpClient.post(OPINION_ENDPOINTS.AI_GENERATE, aiData, {
      timeout: API_CONFIG.AI_TIMEOUT, // 使用AI专用的更长超时时间
      showLoading: false // 在组件中已有loading状态，避免重复显示
    })
  }
}

/**
 * 意见建议工具函数
 */
export const opinionUtils = {
  /**
   * 获取意见建议分类选项（16个分类，涵盖各个方面）
   * @returns {Array} 分类选项列表
   */
  getCategoryOptions() {
    return [
      { label: '城建环保', value: 'urban_construction' },
      { label: '交通出行', value: 'transportation' },
      { label: '教育文化', value: 'education' },
      { label: '医疗卫生', value: 'healthcare' },
      { label: '社会保障', value: 'social_security' },
      { label: '经济发展', value: 'economic' },
      { label: '政务服务', value: 'government_service' },
      { label: '公共安全', value: 'public_safety' },
      { label: '社区服务', value: 'community_service' },
      { label: '住房保障', value: 'housing' },
      { label: '就业创业', value: 'employment' },
      { label: '养老服务', value: 'elderly_care' },
      { label: '食品安全', value: 'food_safety' },
      { label: '文体娱乐', value: 'cultural_sports' },
      { label: '数字政务', value: 'digital_governance' },
      { label: '其他', value: 'other' }
    ]
  },

  /**
   * 获取状态选项
   * @returns {Array} 状态选项列表
   */
  getStatusOptions() {
    return [
      { label: '草稿', value: 'draft', color: 'info' },
      { label: '已提交', value: 'submitted', color: 'primary' },
      { label: '审核通过', value: 'approved', color: 'success' },
      { label: '审核驳回', value: 'rejected', color: 'danger' },
      { label: '已转交', value: 'transferred', color: 'warning' },
      { label: '处理中', value: 'in_progress', color: 'warning' },
      { label: '已办结', value: 'completed', color: 'success' }
    ]
  },

  /**
   * 根据分类值获取分类标签
   * @param {string} categoryValue 分类值
   * @returns {string} 分类标签
   */
  getCategoryLabel(categoryValue) {
    const option = this.getCategoryOptions().find(item => item.value === categoryValue)
    return option ? option.label : categoryValue
  },

  /**
   * 根据状态值获取状态标签
   * @param {string} statusValue 状态值
   * @returns {string} 状态标签
   */
  getStatusLabel(statusValue) {
    const option = this.getStatusOptions().find(item => item.value === statusValue)
    return option ? option.label : statusValue
  },

  /**
   * 根据状态值获取状态颜色
   * @param {string} statusValue 状态值
   * @returns {string} 状态颜色
   */
  getStatusColor(statusValue) {
    const option = this.getStatusOptions().find(item => item.value === statusValue)
    return option ? option.color : 'info'
  },

  /**
   * 根据分类值获取分类颜色
   * @param {string} categoryValue 分类值
   * @returns {string} 分类颜色
   */
  getCategoryColor(categoryValue) {
    const colorMap = {
      'urban_construction': 'success',
      'transportation': 'warning',
      'education': 'primary',
      'healthcare': 'danger',
      'social_security': 'info',
      'economic': 'success',
      'government_service': 'primary',
      'public_safety': 'danger',
      'community_service': 'info',
      'housing': 'warning',
      'employment': 'success',
      'elderly_care': 'primary',
      'food_safety': 'danger',
      'cultural_sports': 'info',
      'digital_governance': 'primary',
      'other': 'info'
    }
    return colorMap[categoryValue] || 'info'
  },

  /**
   * 格式化日期时间
   * @param {string} dateTimeString 日期时间字符串
   * @returns {string} 格式化后的日期时间
   */
  formatDateTime(dateTimeString) {
    if (!dateTimeString) return ''
    const date = new Date(dateTimeString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  },

  /**
   * 格式化日期
   * @param {string} dateString 日期字符串
   * @returns {string} 格式化后的日期
   */
  formatDate(dateString) {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN')
  },

  /**
   * 验证意见建议表单数据
   * @param {Object} formData 表单数据
   * @returns {Object} 验证结果 { valid: boolean, errors: Array }
   */
  validateOpinionForm(formData) {
    const errors = []

    if (!formData.title || formData.title.trim().length < 5) {
      errors.push('标题长度不能少于5个字符')
    }

    if (!formData.category) {
      errors.push('请选择意见建议分类')
    }

    if (!formData.reporter_name || formData.reporter_name.trim().length === 0) {
      errors.push('请输入反映人姓名')
    }

    if (!formData.original_content || formData.original_content.trim().length < 10) {
      errors.push('原始意见内容长度不能少于10个字符')
    }

    if (formData.ai_assisted && !formData.ai_generated_content) {
      errors.push('使用AI辅助时必须提供AI生成的内容')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
}

// 导出默认对象
export default {
  opinionAPI,
  opinionAIAPI,
  opinionUtils
} 