"""
工作计划管理测试

包含以下测试：
1. WorkPlanModelTests - 模型测试
2. WorkPlanAPITests - API功能测试  
3. WorkPlanPermissionTests - 权限测试
"""

from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from datetime import date, timedelta

from .models import WorkPlan

User = get_user_model()


class WorkPlanModelTests(TestCase):
    """工作计划模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            username='teststaff',
            password='testpass123',
            role='staff'
        )
    
    def test_create_work_plan(self):
        """测试创建工作计划"""
        work_plan = WorkPlan.objects.create(
            staff_member=self.user,
            title='测试计划',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=7),
            content='测试内容',
            status='planned',
            reminder_days=3
        )
        
        self.assertEqual(work_plan.title, '测试计划')
        self.assertEqual(work_plan.status, 'planned')
        self.assertEqual(work_plan.reminder_days, 3)
        self.assertEqual(work_plan.staff_member, self.user)
    
    def test_should_remind_property(self):
        """测试提醒属性"""
        # 创建即将到期的计划
        work_plan = WorkPlan.objects.create(
            staff_member=self.user,
            title='即将到期的计划',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=2),  # 2天后到期
            content='测试内容',
            status='planned',
            reminder_days=3  # 提前3天提醒
        )
        
        self.assertTrue(work_plan.should_remind)
    
    def test_is_overdue_property(self):
        """测试逾期属性"""
        # 创建逾期计划
        work_plan = WorkPlan.objects.create(
            staff_member=self.user,
            title='逾期计划',
            start_date=date.today() - timedelta(days=10),
            end_date=date.today() - timedelta(days=1),  # 昨天到期
            content='测试内容',
            status='planned',  # 还未完成
            reminder_days=3
        )
        
        self.assertTrue(work_plan.is_overdue)
    
    def test_get_reminder_plans(self):
        """测试获取提醒计划"""
        # 创建需要提醒的计划
        WorkPlan.objects.create(
            staff_member=self.user,
            title='需要提醒的计划',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=2),
            content='测试内容',
            status='planned',
            reminder_days=3
        )
        
        reminder_plans = WorkPlan.get_reminder_plans()
        self.assertEqual(len(reminder_plans), 1)
        self.assertEqual(reminder_plans[0].title, '需要提醒的计划')
    
    def test_get_overdue_plans(self):
        """测试获取逾期计划"""
        # 创建逾期计划
        WorkPlan.objects.create(
            staff_member=self.user,
            title='逾期计划',
            start_date=date.today() - timedelta(days=10),
            end_date=date.today() - timedelta(days=1),
            content='测试内容',
            status='in_progress',  # 还未完成
            reminder_days=3
        )
        
        overdue_plans = WorkPlan.get_overdue_plans()
        self.assertEqual(len(overdue_plans), 1)
        self.assertEqual(overdue_plans[0].title, '逾期计划')


class WorkPlanAPITests(APITestCase):
    """工作计划API测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
        
        # 创建测试用户
        self.staff_user1 = User.objects.create_user(
            username='staff1',
            password='testpass123',
            role='staff'
        )
        
        self.staff_user2 = User.objects.create_user(
            username='staff2',
            password='testpass123',
            role='staff'
        )
        
        self.normal_user = User.objects.create_user(
            username='normaluser',
            password='testpass123',
            role='representative'
        )
        
        # 创建测试工作计划
        self.work_plan1 = WorkPlan.objects.create(
            staff_member=self.staff_user1,
            title='工作人员1的计划',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=7),
            content='测试内容1',
            status='planned',
            reminder_days=3
        )
        
        self.work_plan2 = WorkPlan.objects.create(
            staff_member=self.staff_user2,
            title='工作人员2的计划',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=14),
            content='测试内容2',
            status='in_progress',
            reminder_days=5
        )
        
        # URL配置
        self.list_url = reverse('workplan:work_plan_list_create')
        self.statistics_url = reverse('workplan:work_plan_statistics')
        self.reminders_url = reverse('workplan:work_plan_reminders')
    
    def get_jwt_token(self, user):
        """获取JWT令牌"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def test_create_work_plan(self):
        """测试创建工作计划"""
        token = self.get_jwt_token(self.staff_user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        data = {
            'title': '新的工作计划',
            'start_date': date.today(),
            'end_date': date.today() + timedelta(days=7),
            'content': '新计划的内容',
            'status': 'planned',
            'reminder_days': 3
        }
        
        response = self.client.post(self.list_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['data']['title'], '新的工作计划')
        self.assertEqual(response.data['data']['staff_member'], self.staff_user1.pk)
    
    def test_create_work_plan_invalid_data(self):
        """测试创建工作计划时的数据验证"""
        token = self.get_jwt_token(self.staff_user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 结束时间早于开始时间
        data = {
            'title': '无效计划',
            'start_date': date.today() + timedelta(days=7),
            'end_date': date.today(),  # 结束时间早于开始时间
            'content': '测试内容',
            'status': 'planned',
            'reminder_days': 3
        }
        
        response = self.client.post(self.list_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('end_date', response.data['errors'])
    
    def test_staff_can_see_all_plans(self):
        """测试工作人员可以查看所有计划"""
        token = self.get_jwt_token(self.staff_user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(self.list_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['results']), 2)  # 应该看到两个计划
        
        # 验证能看到不同工作人员的计划
        titles = [plan['title'] for plan in response.data['data']['results']]
        self.assertIn('工作人员1的计划', titles)
        self.assertIn('工作人员2的计划', titles)
    
    def test_staff_can_access_any_plan_detail(self):
        """测试工作人员可以访问任何计划的详情"""
        token = self.get_jwt_token(self.staff_user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 访问工作人员2的计划
        url = reverse('workplan:work_plan_detail', kwargs={'pk': self.work_plan2.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['title'], '工作人员2的计划')
        self.assertEqual(response.data['data']['id'], self.work_plan2.pk)
    
    def test_staff_can_update_any_plan(self):
        """测试工作人员可以更新任何计划"""
        token = self.get_jwt_token(self.staff_user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 更新工作人员2的计划
        url = reverse('workplan:work_plan_detail', kwargs={'pk': self.work_plan2.pk})
        data = {
            'title': '更新后的计划标题',
            'start_date': self.work_plan2.start_date,
            'end_date': self.work_plan2.end_date,
            'content': '更新后的内容',
            'status': 'completed',
            'reminder_days': 7
        }
        
        response = self.client.put(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['title'], '更新后的计划标题')
        self.assertEqual(response.data['data']['status'], 'completed')
    
    def test_staff_can_delete_any_plan(self):
        """测试工作人员可以删除任何计划"""
        token = self.get_jwt_token(self.staff_user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 删除工作人员2的计划
        url = reverse('workplan:work_plan_detail', kwargs={'pk': self.work_plan2.pk})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证计划已被删除
        with self.assertRaises(WorkPlan.DoesNotExist):
            WorkPlan.objects.get(pk=self.work_plan2.pk)
    
    def test_get_work_plan_statistics(self):
        """测试获取工作计划统计（包含所有计划）"""
        token = self.get_jwt_token(self.staff_user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 创建第三个计划用于统计
        WorkPlan.objects.create(
            staff_member=self.staff_user1,
            title='已完成的计划',
            start_date=date.today() - timedelta(days=10),
            end_date=date.today() - timedelta(days=3),
            content='已完成内容',
            status='completed',
            reminder_days=1
        )
        
        response = self.client.get(self.statistics_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_count', response.data['data'])
        self.assertIn('status_stats', response.data['data'])
        self.assertIn('monthly_stats', response.data['data'])
        self.assertEqual(response.data['data']['total_count'], 3)
    
    def test_get_work_plan_reminders(self):
        """测试获取工作计划提醒（包含所有计划）"""
        token = self.get_jwt_token(self.staff_user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(self.reminders_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('reminder_plans', response.data['data'])
        self.assertIn('overdue_plans', response.data['data'])
        self.assertIn('reminder_count', response.data['data'])
        self.assertIn('overdue_count', response.data['data'])
    
    def test_search_and_filter(self):
        """测试工作计划列表获取功能"""
        token = self.get_jwt_token(self.staff_user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 测试获取所有计划（不使用任何筛选参数）
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['results']), 2)  # 应该返回所有计划
        
        # 验证返回的数据结构
        self.assertIn('pagination', response.data['data'])
        self.assertEqual(response.data['data']['pagination']['total'], 2)
        
        # 验证计划数据包含必要字段
        plan = response.data['data']['results'][0]
        required_fields = ['id', 'title', 'start_date', 'end_date', 'content', 'status', 'created_at', 'updated_at']
        for field in required_fields:
            self.assertIn(field, plan)
    
    def test_pagination(self):
        """测试分页功能"""
        token = self.get_jwt_token(self.staff_user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 测试第一页
        response = self.client.get(self.list_url, {'page': 1, 'page_size': 1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['pagination']['total'], 2)
        self.assertEqual(len(response.data['data']['results']), 1)
        
        # 测试第二页
        response = self.client.get(self.list_url, {'page': 2, 'page_size': 1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['results']), 1)
    
    def test_invalid_plan_id(self):
        """测试无效的计划ID"""
        token = self.get_jwt_token(self.staff_user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        url = reverse('workplan:work_plan_detail', kwargs={'pk': 9999})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_get_work_plans_normal_user(self):
        """测试普通用户访问工作计划列表"""
        token = self.get_jwt_token(self.normal_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(self.list_url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_get_work_plans_unauthorized(self):
        """测试未授权访问工作计划列表"""
        response = self.client.get(self.list_url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class WorkPlanPermissionTests(APITestCase):
    """工作计划权限测试"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
        
        self.staff_user = User.objects.create_user(
            username='staff',
            password='testpass123',
            role='staff'
        )
        
        self.normal_user = User.objects.create_user(
            username='normal',
            password='testpass123',
            role='representative'
        )
        
        # URL配置
        self.list_url = reverse('workplan:work_plan_list_create')
        self.statistics_url = reverse('workplan:work_plan_statistics')
        self.reminders_url = reverse('workplan:work_plan_reminders')
    
    def get_jwt_token(self, user):
        """获取JWT令牌"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def test_normal_user_cannot_access_any_endpoint(self):
        """测试普通用户无法访问任何工作计划端点"""
        token = self.get_jwt_token(self.normal_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 测试所有端点都返回403
        endpoints = [
            self.list_url,
            self.statistics_url,
            self.reminders_url,
        ]
        
        for endpoint in endpoints:
            response = self.client.get(endpoint)
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_unauthenticated_user_cannot_access(self):
        """测试未认证用户无法访问"""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED) 