<template>
  <section class="main-content-block">
    
    <!-- 地图容器 -->
    <div class="map-container">
      <!-- 地图内容区域 -->
      <div class="map-content">
        <!-- ECharts地图容器 - 始终存在 -->
        <div 
          ref="mapChartRef" 
          class="map-chart"
          @click="handleMapClick"
        ></div>
        
        <!-- 加载状态覆盖层 -->
        <div v-if="loading" class="loading-overlay">
          <div class="loading-spinner"></div>
          <div class="loading-text">地图数据加载中...</div>
        </div>
      </div>
      
      <!-- 街道标签列表 -->
      <div v-if="!loading && streetData.length" class="street-labels">
        <div class="labels-title">街道代表分布</div>
        <div class="labels-list">
          <div 
            v-for="(item, index) in streetData" 
            :key="index"
            class="label-item"
            @click="handleStreetLabelClick(item)"
          >
            <div class="label-color" :style="{ backgroundColor: getColorByCount(item.representativeCount) }"></div>
            <div class="label-info">
              <div class="label-name">{{ item.streetName }}</div>
              <div class="label-value">{{ item.representativeCount }}人</div>
            </div>
          </div>
        </div>
        
        <!-- 二维码组件 -->
        <QRCodeComponent 
          :url="qrCodeUrl" 
          :size="120"
          ref="qrCodeRef"
        />
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { getStreetRepresentativeCount } from '@/api/indexscreen/mapData.js'
import QRCodeComponent from '@/components/indexscreen/common/QRCodeComponent.vue'

// 响应式数据
const mapTitle = ref('江南区街道代表分布图')
const mapSubtitle = ref('实时数据展示')
const loading = ref(false)
const mapChartRef = ref(null)
const mapChart = ref(null)
const streetData = ref([])
const jiangnanMapData = ref(null)

// 二维码相关数据
const qrCodeUrl = ref('https://dify.gxaigc.cn/chat/BcwlrHx1D4bYfJ2h')
const qrCodeRef = ref(null)

// 加载江南区地图数据
const loadMapData = async () => {
  try {
    console.log('🗺️ 开始加载江南区地图数据...')
    // 使用动态导入加载JSON文件
    const mapModule = await import('@/assets/江南区.json')
    jiangnanMapData.value = mapModule.default || mapModule
    console.log('🗺️ 江南区地图数据加载成功:', {
      exists: !!jiangnanMapData.value,
      name: jiangnanMapData.value?.name,
      type: jiangnanMapData.value?.type,
      featuresCount: jiangnanMapData.value?.features?.length
    })
    return true
  } catch (error) {
    console.error('❌ 加载江南区地图数据失败:', error)
    return false
  }
}

// 获取街道代表数量数据
const fetchStreetData = async () => {
  try {
    const response = await getStreetRepresentativeCount()
    if (response.code === 200) {
      streetData.value = response.data.list
      console.log('📊 街道代表数量数据加载成功:', streetData.value)
      return true
    } else {
      console.error('❌ 获取街道代表数量数据失败:', response.message)
      return false
    }
  } catch (error) {
    console.error('❌ 街道代表数量数据请求异常:', error)
    return false
  }
}

// 根据代表数量获取颜色 - 使用党建红+金色主题色系
const getColorByCount = (count) => {
      if (count >= 40) return '#02a6b5'      // 青蓝色 - 最高级别
    if (count >= 30) return '#1a5490'      // 中蓝色 - 高级别
    if (count >= 20) return '#49bcf7'      // 亮蓝色 - 中高级别
  if (count >= 10) return '#FFA500'      // 橙色 - 中级别
  if (count >= 5) return '#CD5C5C'       // 印度红 - 低级别
  return '#A0522D'                       // 赭色 - 最低级别
}

// 初始化ECharts地图
const initMapChart = () => {
  // 添加详细的调试信息
  console.log('🔍 地图初始化条件检查:')
  console.log('  - mapChartRef.value:', !!mapChartRef.value)
  console.log('  - jiangnanMapData.value:', !!jiangnanMapData.value, jiangnanMapData.value?.name)
  console.log('  - streetData.value.length:', streetData.value.length)
  
  if (!mapChartRef.value || !jiangnanMapData.value || !streetData.value.length) {
    console.warn('⚠️ 地图初始化条件不满足')
    console.warn('  - DOM元素存在:', !!mapChartRef.value)
    console.warn('  - 地图数据存在:', !!jiangnanMapData.value)
    console.warn('  - 街道数据长度:', streetData.value.length)
    return
  }

  // 如果已存在图表实例，先销毁
  if (mapChart.value) {
    mapChart.value.dispose()
    mapChart.value = null
  }

  // 注册地图数据 - 使用导入的江南区地图数据
  echarts.registerMap('jiangnan', jiangnanMapData.value)
  console.log('🗺️ 江南区地图数据注册成功:', jiangnanMapData.value.name)

  // 创建ECharts实例
  mapChart.value = echarts.init(mapChartRef.value)

  // 准备地图数据 - 简化版本
  const mapSeriesData = streetData.value.map(item => ({
    name: item.streetName,
    value: item.representativeCount
  }))

  // 配置ECharts选项 - 优化显示效果
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 20, 40, 0.95)',
              borderColor: 'rgba(2, 166, 181, 0.3)',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff',
        fontSize: 14
      },
      formatter: (params) => {
        if (!params.data) return ''
        
        return `
          <div style="padding: 10px; text-align: center;">
            <div style="font-weight: bold; font-size: 16px; color: #4FC3F7; margin-bottom: 8px;">
              ${params.name}
            </div>
            <div style="font-size: 14px;">
              <span style="color: #FFA726;">代表人数：</span>
              <span style="color: #ffffff; font-weight: bold;">${params.data.value}人</span>
            </div>
          </div>
        `
      }
    },
    geo: {
      map: 'jiangnan',
      roam: true, // 允许缩放和平移
      zoom: 1.2, // 稍微放大一点
      center: [108.20, 22.60], // 进一步调整中心点位置
      aspectScale: 0.9, // 调整宽高比
      itemStyle: {
        areaColor: 'rgba(0, 20, 40, 0.3)',
        borderColor: '#49bcf7', // 亮蓝色边框
        borderWidth: 1.5
      },
      emphasis: {
        itemStyle: {
          areaColor: 'rgba(0, 30, 60, 0.6)',
          borderColor: 'rgba(73, 188, 247, 0.8)', // 悬浮时亮蓝色边框
          borderWidth: 2
        },
        label: {
          show: true,
          color: '#ffffff',
          fontSize: 12,
          fontWeight: 'bold'
        }
      },
      label: {
        show: true,
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 11,
        fontWeight: 'normal'
      }
    },
    series: [
      {
        name: '代表数量',
        type: 'map',
        map: 'jiangnan',
        geoIndex: 0,
        data: mapSeriesData,
        itemStyle: {
          color: (params) => {
            const color = getColorByCount(params.data?.value || 0)
            return color
          },
          borderColor: 'rgba(73, 188, 247, 0.4)', // 亮蓝色边框
          borderWidth: 1.5
        },
        emphasis: {
          itemStyle: {
            color: (params) => {
              const baseColor = getColorByCount(params.data?.value || 0)
              // 悬浮时稍微加深颜色
              return baseColor
            },
            borderColor: '#49bcf7', // 悬浮时亮蓝色边框
            borderWidth: 2.5,
            shadowColor: 'rgba(73, 188, 247, 0.6)',
            shadowBlur: 12
          }
        }
      }
    ]
  }

  // 设置配置并渲染
  mapChart.value.setOption(option, true) // 强制重新渲染
  
  // 延迟调用resize确保完整渲染
  setTimeout(() => {
    if (mapChart.value) {
      mapChart.value.resize()
      console.log('🔄 地图强制刷新完成')
    }
  }, 200)
  
  // 监听点击事件
  mapChart.value.on('click', (params) => {
    if (params.data) {
      handleStreetClick(params.data)
    }
  })

  console.log('🗺️ ECharts地图初始化完成')
}

// 处理街道点击事件
const handleStreetClick = (streetData) => {
  console.log('🏘️ 点击街道:', streetData.name, '代表人数:', streetData.value)
  // 这里可以添加跳转到街道详情页面的逻辑
}

// 处理街道标签点击事件
const handleStreetLabelClick = (streetItem) => {
  console.log('🏷️ 点击街道标签:', streetItem.streetName, '代表人数:', streetItem.representativeCount)
  // 这里可以添加高亮地图对应区域或显示详细信息的逻辑
  
  // 如果地图实例存在，可以触发地图上对应区域的高亮
  if (mapChart.value) {
    // 可以在这里添加地图交互逻辑
    mapChart.value.dispatchAction({
      type: 'highlight',
      name: streetItem.streetName
    })
    
    // 延迟取消高亮
    setTimeout(() => {
      mapChart.value.dispatchAction({
        type: 'downplay',
        name: streetItem.streetName
      })
    }, 2000)
  }
}

// 处理地图容器点击事件
const handleMapClick = (event) => {
  // 阻止事件冒泡，避免与ECharts事件冲突
  event.stopPropagation()
}

// 更新二维码URL的方法
const updateQRCodeUrl = (newUrl) => {
  qrCodeUrl.value = newUrl
  console.log('🔄 二维码URL已更新:', newUrl)
  
  // 如果二维码组件已加载，触发重新生成
  if (qrCodeRef.value) {
    qrCodeRef.value.regenerate()
  }
}

// 窗口大小变化时重新调整图表
const handleResize = () => {
  if (mapChart.value) {
    // 添加防抖机制
    clearTimeout(handleResize.timer)
    handleResize.timer = setTimeout(() => {
      mapChart.value.resize()
      console.log('🔄 地图尺寸调整完成')
    }, 100)
  }
}

// 初始化地图数据和图表
const initMapData = async () => {
  loading.value = true
  
  try {
    console.log('🗺️ 开始初始化地图数据')
    
    // 先加载地图数据
    const mapSuccess = await loadMapData()
    console.log('🗺️ 地图数据加载结果:', mapSuccess)
    
    if (!mapSuccess) {
      console.error('❌ 地图数据加载失败')
      return
    }
    
    console.log('🗺️ 本地江南区地图数据检查:', {
      exists: !!jiangnanMapData.value,
      name: jiangnanMapData.value?.name,
      type: jiangnanMapData.value?.type,
      featuresCount: jiangnanMapData.value?.features?.length
    })
    
    // 获取街道数据
    console.log('📊 开始获取街道数据...')
    const streetSuccess = await fetchStreetData()
    console.log('📊 街道数据获取结果:', streetSuccess)
    console.log('📊 当前街道数据状态:', {
      length: streetData.value.length,
      data: streetData.value
    })
    
    if (streetSuccess) {
      console.log('✅ 街道数据获取成功，准备初始化图表')
      
      // 先关闭加载状态，让DOM元素显示
      loading.value = false
      
      // 等待DOM更新后初始化图表
      await nextTick()
      console.log('🔄 DOM更新完成，开始初始化图表')
      console.log('🔍 DOM元素检查:', !!mapChartRef.value)
      
      // 增加延迟确保DOM完全准备好，并添加重试机制
      let retryCount = 0
      const maxRetries = 3
      
      const tryInitChart = () => {
        if (mapChartRef.value && mapChartRef.value.offsetWidth > 0) {
          console.log('✅ DOM容器准备完成，开始初始化地图')
          initMapChart()
        } else if (retryCount < maxRetries) {
          retryCount++
          console.log(`🔄 DOM容器未准备好，第${retryCount}次重试...`)
          setTimeout(tryInitChart, 300)
        } else {
          console.error('❌ DOM容器准备失败，已达到最大重试次数')
        }
      }
      
      setTimeout(tryInitChart, 300)
    } else {
      console.error('❌ 街道数据获取失败')
      loading.value = false
    }
  } catch (error) {
    console.error('❌ 初始化地图数据失败:', error)
    loading.value = false
  }
}

// 组件挂载
onMounted(async () => {
  console.log('🚀 组件挂载开始')
  await nextTick()
  
  // 确保容器存在后再初始化
  if (mapChartRef.value) {
    console.log('✅ 地图容器已准备，开始初始化数据')
    await initMapData()
  } else {
    console.warn('⚠️ 地图容器未找到，延迟初始化')
    setTimeout(async () => {
      if (mapChartRef.value) {
        await initMapData()
      }
    }, 500)
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  console.log('🚀 组件挂载完成')
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (mapChart.value) {
    mapChart.value.dispose()
    mapChart.value = null
  }
  
  // 移除事件监听
  window.removeEventListener('resize', handleResize)
})

// 暴露方法供父组件调用
defineExpose({
  updateQRCodeUrl
})
</script>

<style scoped>
/* 主容器 - 使用flex布局 */
.main-content-block {
  /* background: rgba(0, 13, 74, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(2, 166, 181, 0.15); */
  border-radius: 16px;
  /* padding: 20px; */
  color: #ffffff;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  flex: 1;
  min-height: 0;
  position: relative;
  overflow: hidden;
}

.main-content-block:hover {
  background: rgba(0, 20, 40, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 13, 74, 0.2);
  border-color: rgba(2, 166, 181, 0.3);
}

/* 地图头部 - flex布局 */
.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.map-icon {
  font-size: 1.4rem;
  filter: drop-shadow(0 0 4px rgba(73, 188, 247, 0.8));
}

.map-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.map-subtitle {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(2, 166, 181, 0.2);
  padding: 4px 10px;
  border-radius: 12px;
  border: 1px solid rgba(73, 188, 247, 0.3);
}

/* 地图容器 - flex自适应 */
.map-container {
  flex: 1;
  display: flex;
  /* gap: 16px; */
  background: transparent; /* 完全透明背景 */
  border-radius: 8px;
  /* padding: 16px; */
  position: relative;
}

/* 地图内容区域 */
.map-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* ECharts地图 */
.map-chart {
  flex: 1;
  min-height: 400px;
  border-radius: 6px;
  background: transparent; /* 完全透明背景 */
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 16px;
  left: 16px;
  right: 16px;
  bottom: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 6px;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(2, 166, 181, 0.2);
  border-top: 3px solid rgba(2, 166, 181, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content-block {
    padding: 16px;
  }
  
  .map-title {
    font-size: 1rem;
  }
  
  .map-subtitle {
    font-size: 0.75rem;
    padding: 3px 8px;
  }
  
  .map-container {
    gap: 12px;
  }
  
  .street-labels {
    flex: 0 0 180px;
    padding: 10px;
  }
  
  .labels-title {
    font-size: 0.85rem;
    margin-bottom: 6px; /* 减少间距 */
  }
  
  .labels-list {
    gap: 3px; /* 减少间距 */
  }
  
  .label-item {
    padding: 3px 4px; /* 减少内边距 */
    gap: 8px;
  }
  
  .map-chart {
    min-height: 250px;
  }
}

@media (max-width: 768px) {
  .main-content-block {
    padding: 12px;
  }
  
  .map-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 12px;
  }
  
  .map-subtitle {
    align-self: flex-end;
    font-size: 0.7rem;
  }
  
  .map-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .street-labels {
    flex: 0 0 auto;
    padding: 8px;
  }
  
  .labels-list {
    gap: 2px; /* 更紧凑 */
  }
  
  .labels-title {
    font-size: 0.8rem;
    margin-bottom: 4px; /* 更紧凑 */
  }
  
  .label-item {
    padding: 2px 4px; /* 更紧凑 */
    gap: 6px;
    font-size: 0.75rem;
  }
  
  .label-color {
    width: 10px;
    height: 10px;
  }
  
  .label-name {
    font-size: 0.65rem;
  }
  
  .label-value {
    font-size: 0.6rem;
  }
  
  .map-chart {
    min-height: 200px;
  }
}

/* 地图动画效果 - 蓝色科技主题 */
.main-content-block::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(2, 166, 181, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.main-content-block:hover::after {
  left: 100%;
}

/* 街道标签列表 */
.street-labels {
  width: 200px;
  padding: 16px;
  /* background: rgba(0, 13, 74, 0.4); */
  border-radius: 8px;
  /* border: 1px solid rgba(2, 166, 181, 0.2); */
  /* backdrop-filter: blur(5px); */
  display: flex;
  flex-direction: column;
  height: 100%;
}

.labels-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 16px;
  padding-bottom: 12px;
  position: relative;
  text-align: center;
}

/* 美观的分割线设计 */
.labels-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(73, 188, 247, 0.3) 10%,
    rgba(73, 188, 247, 0.8) 30%,
    rgba(73, 188, 247, 1) 50%,
    rgba(73, 188, 247, 0.8) 70%,
    rgba(73, 188, 247, 0.3) 90%,
    transparent 100%
  );
  border-radius: 2px;
  box-shadow: 
    0 0 8px rgba(73, 188, 247, 0.4),
    0 2px 4px rgba(73, 188, 247, 0.2);
  animation: glow-pulse 3s ease-in-out infinite alternate;
}

/* 添加一个细的上层装饰线 */
.labels-title::before {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  border-radius: 1px;
  animation: shimmer 2s ease-in-out infinite alternate;
}

/* 分割线发光动画 */
@keyframes glow-pulse {
  0% {
    box-shadow: 
      0 0 8px rgba(73, 188, 247, 0.4),
      0 2px 4px rgba(73, 188, 247, 0.2);
  }
  100% {
    box-shadow: 
      0 0 12px rgba(73, 188, 247, 0.6),
      0 2px 6px rgba(73, 188, 247, 0.3);
  }
}

/* 上层装饰线闪烁动画 */
@keyframes shimmer {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.labels-list {
  gap: 2px; /* 更紧凑 */
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  max-height: calc(100% - 200px); /* 为二维码预留空间 */
}

.label-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 4px 6px; /* 减少内边距 */
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  border: 1px solid transparent;
}

.label-item:hover {
  background: rgba(73, 188, 247, 0.1); /* 亮蓝色背景 */
  transform: translateX(3px);
  border-color: rgba(73, 188, 247, 0.5); /* 亮蓝色边框 */
  box-shadow: 0 2px 8px rgba(73, 188, 247, 0.3); /* 亮蓝色阴影 */
}

.label-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  flex-shrink: 0;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(73, 188, 247, 0.4); /* 亮蓝色边框 */
}

.label-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 0;
}

.label-name {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  flex: 1;
}

.label-value {
  font-size: 0.7rem;
  color: #49bcf7;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  flex-shrink: 0;
  margin-left: 8px;
}
</style> 