<template>
  <div ref="myChart" style="width: 100%; height: 90%"></div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    this.initChart()
  },
  watch: {
    data: {
      handler() {
        this.initChart()
      },
      deep: true
    }
  },
  methods: {
    async initChart() {
      const myChart = this.$echarts.init(this.$refs.myChart)
      const option = {
        legend: {
          orient: 'vertical',
          itemWidth: 25,
          itemHeight: 15,
          textStyle: {
            color: '#fff',
          },
          top: '20%',
          right: 30,
          data: this.data.map((item) => item.name),
        },
        color: [
          '#37a2da',
          '#32c5e9',
          '#9fe6b8',
          '#ffdb5c',
          '#ff9f7f',
          '#fb7293',
          '#e7bcf3',
          '#8378ea',
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c} ({d}%)',
        },

        calculable: true,
        series: [
          {
            type: 'pie',
            radius: [40, 70],
            center: ['40%', '50%'],
            // roseType: 'area',
            data: this.data,
            label: {
              normal: {
                formatter: function (param) {
                  return param.name + ':\n' + param.value + '\n'
                },
              },
            },
            labelLine: {
              normal: {
                length: 5,
                length2: 15,
                lineStyle: { width: 1 },
              },
            },

            itemStyle: {
              normal: {
                shadowBlur: 30,
                shadowColor: 'rgba(0, 0, 0, 0.4)',
              },
            },
          },
        ],
      }
      await myChart.setOption(option)
    },
  },
}
</script>

<style lang="less">
.con {
  display: flex;
  justify-content: space-around;
  padding: 10px;
}

.item {
  background-color: rgba(4, 48, 142, 0.5);
  color: #fff;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.item .top {
  text-align: right;
  font-size: 12px;
  .iconfont {
    padding-right: 5px;
    color: rgb(0, 113, 247);
    font-size: 18px;
  }
  .number {
    color: rgb(252, 231, 3);
    font-size: 20px;
  }
}
.item .end {
  font-size: 12px;
}
</style>
