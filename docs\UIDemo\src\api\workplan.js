// 工作计划管理API模块

// 模拟工作计划数据
const mockWorkPlans = [
  {
    id: 1,
    title: '2024年度人大代表联络服务工作计划',
    type: '年度',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    content: '建立健全人大代表联络服务机制，定期组织代表活动，收集民意建议，提升代表履职效能。重点包括：组织代表进社区活动、开展专题调研、建立意见建议处理机制。',
    goal: '提升代表履职质量，增强人民群众获得感',
    responsible: '张主任',
    status: '进行中',
    reminderDays: 7,
    reminderType: '站内通知',
    createdAt: '2024-01-15',
    updatedAt: '2024-03-10'
  },
  {
    id: 2,
    title: '第一季度代表培训计划',
    type: '季度',
    startDate: '2024-01-01',
    endDate: '2024-03-31',
    content: '组织本站点人大代表参加履职能力培训，提升代表业务水平和履职技能。包括法律法规学习、实地考察、经验交流等。',
    goal: '提升代表履职能力和水平',
    responsible: '李副主任',
    status: '已完成',
    reminderDays: 3,
    reminderType: '站内通知',
    createdAt: '2023-12-20',
    updatedAt: '2024-03-31'
  },
  {
    id: 3,
    title: '3月份意见建议集中处理',
    type: '月度',
    startDate: '2024-03-01',
    endDate: '2024-03-31',
    content: '集中处理2月份收集的群众意见建议，完成分类整理、转交部门、跟踪办理等工作，确保件件有回音。',
    goal: '及时回应群众关切，提高办事效率',
    responsible: '王专员',
    status: '已完成',
    reminderDays: 5,
    reminderType: '站内通知',
    createdAt: '2024-02-25',
    updatedAt: '2024-03-31'
  },
  {
    id: 4,
    title: '第二季度工作总结与部署',
    type: '季度',
    startDate: '2024-04-01',
    endDate: '2024-06-30',
    content: '对第一季度工作进行全面总结，分析存在问题，部署第二季度重点工作任务。',
    goal: '总结经验，优化工作流程',
    responsible: '张主任',
    status: '待开始',
    reminderDays: 7,
    reminderType: '站内通知',
    createdAt: '2024-03-25',
    updatedAt: '2024-03-25'
  },
  {
    id: 5,
    title: '5月份代表接待日活动',
    type: '月度',
    startDate: '2024-05-15',
    endDate: '2024-05-15',
    content: '组织代表在社区开展接待日活动，现场接待群众来访，收集意见建议，解答政策咨询。',
    goal: '密切联系群众，听取民意诉求',
    responsible: '刘秘书',
    status: '已延期',
    reminderDays: 2,
    reminderType: '站内通知',
    createdAt: '2024-04-20',
    updatedAt: '2024-05-20'
  }
]

/**
 * 获取工作计划列表
 * @param {Object} params - 查询参数
 * @param {string} params.type - 计划类型筛选
 * @param {string} params.status - 状态筛选
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise} 返回工作计划列表
 */
export const getWorkPlans = async (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredPlans = [...mockWorkPlans]
      
      // 类型筛选
      if (params.type && params.type !== '全部') {
        filteredPlans = filteredPlans.filter(plan => plan.type === params.type)
      }
      
      // 状态筛选
      if (params.status && params.status !== '全部') {
        filteredPlans = filteredPlans.filter(plan => plan.status === params.status)
      }
      
      // 关键词搜索
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase()
        filteredPlans = filteredPlans.filter(plan => 
          plan.title.toLowerCase().includes(keyword) ||
          plan.content.toLowerCase().includes(keyword) ||
          plan.responsible.toLowerCase().includes(keyword)
        )
      }
      
      // 分页
      const page = params.page || 1
      const pageSize = params.pageSize || 10
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const pagedPlans = filteredPlans.slice(start, end)
      
      resolve({
        success: true,
        data: {
          list: pagedPlans,
          total: filteredPlans.length,
          page: page,
          pageSize: pageSize
        }
      })
    }, 300)
  })
}

/**
 * 获取工作计划详情
 * @param {number} id - 计划ID
 * @returns {Promise} 返回计划详情
 */
export const getWorkPlanDetail = async (id) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const plan = mockWorkPlans.find(p => p.id === parseInt(id))
      if (plan) {
        resolve({
          success: true,
          data: plan
        })
      } else {
        reject({
          response: {
            data: {
              success: false,
              message: '计划不存在'
            }
          }
        })
      }
    }, 200)
  })
}

/**
 * 创建工作计划
 * @param {Object} planData - 计划数据
 * @returns {Promise} 返回创建结果
 */
export const createWorkPlan = async (planData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 验证必填字段
      if (!planData.title || !planData.type || !planData.startDate || !planData.endDate || !planData.content || !planData.responsible) {
        reject({
          response: {
            data: {
              success: false,
              message: '请填写所有必填字段'
            }
          }
        })
        return
      }
      
      // 验证日期
      if (new Date(planData.endDate) < new Date(planData.startDate)) {
        reject({
          response: {
            data: {
              success: false,
              message: '结束时间不能早于开始时间'
            }
          }
        })
        return
      }
      
      // 创建新计划
      const newPlan = {
        id: mockWorkPlans.length + 1,
        ...planData,
        status: planData.status || '待开始',
        reminderDays: planData.reminderDays || 3,
        reminderType: planData.reminderType || '站内通知',
        createdAt: new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString().split('T')[0]
      }
      
      mockWorkPlans.unshift(newPlan)
      
      resolve({
        success: true,
        data: newPlan,
        message: '工作计划创建成功'
      })
    }, 800)
  })
}

/**
 * 更新工作计划
 * @param {number} id - 计划ID
 * @param {Object} planData - 更新数据
 * @returns {Promise} 返回更新结果
 */
export const updateWorkPlan = async (id, planData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockWorkPlans.findIndex(p => p.id === parseInt(id))
      if (index === -1) {
        reject({
          response: {
            data: {
              success: false,
              message: '计划不存在'
            }
          }
        })
        return
      }
      
      // 验证日期
      if (planData.endDate && planData.startDate && new Date(planData.endDate) < new Date(planData.startDate)) {
        reject({
          response: {
            data: {
              success: false,
              message: '结束时间不能早于开始时间'
            }
          }
        })
        return
      }
      
      // 更新计划
      mockWorkPlans[index] = {
        ...mockWorkPlans[index],
        ...planData,
        updatedAt: new Date().toISOString().split('T')[0]
      }
      
      resolve({
        success: true,
        data: mockWorkPlans[index],
        message: '工作计划更新成功'
      })
    }, 600)
  })
}

/**
 * 删除工作计划
 * @param {number} id - 计划ID
 * @returns {Promise} 返回删除结果
 */
export const deleteWorkPlan = async (id) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockWorkPlans.findIndex(p => p.id === parseInt(id))
      if (index === -1) {
        reject({
          response: {
            data: {
              success: false,
              message: '计划不存在'
            }
          }
        })
        return
      }
      
      mockWorkPlans.splice(index, 1)
      
      resolve({
        success: true,
        message: '工作计划删除成功'
      })
    }, 400)
  })
}

/**
 * 获取计划统计数据
 * @returns {Promise} 返回统计信息
 */
export const getWorkPlanStatistics = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const stats = {
        total: mockWorkPlans.length,
        pending: mockWorkPlans.filter(p => p.status === '待开始').length,
        inProgress: mockWorkPlans.filter(p => p.status === '进行中').length,
        completed: mockWorkPlans.filter(p => p.status === '已完成').length,
        delayed: mockWorkPlans.filter(p => p.status === '已延期').length,
        byType: {
          annual: mockWorkPlans.filter(p => p.type === '年度').length,
          quarterly: mockWorkPlans.filter(p => p.type === '季度').length,
          monthly: mockWorkPlans.filter(p => p.type === '月度').length
        }
      }
      
      resolve({
        success: true,
        data: stats
      })
    }, 200)
  })
}

/**
 * 批量更新计划状态
 * @param {Array} ids - 计划ID数组
 * @param {string} status - 新状态
 * @returns {Promise} 返回更新结果
 */
export const batchUpdatePlanStatus = async (ids, status) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let updatedCount = 0
      ids.forEach(id => {
        const index = mockWorkPlans.findIndex(p => p.id === parseInt(id))
        if (index !== -1) {
          mockWorkPlans[index].status = status
          mockWorkPlans[index].updatedAt = new Date().toISOString().split('T')[0]
          updatedCount++
        }
      })
      
      resolve({
        success: true,
        data: { updatedCount },
        message: `成功更新 ${updatedCount} 个计划的状态`
      })
    }, 500)
  })
}

export default {
  getWorkPlans,
  getWorkPlanDetail,
  createWorkPlan,
  updateWorkPlan,
  deleteWorkPlan,
  getWorkPlanStatistics,
  batchUpdatePlanStatus
} 