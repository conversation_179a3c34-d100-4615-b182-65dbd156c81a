"""
用户管理API视图

包含以下API端点：
1. UserLoginView - 用户登录
2. UserLogoutView - 用户登出
3. UserProfileView - 用户个人信息
4. PasswordChangeView - 密码修改
5. UserListView - 用户列表（管理员使用）
6. UserDetailView - 用户详情（管理员使用）
7. RepresentativeListView - 代表列表
8. StaffMemberListView - 工作人员列表
"""

import logging
from django.utils import timezone
from django.contrib.auth import login, logout
from rest_framework import status, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .models import User, Representative, StaffMember
from .serializers import (
    UserLoginSerializer,
    UserSerializer,
    UserProfileSerializer,
    PasswordChangeSerializer,
    RepresentativeSerializer,
    StaffMemberSerializer,
    UserCreateSerializer,
    AccountCreateSerializer,
    UserImportSerializer,
    UserExportSerializer
)
from .permissions import (
    IsOwnerOrStaff, 
    IsStaff, 
    CanManageUsers, 
    CanChangePassword
)

# 设置日志
logger = logging.getLogger(__name__)


class UserLoginView(APIView):
    """
    用户登录API
    
    对应用户故事US-UM-001：通过用户名和密码登录系统
    """
    
    permission_classes = [permissions.AllowAny]  # 允许匿名访问
    
    @swagger_auto_schema(
        operation_summary='用户登录',
        operation_description='通过用户名和密码进行登录，成功后返回JWT令牌',
        request_body=UserLoginSerializer,
        responses={
            200: openapi.Response(
                description='登录成功',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='操作是否成功'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='响应消息'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'access_token': openapi.Schema(type=openapi.TYPE_STRING, description='访问令牌'),
                                'refresh_token': openapi.Schema(type=openapi.TYPE_STRING, description='刷新令牌'),
                                'user': openapi.Schema(type=openapi.TYPE_OBJECT, description='用户信息'),
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(description='请求参数错误'),
            401: openapi.Response(description='用户名或密码错误'),
        },
        tags=['用户认证']
    )
    def post(self, request):
        """用户登录"""
        serializer = UserLoginSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # 更新最后登录时间
            user.last_login = timezone.now()
            user.save()
            
            # 生成JWT令牌
            refresh = RefreshToken.for_user(user)
            
            # 获取用户完整信息
            user_serializer = UserProfileSerializer(user)
            
            # 记录登录日志
            logger.info(f"用户 {user.username} 登录成功，IP: {request.META.get('REMOTE_ADDR')}")
            
            return Response({
                'success': True,
                'message': '登录成功',
                'data': {
                    'access_token': str(refresh.access_token),
                    'refresh_token': str(refresh),
                    'user': user_serializer.data
                }
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': '登录失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UserLogoutView(APIView):
    """
    用户登出API
    
    将刷新令牌加入黑名单
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    @swagger_auto_schema(
        operation_summary='用户登出',
        operation_description='用户登出，将刷新令牌加入黑名单',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'refresh_token': openapi.Schema(type=openapi.TYPE_STRING, description='刷新令牌')
            },
            required=['refresh_token']
        ),
        responses={
            200: openapi.Response(description='登出成功'),
            400: openapi.Response(description='请求参数错误'),
        },
        tags=['用户认证']
    )
    def post(self, request):
        """用户登出"""
        try:
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            
            # 记录登出日志
            logger.info(f"用户 {request.user.username} 登出")
            
            return Response({
                'success': True,
                'message': '登出成功'
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error(f"用户登出失败: {str(e)}")
            return Response({
                'success': False,
                'message': '登出失败',
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class UserProfileView(APIView):
    """
    用户个人信息API
    
    对应用户故事US-RM-001：查看和编辑个人信息
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    @swagger_auto_schema(
        operation_summary='获取用户个人信息',
        operation_description='获取当前登录用户的完整个人信息',
        responses={
            200: openapi.Response(
                description='获取成功',
                schema=UserProfileSerializer
            ),
        },
        tags=['用户信息']
    )
    def get(self, request):
        """获取用户个人信息"""
        serializer = UserProfileSerializer(request.user)
        
        return Response({
            'success': True,
            'message': '获取用户信息成功',
            'data': serializer.data
        }, status=status.HTTP_200_OK)
    
    @swagger_auto_schema(
        operation_summary='更新用户个人信息',
        operation_description='更新当前登录用户的个人信息',
        request_body=UserProfileSerializer,
        responses={
            200: openapi.Response(
                description='更新成功',
                schema=UserProfileSerializer
            ),
            400: openapi.Response(description='请求参数错误'),
        },
        tags=['用户信息']
    )
    def put(self, request):
        """更新用户个人信息"""
        serializer = UserProfileSerializer(
            request.user,
            data=request.data,
            partial=True
        )
        
        if serializer.is_valid():
            serializer.save()

            # 记录更新日志，特别标注头像更新
            update_fields = list(request.data.keys())
            if 'avatar' in update_fields:
                logger.info(f"用户 {request.user.username} 更新个人信息（包含头像）")
            else:
                logger.info(f"用户 {request.user.username} 更新个人信息")

            return Response({
                'success': True,
                'message': '更新个人信息成功',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': '更新个人信息失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class PasswordChangeView(APIView):
    """
    密码修改API
    
    对应用户故事US-UM-002：修改登录密码
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    @swagger_auto_schema(
        operation_summary='修改密码',
        operation_description='修改当前用户的登录密码',
        request_body=PasswordChangeSerializer,
        responses={
            200: openapi.Response(description='密码修改成功'),
            400: openapi.Response(description='请求参数错误'),
        },
        tags=['用户信息']
    )
    def post(self, request):
        """修改密码"""
        serializer = PasswordChangeSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            serializer.save()
            
            logger.info(f"用户 {request.user.username} 修改密码成功")
            
            return Response({
                'success': True,
                'message': '密码修改成功'
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': '密码修改失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UserListView(APIView):
    """
    用户列表API（管理员使用）
    
    对应用户故事US-UM-003：管理所有用户账号
    """
    
    permission_classes = [CanManageUsers]
    
    @swagger_auto_schema(
        operation_summary='获取用户列表',
        operation_description='获取系统中所有用户的列表（仅工作人员可访问）',
        manual_parameters=[
            openapi.Parameter(
                'role', openapi.IN_QUERY, 
                description='按角色筛选（representative/staff）',
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'is_active', openapi.IN_QUERY,
                description='按激活状态筛选（true/false）',
                type=openapi.TYPE_BOOLEAN
            ),
            openapi.Parameter(
                'page', openapi.IN_QUERY,
                description='页码',
                type=openapi.TYPE_INTEGER
            ),
            openapi.Parameter(
                'page_size', openapi.IN_QUERY,
                description='每页大小（默认20，最大100）',
                type=openapi.TYPE_INTEGER
            ),
            openapi.Parameter(
                'search', openapi.IN_QUERY,
                description='搜索关键词（用户名或姓名）',
                type=openapi.TYPE_STRING
            ),
        ],
        responses={
            200: openapi.Response(
                description='获取成功',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'count': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'results': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                                )
                            }
                        )
                    }
                )
            ),
            403: openapi.Response(description='权限不足'),
        },
        tags=['用户管理']
    )
    def get(self, request):
        """获取用户列表"""
        # 获取查询参数
        role = request.query_params.get('role')
        is_active = request.query_params.get('is_active')
        search = request.query_params.get('search')
        
        # 构建查询条件，预加载相关信息以避免N+1查询
        queryset = User.objects.select_related('representative', 'staffmember').all().order_by('-created_at')
        
        if role:
            queryset = queryset.filter(role=role)
        
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active_bool)
        
        # 搜索功能：支持用户名和姓名搜索
        if search:
            from django.db.models import Q
            search_query = Q(username__icontains=search)
            
            # 根据角色搜索相应的姓名字段
            search_query |= Q(representative__name__icontains=search)
            search_query |= Q(staffmember__name__icontains=search)
            
            queryset = queryset.filter(search_query).distinct()
        
        # 简单分页（这里使用手动分页，也可以使用DRF的分页器）
        page_size = int(request.query_params.get('page_size', 20))
        page = int(request.query_params.get('page', 1))
        
        # 限制分页大小范围，防止恶意请求
        page_size = max(1, min(page_size, 100))
        
        start = (page - 1) * page_size
        end = start + page_size
        
        total_count = queryset.count()
        users = queryset[start:end]
        
        serializer = UserSerializer(users, many=True)
        
        return Response({
            'success': True,
            'message': '获取用户列表成功',
            'data': {
                'count': total_count,
                'page': page,
                'page_size': page_size,
                'results': serializer.data
            }
        }, status=status.HTTP_200_OK)
    
    @swagger_auto_schema(
        operation_summary='创建用户账号',
        operation_description='创建新用户账号及相关信息（仅工作人员可操作）',
        request_body=AccountCreateSerializer,
        responses={
            201: openapi.Response(
                description='创建成功',
                schema=UserProfileSerializer
            ),
            400: openapi.Response(description='请求参数错误'),
            403: openapi.Response(description='权限不足'),
        },
        tags=['用户管理']
    )
    def post(self, request):
        """创建用户账号及相关信息"""
        serializer = AccountCreateSerializer(data=request.data)
        
        if serializer.is_valid():
            user = serializer.save()
            
            # 返回完整的用户信息（包含角色相关信息）
            user_serializer = UserProfileSerializer(user)
            
            logger.info(f"工作人员 {request.user.username} 创建用户 {user.username}")
            
            return Response({
                'success': True,
                'message': '创建用户账号成功',
                'data': user_serializer.data
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'message': '创建用户账号失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UserDetailView(APIView):
    """
    用户详情API（管理员使用）
    
    对应用户故事US-UM-003：管理特定用户账号
    """
    
    permission_classes = [CanManageUsers]
    
    def get_object(self, user_id):
        """获取用户对象"""
        try:
            return User.objects.get(id=user_id)
        except User.DoesNotExist:
            return None
    
    @swagger_auto_schema(
        operation_summary='获取用户详情',
        operation_description='获取指定用户的详细信息（仅工作人员可访问）',
        responses={
            200: openapi.Response(
                description='获取成功',
                schema=UserProfileSerializer
            ),
            404: openapi.Response(description='用户不存在'),
            403: openapi.Response(description='权限不足'),
        },
        tags=['用户管理']
    )
    def get(self, request, user_id):
        """获取用户详情"""
        user = self.get_object(user_id)
        if not user:
            return Response({
                'success': False,
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        serializer = UserProfileSerializer(user)
        
        return Response({
            'success': True,
            'message': '获取用户详情成功',
            'data': serializer.data
        }, status=status.HTTP_200_OK)
    
    @swagger_auto_schema(
        operation_summary='更新用户状态',
        operation_description='更新指定用户的状态（激活/禁用）',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否激活')
            }
        ),
        responses={
            200: openapi.Response(description='更新成功'),
            400: openapi.Response(description='请求参数错误'),
            404: openapi.Response(description='用户不存在'),
            403: openapi.Response(description='权限不足'),
        },
        tags=['用户管理']
    )
    def patch(self, request, user_id):
        """更新用户状态"""
        user = self.get_object(user_id)
        if not user:
            return Response({
                'success': False,
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        is_active = request.data.get('is_active')
        if is_active is not None:
            user.is_active = is_active
            user.save()
            
            action = '激活' if is_active else '禁用'
            logger.info(f"工作人员 {request.user.username} {action}用户 {user.username}")
            
            return Response({
                'success': True,
                'message': f'用户账号{action}成功'
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': '请提供is_active参数'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    @swagger_auto_schema(
        operation_summary='删除用户账号',
        operation_description='删除用户账号及相关信息（仅工作人员可操作）',
        responses={
            200: openapi.Response(
                description='删除成功',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                    }
                )
            ),
            400: openapi.Response(description='请求参数错误'),
            403: openapi.Response(description='权限不足'),
            404: openapi.Response(description='用户不存在'),
        },
        tags=['用户管理']
    )
    def delete(self, request, user_id):
        """删除用户账号"""
        user = self.get_object(user_id)
        if not user:
            return Response({
                'success': False,
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 防止删除自己的账号
        if user.id == request.user.id:
            return Response({
                'success': False,
                'message': '不能删除自己的账号'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        username = user.username
        user_role = user.get_role_display()
        
        # 删除用户（相关信息会通过级联删除自动删除）
        user.delete()
        
        logger.info(f"工作人员 {request.user.username} 删除了用户 {username}（{user_role}）")
        
        return Response({
            'success': True,
            'message': f'用户账号"{username}"删除成功'
        })
    
    @swagger_auto_schema(
        operation_summary='重置用户密码',
        operation_description='重置指定用户的登录密码（仅工作人员可操作）',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'new_password': openapi.Schema(type=openapi.TYPE_STRING, description='新密码'),
                'confirm_password': openapi.Schema(type=openapi.TYPE_STRING, description='确认新密码'),
            },
            required=['new_password', 'confirm_password']
        ),
        responses={
            200: openapi.Response(
                description='重置成功',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                    }
                )
            ),
            400: openapi.Response(description='请求参数错误'),
            403: openapi.Response(description='权限不足'),
            404: openapi.Response(description='用户不存在'),
        },
        tags=['用户管理']
    )
    def put(self, request, user_id):
        """重置用户密码"""
        user = self.get_object(user_id)
        if not user:
            return Response({
                'success': False,
                'message': '用户不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        new_password = request.data.get('new_password')
        confirm_password = request.data.get('confirm_password')
        
        if not new_password or not confirm_password:
            return Response({
                'success': False,
                'message': '新密码和确认密码不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if new_password != confirm_password:
            return Response({
                'success': False,
                'message': '两次输入的密码不一致'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证密码强度
        from django.contrib.auth.password_validation import validate_password
        from django.core.exceptions import ValidationError
        
        try:
            validate_password(new_password, user)
        except ValidationError as e:
            return Response({
                'success': False,
                'message': f'密码不符合要求：{", ".join(e.messages)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 重置密码
        user.set_password(new_password)
        user.save()
        
        logger.info(f"工作人员 {request.user.username} 重置了用户 {user.username} 的密码")
        
        return Response({
            'success': True,
            'message': f'用户"{user.username}"的密码重置成功'
        })


class RepresentativeListView(APIView):
    """
    人大代表列表API（仅工作人员可访问）
    
    安全说明：只有工作人员才能查看所有代表列表，代表用户只能查看自己的信息
    """
    
    permission_classes = [IsStaff]  # 修复安全漏洞：只允许工作人员访问
    
    @swagger_auto_schema(
        operation_summary='获取人大代表列表',
        operation_description='获取系统中所有人大代表的信息（仅工作人员可访问）',
        manual_parameters=[
            openapi.Parameter(
                'level', openapi.IN_QUERY,
                description='按代表层级筛选',
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'search', openapi.IN_QUERY,
                description='按姓名搜索',
                type=openapi.TYPE_STRING
            ),
        ],
        responses={
            200: openapi.Response(
                description='获取成功',
                schema=RepresentativeSerializer(many=True)
            ),
            403: openapi.Response(description='权限不足，只有工作人员可以访问'),
        },
        tags=['人大代表']
    )
    def get(self, request):
        """获取人大代表列表"""
        # 获取查询参数
        level = request.query_params.get('level')
        search = request.query_params.get('search')
        
        # 构建查询条件
        queryset = Representative.objects.select_related('user').filter(
            user__is_active=True
        ).order_by('name')
        
        if level:
            queryset = queryset.filter(level=level)
        
        if search:
            queryset = queryset.filter(name__icontains=search)
        
        serializer = RepresentativeSerializer(queryset, many=True)
        
        return Response({
            'success': True,
            'message': '获取人大代表列表成功',
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class StaffMemberListView(APIView):
    """
    站点工作人员列表API
    """
    
    permission_classes = [IsStaff]
    
    @swagger_auto_schema(
        operation_summary='获取工作人员列表',
        operation_description='获取系统中所有工作人员的信息（仅工作人员可访问）',
        manual_parameters=[
            openapi.Parameter(
                'station_name', openapi.IN_QUERY,
                description='按站点名称筛选',
                type=openapi.TYPE_STRING
            ),
        ],
        responses={
            200: openapi.Response(
                description='获取成功',
                schema=StaffMemberSerializer(many=True)
            ),
        },
        tags=['工作人员']
    )
    def get(self, request):
        """获取工作人员列表"""
        # 获取查询参数
        station_name = request.query_params.get('station_name')
        
        # 构建查询条件
        queryset = StaffMember.objects.select_related('user').filter(
            user__is_active=True
        ).order_by('name')
        
        if station_name:
            queryset = queryset.filter(station_name__icontains=station_name)
        
        serializer = StaffMemberSerializer(queryset, many=True)
        
        return Response({
            'success': True,
            'message': '获取工作人员列表成功',
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class UserExportView(APIView):
    """
    用户导出API

    导出用户列表为Excel文件
    """

    permission_classes = [CanManageUsers]

    @swagger_auto_schema(
        operation_summary='导出用户列表',
        operation_description='导出用户列表为Excel文件（仅工作人员可操作）',
        manual_parameters=[
            openapi.Parameter(
                'role', openapi.IN_QUERY,
                description='按角色筛选（representative/staff）',
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'is_active', openapi.IN_QUERY,
                description='按激活状态筛选（true/false）',
                type=openapi.TYPE_BOOLEAN
            ),
        ],
        responses={
            200: openapi.Response(
                description='导出成功',
                schema=openapi.Schema(
                    type=openapi.TYPE_FILE,
                    format=openapi.FORMAT_BINARY
                )
            ),
            403: openapi.Response(description='权限不足'),
        },
        tags=['用户管理']
    )
    def get(self, request):
        """导出用户列表为Excel"""
        import openpyxl
        from django.http import HttpResponse
        from datetime import datetime

        # 获取查询参数
        role = request.query_params.get('role')
        is_active = request.query_params.get('is_active')

        # 构建查询条件
        queryset = User.objects.select_related('representative', 'staffmember').all().order_by('-created_at')

        if role:
            queryset = queryset.filter(role=role)

        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active_bool)

        # 序列化数据
        serializer = UserExportSerializer(queryset, many=True)

        # 创建Excel工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "用户列表"

        # 根据角色定义不同的表头和数据
        if role == 'representative':
            # 代表导出表头
            headers = [
                '用户名', '角色', '账号状态', '创建时间',
                '代表姓名', '代表层级', '性别', '民族', '所属片区', '出生日期',
                '籍贯', '党派', '现任职务', '手机号码', '学历', '毕业院校', '所学专业'
            ]

            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = openpyxl.styles.Font(bold=True)
                cell.fill = openpyxl.styles.PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            # 写入代表数据
            for row, user_data in enumerate(serializer.data, 2):
                ws.cell(row=row, column=1, value=user_data['username'])
                ws.cell(row=row, column=2, value=user_data['role_display'])
                ws.cell(row=row, column=3, value=user_data['is_active_display'])
                ws.cell(row=row, column=4, value=user_data.get('created_at', ''))
                ws.cell(row=row, column=5, value=user_data['representative_name'])
                ws.cell(row=row, column=6, value=user_data['representative_level'])
                ws.cell(row=row, column=7, value=user_data['representative_gender'])
                ws.cell(row=row, column=8, value=user_data['representative_nationality'])
                ws.cell(row=row, column=9, value=user_data['representative_district'])
                ws.cell(row=row, column=10, value=user_data['representative_birth_date'])
                ws.cell(row=row, column=11, value=user_data['representative_birthplace'])
                ws.cell(row=row, column=12, value=user_data['representative_party'])
                ws.cell(row=row, column=13, value=user_data['representative_current_position'])
                ws.cell(row=row, column=14, value=user_data['representative_mobile_phone'])
                ws.cell(row=row, column=15, value=user_data['representative_education'])
                ws.cell(row=row, column=16, value=user_data['representative_graduated_school'])
                ws.cell(row=row, column=17, value=user_data['representative_major'])

        elif role == 'staff':
            # 工作人员导出表头
            headers = [
                '用户名', '角色', '账号状态', '创建时间',
                '工作人员姓名', '职位', '手机号码', '邮箱地址', '工作站点'
            ]

            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = openpyxl.styles.Font(bold=True)
                cell.fill = openpyxl.styles.PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            # 写入工作人员数据
            for row, user_data in enumerate(serializer.data, 2):
                ws.cell(row=row, column=1, value=user_data['username'])
                ws.cell(row=row, column=2, value=user_data['role_display'])
                ws.cell(row=row, column=3, value=user_data['is_active_display'])
                ws.cell(row=row, column=4, value=user_data.get('created_at', ''))
                ws.cell(row=row, column=5, value=user_data['staff_name'])
                ws.cell(row=row, column=6, value=user_data['staff_position'])
                ws.cell(row=row, column=7, value=user_data['staff_mobile_phone'])
                ws.cell(row=row, column=8, value=user_data['staff_email'])
                ws.cell(row=row, column=9, value=user_data['staff_station_name'])

        else:
            # 混合导出（保持原有逻辑）
            headers = [
                '用户名', '角色', '角色显示', '账号状态', '状态显示',
                '代表姓名', '代表层级', '性别', '民族', '所属片区', '出生日期',
                '籍贯', '党派', '现任职务', '代表手机号码', '学历', '毕业院校', '所学专业',
                '工作人员姓名', '职位', '工作人员手机号码', '邮箱地址', '工作站点'
            ]

            # 写入表头
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # 写入数据
            for row, user_data in enumerate(serializer.data, 2):
                ws.cell(row=row, column=1, value=user_data['username'])
                ws.cell(row=row, column=2, value=user_data['role'])
                ws.cell(row=row, column=3, value=user_data['role_display'])
                ws.cell(row=row, column=4, value=user_data['is_active'])
                ws.cell(row=row, column=5, value=user_data['is_active_display'])
                ws.cell(row=row, column=6, value=user_data['representative_name'])
                ws.cell(row=row, column=7, value=user_data['representative_level'])
                ws.cell(row=row, column=8, value=user_data['representative_gender'])
                ws.cell(row=row, column=9, value=user_data['representative_nationality'])
                ws.cell(row=row, column=10, value=user_data['representative_district'])
                ws.cell(row=row, column=11, value=user_data['representative_birth_date'])
                ws.cell(row=row, column=12, value=user_data['representative_birthplace'])
                ws.cell(row=row, column=13, value=user_data['representative_party'])
                ws.cell(row=row, column=14, value=user_data['representative_current_position'])
                ws.cell(row=row, column=15, value=user_data['representative_mobile_phone'])
                ws.cell(row=row, column=16, value=user_data['representative_education'])
                ws.cell(row=row, column=17, value=user_data['representative_graduated_school'])
                ws.cell(row=row, column=18, value=user_data['representative_major'])
                ws.cell(row=row, column=19, value=user_data['staff_name'])
                ws.cell(row=row, column=20, value=user_data['staff_position'])
                ws.cell(row=row, column=21, value=user_data['staff_mobile_phone'])
                ws.cell(row=row, column=22, value=user_data['staff_email'])
                ws.cell(row=row, column=23, value=user_data['staff_station_name'])

        # 创建HTTP响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        role_suffix = f'_{role}' if role else ''
        filename = f'用户列表{role_suffix}_{timestamp}.xlsx'
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        # 保存工作簿到响应
        wb.save(response)

        logger.info(f"工作人员 {request.user.username} 导出用户列表，筛选条件：role={role}, is_active={is_active}")

        return response


class UserTemplateDownloadView(APIView):
    """
    下载导入模板API

    生成Excel导入模板文件
    """

    permission_classes = [CanManageUsers]

    @swagger_auto_schema(
        operation_summary='下载导入模板',
        operation_description='下载用户导入的Excel模板文件（仅工作人员可操作）',
        manual_parameters=[
            openapi.Parameter(
                'role', openapi.IN_QUERY,
                description='角色类型（representative/staff）',
                type=openapi.TYPE_STRING,
                required=True
            ),
        ],
        responses={
            200: openapi.Response(
                description='模板下载成功',
                schema=openapi.Schema(
                    type=openapi.TYPE_FILE,
                    format=openapi.FORMAT_BINARY
                )
            ),
            400: openapi.Response(description='参数错误'),
            403: openapi.Response(description='权限不足'),
        },
        tags=['用户管理']
    )
    def get(self, request):
        """下载导入模板"""
        import os
        from django.http import HttpResponse, Http404
        from django.conf import settings

        role = request.query_params.get('role')
        if not role or role not in ['representative', 'staff']:
            return Response({
                'success': False,
                'message': '请指定角色类型：representative 或 staff'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 确定模板文件路径
        role_name = '代表' if role == 'representative' else '工作人员'
        filename = f'{role_name}账号导入模板.xlsx'

        # 模板文件路径
        template_path = os.path.join(settings.BASE_DIR, 'static', 'templates', filename)

        # 检查文件是否存在
        if not os.path.exists(template_path):
            return Response({
                'success': False,
                'message': f'模板文件不存在：{filename}'
            }, status=status.HTTP_404_NOT_FOUND)

        try:
            # 读取文件内容
            with open(template_path, 'rb') as f:
                file_content = f.read()

            # 创建HTTP响应
            response = HttpResponse(
                file_content,
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            logger.info(f"工作人员 {request.user.username} 下载{role_name}导入模板")

            return response

        except Exception as e:
            logger.error(f"下载模板文件失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'下载模板文件失败：{str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserImportView(APIView):
    """
    用户导入API

    批量导入用户账号
    """

    permission_classes = [CanManageUsers]

    @swagger_auto_schema(
        operation_summary='批量导入用户',
        operation_description='通过Excel文件批量导入用户账号（仅工作人员可操作）',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'file': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format=openapi.FORMAT_BINARY,
                    description='Excel文件'
                )
            },
            required=['file']
        ),
        responses={
            200: openapi.Response(
                description='导入成功',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'total': openapi.Schema(type=openapi.TYPE_INTEGER, description='总记录数'),
                                'success_count': openapi.Schema(type=openapi.TYPE_INTEGER, description='成功导入数'),
                                'error_count': openapi.Schema(type=openapi.TYPE_INTEGER, description='失败记录数'),
                                'errors': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(type=openapi.TYPE_OBJECT),
                                    description='错误详情'
                                )
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(description='请求参数错误'),
            403: openapi.Response(description='权限不足'),
        },
        tags=['用户管理']
    )
    def post(self, request):
        """批量导入用户"""
        import openpyxl
        from django.db import transaction
        from datetime import datetime

        # 检查文件
        if 'file' not in request.FILES:
            return Response({
                'success': False,
                'message': '请上传Excel文件'
            }, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']

        # 验证文件类型
        if not file.name.endswith(('.xlsx', '.xls')):
            return Response({
                'success': False,
                'message': '请上传Excel文件（.xlsx或.xls格式）'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 读取Excel文件
            wb = openpyxl.load_workbook(file)
            ws = wb.active

            # 获取表头
            headers = []
            for cell in ws[1]:
                headers.append(cell.value)

            # 定义字段映射（支持中文和英文表头）
            field_mapping = {
                # 基础字段 - 中文表头
                '用户名': 'username',
                '用户名*': 'username',
                '密码': 'password',
                '密码*': 'password',
                '角色': 'role',
                '角色*': 'role',
                '账号状态': 'is_active',

                # 代表字段 - 中文表头
                '代表姓名': 'representative_name',
                '代表姓名*': 'representative_name',
                '代表层级': 'representative_level',
                '代表层级*': 'representative_level',
                '性别': 'representative_gender',
                '性别*': 'representative_gender',
                '民族': 'representative_nationality',
                '所属片区': 'representative_district',
                '出生日期': 'representative_birth_date',
                '出生日期*': 'representative_birth_date',
                '籍贯': 'representative_birthplace',
                '籍贯*': 'representative_birthplace',
                '党派': 'representative_party',
                '党派*': 'representative_party',
                '现任职务': 'representative_current_position',
                '现任职务*': 'representative_current_position',
                '手机号码': 'representative_mobile_phone',
                '手机号码*': 'representative_mobile_phone',
                '学历': 'representative_education',
                '毕业院校': 'representative_graduated_school',
                '所学专业': 'representative_major',

                # 工作人员字段 - 中文表头
                '工作人员姓名': 'staff_name',
                '工作人员姓名*': 'staff_name',
                '职位': 'staff_position',
                '职位*': 'staff_position',
                '工作人员手机号码': 'staff_mobile_phone',
                '工作人员手机号码*': 'staff_mobile_phone',
                '邮箱地址': 'staff_email',
                '工作站点': 'staff_station_name',
                '工作站点*': 'staff_station_name',

                # 英文表头（兼容性）
                'username': 'username',
                'password': 'password',
                'role': 'role',
                'is_active': 'is_active',
                'representative_name': 'representative_name',
                'representative_level': 'representative_level',
                'representative_gender': 'representative_gender',
                'representative_nationality': 'representative_nationality',
                'representative_district': 'representative_district',
                'representative_birth_date': 'representative_birth_date',
                'representative_birthplace': 'representative_birthplace',
                'representative_party': 'representative_party',
                'representative_current_position': 'representative_current_position',
                'representative_mobile_phone': 'representative_mobile_phone',
                'representative_education': 'representative_education',
                'representative_graduated_school': 'representative_graduated_school',
                'representative_major': 'representative_major',
                'staff_name': 'staff_name',
                'staff_position': 'staff_position',
                'staff_mobile_phone': 'staff_mobile_phone',
                'staff_email': 'staff_email',
                'staff_station_name': 'staff_station_name'
            }

            # 处理数据
            import_data = []
            errors = []

            for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                if not any(row):  # 跳过空行
                    continue

                row_data = {}
                for col_num, value in enumerate(row):
                    if col_num < len(headers) and headers[col_num] and headers[col_num] in field_mapping:
                        field_name = field_mapping[headers[col_num]]

                        # 跳过空值
                        if value is None or (isinstance(value, str) and value.strip() == ''):
                            continue

                        # 处理特殊字段
                        if field_name == 'is_active':
                            if isinstance(value, str):
                                row_data[field_name] = value.upper().strip() in ['TRUE', '是', '启用', '1']
                            elif isinstance(value, bool):
                                row_data[field_name] = value
                            else:
                                row_data[field_name] = bool(value)
                        elif field_name == 'representative_birth_date' and value:
                            if isinstance(value, datetime):
                                row_data[field_name] = value.date()
                            elif isinstance(value, str):
                                try:
                                    # 尝试多种日期格式
                                    date_str = value.strip()
                                    if '/' in date_str:
                                        row_data[field_name] = datetime.strptime(date_str, '%Y/%m/%d').date()
                                    else:
                                        row_data[field_name] = datetime.strptime(date_str, '%Y-%m-%d').date()
                                except ValueError:
                                    errors.append({
                                        'row': row_num,
                                        'field': headers[col_num],
                                        'value': value,
                                        'error': '日期格式错误，请使用YYYY-MM-DD或YYYY/MM/DD格式'
                                    })
                                    continue
                        else:
                            # 处理字符串值
                            if isinstance(value, str):
                                row_data[field_name] = value.strip()
                            else:
                                row_data[field_name] = str(value) if value is not None else ''

                # 验证数据
                serializer = UserImportSerializer(data=row_data)
                if serializer.is_valid():
                    import_data.append(serializer.validated_data)
                else:
                    for field, field_errors in serializer.errors.items():
                        for error in field_errors:
                            errors.append({
                                'row': row_num,
                                'field': field,
                                'value': row_data.get(field, ''),
                                'error': str(error)
                            })

            # 如果有验证错误，返回错误信息
            if errors:
                return Response({
                    'success': False,
                    'message': '数据验证失败',
                    'data': {
                        'total': len(import_data) + len(errors),
                        'success_count': 0,
                        'error_count': len(errors),
                        'errors': errors
                    }
                }, status=status.HTTP_400_BAD_REQUEST)

            # 批量创建用户
            success_count = 0
            creation_errors = []

            for row_index, data in enumerate(import_data, 2):  # 从第2行开始（第1行是表头）
                try:
                    with transaction.atomic():
                        # 创建用户
                        user_data = {
                            'username': data['username'],
                            'role': data['role'],
                            'is_active': data.get('is_active', True)
                        }

                        password = data['password']
                        role = data['role']

                        user = User.objects.create_user(password=password, **user_data)

                        # 根据角色创建相关信息
                        if role == 'representative':
                            Representative.objects.create(
                                user=user,
                                level=data['representative_level'],
                                name=data['representative_name'],
                                gender=data['representative_gender'],
                                nationality=data.get('representative_nationality', ''),
                                district=data.get('representative_district', ''),
                                birth_date=data['representative_birth_date'],
                                birthplace=data['representative_birthplace'],
                                party=data['representative_party'],
                                current_position=data['representative_current_position'],
                                mobile_phone=data['representative_mobile_phone'],
                                education=data.get('representative_education', ''),
                                graduated_school=data.get('representative_graduated_school', ''),
                                major=data.get('representative_major', '')
                            )

                        elif role == 'staff':
                            StaffMember.objects.create(
                                user=user,
                                name=data['staff_name'],
                                position=data['staff_position'],
                                mobile_phone=data['staff_mobile_phone'],
                                email=data.get('staff_email', ''),
                                station_name=data['staff_station_name']
                            )

                        success_count += 1

                except Exception as e:
                    # 友好的错误信息处理
                    error_message = str(e)
                    username = data.get('username', '未知')

                    # 处理常见的数据库错误，转换为用户友好的提示
                    if 'UNIQUE constraint failed: users.username' in error_message:
                        error_message = f'用户名 "{username}" 已存在，请使用其他用户名'
                    elif 'UNIQUE constraint failed' in error_message:
                        error_message = f'数据重复，用户名 "{username}" 可能已存在'
                    elif 'NOT NULL constraint failed' in error_message:
                        if 'username' in error_message:
                            error_message = '用户名不能为空'
                        elif 'password' in error_message:
                            error_message = '密码不能为空'
                        else:
                            error_message = '必填字段不能为空'
                    elif 'IntegrityError' in error_message:
                        error_message = f'数据完整性错误，用户名 "{username}" 可能已存在或数据格式不正确'
                    elif 'ValidationError' in error_message:
                        error_message = '数据验证失败，请检查数据格式'
                    elif 'DoesNotExist' in error_message:
                        error_message = '关联数据不存在'
                    else:
                        # 如果是其他未知错误，保留原始错误但添加用户名信息
                        error_message = f'创建用户 "{username}" 时发生错误：{error_message}'

                    creation_errors.append({
                        'row': row_index,
                        'field': 'username',
                        'value': username,
                        'error': error_message
                    })

            logger.info(f"工作人员 {request.user.username} 批量导入用户，成功：{success_count}，失败：{len(creation_errors)}")

            return Response({
                'success': True,
                'message': f'导入完成，成功：{success_count}，失败：{len(creation_errors)}',
                'data': {
                    'total': len(import_data),
                    'success_count': success_count,
                    'error_count': len(creation_errors),
                    'errors': creation_errors
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"导入用户失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'导入失败：{str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)