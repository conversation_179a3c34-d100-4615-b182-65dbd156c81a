#!/usr/bin/env python
"""
数据迁移脚本：修复performance_type字段的值以匹配新的choices
在应用0002_update_performance_types迁移之前运行此脚本
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'npcsite.settings')
django.setup()

from api.performance.models import PerformanceRecord

def fix_performance_types():
    """修复performance_type字段的值"""
    
    # 定义映射关系
    type_mapping = {
        "培训学习": "学习培训",
        "实地调研": "视察调研", 
        "视察活动": "视察调研",
        "会议参与": "会议",
        "其他活动": "其它",
        "座谈交流": "接待走访",  # 归类为接待走访
        "联络活动": "接待走访",  # 归类为接待走访
        "走访群众": "接待走访",  # 归类为接待走访
    }
    
    print("开始修复performance_type数据...")
    print(f"总记录数: {PerformanceRecord.objects.count()}")
    
    # 统计需要修复的记录
    total_fixed = 0
    
    for old_type, new_type in type_mapping.items():
        records = PerformanceRecord.objects.filter(performance_type=old_type)
        count = records.count()
        if count > 0:
            print(f"修复 '{old_type}' -> '{new_type}': {count} 条记录")
            records.update(performance_type=new_type)
            total_fixed += count
    
    print(f"\n修复完成！共修复了 {total_fixed} 条记录")
    
    # 检查是否还有不匹配的值
    valid_choices = [
        '视察调研', '学习培训', '接待走访', '执法检查', '主题活动', 
        '述职', '法规征询意见', '政策法规宣传', '议案建议办理督办', 
        '会议', '其它'
    ]
    
    print("\n检查修复后的数据...")
    current_types = set(PerformanceRecord.objects.values_list('performance_type', flat=True))
    invalid_types = current_types - set(valid_choices)
    
    if invalid_types:
        print(f"⚠️  仍有不匹配的类型: {invalid_types}")
        for invalid_type in invalid_types:
            count = PerformanceRecord.objects.filter(performance_type=invalid_type).count()
            print(f"   '{invalid_type}': {count} 条记录")
        print("请手动处理这些记录或更新映射关系")
    else:
        print("✅ 所有performance_type值都已匹配新的choices")
    
    # 显示最终统计
    print("\n最终数据统计:")
    for choice in valid_choices:
        count = PerformanceRecord.objects.filter(performance_type=choice).count()
        if count > 0:
            print(f"  {choice}: {count} 条记录")

if __name__ == "__main__":
    fix_performance_types()
