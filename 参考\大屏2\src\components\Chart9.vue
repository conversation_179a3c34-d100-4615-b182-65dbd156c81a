<template>
  <div class="con">
    <ul class="advice-list">
      <li v-for="(item, idx) in data" :key="idx">
        <span class="advice-content"> · {{ item.content }} </span>
        <span class="advice-time">{{ item.time }}</span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  mounted() {},
}
</script>

<style>
.con {
  color: #ecefff;
  font-size: 13px;
  width: 100%;
  height: 200px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.advice-list {
  width: 100%;
  flex: 1;
  overflow-y: auto;
  /* background: rgba(20, 50,   90, 0.8); */
  border-radius: 8px;
  padding: 0;
  list-style: none;
  margin: 0;
}
.advice-list::-webkit-scrollbar {
  width: 4px;
}
.advice-list::-webkit-scrollbar-track {
  background: rgba(53, 89, 146, 0.3);
  border-radius: 2px;
}
.advice-list::-webkit-scrollbar-thumb {
  background: rgba(176, 196, 222, 0.6);
  border-radius: 2px;
}
.advice-list::-webkit-scrollbar-thumb:hover {
  background: rgba(176, 196, 222, 0.8);
}
.advice-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  line-height: 16px;
  border-bottom: 1px solid #355992;
  position: relative;
}
.advice-list li:last-child {
  border-bottom: none;
}
.advice-list li:hover {
  background: rgba(64, 128, 255, 0.12);
}
.advice-content {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.advice-time {
  margin-left: 16px;
  color: #b0c4de;
  flex-shrink: 0;
}
</style>
