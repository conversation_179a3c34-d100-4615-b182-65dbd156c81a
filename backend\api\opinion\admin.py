"""
意见建议管理Django Admin配置

仅用于开发和调试，生产环境不使用Django Admin
"""

from django.contrib import admin
from .models import OpinionSuggestion, OpinionReview


@admin.register(OpinionSuggestion)
class OpinionSuggestionAdmin(admin.ModelAdmin):
    """意见建议管理界面"""
    
    list_display = [
        'id', 'title', 'category', 'representative', 'reporter_name',
        'current_status', 'ai_assisted', 'created_at'
    ]
    
    list_filter = [
        'category', 'ai_assisted', 'created_at', 'updated_at'
    ]
    
    search_fields = [
        'title', 'reporter_name', 'original_content', 'final_suggestion'
    ]
    
    readonly_fields = [
        'id', 'current_status', 'current_review', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'representative', 'title', 'category', 'reporter_name')
        }),
        ('内容信息', {
            'fields': ('original_content', 'final_suggestion')
        }),
        ('AI辅助', {
            'fields': ('ai_assisted', 'ai_generated_content'),
            'classes': ('collapse',)
        }),
        ('状态信息', {
            'fields': ('current_status', 'current_review'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def current_status(self, obj):
        """显示当前状态"""
        return obj.current_status
    current_status.short_description = '当前状态'
    
    def current_review(self, obj):
        """显示最新审核记录"""
        review = obj.current_review
        if review:
            return f"{review.get_action_display()} - {review.action_time.strftime('%Y-%m-%d %H:%M')}"
        return "无审核记录"
    current_review.short_description = '最新审核'


@admin.register(OpinionReview)
class OpinionReviewAdmin(admin.ModelAdmin):
    """意见审核记录管理界面"""
    
    list_display = [
        'id', 'opinion', 'action', 'status', 'reviewer', 
        'transferred_department', 'action_time'
    ]
    
    list_filter = [
        'action', 'status', 'action_time', 'created_at'
    ]
    
    search_fields = [
        'opinion__title', 'review_comment', 'processing_result', 
        'transferred_department'
    ]
    
    readonly_fields = [
        'id', 'created_at'
    ]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'opinion', 'reviewer', 'action', 'status')
        }),
        ('审核内容', {
            'fields': ('review_comment', 'processing_result', 'transferred_department')
        }),
        ('附件信息', {
            'fields': ('attachments',),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('action_time', 'created_at'),
            'classes': ('collapse',)
        }),
    )
    
    def opinion(self, obj):
        """显示关联的意见建议"""
        return obj.opinion.title
    opinion.short_description = '意见建议'
