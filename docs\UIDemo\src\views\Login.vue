<template>
  <div class="login-container">
    <div class="login-form">
      <!-- 系统标题 -->
      <div class="login-title">
        <el-icon size="32" style="margin-right: 10px;">
          <User />
        </el-icon>
        人大代表履职服务与管理平台
      </div>
      <div class="login-subtitle">基于角色的管理系统</div>
      
      <!-- 登录表单 -->
      <el-form 
        ref="loginFormRef" 
        :model="loginForm" 
        :rules="loginRules" 
        size="large"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input 
            v-model="loginForm.username" 
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input 
            v-model="loginForm.password" 
            type="password" 
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            style="width: 100%;" 
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 测试账号信息 -->
      <el-divider>测试账号</el-divider>
      <div class="test-accounts">
        <el-row :gutter="10">
          <el-col 
            v-for="account in testAccounts" 
            :key="account.username" 
            :span="12"
          >
            <el-card 
              class="test-account-card" 
              @click="fillAccount(account)"
              shadow="hover"
            >
              <div class="account-info">
                <div class="account-name">{{ account.name }}</div>
                <div class="account-role">{{ account.role }}</div>
                <div class="account-username">{{ account.username }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <div class="test-tip">
          <el-icon><InfoFilled /></el-icon>
          点击上方卡片可快速填入测试账号
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getTestAccounts } from '@/api/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const loginFormRef = ref()

// 加载状态
const loading = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 测试账号数据
const testAccounts = ref([])

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  // 表单验证
  const valid = await loginFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  loading.value = true
  
  try {
    const result = await userStore.login(loginForm)
    
    if (result.success) {
      // 根据用户角色跳转到对应页面
      const userRole = result.userInfo.role
      if (userRole === 'representative') {
        router.push('/representative')
      } else if (userRole === 'staff') {
        router.push('/staff')
      } else {
        router.push('/dashboard')
      }
    }
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请重试')
  } finally {
    loading.value = false
  }
}

// 填入测试账号
const fillAccount = (account) => {
  loginForm.username = account.username
  loginForm.password = account.password
  ElMessage.success(`已填入${account.role}测试账号`)
}

// 组件挂载时获取测试账号
onMounted(() => {
  testAccounts.value = getTestAccounts()
})
</script>

<style scoped>
.test-accounts {
  margin-top: 20px;
}

.test-account-card {
  cursor: pointer;
  margin-bottom: 10px;
  transition: all 0.3s;
}

.test-account-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(200, 16, 46, 0.3);
}

.account-info {
  text-align: center;
  padding: 10px;
}

.account-name {
  font-weight: bold;
  color: var(--china-red);
  margin-bottom: 5px;
}

.account-role {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.account-username {
  font-size: 11px;
  color: #999;
  font-family: monospace;
}

.test-tip {
  text-align: center;
  font-size: 12px;
  color: #999;
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

/* 卡片内容样式调整 */
:deep(.el-card__body) {
  padding: 10px !important;
}

/* 分割线样式 */
:deep(.el-divider__text) {
  background-color: white;
  color: var(--china-red);
  font-weight: bold;
}
</style> 