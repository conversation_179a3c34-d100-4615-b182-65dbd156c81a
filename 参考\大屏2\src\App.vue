<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import Chart1 from './components/Chart1.vue'
import Chart2 from './components/Chart2.vue'
import Chart4 from './components/Chart4.vue'
import Chart5 from './components/Chart5.vue'
import Chart6 from './components/Chart6.vue'
import Chart7 from './components/Chart7.vue'
import Chart8 from './components/Chart8.vue'
import Chart9 from './components/Chart9.vue'

// 时间显示
const currentTime = ref('')
const currentDate = ref('')
let timer = null

// 数据状态
const chartData = ref({})
const loading = ref(true)

// 模拟请求获取数据
const fetchData = async () => {
  try {
    loading.value = true
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const response = await fetch('/data.json')
    if (!response.ok) {
      throw new Error('Failed to fetch data')
    }
    
    const data = await response.json()
    chartData.value = data
    console.log('数据加载成功:', data)
  } catch (error) {
    console.error('数据加载失败:', error)
    // 如果请求失败，使用默认数据
    chartData.value = {
      chart1: { data: [] },
      chart2: { data: [] },
      chart4: { data: [] },
      chart5: { regions: {} },
      chart6: { selectedMonth: '', months: [], chartData: {} },
      chart7: { rankingData: [] },
      chart8: { data: [] },
      chart9: { data: [] }
    }
  } finally {
    loading.value = false
  }
}

const updateTime = () => {
  const now = new Date()
  currentDate.value = now
    .toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
    .replace(/\//g, '/')
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

const loginBackend = () => {
  // 这里可以添加登录后台的逻辑
  console.log('登录后台')
}

onMounted(async () => {
  updateTime()
  timer = setInterval(updateTime, 1000)
  
  // 获取数据
  await fetchData()
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<template>
  <div class="wrap">
    <!-- 标题 -->
    <div class="num0">
      <h1 class="header">履职服务平台数据大屏</h1>
      <div class="header-right">
        <div class="time-display">
          <div class="date">{{ currentDate }}</div>
          <div class="time">{{ currentTime }}</div>
        </div>
        <button class="login-btn" @click="loginBackend">登录后台</button>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">数据加载中...</div>
    </div>
    
    <!-- 图表组件 -->
    <div class="num1 active">
      <div class="tit"><span>代表层级统计</span></div>
      <Chart1 :data="chartData.chart1?.data || []"></Chart1>
    </div>
    <div class="num2 active">
      <div class="tit"><span>代表结构组成</span></div>
      <Chart2 :data="chartData.chart2?.data || []"></Chart2>
    </div>
    <div class="num4 active">
      <div class="tit"><span>意⻅建议</span></div>
      <Chart4 :data="chartData.chart4?.data || []"></Chart4>
    </div>
    <div class="num5">
      <Chart5 :regions="chartData.chart5?.regions || {}"></Chart5>
    </div>
    <div class="num6 active">
      <div class="tit"><span>履职明细</span></div>
      <Chart6 
        :selectedMonth="chartData.chart6?.selectedMonth || ''"
        :months="chartData.chart6?.months || []"
        :chartData="chartData.chart6?.chartData || {}"
      ></Chart6>
    </div>
    <div class="num7 active">
      <div class="tit"><span>代表名单</span></div>
      <Chart7 :rankingData="chartData.chart7?.rankingData || []"></Chart7>
    </div>
    <div class="num8 active">
      <div class="tit"><span>履职统计</span></div>
      <Chart8 :data="chartData.chart8?.data || []"></Chart8>
    </div>
    <div class="num9 active">
      <div class="tit"><span>AI知识库</span></div>
      <Chart9 :data="chartData.chart9?.data || []"></Chart9>
    </div>
  </div>
</template>

<style>
.wrap {
  width: 100vw;
  height: 100vh;
  padding: 0 10px 5px;
  background-color: var(--background);
  background-image: url('./img/bgImg.png');
  background-size: 100% 100%;
  display: grid;
  grid-template-rows: 1.3fr 4.5fr 1.5fr 1fr 1fr 1fr 2fr 4fr;
  grid-template-columns: 3fr 4fr 3fr;
  grid-gap: 5px 10px;
  grid-template-areas:
    'num0 num0 num0'
    'num1 num5 num7'
    'num2 num5 num7'
    'num2 num5 num7'
    'num2 num5 num8'
    'num2 num6 num8'
    'num4 num6 num8'
    'num4 num6 num9';
}

/* 加载状态样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(94, 203, 255, 0.3);
  border-top: 3px solid #5ecbff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  color: #5ecbff;
  font-size: 18px;
  font-family: 'Microsoft YaHei', sans-serif;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.num0 {
  grid-area: num0;
}
.num1 {
  grid-area: num1;
}
.num2 {
  grid-area: num2;
}
.num3 {
  grid-area: num3;
}
.num4 {
  grid-area: num4;
}
.num5 {
  grid-area: num5;
}
.num6 {
  grid-area: num6;
}
.num7 {
  grid-area: num7;
}
.num8 {
  grid-area: num8;
}
.num9 {
  grid-area: num9;
}
.active {
  border: 1px solid #5ecbff;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: inset 0 0 18px 2px rgba(30, 144, 255, 0.5);
}
.active .tit {
  color: rgb(202, 251, 251);
  margin-top: -10px;
  margin-left: 5px;
  /* font-weight: bold; */
}
.active .tit span {
  background-color: rgb(1, 15, 60);
  letter-spacing: 2px;
  padding: 5px 6px 5px 8px;
}

.num0 .header {
  color: aliceblue;
  text-align: center;
  letter-spacing: 4px;
  font-size: 26px;
  line-height: 60px;
  text-shadow: 6px 7px 4px BLACK;
}

.num0 {
  position: relative;
  padding: 0 20px;
}

.header-right {
  position: absolute;
  top: 15px;
  right: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.time-display {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  color: #5ecbff;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.2;
}

.date {
  font-size: 16px;
  margin-bottom: 2px;
}

.time {
  font-size: 16px;
}

.login-btn {
  background: transparent;
  border: 1px solid #5ecbff;
  border-radius: 6px;
  color: #5ecbff;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Microsoft YaHei', sans-serif;
}

.login-btn:hover {
  background: rgba(94, 203, 255, 0.1);
  box-shadow: 0 0 10px rgba(94, 203, 255, 0.3);
}

.login-btn:active {
  transform: scale(0.98);
}
</style>
