<template>
  <div class="login-container">
    <div class="login-form">
      <!-- 系统标题 -->
      <div class="login-title">
        <el-icon size="32" style="margin-right: 10px;">
          <User />
        </el-icon>
        人大代表履职服务与管理平台
      </div>
      <div class="login-subtitle">基于角色的管理系统</div>
      
      <!-- 登录表单 -->
      <el-form 
        ref="loginFormRef" 
        :model="loginForm" 
        :rules="loginRules" 
        size="large"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input 
            v-model="loginForm.username" 
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input 
            v-model="loginForm.password" 
            type="password" 
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            style="width: 100%;"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '正在进入系统...' : '登录' }}
          </el-button>
        </el-form-item>

        <!-- 跳转到大屏按钮 -->
        <el-form-item>
          <el-button
            type="info"
            style="width: 100%;"
            plain
            @click="goToBigScreen"
          >
            查看数据大屏
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 使用说明 -->
      <el-divider>使用说明</el-divider>
      <div class="usage-info">
        <el-alert
          title="登录说明"
          type="info"
          show-icon
          :closable="false"
        >
          <template #default>
            <p>请使用后端数据库中的真实用户账号登录</p>
            <p>如需创建测试账号，请在后端执行：</p>
            <code>uv run manage.py init_test_data</code>
          </template>
        </el-alert>
      </div>
    </div>
  </div>
  
  <!-- 工作计划提醒弹窗 -->
  <WorkPlanReminderDialog 
    ref="reminderDialogRef"
    @close="handleReminderClose"
    @goToWorkPlan="handleGoToWorkPlan"
  />
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import WorkPlanReminderDialog from '@/components/WorkPlanReminderDialog.vue'

const router = useRouter()
const userStore = useUserStore()

// 提醒弹窗引用
const reminderDialogRef = ref()

// 调试方法：检查组件挂载状态
const debugComponentMount = () => {
  console.log('🔍 调试组件挂载状态:')
  console.log('- reminderDialogRef:', reminderDialogRef)
  console.log('- reminderDialogRef.value:', reminderDialogRef.value)
  console.log('- 组件类型:', typeof reminderDialogRef.value)
  if (reminderDialogRef.value) {
    console.log('- 组件方法:', Object.keys(reminderDialogRef.value))
  }
}

// 检查用户是否已登录，如果已登录则自动跳转
onMounted(() => {
  if (userStore.isLoggedIn) {
    console.log('用户已登录，自动跳转到对应页面')
    redirectToUserPage()
  }
})

// 预加载组件函数
const preloadComponents = async (role) => {
  try {
    if (role === 'representative') {
      // 预加载代表工作台组件
      await import('@/views/representative/Index.vue')
    } else if (role === 'staff') {
      // 预加载工作人员工作台组件
      await import('@/views/staff/Index.vue')
    } else {
      // 预加载通用工作台组件
      await import('@/views/Dashboard.vue')
    }
  } catch (error) {
    console.warn('组件预加载失败:', error)
  }
}

// 根据用户角色跳转到对应页面
const redirectToUserPage = async () => {
  // 检查是否有重定向参数
  const redirectPath = router.currentRoute.value.query.redirect
  
  if (redirectPath) {
    console.log('🔄 登录后重定向到:', redirectPath)
    router.replace(decodeURIComponent(redirectPath))
    return
  }
  
  // 没有重定向参数，根据角色跳转到默认页面
  const userRole = userStore.userInfo.role
  console.log('📍 根据角色跳转:', userRole)
  
  if (userRole === 'representative') {
    router.replace('/representative')
  } else if (userRole === 'staff') {
    // 工作人员登录后先检查工作计划提醒
    console.log('🔔 工作人员登录，先检查工作计划提醒')
    await checkWorkPlanReminders()
    
    // 不自动跳转，等待用户关闭提醒弹窗后再跳转
    // 跳转逻辑移到handleReminderClose和handleGoToWorkPlan中
  } else {
    router.replace('/dashboard')
  }
}

// 检查工作计划提醒
const checkWorkPlanReminders = async () => {
  try {
    console.log('🔍 开始检查工作计划提醒...')
    console.log('👤 当前用户信息:', {
      role: userStore.userInfo.role,
      username: userStore.userInfo.username,
      isStaff: userStore.userInfo.role === 'staff'
    })
    
    // 确保用户是工作人员
    if (userStore.userInfo.role !== 'staff') {
      console.log('⚠️ 用户不是工作人员，跳过工作计划提醒')
      return
    }
    
    console.log('✅ 用户是工作人员，准备显示工作计划提醒弹窗')
    
    // 等待DOM更新完成
    await nextTick()
    
    // 调试组件挂载状态
    debugComponentMount()
    
    // 直接检查组件引用并调用
    if (reminderDialogRef.value) {
      console.log('📱 找到提醒弹窗组件，开始显示')
      await reminderDialogRef.value.show()
    } else {
      console.error('❌ 提醒弹窗组件引用不存在！检查组件是否正确挂载')
      console.log('🔧 尝试延迟重试...')
      
      // 最后尝试延迟调用
      setTimeout(async () => {
        if (reminderDialogRef.value) {
          console.log('🔄 延迟重试成功，显示提醒弹窗')
          await reminderDialogRef.value.show()
        } else {
          console.error('❌ 延迟重试仍然失败，组件无法挂载，直接跳转')
          // 如果组件无法挂载，直接跳转到工作人员首页
          router.replace('/staff')
        }
      }, 200)
    }
    
  } catch (error) {
    console.error('❌ 检查工作计划提醒失败:', error)
    // 提醒检查失败不影响正常登录流程，只在控制台记录错误
  }
}

// 处理提醒弹窗关闭
const handleReminderClose = () => {
  console.log('工作计划提醒弹窗已关闭，跳转到工作人员首页')
  router.replace('/staff')
}

// 处理跳转到工作计划管理
const handleGoToWorkPlan = () => {
  console.log('用户选择管理工作计划，跳转到工作计划页面')
  router.replace('/staff/work-plan')
}

// 表单引用
const loginFormRef = ref()

// 加载状态
const loading = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 跳转到大屏
const goToBigScreen = () => {
  router.push('/')
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  // 表单验证
  const valid = await loginFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  loading.value = true
  
  try {
    // 并行执行登录请求和组件预加载
    const [result] = await Promise.all([
      userStore.login(loginForm),
      // 根据用户名推测用户角色并预加载组件（可选优化）
      // 这里简化处理，登录成功后再根据实际角色加载
    ])
    
    if (result.success) {
      // 在跳转前预加载目标组件
      await preloadComponents(result.userInfo.role)
      
      // 立即跳转，组件已预加载完成
      redirectToUserPage()
      
      // 延迟隐藏loading，给用户一个平滑的过渡体验
      setTimeout(() => {
        loading.value = false
      }, 200)
      return
    }
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请重试')
  } finally {
    // 只有登录失败时才立即隐藏loading
    if (!userStore.isLoggedIn) {
      loading.value = false
    }
  }
}

</script>

<style scoped>
.usage-info {
  margin-top: 20px;
}

.usage-info code {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  color: #c7254e;
  font-size: 12px;
}

.usage-info p {
  margin: 8px 0;
  line-height: 1.5;
}

/* 分割线样式 */
:deep(.el-divider__text) {
  background-color: white;
  color: var(--china-red);
  font-weight: bold;
}

/* Alert组件样式调整 */
:deep(.el-alert__content) {
  padding-left: 8px;
}
</style> 