<template>
  <div class="annual-achievements">
    <!-- 年度列表视图 -->
    <div v-if="!showReport" class="year-list-view">
      <el-card class="page-header" shadow="never">
        <div class="header-content">
          <div class="header-left">
            <h1 class="main-title">年度履职AI分析展示</h1>
            <p class="subtitle">AI智能分析您的年度履职记录，生成图文并茂的精美报告</p>
          </div>
          <div class="header-decoration">
            <el-icon :size="64" color="#c62d2d">
              <Trophy />
            </el-icon>
          </div>
        </div>
      </el-card>

      <el-card class="year-grid-container" shadow="hover">
        <div class="year-grid">
          <div 
            v-for="year in availableYears" 
            :key="year"
            class="year-card"
            @click="selectYear(year)"
          >
            <div class="year-card-content">
              <div class="year-number">{{ year }}</div>
              <div class="year-label">年度</div>
              <div class="year-status" v-if="yearStatusMap[year]">
                <el-tag 
                  :type="yearStatusMap[year].completed ? 'success' : 'warning'"
                  size="small"
                >
                  {{ yearStatusMap[year].completed ? '已生成' : '未生成' }}
                </el-tag>
              </div>
            </div>
            <div class="year-overlay">
              <el-icon :size="24">
                <TrendCharts />
              </el-icon>
              <span>查看分析</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 报告详情视图 -->
    <div v-if="showReport" class="report-view">
      <!-- 返回和年度信息头部 -->
      <el-card class="report-header" shadow="never">
        <div class="report-header-content">
          <div class="back-section">
            <el-button 
              @click="backToYearList" 
              type="primary"
              size="large"
              circle
            >
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            <div class="current-year-info">
              <h2>{{ selectedYear }}年度履职分析</h2>
              <p>AI智能生成的年度履职成果展示</p>
            </div>
          </div>
          <div class="header-actions">
            <el-button 
              type="primary"
              @click="handleHeaderButtonClick"
              :loading="loadingAchievements"
              size="large"
            >
              <el-icon><TrendCharts /></el-icon>
              {{ achievements ? '重新生成' : '生成分析' }}
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 成果展示区域 -->
      <div v-if="achievements" class="achievements-display">
        <!-- 使用新的AI报告渲染器 -->
        <AIReportRenderer
          :data="achievements"
          :representative-info="representativeInfo"
          :generated-time="achievementsGeneratedTime"
        />
        
        <!-- 报告底部操作区 -->
        <el-card class="report-actions" shadow="hover">
          <div class="action-buttons">
            <el-button 
              type="primary"
              size="large"
              @click="regenerateAchievements"
              :loading="loadingAchievements"
            >
              <el-icon><Refresh /></el-icon>
              重新生成分析
            </el-button>
            <el-button 
              type="default"
              size="large"
              @click="exportAchievements"
              :loading="exportingPDF"
            >
              <el-icon><Download /></el-icon>
              {{ exportingPDF ? '正在导出...' : '导出PDF报告' }}
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 生成中的加载状态 -->
      <div v-if="loadingAchievements && !achievements" class="loading-container">
        <el-card class="loading-card" shadow="hover">
          <div class="loading-content">
            <el-icon class="loading-icon" :size="48" color="#c62d2d">
              <Loading />
            </el-icon>
            <h3>AI正在生成您的成果展示</h3>
            <p>{{ loadingText }}</p>
            <el-progress 
              :percentage="loadingProgress" 
              :show-text="false"
              color="#c62d2d"
              style="margin-top: 20px;"
            />
          </div>
        </el-card>
      </div>

      <!-- 暂无分析提示 -->
      <div v-if="!achievements && !loadingAchievements" class="no-analysis">
        <el-card class="no-analysis-card" shadow="hover">
          <div class="no-analysis-content">
            <el-icon :size="64" color="#c62d2d">
              <DataLine />
            </el-icon>
            <h3>暂无{{ selectedYear }}年度分析</h3>
            <p>点击上方"生成分析"按钮，AI将为您智能分析年度履职数据</p>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  TrendCharts, 
  Star, 
  Refresh, 
  Download, 
  Loading,
  Trophy,
  DataLine,
  User,
  Timer,
  ArrowLeft
} from '@element-plus/icons-vue'
import { aiSummaryAPI, aiSummaryUtils } from '@/api/modules/aisummary'
import { AI_SUMMARY_CONFIG } from '@/api/modules/aisummary/config'
import AIReportRenderer from '@/components/performance/AIReportRenderer.vue'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

// 状态管理
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const selectedYear = ref(null)
const showReport = ref(false)
const loadingAchievements = ref(false)
const loadingProgress = ref(0)
const loadingText = ref('正在分析数据...')
const achievements = ref(null)
const achievementsGeneratedTime = ref(null)
const yearStatusMap = ref({})
const exportingPDF = ref(false)

// 可选年份
const availableYears = computed(() => {
  return aiSummaryUtils.getAvailableYears(6)
})

// 代表信息
const representativeInfo = computed(() => {
  return userStore.userInfo.representative_info || {}
})

// 选择年度
const selectYear = async (year) => {
  selectedYear.value = year
  showReport.value = true
  await loadExistingAnalysis()
}

// 返回年度列表
const backToYearList = () => {
  showReport.value = false
  selectedYear.value = null
  achievements.value = null
  achievementsGeneratedTime.value = null
}

// 加载年度状态信息
const loadYearStatuses = async () => {
  try {
    for (const year of availableYears.value) {
      try {
        const response = await aiSummaryAPI.getDetail(year)
        // 检查响应数据结构并正确判断状态
        let hasAnalysis = false
        let isCompleted = false
        
        if (response.data) {
          // 格式1: 直接的数据结构
          if (response.data.is_completed !== undefined) {
            hasAnalysis = true
            isCompleted = response.data.is_completed
          }
          // 格式2: 嵌套的data结构
          else if (response.data.data && response.data.data.is_completed !== undefined) {
            hasAnalysis = true
            isCompleted = response.data.data.is_completed
          }
          // 格式3: success字段的响应
          else if (response.data.success && response.data.data) {
            hasAnalysis = true
            isCompleted = response.data.data.is_completed || false
          }
        }
        
        yearStatusMap.value[year] = {
          completed: isCompleted,
          hasData: hasAnalysis
        }
        
        console.log(`[年度状态] ${year}年: 有分析=${hasAnalysis}, 已完成=${isCompleted}`)
        
      } catch (error) {
        if (error.response?.status === 404) {
          yearStatusMap.value[year] = {
            completed: false,
            hasData: false
          }
        } else {
          console.error(`[年度状态] 检查${year}年状态失败:`, error)
          yearStatusMap.value[year] = {
            completed: false,
            hasData: false
          }
        }
      }
    }
  } catch (error) {
    console.error('[年度状态] 批量检查失败:', error)
  }
}

// 加载已有的AI分析结果
const loadExistingAnalysis = async () => {
  if (!selectedYear.value) return
  
  try {
    console.log(`[AI总结] 尝试加载 ${selectedYear.value}年 已有分析`)
    
    const response = await aiSummaryAPI.getDetail(selectedYear.value)
    
    // 兼容不同的响应数据结构
    let analysisData = null
    
    // 检查响应数据格式
    if (response.data) {
      // 格式1: response.data直接包含分析数据
      if (response.data.is_completed && response.data.ai_result_data) {
        analysisData = response.data.ai_result_data
      }
      // 格式2: response.data.data包含分析数据（与工作人员端兼容）
      else if (response.data.data && response.data.data.is_completed && response.data.data.ai_result_data) {
        analysisData = response.data.data.ai_result_data
      }
      // 格式3: 检查success字段的响应
      else if (response.data.success && response.data.data && response.data.data.is_completed && response.data.data.ai_result_data) {
        analysisData = response.data.data.ai_result_data
      }
    }
    
    if (analysisData) {
      achievements.value = analysisData
      
      // 保存生成时间
      if (response.data) {
        // 格式1: response.data直接包含时间
        if (response.data.completed_at) {
          achievementsGeneratedTime.value = response.data.completed_at
        }
        // 格式2: response.data.data包含时间
        else if (response.data.data && response.data.data.completed_at) {
          achievementsGeneratedTime.value = response.data.data.completed_at
        }
        // 格式3: success字段的响应
        else if (response.data.success && response.data.data && response.data.data.completed_at) {
          achievementsGeneratedTime.value = response.data.data.completed_at
        }
      }
      
      console.log(`[AI总结] 成功加载已有分析结果，数据结构:`, Object.keys(analysisData))
      console.log(`[AI总结] 生成时间:`, achievementsGeneratedTime.value)
    } else {
      achievements.value = null
      achievementsGeneratedTime.value = null
      console.log(`[AI总结] 该年度暂无已完成的分析结果，响应数据:`, response.data)
    }
    
  } catch (error) {
    // 404 错误表示没有该年度的分析数据，这是正常情况
    if (error.response?.status === 404) {
      achievements.value = null
      console.log(`[AI总结] 该年度暂无分析数据`)
    } else {
      console.error('[AI总结] 加载已有分析失败:', error)
    }
  }
}

// 生成成果展示
const generateAchievements = async () => {
  if (!selectedYear.value) {
    ElMessage.warning('请选择年度')
    return
  }

  loadingAchievements.value = true
  loadingProgress.value = 0
  
  try {
    console.log(`[AI总结] 开始生成 ${selectedYear.value}年 AI分析`)
    
    // 先尝试不强制重新生成
    let result
    try {
      result = await aiSummaryAPI.generate({
        analysis_year: selectedYear.value,
        force_regenerate: false
      })
    } catch (error) {
      // 如果返回400错误且提示已存在，询问用户是否重新生成
      if (error.response?.status === 400 && 
          error.response?.data?.analysis_year && 
          error.response.data.analysis_year[0]?.includes('已存在')) {
        const confirmResult = await ElMessageBox.confirm(
          `${selectedYear.value}年的AI分析已存在，是否重新生成？`,
          '确认操作',
          {
            confirmButtonText: '重新生成',
            cancelButtonText: '查看已有分析',
            type: 'warning'
          }
        ).catch(() => 'cancel')
        
        if (confirmResult === 'confirm') {
          // 用户选择重新生成
          result = await aiSummaryAPI.generate({
            analysis_year: selectedYear.value,
            force_regenerate: true
          })
        } else {
          // 用户选择查看已有分析，直接加载
          await loadExistingAnalysis()
          loadingAchievements.value = false
          if (achievements.value) {
            ElMessage.success('AI分析数据加载完成！')
          } else {
            ElMessage.warning('暂无该年度的AI分析数据')
          }
          return
        }
      } else {
        // 其他错误，重新抛出
        throw error
      }
    }
    
    console.log('[AI总结] 生成请求结果:', result.data)
    
    // 如果正在生成，开始轮询状态并模拟进度
    if (result.data.status === 'generating') {
      loadingText.value = 'AI正在智能分析您的年度履职数据...'
      
      // 模拟生成进度
      const progressSteps = AI_SUMMARY_CONFIG.PROGRESS_SIMULATION.STEPS

      // 启动进度模拟
      const progressInterval = setInterval(() => {
        const currentStep = progressSteps.find(step => step.progress > loadingProgress.value)
        if (currentStep) {
          loadingProgress.value = currentStep.progress
          loadingText.value = currentStep.text
        }
              }, AI_SUMMARY_CONFIG.PROGRESS_SIMULATION.INTERVAL)

      try {
        const finalResult = await aiSummaryAPI.pollGenerationStatus(selectedYear.value)
        console.log('[AI总结] 最终生成结果:', finalResult)
        
        clearInterval(progressInterval)
        loadingProgress.value = 100
        loadingText.value = '生成完成！'
        
        if (finalResult.is_completed && finalResult.ai_result_data) {
          achievements.value = finalResult.ai_result_data
          achievementsGeneratedTime.value = finalResult.completed_at
          ElMessage.success(AI_SUMMARY_CONFIG.SUCCESS_MESSAGES.GENERATION_COMPLETED)
          // 更新年度状态
          await loadYearStatuses()
        } else if (finalResult.is_failed) {
          throw new Error(finalResult.error_message || 'AI分析生成失败')
        }
      } catch (error) {
        clearInterval(progressInterval)
        throw error
      }
    } else if (result.data.is_completed || result.data.status === 'completed') {
      // 如果已经完成，直接显示结果
      console.log('[AI总结] 检测到已完成状态，立即加载结果')
      const detailResponse = await aiSummaryAPI.getDetail(selectedYear.value)
      
      // 兼容不同的响应数据结构
      let analysisData = null
      if (detailResponse.data) {
        // 格式1: response.data直接包含分析数据
        if (detailResponse.data.ai_result_data) {
          analysisData = detailResponse.data.ai_result_data
        }
        // 格式2: response.data.data包含分析数据（与工作人员端兼容）
        else if (detailResponse.data.data && detailResponse.data.data.ai_result_data) {
          analysisData = detailResponse.data.data.ai_result_data
        }
        // 格式3: 检查success字段的响应
        else if (detailResponse.data.success && detailResponse.data.data && detailResponse.data.data.ai_result_data) {
          analysisData = detailResponse.data.data.ai_result_data
        }
      }
      
      if (analysisData) {
        achievements.value = analysisData
        // 保存生成时间
        if (detailResponse.data) {
          if (detailResponse.data.completed_at) {
            achievementsGeneratedTime.value = detailResponse.data.completed_at
          } else if (detailResponse.data.data && detailResponse.data.data.completed_at) {
            achievementsGeneratedTime.value = detailResponse.data.data.completed_at
          } else if (detailResponse.data.success && detailResponse.data.data && detailResponse.data.data.completed_at) {
            achievementsGeneratedTime.value = detailResponse.data.data.completed_at
          }
        }
        loadingProgress.value = 100
        ElMessage.success('AI履职分析生成完成！')
        // 更新年度状态
        await loadYearStatuses()
      } else {
        throw new Error('获取分析结果失败')
      }
    }
    
      } catch (error) {
      console.error('[AI总结] 生成失败:', error)
      
      // 根据错误类型显示不同的友好提示
      if (error.response?.data?.error === 'data_insufficient') {
        // 数据不足的友好提示
        ElMessage({
          type: 'warning',
          message: `${selectedYear.value}年暂无履职数据`,
          description: '您在选定年份没有履职记录和意见建议，无法生成AI分析报告。请选择其他年份或等待履职数据产生后再试。',
          duration: 6000,
          showClose: true
        })
      } else if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        // 特殊处理超时错误
        ElMessage.warning({
          message: AI_SUMMARY_CONFIG.ERROR_MESSAGES.TIMEOUT,
          duration: 6000,
          showClose: true
        })
      } else {
        // 其他错误的通用提示
        const errorMsg = error.response?.data?.message || error.message || AI_SUMMARY_CONFIG.ERROR_MESSAGES.GENERATION_FAILED
        ElMessage.error(errorMsg)
      }
    } finally {
      loadingAchievements.value = false
      loadingProgress.value = 0
    }
}

// 处理顶部按钮点击
const handleHeaderButtonClick = () => {
  console.log('顶部按钮点击，当前achievements状态:', !!achievements.value)
  if (achievements.value) {
    regenerateAchievements()
  } else {
    generateAchievements()
  }
}

// 重新生成
const regenerateAchievements = async () => {
  try {
    await ElMessageBox.confirm(
      '重新生成将覆盖当前的AI分析结果，是否继续？',
      '确认重新生成',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 执行重新生成
    loadingAchievements.value = true
    loadingProgress.value = 0
    
    try {
      console.log(`[AI总结] 开始重新生成 ${selectedYear.value}年 AI分析`)
      
      const result = await aiSummaryAPI.generate({
        analysis_year: selectedYear.value,
        force_regenerate: true
      })
      console.log('[AI总结] 重新生成请求结果:', result.data)
      
      if (result.data.status === 'generating') {
        loadingText.value = 'AI正在重新分析您的年度履职数据...'
        
        // 模拟生成进度
        const progressSteps = AI_SUMMARY_CONFIG.PROGRESS_SIMULATION.STEPS

        // 启动进度模拟
        const progressInterval = setInterval(() => {
          const currentStep = progressSteps.find(step => step.progress > loadingProgress.value)
          if (currentStep) {
            loadingProgress.value = currentStep.progress
            loadingText.value = currentStep.text
          }
        }, AI_SUMMARY_CONFIG.PROGRESS_SIMULATION.INTERVAL)
        
        try {
          const finalResult = await aiSummaryAPI.pollGenerationStatus(selectedYear.value)
          console.log('[AI总结] 重新生成最终结果:', finalResult)
          
          clearInterval(progressInterval)
          loadingProgress.value = 100
          
          if (finalResult.is_completed && finalResult.ai_result_data) {
            achievements.value = finalResult.ai_result_data
            achievementsGeneratedTime.value = finalResult.completed_at
            ElMessage.success('AI履职分析重新生成完成！')
            // 更新年度状态
            await loadYearStatuses()
          } else if (finalResult.is_failed) {
            throw new Error(finalResult.error_message || 'AI分析重新生成失败')
          }
        } catch (error) {
          clearInterval(progressInterval)
          throw error
        }
      } else if (result.data.status === 'completed') {
        // 如果状态显示已完成，立即加载结果
        console.log('[AI总结] 检测到已完成状态，立即加载结果')
        loadingProgress.value = 100
        
        try {
          const detailResponse = await aiSummaryAPI.getDetail(selectedYear.value)
          
          // 兼容不同的响应数据结构
          let analysisData = null
          if (detailResponse.data) {
            // 格式1: response.data直接包含分析数据
            if (detailResponse.data.ai_result_data) {
              analysisData = detailResponse.data.ai_result_data
            }
            // 格式2: response.data.data包含分析数据（与工作人员端兼容）
            else if (detailResponse.data.data && detailResponse.data.data.ai_result_data) {
              analysisData = detailResponse.data.data.ai_result_data
            }
            // 格式3: 检查success字段的响应
            else if (detailResponse.data.success && detailResponse.data.data && detailResponse.data.data.ai_result_data) {
              analysisData = detailResponse.data.data.ai_result_data
            }
          }
          
          if (analysisData) {
            achievements.value = analysisData
            // 保存生成时间
            if (detailResponse.data) {
              if (detailResponse.data.completed_at) {
                achievementsGeneratedTime.value = detailResponse.data.completed_at
              } else if (detailResponse.data.data && detailResponse.data.data.completed_at) {
                achievementsGeneratedTime.value = detailResponse.data.data.completed_at
              } else if (detailResponse.data.success && detailResponse.data.data && detailResponse.data.data.completed_at) {
                achievementsGeneratedTime.value = detailResponse.data.data.completed_at
              }
            }
            ElMessage.success('AI履职分析重新生成完成！')
            // 更新年度状态
            await loadYearStatuses()
          } else {
            throw new Error('获取分析结果失败')
          }
        } catch (error) {
          console.error('[AI总结] 加载完成结果失败:', error)
          throw new Error('加载分析结果失败')
        }
      }
      
    } catch (error) {
      console.error('[AI总结] 重新生成失败:', error)
      
      // 根据错误类型显示不同的友好提示
      if (error.response?.data?.error === 'data_insufficient') {
        // 数据不足的友好提示
        ElMessage({
          type: 'warning',
          message: `${selectedYear.value}年暂无履职数据`,
          description: '您在选定年份没有履职记录和意见建议，无法生成AI分析报告。请选择其他年份或等待履职数据产生后再试。',
          duration: 6000,
          showClose: true
        })
      } else if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        // 特殊处理超时错误
        ElMessage.warning({
          message: AI_SUMMARY_CONFIG.ERROR_MESSAGES.TIMEOUT,
          duration: 6000,
          showClose: true
        })
      } else {
        // 其他错误的通用提示
        const errorMsg = error.response?.data?.message || error.message || 'AI分析重新生成失败，请稍后重试'
        ElMessage.error(errorMsg)
      }
    } finally {
      loadingAchievements.value = false
      loadingProgress.value = 0
    }
    
  } catch {
    // 用户取消操作
  }
}

// 导出PDF
const exportAchievements = async () => {
  if (!achievements.value) {
    ElMessage.warning('暂无报告内容可导出')
    return
  }

  exportingPDF.value = true
  try {
    ElMessage.info('正在生成PDF，请稍候...')
    
    // 查找报告渲染器组件
    const reportElement = document.querySelector('.ai-report-renderer')
    if (!reportElement) {
      ElMessage.error('未找到报告内容')
      return
    }

    // 临时设置样式以优化PDF输出
    const originalOverflow = reportElement.style.overflow
    const originalHeight = reportElement.style.height
    const originalMaxWidth = reportElement.style.maxWidth
    const originalWidth = reportElement.style.width
    const originalPadding = reportElement.style.padding
    
    reportElement.style.overflow = 'visible'
    reportElement.style.height = 'auto'
    reportElement.style.maxWidth = '1000px' // 设置更宽的最大宽度
    reportElement.style.width = '1000px'    // 固定宽度以确保一致性
    reportElement.style.padding = '20px'    // 适当的内边距

    // 使用html2canvas截图
    const canvas = await html2canvas(reportElement, {
      scale: 2, // 提高清晰度
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: reportElement.scrollWidth,
      height: reportElement.scrollHeight,
      scrollX: 0,
      scrollY: 0,
      // 优化PDF输出的选项
      removeContainer: true,
      imageTimeout: 15000,
      logging: false
    })

    // 恢复原始样式
    reportElement.style.overflow = originalOverflow
    reportElement.style.height = originalHeight
    reportElement.style.maxWidth = originalMaxWidth
    reportElement.style.width = originalWidth
    reportElement.style.padding = originalPadding

    // 创建PDF
    const imgData = canvas.toDataURL('image/jpeg', 0.8)
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    })

    // A4页面尺寸 (210 x 297 mm)
    const pageWidth = 210
    const pageHeight = 297
    const margin = 5 // 减小边距，充分利用纸张空间
    const contentWidth = pageWidth - (margin * 2)
    const contentHeight = pageHeight - (margin * 2)

    // 计算图片在PDF中的尺寸 - 优化宽度适配
    const imgWidth = canvas.width
    const imgHeight = canvas.height
    
    // 优先按宽度适配，充分利用纸张宽度
    const widthRatio = contentWidth / (imgWidth * 0.264583)
    const heightRatio = contentHeight / (imgHeight * 0.264583)
    
    // 使用宽度比例，除非高度超出太多
    let ratio = widthRatio
    const projectedHeight = imgHeight * 0.264583 * widthRatio
    
    // 如果按宽度缩放后高度过高，则适当调整
    if (projectedHeight > contentHeight * 3) { // 允许3页的高度
      ratio = heightRatio
    }
    
    const pdfImgWidth = Math.min(imgWidth * 0.264583 * ratio, contentWidth)
    const pdfImgHeight = imgHeight * 0.264583 * ratio

    // 如果内容高度超过一页，需要分页
    if (pdfImgHeight > contentHeight) {
      // 计算需要多少页
      const totalPages = Math.ceil(pdfImgHeight / contentHeight)
      
      for (let page = 0; page < totalPages; page++) {
        if (page > 0) {
          pdf.addPage()
        }
        
        // 计算当前页要显示的部分
        const sourceY = (imgHeight / totalPages) * page
        const sourceHeight = imgHeight / totalPages
        
        // 创建当前页的canvas
        const pageCanvas = document.createElement('canvas')
        pageCanvas.width = imgWidth
        pageCanvas.height = sourceHeight
        const pageCtx = pageCanvas.getContext('2d')
        
        pageCtx.drawImage(canvas, 0, sourceY, imgWidth, sourceHeight, 0, 0, imgWidth, sourceHeight)
        
        const pageImgData = pageCanvas.toDataURL('image/jpeg', 0.8)
        const pageImgHeight = sourceHeight * 0.264583 * ratio
        
                 pdf.addImage(pageImgData, 'JPEG', margin, margin, pdfImgWidth, pageImgHeight)
      }
    } else {
      // 单页内容 - 居中显示，如果宽度已经充满则左对齐
      const x = pdfImgWidth >= contentWidth ? margin : margin + (contentWidth - pdfImgWidth) / 2
      const y = margin
      pdf.addImage(imgData, 'JPEG', x, y, pdfImgWidth, pdfImgHeight)
    }

    // 生成文件名
    const userName = userStore.userName || '代表'
    const year = selectedYear.value
    const timestamp = new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')
    const filename = `${userName}-${year}年度履职AI分析报告-${timestamp}.pdf`

    // 下载PDF
    pdf.save(filename)
    ElMessage.success('PDF导出成功！')
    
  } catch (error) {
    console.error('PDF导出失败:', error)
    ElMessage.error('PDF导出失败，请重试')
  } finally {
    exportingPDF.value = false
  }
}

// 初始化
onMounted(async () => {
  console.log('年度成就页面加载完成')
  // 加载年度状态信息
  await loadYearStatuses()
})
</script>

<style scoped>
.annual-achievements {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 100%);
}

/* 页面头部 */
.page-header {
  margin-bottom: 30px;
  background: linear-gradient(135deg, #c62d2d 0%, #8b1e1e 100%);
  color: white;
  border: none;
  border-radius: 16px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30px;
}

.header-left {
  flex: 1;
}

.main-title {
  font-size: 32px;
  font-weight: bold;
  margin: 0 0 12px 0;
  color: white;
}

.subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  color: white;
}

.header-decoration {
  flex-shrink: 0;
  opacity: 0.6;
}

/* 年度网格容器 */
.year-grid-container {
  border-radius: 16px;
  border: 2px solid #fee2e2;
  background: white;
}

.year-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  padding: 30px;
}

/* 年度卡片 */
.year-card {
  position: relative;
  aspect-ratio: 1;
  border: 3px solid #fee2e2;
  border-radius: 20px;
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.year-card:hover {
  border-color: #c62d2d;
  transform: translateY(-8px);
  box-shadow: 0 12px 32px rgba(198, 45, 45, 0.2);
}

.year-card:hover .year-overlay {
  opacity: 1;
}

.year-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  text-align: center;
}

.year-number {
  font-size: 48px;
  font-weight: bold;
  color: #c62d2d;
  line-height: 1;
  margin-bottom: 8px;
}

.year-label {
  font-size: 18px;
  color: #666;
  font-weight: 500;
  margin-bottom: 15px;
}

.year-status {
  margin-top: 10px;
}

.year-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(198, 45, 45, 0.9) 0%, rgba(139, 30, 30, 0.9) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: bold;
  opacity: 0;
  transition: opacity 0.3s ease;
  gap: 10px;
}

/* 报告头部 */
.report-header {
  margin-bottom: 30px;
  background: linear-gradient(135deg, #c62d2d 0%, #8b1e1e 100%);
  color: white;
  border: none;
  border-radius: 16px;
}

.report-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
}

.back-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.current-year-info h2 {
  font-size: 24px;
  margin: 0 0 5px 0;
  color: white;
  font-weight: bold;
}

.current-year-info p {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
  color: white;
}

.header-actions {
  flex-shrink: 0;
}

/* 成果展示区域 */
.achievements-display {
  margin-bottom: 30px;
}

/* 报告操作区域 */
.report-actions {
  margin-top: 40px;
  border-radius: 16px;
  border: 2px solid #fee2e2;
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
  padding: 20px;
}

.action-buttons .el-button {
  padding: 14px 28px;
  font-size: 16px;
  border-radius: 12px;
  font-weight: 500;
}

/* 暂无分析 */
.no-analysis {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.no-analysis-card {
  width: 500px;
  text-align: center;
  border-radius: 16px;
  border: 2px solid #fee2e2;
  background: white;
}

.no-analysis-content {
  padding: 60px 40px;
}

.no-analysis-content h3 {
  color: #c62d2d;
  font-size: 24px;
  margin: 20px 0 15px 0;
  font-weight: bold;
}

.no-analysis-content p {
  color: #666;
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-card {
  width: 450px;
  text-align: center;
  border-radius: 16px;
  border: 2px solid #fee2e2;
  background: white;
}

.loading-content {
  padding: 50px 30px;
}

.loading-icon {
  animation: spin 2s linear infinite;
  margin-bottom: 25px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-content h3 {
  color: #c62d2d;
  margin-bottom: 15px;
  font-size: 22px;
  font-weight: bold;
}

.loading-content p {
  color: #666;
  margin-bottom: 0;
  font-size: 16px;
}

/* 重写Element Plus按钮样式以符合中国红主题 */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #c62d2d 0%, #8b1e1e 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(198, 45, 45, 0.3);
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #b02626 0%, #7a1a1a 100%);
  box-shadow: 0 6px 16px rgba(198, 45, 45, 0.4);
}

:deep(.el-button--default) {
  background: white;
  border: 2px solid #c62d2d;
  color: #c62d2d;
  border-radius: 12px;
}

:deep(.el-button--default:hover) {
  background: #c62d2d;
  color: white;
}

:deep(.el-tag--success) {
  background: #f0f9ff;
  color: #c62d2d;
  border: 1px solid #c62d2d;
  border-radius: 8px;
}

:deep(.el-tag--warning) {
  background: #fff7ed;
  color: #ea580c;
  border: 1px solid #ea580c;
  border-radius: 8px;
}

:deep(.el-progress-bar__outer) {
  background-color: #fee2e2;
  border-radius: 10px;
}

:deep(.el-progress-bar__inner) {
  background: linear-gradient(90deg, #c62d2d 0%, #e85555 100%);
  border-radius: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .annual-achievements {
    padding: 15px;
  }
  
  .year-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    padding: 20px;
  }
  
  .year-number {
    font-size: 36px;
  }
  
  .year-label {
    font-size: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .report-header-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .back-section {
    flex-direction: column;
    text-align: center;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .main-title {
    font-size: 24px;
  }
}
</style> 