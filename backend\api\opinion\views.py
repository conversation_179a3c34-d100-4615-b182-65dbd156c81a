"""
意见建议管理API视图

包含以下API端点：
1. OpinionSuggestionListView - 意见建议列表（代表查看自己的，工作人员查看所有）
2. OpinionSuggestionDetailView - 意见建议详情
3. OpinionSuggestionCreateView - 创建意见建议（仅代表）
4. OpinionSuggestionUpdateView - 更新意见建议（仅代表，限草稿/驳回状态）
5. OpinionSuggestionDeleteView - 删除意见建议
6. OpinionSuggestionSubmitView - 提交意见建议（代表提交到站点）
7. OpinionReviewCreateView - 创建审核记录（仅工作人员）
8. AIGenerateView - AI辅助生成建议（仅代表）
9. OpinionStatisticsView - 意见建议统计
10. StaffWorkbenchView - 工作人员工作台
11. RepresentativeWorkbenchView - 代表工作台

设计原则：
- 严格的权限控制，确保数据安全
- 详尽的日志记录，便于审计
- 完整的数据验证，防止恶意输入
- 清晰的错误处理，提供友好的错误信息
"""

import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Count, Q
from django.db import transaction
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from rest_framework import status, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .models import OpinionSuggestion, OpinionReview
from .serializers import (
    OpinionSuggestionSerializer,
    OpinionSuggestionListSerializer,
    OpinionSuggestionDetailSerializer,
    OpinionSuggestionCreateSerializer,
    OpinionSuggestionUpdateSerializer,
    OpinionReviewCreateSerializer,
    AIGenerateRequestSerializer,
    AIGenerateResponseSerializer,
    OpinionStatisticsSerializer
)
from .permissions import (
    IsRepresentativeOwnerOrStaff,
    IsRepresentativeOwner,
    CanReviewOpinion,
    CanViewOpinionList,
    CanCreateOpinion,
    CanModifyOpinion,
    CanDeleteOpinion,
    CanSubmitOpinion,
    CanUseAI,
    CanViewStatistics
)

# 设置日志
logger = logging.getLogger(__name__)


class OpinionSuggestionListView(APIView):
    """
    意见建议列表API
    
    对应用户故事：
    - US-IM-004: 代表查看自己提交的意见建议列表
    - US-IM-003: 工作人员查看待审核的意见建议列表
    
    安全控制：
    - 代表只能查看自己提交的意见建议
    - 工作人员可以查看所有意见建议
    """
    
    permission_classes = [CanViewOpinionList]
    
    @swagger_auto_schema(
        operation_summary='获取意见建议列表',
        operation_description='根据用户角色返回相应的意见建议列表',
        manual_parameters=[
            openapi.Parameter(
                'status', openapi.IN_QUERY,
                description='按状态筛选（draft/submitted/approved/rejected/transferred/in_progress/completed）',
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'category', openapi.IN_QUERY,
                description='按分类筛选',
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'search', openapi.IN_QUERY,
                description='搜索关键词（标题、反映人姓名）',
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'page', openapi.IN_QUERY,
                description='页码（默认1）',
                type=openapi.TYPE_INTEGER
            ),
            openapi.Parameter(
                'page_size', openapi.IN_QUERY,
                description='每页大小（默认20，最大100）',
                type=openapi.TYPE_INTEGER
            ),
            openapi.Parameter(
                'ordering', openapi.IN_QUERY,
                description='排序字段（created_at/-created_at/updated_at/-updated_at/last_updated_time/-last_updated_time）',
                type=openapi.TYPE_STRING
            ),
        ],
        responses={
            200: openapi.Response(
                description='获取成功',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'count': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'total_pages': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'current_page': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'results': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                                )
                            }
                        )
                    }
                )
            ),
            403: openapi.Response(description='权限不足'),
        },
        tags=['意见建议管理']
    )
    def get(self, request):
        """获取意见建议列表"""
        try:
            # 获取基础查询集
            queryset = OpinionSuggestion.objects.select_related(
                'representative__user'
            ).prefetch_related('reviews__reviewer')
            
            # 根据用户角色过滤数据
            if request.user.role == 'staff':
                # 工作人员可以查看所有意见建议
                pass
            elif request.user.role == 'representative' and hasattr(request.user, 'representative'):
                # 代表只能查看自己提交的意见建议
                queryset = queryset.filter(representative=request.user.representative)
            else:
                # 其他情况返回空结果
                queryset = queryset.none()
            
            # 应用过滤器
            status_filter = request.query_params.get('status')
            if status_filter:
                # 根据状态筛选逻辑，与模型中current_status属性保持一致
                # 由于创建意见建议时会自动创建审核记录，所以所有状态都通过审核记录筛选
                from django.db.models import OuterRef, Subquery, Exists
                
                # 获取最新审核记录（按ID倒序，与模型current_status保持一致）
                latest_review = OpinionReview.objects.filter(
                    opinion=OuterRef('pk')
                ).order_by('-id')  # 使用ID排序，不是action_time
                
                # 筛选具有审核记录且最新状态匹配的意见建议
                queryset = queryset.filter(
                    Exists(latest_review),  # 确保有审核记录
                ).annotate(
                    current_status_db=Subquery(latest_review.values('status')[:1])
                ).filter(current_status_db=status_filter)
            
            category_filter = request.query_params.get('category')
            if category_filter:
                queryset = queryset.filter(category=category_filter)
            
            search = request.query_params.get('search')
            if search:
                queryset = queryset.filter(
                    Q(title__icontains=search) |
                    Q(reporter_name__icontains=search) |
                    Q(original_content__icontains=search) |
                    Q(representative__name__icontains=search)  # 添加代表姓名搜索
                )
            
            # 排序
            ordering = request.query_params.get('ordering', '-created_at')
            allowed_orderings = [
                'created_at', '-created_at', 'updated_at', '-updated_at',
                'last_updated_time', '-last_updated_time'
            ]
            
            if ordering in allowed_orderings:
                if ordering in ['last_updated_time', '-last_updated_time']:
                    # 对于last_updated_time排序，需要使用子查询获取最新审核记录的action_time
                    from django.db.models import OuterRef, Subquery
                    
                    latest_action_time = OpinionReview.objects.filter(
                        opinion=OuterRef('pk')
                    ).order_by('-id').values('action_time')[:1]
                    
                    # 添加子查询注解
                    queryset = queryset.annotate(
                        latest_action_time=Subquery(latest_action_time)
                    )
                    
                    # 使用注解字段排序，如果没有审核记录则使用创建时间
                    if ordering == 'last_updated_time':
                        queryset = queryset.order_by('latest_action_time', 'created_at')
                    else:  # '-last_updated_time'
                        queryset = queryset.order_by('-latest_action_time', '-created_at')
                else:
                    queryset = queryset.order_by(ordering)
            
            # 分页
            page = int(request.query_params.get('page', 1))
            page_size = min(int(request.query_params.get('page_size', 20)), 100)
            
            paginator = Paginator(queryset, page_size)
            
            try:
                page_obj = paginator.page(page)
            except PageNotAnInteger:
                page_obj = paginator.page(1)
            except EmptyPage:
                page_obj = paginator.page(paginator.num_pages)
            
            # 序列化数据（使用列表序列化器，不包含头像）
            serializer = OpinionSuggestionListSerializer(page_obj.object_list, many=True)
            
            # 记录访问日志
            logger.info(f"用户 {request.user.username} 查看意见建议列表，返回 {len(page_obj.object_list)} 条记录")
            
            return Response({
                'success': True,
                'message': '获取意见建议列表成功',
                'data': {
                    'count': paginator.count,
                    'total_pages': paginator.num_pages,
                    'current_page': page_obj.number,
                    'results': serializer.data
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取意见建议列表失败: {str(e)}")
            return Response({
                'success': False,
                'message': '获取意见建议列表失败',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class OpinionSuggestionDetailView(APIView):
    """
    意见建议详情API
    
    对应用户故事：
    - US-IM-004: 查看意见建议的详细信息和处理历史
    
    安全控制：
    - 代表只能查看自己提交的意见建议详情
    - 工作人员可以查看所有意见建议详情
    """
    
    permission_classes = [IsRepresentativeOwnerOrStaff]
    
    def get_object(self, opinion_id):
        """获取意见建议对象"""
        try:
            return OpinionSuggestion.objects.select_related(
                'representative__user'
            ).prefetch_related(
                'reviews__reviewer__user'
            ).get(id=opinion_id)
        except OpinionSuggestion.DoesNotExist:
            return None
    
    @swagger_auto_schema(
        operation_summary='获取意见建议详情',
        operation_description='获取指定意见建议的详细信息，包括审核历史',
        responses={
            200: openapi.Response(
                description='获取成功',
                schema=OpinionSuggestionDetailSerializer
            ),
            404: openapi.Response(description='意见建议不存在'),
            403: openapi.Response(description='权限不足'),
        },
        tags=['意见建议管理']
    )
    def get(self, request, opinion_id):
        """获取意见建议详情"""
        opinion = self.get_object(opinion_id)
        if not opinion:
            return Response({
                'success': False,
                'message': '意见建议不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 检查权限
        self.check_object_permissions(request, opinion)
        
        try:
            serializer = OpinionSuggestionDetailSerializer(opinion)
            
            # 记录访问日志
            logger.info(f"用户 {request.user.username} 查看意见建议详情，ID: {opinion_id}")
            
            return Response({
                'success': True,
                'message': '获取意见建议详情成功',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取意见建议详情失败: {str(e)}")
            return Response({
                'success': False,
                'message': '获取意见建议详情失败',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class OpinionSuggestionCreateView(APIView):
    """
    创建意见建议API
    
    对应用户故事：
    - US-IM-001: 代表录入群众意见建议
    - US-IM-002: 使用AI辅助生成规范化建议
    
    安全控制：
    - 只有人大代表可以创建意见建议
    - 自动关联到当前登录的代表
    """
    
    permission_classes = [CanCreateOpinion]
    
    @swagger_auto_schema(
        operation_summary='创建意见建议',
        operation_description='代表创建新的意见建议，支持AI辅助生成',
        request_body=OpinionSuggestionCreateSerializer,
        responses={
            201: openapi.Response(
                description='创建成功',
                schema=OpinionSuggestionDetailSerializer
            ),
            400: openapi.Response(description='请求参数错误'),
            403: openapi.Response(description='权限不足，只有代表可以创建'),
        },
        tags=['意见建议管理']
    )
    def post(self, request):
        """创建意见建议"""
        serializer = OpinionSuggestionCreateSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            try:
                with transaction.atomic():
                    # 创建意见建议
                    opinion = serializer.save()
                    
                    # 记录创建日志
                    logger.info(f"代表 {request.user.username} 创建意见建议，ID: {opinion.id}，标题: {opinion.title}")
                    
                    # 返回详情数据
                    detail_serializer = OpinionSuggestionDetailSerializer(opinion)
                    
                    return Response({
                        'success': True,
                        'message': '创建意见建议成功',
                        'data': detail_serializer.data
                    }, status=status.HTTP_201_CREATED)
                    
            except Exception as e:
                logger.error(f"创建意见建议失败: {str(e)}")
                return Response({
                    'success': False,
                    'message': '创建意见建议失败',
                    'error': str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'success': False,
            'message': '数据验证失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class OpinionSuggestionUpdateView(APIView):
    """
    更新意见建议API
    
    对应用户故事：
    - US-IM-002: 代表修改自己的意见建议（限草稿或被驳回状态）
    
    安全控制：
    - 只有提交代表可以修改自己的意见建议
    - 只有草稿或被驳回状态的意见建议才能修改
    """
    
    permission_classes = [CanModifyOpinion]
    
    def get_object(self, opinion_id):
        """获取意见建议对象"""
        try:
            return OpinionSuggestion.objects.get(id=opinion_id)
        except OpinionSuggestion.DoesNotExist:
            return None
    
    @swagger_auto_schema(
        operation_summary='更新意见建议',
        operation_description='代表更新自己的意见建议（仅限草稿或被驳回状态）',
        request_body=OpinionSuggestionUpdateSerializer,
        responses={
            200: openapi.Response(
                description='更新成功',
                schema=OpinionSuggestionDetailSerializer
            ),
            400: openapi.Response(description='请求参数错误或状态不允许修改'),
            404: openapi.Response(description='意见建议不存在'),
            403: openapi.Response(description='权限不足'),
        },
        tags=['意见建议管理']
    )
    def put(self, request, opinion_id):
        """更新意见建议"""
        opinion = self.get_object(opinion_id)
        if not opinion:
            return Response({
                'success': False,
                'message': '意见建议不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 检查权限
        self.check_object_permissions(request, opinion)
        
        serializer = OpinionSuggestionUpdateSerializer(
            opinion,
            data=request.data,
            partial=True,
            context={'request': request}
        )
        
        if serializer.is_valid():
            try:
                with transaction.atomic():
                    # 更新意见建议
                    updated_opinion = serializer.save()
                    
                    # 记录更新日志
                    logger.info(f"代表 {request.user.username} 更新意见建议，ID: {opinion_id}")
                    
                    # 返回详情数据
                    detail_serializer = OpinionSuggestionDetailSerializer(updated_opinion)
                    
                    return Response({
                        'success': True,
                        'message': '更新意见建议成功',
                        'data': detail_serializer.data
                    }, status=status.HTTP_200_OK)
                    
            except Exception as e:
                logger.error(f"更新意见建议失败: {str(e)}")
                return Response({
                    'success': False,
                    'message': '更新意见建议失败',
                    'error': str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'success': False,
            'message': '数据验证失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class OpinionSuggestionDeleteView(APIView):
    """
    删除意见建议API
    
    安全控制：
    - 代表只能删除自己的草稿状态意见建议
    - 工作人员可以删除任何意见建议
    """
    
    permission_classes = [CanDeleteOpinion]
    
    def get_object(self, opinion_id):
        """获取意见建议对象"""
        try:
            return OpinionSuggestion.objects.get(id=opinion_id)
        except OpinionSuggestion.DoesNotExist:
            return None
    
    @swagger_auto_schema(
        operation_summary='删除意见建议',
        operation_description='删除指定的意见建议（代表仅限草稿状态，工作人员无限制）',
        responses={
            200: openapi.Response(description='删除成功'),
            404: openapi.Response(description='意见建议不存在'),
            403: openapi.Response(description='权限不足'),
        },
        tags=['意见建议管理']
    )
    def delete(self, request, opinion_id):
        """删除意见建议"""
        opinion = self.get_object(opinion_id)
        if not opinion:
            return Response({
                'success': False,
                'message': '意见建议不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 检查权限
        self.check_object_permissions(request, opinion)
        
        try:
            with transaction.atomic():
                opinion_title = opinion.title
                opinion.delete()
                
                # 记录删除日志
                logger.info(f"用户 {request.user.username} 删除意见建议，ID: {opinion_id}，标题: {opinion_title}")
                
                return Response({
                    'success': True,
                    'message': '删除意见建议成功'
                }, status=status.HTTP_200_OK)
                
        except Exception as e:
            logger.error(f"删除意见建议失败: {str(e)}")
            return Response({
                'success': False,
                'message': '删除意见建议失败',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class OpinionSuggestionSubmitView(APIView):
    """
    提交意见建议API
    
    对应用户故事：
    - US-IM-002: 代表审核确认后提交意见建议到站点
    
    安全控制：
    - 只有提交代表可以提交自己的意见建议
    - 只有草稿或被驳回状态的意见建议才能提交
    """
    
    permission_classes = [CanSubmitOpinion]
    
    def get_object(self, opinion_id):
        """获取意见建议对象"""
        try:
            return OpinionSuggestion.objects.get(id=opinion_id)
        except OpinionSuggestion.DoesNotExist:
            return None
    
    @swagger_auto_schema(
        operation_summary='提交意见建议',
        operation_description='代表提交意见建议到站点进行审核',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={},
            description="空请求体（意见建议ID从URL路径获取）"
        ),
        responses={
            200: openapi.Response(
                description='提交成功',
                schema=OpinionSuggestionDetailSerializer
            ),
            400: openapi.Response(description='状态不允许提交'),
            404: openapi.Response(description='意见建议不存在'),
            403: openapi.Response(description='权限不足'),
        },
        tags=['意见建议管理']
    )
    def post(self, request, opinion_id):
        """提交意见建议"""
        opinion = self.get_object(opinion_id)
        if not opinion:
            return Response({
                'success': False,
                'message': '意见建议不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 检查权限
        self.check_object_permissions(request, opinion)
        
        try:
            with transaction.atomic():
                # 创建提交审核记录
                OpinionReview.objects.create(
                    opinion=opinion,
                    reviewer=None,  # 代表提交，审核人为空
                    action='submit',
                    status='submitted',
                    review_comment='代表提交意见建议到站点，等待工作人员审核',
                    action_time=timezone.now()
                )
                
                # 记录提交日志
                logger.info(f"代表 {request.user.username} 提交意见建议，ID: {opinion_id}")
                
                # 返回更新后的详情
                detail_serializer = OpinionSuggestionDetailSerializer(opinion)
                
                return Response({
                    'success': True,
                    'message': '提交意见建议成功，等待工作人员审核',
                    'data': detail_serializer.data
                }, status=status.HTTP_200_OK)
                
        except Exception as e:
            logger.error(f"提交意见建议失败: {str(e)}")
            return Response({
                'success': False,
                'message': '提交意见建议失败',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class OpinionReviewCreateView(APIView):
    """
    创建审核记录API
    
    对应用户故事：
    - US-IM-003: 工作人员审核代表提交的意见建议
    - US-IM-004: 工作人员更新意见建议处理进展
    - US-IM-005: 工作人员标记意见建议办结
    
    安全控制：
    - 只有工作人员可以创建审核记录
    - 自动关联到当前登录的工作人员
    """
    
    permission_classes = [CanReviewOpinion]
    
    def get_object(self, opinion_id):
        """获取意见建议对象"""
        try:
            return OpinionSuggestion.objects.get(id=opinion_id)
        except OpinionSuggestion.DoesNotExist:
            return None
    
    @swagger_auto_schema(
        operation_summary='创建审核记录',
        operation_description='工作人员对意见建议执行审核操作',
        request_body=OpinionReviewCreateSerializer,
        responses={
            201: openapi.Response(
                description='审核成功',
                schema=OpinionSuggestionDetailSerializer
            ),
            400: openapi.Response(description='请求参数错误'),
            404: openapi.Response(description='意见建议不存在'),
            403: openapi.Response(description='权限不足，只有工作人员可以审核'),
        },
        tags=['意见建议管理']
    )
    def post(self, request, opinion_id):
        """创建审核记录"""
        opinion = self.get_object(opinion_id)
        if not opinion:
            return Response({
                'success': False,
                'message': '意见建议不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        serializer = OpinionReviewCreateSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            try:
                with transaction.atomic():
                    # 创建审核记录
                    review = serializer.save(opinion=opinion)
                    
                    # 记录审核日志
                    logger.info(f"工作人员 {request.user.username} 审核意见建议，ID: {opinion_id}，操作: {review.action}")
                    
                    # 返回更新后的意见建议详情
                    detail_serializer = OpinionSuggestionDetailSerializer(opinion)
                    
                    return Response({
                        'success': True,
                        'message': f'审核操作成功：{review.get_action_display()}',
                        'data': detail_serializer.data
                    }, status=status.HTTP_201_CREATED)
                    
            except Exception as e:
                logger.error(f"创建审核记录失败: {str(e)}")
                return Response({
                    'success': False,
                    'message': '审核操作失败',
                    'error': str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'success': False,
            'message': '数据验证失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class AIGenerateView(APIView):
    """
    AI辅助生成建议API
    
    对应用户故事：
    - US-IM-002: AI辅助生成高质量意见建议
    
    安全控制：
    - 只有人大代表可以使用AI功能
    """
    
    permission_classes = [CanUseAI]
    
    @swagger_auto_schema(
        operation_summary='AI辅助生成建议',
        operation_description='使用AI根据原始意见内容生成规范化的建议',
        request_body=AIGenerateRequestSerializer,
        responses={
            200: openapi.Response(
                description='生成成功',
                schema=AIGenerateResponseSerializer
            ),
            400: openapi.Response(description='请求参数错误'),
            403: openapi.Response(description='权限不足，只有代表可以使用AI功能'),
            503: openapi.Response(description='AI服务暂时不可用'),
        },
        tags=['AI辅助功能']
    )
    def post(self, request):
        """AI辅助生成建议"""
        serializer = AIGenerateRequestSerializer(data=request.data)
        
        if serializer.is_valid():
            try:
                original_content = serializer.validated_data['original_content']
                category = serializer.validated_data['category']
                context = serializer.validated_data.get('context', '')
                
                # 记录AI使用日志
                logger.info(f"代表 {request.user.username} 使用AI生成建议，分类: {category}")
                
                # TODO: 集成实际的AI服务
                # 这里是模拟的AI生成逻辑，实际应该调用AI服务
                generated_suggestion = self._simulate_ai_generation(
                    original_content, category, context
                )
                
                response_serializer = AIGenerateResponseSerializer(data=generated_suggestion)
                if response_serializer.is_valid():
                    return Response({
                        'success': True,
                        'message': 'AI生成建议成功',
                        'data': response_serializer.validated_data
                    }, status=status.HTTP_200_OK)
                
            except Exception as e:
                logger.error(f"AI生成建议失败: {str(e)}")
                return Response({
                    'success': False,
                    'message': 'AI服务暂时不可用',
                    'error': str(e)
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        
        return Response({
            'success': False,
            'message': '数据验证失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    def _simulate_ai_generation(self, original_content, category, context):
        """
        模拟AI生成逻辑
        实际应用中应该调用真实的AI服务
        """
        category_map = dict(OpinionSuggestion.CATEGORY_CHOICES)
        category_name = category_map.get(category, category)
        
        return {
            'generated_suggestion': f"关于{category_name}的建议：{original_content[:100]}...[AI优化后的规范化建议内容]",
            'key_points': [
                "问题核心要点1",
                "问题核心要点2",
                "问题核心要点3"
            ],
            'suggested_actions': [
                "建议采取的措施1",
                "建议采取的措施2",
                "建议采取的措施3"
            ],
            'confidence_score': 0.85
        }


class OpinionStatisticsView(APIView):
    """
    意见建议统计API
    
    对应用户故事：
    - 代表查看自己的意见建议统计
    - 工作人员查看全局统计数据
    
    安全控制：
    - 代表只能查看自己的统计数据
    - 工作人员可以查看全局统计数据
    """
    
    permission_classes = [CanViewStatistics]
    
    @swagger_auto_schema(
        operation_summary='获取意见建议统计',
        operation_description='根据用户角色返回相应的统计数据',
        responses={
            200: openapi.Response(
                description='获取成功',
                schema=OpinionStatisticsSerializer
            ),
        },
        tags=['统计报表']
    )
    def get(self, request):
        """获取意见建议统计"""
        try:
            if request.user.role == 'representative' and hasattr(request.user, 'representative'):
                # 代表统计
                return self._get_representative_statistics(request.user.representative)
            elif request.user.role == 'staff':
                # 工作人员统计
                return self._get_staff_statistics()
            else:
                return Response({
                    'success': False,
                    'message': '权限不足'
                }, status=status.HTTP_403_FORBIDDEN)
                
        except Exception as e:
            logger.error(f"获取统计数据失败: {str(e)}")
            return Response({
                'success': False,
                'message': '获取统计数据失败',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _get_representative_statistics(self, representative):
        """获取代表统计数据"""
        # 基础查询集
        opinions = OpinionSuggestion.objects.filter(representative=representative)
        
        # 当前月份
        current_month = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # 统计数据
        total_opinions = opinions.count()
        monthly_opinions = opinions.filter(created_at__gte=current_month).count()
        
        # 状态统计（通过子查询）
        from django.db.models import OuterRef, Subquery, Case, When, Value, CharField
        
        latest_status = OpinionReview.objects.filter(
            opinion=OuterRef('pk')
        ).order_by('-id').values('status')[:1]
        
        opinions_with_status = opinions.annotate(
            current_status_db=Subquery(latest_status)
        )
        
        # 未完成意见：除了已办结外的所有意见
        pending_opinions = opinions_with_status.exclude(
            current_status_db='completed'
        ).count()
        
        # 已完成意见：已办结状态的意见
        completed_opinions = opinions_with_status.filter(
            current_status_db='completed'
        ).count()
        
        # 获取最近5条更新的意见建议用于工作台显示
        from django.db.models import OuterRef, Subquery
        
        # 获取最新审核时间
        latest_review_time = OpinionReview.objects.filter(
            opinion=OuterRef('pk')
        ).order_by('-id').values('action_time')[:1]
        
        recent_opinions = opinions.annotate(
            last_review_time=Subquery(latest_review_time)
        ).order_by('-last_review_time', '-created_at')[:8]
        
        # 过滤并格式化最近意见数据
        recent_opinions_data = []
        for opinion in recent_opinions:
            # 获取最新状态
            latest_review = OpinionReview.objects.filter(
                opinion=opinion
            ).order_by('-id').first()
            
            current_status = latest_review.status if latest_review else 'draft'
            
            # 跳过草稿和待审核状态
            if current_status not in ['draft', 'submitted']:
                recent_opinions_data.append({
                    'id': opinion.id,
                    'title': opinion.title,
                    'current_status': current_status,
                    'last_updated_time': latest_review.action_time.isoformat() if latest_review else opinion.created_at.isoformat(),
                    'category': opinion.category
                })
                
                # 只要5条
                if len(recent_opinions_data) >= 5:
                    break
        
        statistics = {
            'total_opinions': total_opinions,
            'pending_opinions': pending_opinions,
            'completed_opinions': completed_opinions,
            'monthly_opinions': monthly_opinions,
            'recent_opinions': recent_opinions_data,  # 添加最近意见
        }
        
        serializer = OpinionStatisticsSerializer(data=statistics)
        if serializer.is_valid():
            return Response({
                'success': True,
                'message': '获取代表统计数据成功',
                'data': serializer.validated_data
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': '统计数据序列化失败'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _get_staff_statistics(self):
        """获取工作人员统计数据"""
        # 当前月份
        current_month = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # 基础查询集 - 所有意见建议
        opinions = OpinionSuggestion.objects.all()
        
        # 使用子查询获取每个意见建议的最新审核状态
        from django.db.models import OuterRef, Subquery
        
        latest_status = OpinionReview.objects.filter(
            opinion=OuterRef('pk')
        ).order_by('-id').values('status')[:1]  # 使用-id排序确保获取最新记录
        
        opinions_with_status = opinions.annotate(
            current_status_db=Subquery(latest_status)
        )
        
        # 按最新状态统计
        submitted_count = opinions_with_status.filter(
            current_status_db='submitted'
        ).count()
        
        approved_count = opinions_with_status.filter(
            current_status_db='approved'
        ).count()
        
        # 分别统计已转交和处理中
        transferred_count = opinions_with_status.filter(
            current_status_db='transferred'
        ).count()
        
        in_progress_count = opinions_with_status.filter(
            current_status_db='in_progress'
        ).count()
        
        completed_count = opinions_with_status.filter(
            current_status_db='completed'
        ).count()
        
        # 本月办结数量（基于审核记录时间）
        monthly_completed_count = OpinionReview.objects.filter(
            status='completed',
            action_time__gte=current_month
        ).count()
        
        # 总体统计
        total_opinions = opinions.count()
        monthly_opinions = opinions.filter(created_at__gte=current_month).count()
        
        # 状态显示名称映射
        status_display_map = {
            'submitted': '已提交',
            'approved': '审核通过',
            'transferred': '已转交',
            'in_progress': '处理中',
            'completed': '已办结'
        }
        
        # 获取最近5条待审核意见建议用于工作台显示
        pending_reviews = opinions_with_status.filter(
            current_status_db='submitted'
        ).order_by('-created_at')[:5]
        
        pending_reviews_data = []
        for opinion in pending_reviews:
            pending_reviews_data.append({
                'id': opinion.id,
                'title': opinion.title,
                'representative_name': opinion.representative.name if opinion.representative else '未知代表',
                'category': opinion.category,
                'submitted_time': opinion.created_at.isoformat()
            })
        
        # 获取最近5条更新的意见建议用于工作台显示（排除待审核）
        latest_review_time = OpinionReview.objects.filter(
            opinion=OuterRef('pk')
        ).order_by('-id').values('action_time')[:1]
        
        recent_opinions = opinions.annotate(
            last_review_time=Subquery(latest_review_time)
        ).order_by('-last_review_time', '-created_at')[:8]
        
        recent_opinions_data = []
        for opinion in recent_opinions:
            # 获取最新状态
            latest_review = OpinionReview.objects.filter(
                opinion=opinion
            ).order_by('-id').first()
            
            current_status = latest_review.status if latest_review else 'draft'
            
            # 跳过草稿和待审核状态
            if current_status not in ['draft', 'submitted']:
                recent_opinions_data.append({
                    'id': opinion.id,
                    'title': opinion.title,
                    'current_status': current_status,
                    'last_updated_time': latest_review.action_time.isoformat() if latest_review else opinion.created_at.isoformat(),
                    'category': opinion.category,
                    'representative_name': opinion.representative.name if opinion.representative else '未知代表'
                })
                
                # 只要5条
                if len(recent_opinions_data) >= 5:
                    break

        statistics = {
            'total_opinions': total_opinions,
            'pending_opinions': submitted_count,  # 待审核
            'completed_opinions': completed_count,  # 总完成数
            'monthly_opinions': monthly_opinions,
            'pending_review_count': submitted_count,  # 待审核数量
            'monthly_completed_count': monthly_completed_count,  # 本月办结数量
            'approved_count': approved_count,  # 审核通过数量  
            'transferred_count': transferred_count,  # 已转交数量
            'in_progress_count': in_progress_count,  # 处理中数量
            'pending_reviews': pending_reviews_data,  # 待审核列表
            'recent_opinions': recent_opinions_data,  # 最近更新意见
            'status_distribution': [
                {
                    'status': 'submitted', 
                    'status_display': status_display_map['submitted'],
                    'count': submitted_count
                },
                {
                    'status': 'approved', 
                    'status_display': status_display_map['approved'],
                    'count': approved_count
                },
                {
                    'status': 'transferred', 
                    'status_display': status_display_map['transferred'],
                    'count': transferred_count
                },
                {
                    'status': 'in_progress', 
                    'status_display': status_display_map['in_progress'],
                    'count': in_progress_count
                },
                {
                    'status': 'completed', 
                    'status_display': status_display_map['completed'],
                    'count': completed_count
                },
            ]
        }
        
        serializer = OpinionStatisticsSerializer(data=statistics)
        if serializer.is_valid():
            return Response({
                'success': True,
                'message': '获取工作人员统计数据成功',
                'data': serializer.validated_data
            }, status=status.HTTP_200_OK)
        
        return Response({
            'success': False,
            'message': '统计数据序列化失败'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
