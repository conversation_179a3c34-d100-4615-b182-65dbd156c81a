"""
履职管理数据初始化命令

用于：
1. 创建数据库表
2. 创建测试数据
3. 验证系统功能

使用方法：
python manage.py setup_performance
manage.py setup_performance --create-test-data --verbose
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from datetime import date, timedelta
import random

from api.users.models import Representative
from api.performance.models import PerformanceRecord, PerformanceAttachment

User = get_user_model()


class Command(BaseCommand):
    help = '初始化履职管理数据'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--create-test-data',
            action='store_true',
            help='创建测试数据'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='显示详细信息'
        )
    
    def handle(self, *args, **options):
        """处理命令"""
        self.verbose = options['verbose']
        
        # 执行数据库迁移
        self.stdout.write('正在检查数据库表结构...')
        self._check_tables()
        
        # 创建测试数据
        if options['create_test_data']:
            self.stdout.write('正在创建测试数据...')
            self._create_test_data()
        
        self.stdout.write(
            self.style.SUCCESS('履职管理数据初始化完成！')
        )
    
    def _check_tables(self):
        """检查数据库表"""
        try:
            # 检查表是否存在
            PerformanceRecord.objects.count()
            PerformanceAttachment.objects.count()
            
            if self.verbose:
                self.stdout.write('✓ 数据库表结构正常')
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'数据库表检查失败: {e}')
            )
            self.stdout.write('请先运行: python manage.py makemigrations performance')
            self.stdout.write('然后运行: python manage.py migrate')
            return
    
    def _create_test_data(self):
        """创建测试数据"""
        with transaction.atomic():
            # 获取或创建测试代表
            test_users = []
            
            # 创建测试代表用户
            for i in range(3):
                username = f'test_rep_{i+1}'
                try:
                    user = User.objects.get(username=username)
                    if self.verbose:
                        self.stdout.write(f'✓ 用户 {username} 已存在')
                except User.DoesNotExist:
                    user = User.objects.create_user(
                        username=username,
                        password='testpass123',
                        role='representative'
                    )
                    if self.verbose:
                        self.stdout.write(f'✓ 创建用户 {username}')
                
                test_users.append(user)
                
                # 创建代表信息
                try:
                    rep = Representative.objects.get(user=user)
                    if self.verbose:
                        self.stdout.write(f'✓ 代表信息 {rep.name} 已存在')
                except Representative.DoesNotExist:
                    rep = Representative.objects.create(
                        user=user,
                        level=['县级', '市级', '省级'][i],
                        name=f'测试代表{i+1}',
                        gender='male',
                        nationality='汉族',
                        birth_date='1970-01-01',
                        birthplace=f'测试市测试区{i+1}',
                        party='中国共产党',
                        current_position=f'测试单位{i+1}主任',
                        mobile_phone=f'1380013800{i}',
                        education='本科',
                        graduated_school=f'测试大学{i+1}',
                        major=f'测试专业{i+1}'
                    )
                    if self.verbose:
                        self.stdout.write(f'✓ 创建代表信息 {rep.name}')
            
            # 创建履职记录测试数据
            performance_types = [
                '会议参与', '实地调研', '走访群众', '议案提交', '建议办理',
                '监督检查', '法律宣传', '信访接待', '培训学习', '联络活动'
            ]
            
            performance_status = ['已完成', '进行中', '已暂停']
            
            locations = [
                '市人大会议室', '县政府会议室', '社区服务中心',
                '企业调研现场', '学校实地走访', '农村基层组织'
            ]
            
            contents = [
                '参加人大常委会会议，审议相关法案',
                '深入企业调研，了解经营困难和政策需求',
                '走访社区群众，收集民生意见建议',
                '提交关于教育改革的议案',
                '办理群众反映的环境污染建议',
                '监督检查食品安全执法情况',
                '开展法律知识宣传活动',
                '接待群众信访，协调解决问题',
                '参加代表履职能力培训',
                '参与代表小组联络活动'
            ]
            
            created_records = 0
            for user in test_users:
                rep = user.representative
                
                # 为每个代表创建10-20条履职记录
                num_records = random.randint(10, 20)
                
                for j in range(num_records):
                    # 随机日期（最近6个月）
                    days_ago = random.randint(1, 180)
                    perf_date = date.today() - timedelta(days=days_ago)
                    
                    record = PerformanceRecord.objects.create(
                        representative=rep,
                        performance_date=perf_date,
                        performance_type=random.choice(performance_types),
                        performance_content=random.choice(contents),
                        detailed_description=f'详细描述：{random.choice(contents)} 具体执行情况和效果分析。',
                        activity_location=random.choice(locations),
                        performance_status=random.choice(performance_status)
                    )
                    
                    created_records += 1
                    
                    if self.verbose and created_records % 10 == 0:
                        self.stdout.write(f'✓ 已创建 {created_records} 条履职记录')
            
            self.stdout.write(
                self.style.SUCCESS(f'✓ 成功创建 {created_records} 条履职记录')
            )
            
            # 显示统计信息
            self._show_statistics()
    
    def _show_statistics(self):
        """显示统计信息"""
        total_records = PerformanceRecord.objects.count()
        total_attachments = PerformanceAttachment.objects.count()
        total_representatives = Representative.objects.count()
        
        self.stdout.write('\n=== 数据统计 ===')
        self.stdout.write(f'代表总数: {total_representatives}')
        self.stdout.write(f'履职记录总数: {total_records}')
        self.stdout.write(f'附件总数: {total_attachments}')
        
        # 按类型统计
        type_stats = PerformanceRecord.objects.values_list('performance_type', flat=True)
        type_counts = {}
        for ptype in type_stats:
            type_counts[ptype] = type_counts.get(ptype, 0) + 1
        
        self.stdout.write('\n按履职类型统计:')
        for ptype, count in sorted(type_counts.items()):
            self.stdout.write(f'  {ptype}: {count}')
        
        # 按状态统计
        status_stats = PerformanceRecord.objects.values_list('performance_status', flat=True)
        status_counts = {}
        for status in status_stats:
            status_counts[status] = status_counts.get(status, 0) + 1
        
        self.stdout.write('\n按履职状态统计:')
        for status, count in sorted(status_counts.items()):
            self.stdout.write(f'  {status}: {count}')
        
        self.stdout.write('\n可用的API端点:')
        self.stdout.write('  GET /api/v1/performance/records/ - 获取履职记录列表')
        self.stdout.write('  POST /api/v1/performance/records/ - 创建履职记录')
        self.stdout.write('  GET /api/v1/performance/records/{id}/ - 获取履职记录详情')
        self.stdout.write('  GET /api/v1/performance/records/stats/ - 获取统计数据')
        self.stdout.write('  POST /api/v1/performance/upload/ - 上传附件')
        self.stdout.write('  GET /api/v1/performance/choices/types/ - 获取履职类型选项')
        self.stdout.write('\n认证信息:')
        self.stdout.write('  测试用户: test_rep_1, test_rep_2, test_rep_3')
        self.stdout.write('  密码: testpass123')
        self.stdout.write('  先登录获取JWT token: POST /api/v1/users/login/')
        self.stdout.write('  然后在请求头添加: Authorization: Bearer <token>') 