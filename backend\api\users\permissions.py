"""
用户管理权限类

定义系统中的权限控制规则：
1. IsOwnerOrStaff - 只有数据所有者或工作人员可以访问
2. IsRepresentative - 只有人大代表可以访问
3. IsStaff - 只有工作人员可以访问
4. IsOwner - 只有数据所有者可以访问
5. CanManageUsers - 可以管理用户账号的权限
"""

from rest_framework import permissions


class IsOwnerOrStaff(permissions.BasePermission):
    """
    只有数据所有者或工作人员可以访问
    用于用户查看/修改自己的信息，或工作人员管理其他用户
    """
    
    def has_permission(self, request, view):
        """检查用户是否已认证"""
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        """检查对象级权限"""
        # 数据所有者可以访问
        if hasattr(obj, 'user'):
            # 如果对象有user属性，检查是否为同一用户
            return obj.user == request.user or request.user.role == 'staff'
        elif hasattr(obj, 'id') and obj == request.user:
            # 如果对象就是用户本身
            return True
        elif request.user.role == 'staff':
            # 工作人员可以访问所有数据
            return True
        
        return False


class IsRepresentative(permissions.BasePermission):
    """
    只有人大代表可以访问
    用于代表专属功能
    """
    
    def has_permission(self, request, view):
        """检查用户是否为人大代表"""
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'representative'
        )


class IsStaff(permissions.BasePermission):
    """
    只有工作人员可以访问
    用于工作人员专属功能
    """
    
    def has_permission(self, request, view):
        """检查用户是否为工作人员"""
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'staff'
        )


class IsOwner(permissions.BasePermission):
    """
    只有数据所有者可以访问
    用于用户只能访问自己的数据
    """
    
    def has_permission(self, request, view):
        """检查用户是否已认证"""
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        """检查对象级权限"""
        # 检查是否为数据所有者
        if hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'id') and obj == request.user:
            return True
        
        return False


class CanManageUsers(permissions.BasePermission):
    """
    可以管理用户账号的权限
    只有工作人员可以创建、修改、禁用其他用户账号
    """
    
    def has_permission(self, request, view):
        """检查用户是否有管理用户的权限"""
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'staff'
        )
    
    def has_object_permission(self, request, view, obj):
        """检查对象级权限"""
        # 工作人员可以管理所有用户账号
        return request.user.role == 'staff'


class CanChangePassword(permissions.BasePermission):
    """
    密码修改权限
    代表只能修改自己的密码，工作人员可以重置任何人的密码
    """
    
    def has_permission(self, request, view):
        """检查用户是否已认证"""
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        """检查对象级权限"""
        # 用户可以修改自己的密码
        if obj == request.user:
            return True
        
        # 工作人员可以重置其他用户的密码
        if request.user.role == 'staff':
            return True
        
        return False


class ReadOnlyOrStaff(permissions.BasePermission):
    """
    只读权限或工作人员权限
    所有用户可以读取，只有工作人员可以修改
    """
    
    def has_permission(self, request, view):
        """检查基本权限"""
        if not (request.user and request.user.is_authenticated):
            return False
        
        # 对于安全的方法（GET, HEAD, OPTIONS），所有认证用户都可以访问
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 对于修改操作，只有工作人员可以访问
        return request.user.role == 'staff' 