"""
代表AI总结服务类

包含以下服务：
1. AISummaryService - AI总结核心服务类
2. DataCollectionService - 数据收集服务类
3. AIProviderService - AI提供商服务类

设计原则：
- 遵循现有应用的服务类风格
- 模块化设计，职责分离
- 完善的错误处理和日志记录
- 只使用真实AI服务，不提供模拟备用方案
"""

import time
import json
import requests
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from django.utils import timezone
from django.db.models import Count, Q
from django.db import transaction
from django.conf import settings

from .models import RepresentativeAISummary
from api.performance.models import PerformanceRecord
from api.opinion.models import OpinionSuggestion, OpinionReview
from api.users.models import Representative

# 设置日志
logger = logging.getLogger(__name__)

# 导入配置
from .config import get_ai_config, should_use_real_ai


class DataCollectionService:
    """数据收集服务类 - 为第三方AI API准备数据"""
    
    # 意见建议分类中英文映射
    CATEGORY_MAPPING = {
        'urban_construction': '城建环保',
        'transportation': '交通出行',
        'education': '教育文化',
        'healthcare': '医疗卫生',
        'social_security': '社会保障',
        'economic': '经济发展',
        'government_service': '政务服务',
        'other': '其他',
    }
    
    @classmethod
    def collect_representative_data(cls, representative: Representative, analysis_year: int) -> Dict[str, Any]:
        """
        收集代表数据并格式化为第三方AI API所需格式
        
        Args:
            representative: 代表对象
            analysis_year: 分析年份
            
        Returns:
            Dict: 格式化后的数据，直接传递给第三方AI
        """
        logger.info(f"开始收集代表 {representative.name} {analysis_year}年的数据")
        
        # 收集履职记录数据
        performance_data = cls._get_performance_data(representative, analysis_year)
        
        # 收集意见建议数据
        opinion_data = cls._get_opinion_data(representative, analysis_year)
        
        # 格式化为第三方AI所需的数据结构
        formatted_data = {
            '姓名': representative.name,
            '代表层级': representative.level,
            '年份': analysis_year,
            '履职数量': performance_data['total_count'],
            '意见建议提交数量': opinion_data['total_count'],
            '总活动数量': performance_data['total_count'] + opinion_data['total_count'],
            '履职类型': cls._format_performance_types(performance_data.get('type_stats', {})),
            '履职状态': cls._format_performance_status(performance_data.get('status_stats', {})),
            '履职每月统计': cls._format_monthly_stats(performance_data.get('monthly_stats', [])),
            '意见建议分类': cls._format_opinion_categories(opinion_data.get('category_stats', {})),
            '意见建议每月统计': cls._format_monthly_stats(opinion_data.get('monthly_stats', [])),

            '履职记录详情': cls._format_performance_details(performance_data.get('details', [])),
            '意见建议提交详情': cls._format_opinion_details(opinion_data.get('details', []))
        }
        
        logger.info(f"数据收集完成 - 代表: {representative.name}, 年份: {analysis_year}")
        logger.info(f"履职数量: {formatted_data['履职数量']}, 意见建议数量: {formatted_data['意见建议提交数量']}")
        
        return formatted_data
    
    @classmethod
    def _get_performance_data(cls, representative: Representative, year: int) -> Dict[str, Any]:
        """获取履职记录数据"""
        from datetime import datetime
        
        # 获取指定年份的履职记录
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31, 23, 59, 59)
        
        performance_records = PerformanceRecord.objects.filter(
            representative=representative,
            performance_date__range=[start_date, end_date]
        ).order_by('-performance_date')
        
        # 统计数据
        total_count = performance_records.count()
        
        # 类型统计
        type_stats = {}
        for record in performance_records:
            ptype = record.performance_type
            type_stats[ptype] = type_stats.get(ptype, 0) + 1
        
        # 状态统计
        status_stats = {}
        for record in performance_records:
            status = record.performance_status
            status_stats[status] = status_stats.get(status, 0) + 1
        
        # 月度统计
        monthly_stats = [{"month": i, "count": 0} for i in range(1, 13)]
        for record in performance_records:
            month = record.performance_date.month
            monthly_stats[month - 1]["count"] += 1
        
        # 详细信息
        details = []
        for record in performance_records:
            details.append({
                "date": record.performance_date.isoformat(),
                "type": record.performance_type,
                "content": record.performance_content or ""
            })
        
        return {
            'total_count': total_count,
            'type_stats': type_stats,
            'status_stats': status_stats,
            'monthly_stats': monthly_stats,
            'details': details
        }
    
    @classmethod
    def _get_opinion_data(cls, representative: Representative, year: int) -> Dict[str, Any]:
        """获取意见建议数据"""
        from datetime import datetime
        
        # 获取指定年份的意见建议
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31, 23, 59, 59)
        
        opinion_suggestions = OpinionSuggestion.objects.filter(
            representative=representative,
            created_at__range=[start_date, end_date]
        ).order_by('-created_at')
        
        # 统计数据
        total_count = opinion_suggestions.count()
        
        # 分类统计 - 转换为中文
        category_stats = {}
        for opinion in opinion_suggestions:
            category_en = opinion.category
            category_cn = cls.CATEGORY_MAPPING.get(category_en, category_en)
            category_stats[category_cn] = category_stats.get(category_cn, 0) + 1
        
        # 月度统计
        monthly_stats = [{"month": i, "count": 0} for i in range(1, 13)]
        for opinion in opinion_suggestions:
            month = opinion.created_at.month
            monthly_stats[month - 1]["count"] += 1
        
        # 详细信息 - category转换为中文
        details = []
        for opinion in opinion_suggestions:
            details.append({
                "title": opinion.title,
                "category": cls.CATEGORY_MAPPING.get(opinion.category, opinion.category),
                "created_at": opinion.created_at.isoformat()
            })
        
        return {
            'total_count': total_count,
            'category_stats': category_stats,
            'monthly_stats': monthly_stats,
            'details': details
        }
    
    @classmethod
    def _format_performance_types(cls, type_stats: Dict[str, int]) -> str:
        """格式化履职类型统计为JSON字符串"""
        import json
        # 转换为 [{"类型名": 数量}] 格式
        formatted_types = [{k: v} for k, v in type_stats.items()]
        return json.dumps(formatted_types, ensure_ascii=False)
    
    @classmethod
    def _format_performance_status(cls, status_stats: Dict[str, int]) -> str:
        """格式化履职状态统计为JSON字符串"""
        import json
        # 转换为 [{"status": "状态", "count": 数量}] 格式
        formatted_status = [{"status": k, "count": v} for k, v in status_stats.items()]
        return json.dumps(formatted_status, ensure_ascii=False)
    
    @classmethod
    def _format_monthly_stats(cls, monthly_stats: List[Dict]) -> str:
        """格式化月度统计为JSON字符串"""
        import json
        return json.dumps(monthly_stats, ensure_ascii=False)
    
    @classmethod
    def _format_opinion_categories(cls, category_stats: Dict[str, int]) -> str:
        """格式化意见建议分类统计为JSON字符串"""
        import json
        # 转换为 [{"category": "类型", "count": 数量}] 格式
        formatted_categories = [{"category": k, "count": v} for k, v in category_stats.items()]
        return json.dumps(formatted_categories, ensure_ascii=False)
    
    @classmethod
    def _format_performance_details(cls, details: List[Dict]) -> str:
        """格式化履职详情为JSON字符串"""
        import json
        return json.dumps(details, ensure_ascii=False)
    
    @classmethod
    def _format_opinion_details(cls, details: List[Dict]) -> str:
        """格式化意见建议详情为JSON字符串"""
        import json
        return json.dumps(details, ensure_ascii=False)
    



class AIProviderService:
    """AI提供商服务类"""
    
    def __init__(self):
        """初始化AI服务配置"""
        self.config = get_ai_config()
        self.base_url = self.config['base_url'].rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {self.config["api_key"]}',
            'Content-Type': 'application/json',
        }
    
    @classmethod
    def generate_ai_summary(cls, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成AI总结
        
        Args:
            source_data: 数据来源统计信息
            
        Returns:
            Dict: AI分析结果
            
        Raises:
            Exception: 当AI服务调用失败时抛出异常
        """
        logger.info(f"开始生成AI总结，代表：{source_data.get('姓名', 'unknown')}")
        
        # 检查是否应该使用真实AI服务
        if should_use_real_ai():
            service = cls()
            return service._call_dify_api(source_data)
        else:
            logger.error("未配置真实AI服务，无法生成AI总结")
            raise Exception("未配置AI服务密钥，无法生成AI总结")
    
    def _call_dify_api(self, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用Dify AI API生成分析
        
        Args:
            source_data: 格式化后的数据（直接传递给第三方AI）
            
        Returns:
            Dict: AI分析结果
        """
        try:
            logger.info(f"调用Dify AI API进行总结分析")
            
            # 调用Dify API - 直接传递格式化后的数据
            request_data = {
                "inputs": {},
                "query": source_data,  # 直接传递格式化后的数据
                "response_mode": "blocking",
                "user": f"ai_summary_{source_data.get('姓名', 'unknown')}"
            }
            logger.info(f"request_data: {request_data}")
            # 调试信息：记录发送给AI的数据
            logger.info("=" * 60)
            logger.info("🚀 发送给AI的请求数据:")
            logger.info(f"📍 API端点: {self.base_url}/chat-messages")
            logger.info(f"👤 代表: {source_data.get('姓名', '未知')}")
            logger.info(f"📅 年度: {source_data.get('年份', '未知')}")
            logger.info("📊 数据摘要:")
            logger.info(f"  - 履职数量: {source_data.get('履职数量', 0)}")
            logger.info(f"  - 意见建议提交数量: {source_data.get('意见建议提交数量', 0)}")
            logger.info(f"  - 总活动数量: {source_data.get('总活动数量', 0)}")
            logger.info(f"📝 数据内容: {json.dumps(source_data, ensure_ascii=False, indent=2)}")
            logger.info("=" * 60)
            
            response = requests.post(
                f"{self.base_url}/chat-messages",
                headers=self.headers,
                json=request_data,
                timeout=self.config['timeout']
            )
            
            if response.status_code == 200:
                result = response.json()
                ai_generated_content = result.get('answer', '')
                
                # 调试信息：记录AI返回的数据
                logger.info("=" * 60)
                logger.info("🤖 AI返回的响应数据:")
                logger.info(f"📏 响应长度: {len(ai_generated_content)} 字符")
                logger.info(f"📝 原始内容预览: {ai_generated_content[:300]}...")
                logger.info(f"💾 完整API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                logger.info("=" * 60)
                
                # 解析AI返回的JSON格式结果
                ai_result = self._parse_ai_response(ai_generated_content, source_data)
                
                # 调试信息：记录解析后的结果
                logger.info("=" * 60)
                logger.info("✅ 解析后的AI分析结果:")
                logger.info(f"📊 结果结构: {list(ai_result.keys())}")
                if 'overview' in ai_result:
                    logger.info(f"📋 概览信息: {ai_result['overview']}")
                if 'coreMetrics' in ai_result:
                    logger.info(f"📈 核心指标数量: {len(ai_result.get('coreMetrics', []))}")
                if 'highlights' in ai_result:
                    logger.info(f"⭐ 履职亮点数量: {len(ai_result.get('highlights', []))}")
                logger.info("=" * 60)
                
                logger.info(f"AI总结生成成功，内容长度: {len(ai_generated_content)}")
                return ai_result
            else:
                error_msg = f"Dify AI API请求失败: {response.status_code} - {response.text}"
                logger.error("=" * 60)
                logger.error("❌ AI API调用失败:")
                logger.error(f"📍 状态码: {response.status_code}")
                logger.error(f"📝 错误信息: {response.text}")
                logger.error("=" * 60)
                raise Exception(error_msg)
                
        except requests.exceptions.RequestException as e:
            logger.error("=" * 60)
            logger.error("🌐 AI API网络请求失败:")
            logger.error(f"🔍 异常类型: {type(e).__name__}")
            logger.error(f"📝 异常信息: {str(e)}")
            logger.error("=" * 60)
            raise Exception(f"AI API网络请求失败: {str(e)}")
            
        except Exception as e:
            logger.error("=" * 60)
            logger.error("💥 AI API调用异常:")
            logger.error(f"🔍 异常类型: {type(e).__name__}")
            logger.error(f"📝 异常信息: {str(e)}")
            logger.error("=" * 60)
            raise Exception(f"AI API调用异常: {str(e)}")
    
    def _parse_ai_response(self, ai_content: str, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析AI返回的内容，优雅处理深度思考标签
        
        Args:
            ai_content: AI生成的内容（可能包含<think>标签或纯JSON）
            source_data: 原始数据
            
        Returns:
            Dict: 解析后的结构化数据
            
        Raises:
            Exception: 当AI返回的内容不是有效JSON时抛出异常
        """
        try:
            logger.info("=" * 60)
            logger.info("🧠 开始解析AI返回内容")
            logger.info(f"📏 原始内容长度: {len(ai_content)} 字符")
            logger.info(f"📝 原始内容预览: {ai_content[:200]}...")
            
            # 清理AI返回的内容，去除深度思考标签
            cleaned_content = self._clean_ai_content(ai_content)
            
            logger.info(f"🧹 清理后内容长度: {len(cleaned_content)} 字符")
            logger.info(f"📝 清理后内容预览: {cleaned_content[:200]}...")
            
            # 尝试解析为JSON
            if cleaned_content.strip().startswith('{'):
                ai_result = json.loads(cleaned_content)
                logger.info("✅ 成功解析AI返回的JSON格式数据")
                logger.info(f"📊 解析结果包含字段: {list(ai_result.keys())}")
                logger.info("=" * 60)
                return ai_result
            else:
                logger.error("❌ 清理后的内容不是JSON格式")
                logger.error(f"📝 内容开头: {cleaned_content[:50]}")
                logger.info("=" * 60)
                raise Exception("AI返回内容格式错误，期望JSON格式")
                
        except json.JSONDecodeError as e:
            logger.error("❌ AI返回内容JSON解析失败")
            logger.error(f"🔍 JSON错误: {str(e)}")
            logger.error(f"📝 解析内容: {cleaned_content[:500]}")
            logger.info("=" * 60)
            raise Exception(f"AI返回内容JSON解析失败：{str(e)}")
        except Exception as e:
            logger.error(f"❌ AI内容解析异常: {str(e)}")
            logger.info("=" * 60)
            raise e
    
    def _clean_ai_content(self, ai_content: str) -> str:
        """
        清理AI返回的内容，去除深度思考标签
        
        Args:
            ai_content: AI返回的原始内容
            
        Returns:
            str: 清理后的纯JSON内容
        """
        import re
        
        logger.info("🧹 开始清理AI内容")
        
        # 去除可能的深度思考标签 <think>...</think>
        # 使用正则表达式匹配并移除
        think_pattern = r'<think>.*?</think>'
        cleaned = re.sub(think_pattern, '', ai_content, flags=re.DOTALL)
        
        # 检查是否移除了思考内容
        original_length = len(ai_content)
        cleaned_length = len(cleaned)
        
        if original_length != cleaned_length:
            logger.info(f"🎯 检测到并移除了深度思考内容")
            logger.info(f"📏 移除字符数: {original_length - cleaned_length}")
        else:
            logger.info("📄 未检测到深度思考标签，内容无变化")
        
        # 清理多余的空白字符
        cleaned = cleaned.strip()
        
        # 如果清理后为空，说明整个内容都在think标签内，这是异常情况
        if not cleaned:
            logger.warning("⚠️ 清理后内容为空，可能所有内容都在思考标签内")
            # 尝试提取think标签后的内容
            think_end_pattern = r'</think>\s*(.*)$'
            match = re.search(think_end_pattern, ai_content, flags=re.DOTALL)
            if match:
                cleaned = match.group(1).strip()
                logger.info(f"🔄 从思考标签后提取到内容: {len(cleaned)} 字符")
            else:
                # 如果仍然为空，尝试查找JSON模式
                json_pattern = r'\{.*\}'
                match = re.search(json_pattern, ai_content, flags=re.DOTALL)
                if match:
                    cleaned = match.group(0).strip()
                    logger.info(f"🔍 通过JSON模式匹配提取内容: {len(cleaned)} 字符")
        
        logger.info(f"✨ 内容清理完成，最终长度: {len(cleaned)} 字符")
        
        return cleaned


class AISummaryService:
    """AI总结核心服务类"""
    
    @classmethod
    def generate_summary(cls, representative: Representative, analysis_year: int, force_regenerate: bool = False) -> RepresentativeAISummary:
        """
        生成AI总结
        
        Args:
            representative: 代表对象
            analysis_year: 分析年份
            force_regenerate: 是否强制重新生成
            
        Returns:
            RepresentativeAISummary: AI总结对象
        """
        logger.info(f"开始为代表 {representative.name} 生成 {analysis_year}年 AI总结")
        
        # 检查是否已存在
        existing = RepresentativeAISummary.objects.filter(
            representative=representative,
            analysis_year=analysis_year
        ).first()
        
        if existing and not force_regenerate:
            if existing.is_completed:
                logger.info(f"AI总结已存在且已完成，直接返回")
                return existing
            elif existing.is_generating:
                logger.info(f"AI总结正在生成中")
                return existing
        
        # 开始生成流程
        with transaction.atomic():
            # 创建或更新记录
            if existing:
                ai_summary = existing
                ai_summary.mark_as_generating()
                logger.info(f"重新生成AI总结，ID: {ai_summary.id}")
            else:
                ai_summary = RepresentativeAISummary.objects.create(
                    representative=representative,
                    analysis_year=analysis_year,
                    status='generating'
                )
                logger.info(f"创建新的AI总结记录，ID: {ai_summary.id}")
        
        try:
            # 收集数据
            start_time = time.time()
            source_data = DataCollectionService.collect_representative_data(representative, analysis_year)
            
            # 更新数据来源概要
            ai_summary.update_source_data_summary(source_data)
            
            # 检查数据是否足够
            if not cls._check_data_sufficiency(source_data):
                error_msg = f"{analysis_year}年数据不足，无法生成有效的AI分析报告"
                # 如果记录已保存到数据库，则删除；否则只是不保存
                if ai_summary.pk:
                    ai_summary.delete()
                raise Exception(error_msg)
            
            # 调用AI服务生成分析
            ai_result = AIProviderService.generate_ai_summary(source_data)
            
            # 计算生成耗时
            generation_duration = int(time.time() - start_time)
            
            # 标记为完成
            ai_summary.mark_as_completed(ai_result, generation_duration)
            
            logger.info(f"AI总结生成完成，耗时 {generation_duration}秒")
            return ai_summary
            
        except Exception as e:
            logger.error(f"AI总结生成失败：{str(e)}")
            # 如果记录已保存到数据库，则删除；否则只是不保存
            if ai_summary.pk:
                ai_summary.delete()
            raise e  # 重新抛出异常
    
    @classmethod
    def _check_data_sufficiency(cls, source_data: Dict[str, Any]) -> bool:
        """检查数据是否足够生成分析"""
        performance_count = source_data.get('履职数量', 0)
        opinion_count = source_data.get('意见建议提交数量', 0)
        
        # 至少需要有1条履职记录或意见建议
        return (performance_count + opinion_count) > 0
    
    @classmethod
    def check_data_availability(cls, representative: Representative, analysis_year: int) -> Dict[str, Any]:
        """
        检查指定代表和年份的数据可用性
        
        Args:
            representative: 代表对象
            analysis_year: 分析年份
            
        Returns:
            Dict: 数据可用性检查结果
        """
        try:
            # 收集数据
            source_data = DataCollectionService.collect_representative_data(representative, analysis_year)
            
            # 检查数据充足性
            is_sufficient = cls._check_data_sufficiency(source_data)
            
            return {
                'success': True,
                'representative_name': representative.name,
                'analysis_year': analysis_year,
                'is_sufficient': is_sufficient,
                'performance_count': source_data.get('履职数量', 0),
                'opinion_count': source_data.get('意见建议提交数量', 0),
                'total_activities': source_data.get('总活动数量', 0),
                'message': '数据充足，可以生成AI分析' if is_sufficient else '数据不足，建议增加履职记录或意见建议后再生成分析'
            }
            
        except Exception as e:
            logger.error(f"检查数据可用性失败：{str(e)}")
            return {
                'success': False,
                'representative_name': representative.name,
                'analysis_year': analysis_year,
                'is_sufficient': False,
                'message': f'数据检查失败：{str(e)}'
            }
    
 