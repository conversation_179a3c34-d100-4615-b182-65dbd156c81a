# Generated by Django 5.2.3 on 2025-07-22 12:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0005_update_representative_levels'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='representative',
            name='district',
            field=models.CharField(blank=True, choices=[('那洪片区', '那洪片区'), ('那历片区', '那历片区'), ('沛鸿片区', '沛鸿片区')], help_text='代表所属的片区，可选字段', max_length=20, null=True, verbose_name='所属片区'),
        ),
        migrations.AlterField(
            model_name='representative',
            name='education',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='学历'),
        ),
        migrations.AlterField(
            model_name='representative',
            name='nationality',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='民族'),
        ),
    ]
