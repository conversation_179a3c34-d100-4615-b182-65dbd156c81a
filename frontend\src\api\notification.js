// 通知模块API接口
import { ref } from 'vue'

// 模拟通知数据存储
const mockNotifications = ref([
  {
    id: 1,
    userId: 1, // 代表1
    type: 'opinion_audit',
    title: '意见审核通过',
    content: '您提交的意见《关于改善社区停车难问题的建议》已审核通过，已转交至城管局处理。',
    isRead: false,
    relatedId: 1, // 关联的业务ID（如意见ID）
    createdAt: new Date('2024-01-15 10:30:00'),
    sender: '站点工作人员'
  },
  {
    id: 2,
    userId: 2, // 工作人员1
    type: 'opinion_submit',
    title: '新的待审核意见',
    content: '代表张三提交了一条新的意见建议，请及时审核处理。',
    isRead: false,
    relatedId: 2,
    createdAt: new Date('2024-01-15 14:20:00'),
    sender: '张三代表'
  },
  {
    id: 3,
    userId: 1, // 代表1
    type: 'opinion_result',
    title: '意见办理结果更新',
    content: '您的意见《关于改善社区停车难问题的建议》已处理完成，城管局已增设10个停车位。',
    isRead: true,
    relatedId: 1,
    createdAt: new Date('2024-01-14 16:45:00'),
    sender: '站点工作人员'
  },
  {
    id: 4,
    userId: 1, // 代表1
    type: 'system',
    title: '系统维护通知',
    content: '系统将于今晚22:00-24:00进行维护升级，期间可能影响使用，敬请谅解。',
    isRead: true,
    relatedId: null,
    createdAt: new Date('2024-01-13 09:00:00'),
    sender: '系统管理员'
  }
])

// 通知类型配置
const notificationTypes = {
  'opinion_submit': {
    title: '新的待审核意见',
    icon: 'Document',
    color: '#409eff'
  },
  'opinion_audit': {
    title: '意见审核通知',
    icon: 'Check',
    color: '#67c23a'
  },
  'opinion_result': {
    title: '意见办理结果',
    icon: 'Finished',
    color: '#909399'
  },
  'mediation_submit': {
    title: '新的调解案件',
    icon: 'Scale',
    color: '#e6a23c'
  },
  'mediation_analysis': {
    title: 'AI分析完成',
    icon: 'DataAnalysis',
    color: '#f56c6c'
  },
  'system': {
    title: '系统通知',
    icon: 'Bell',
    color: '#909399'
  }
}

/**
 * 获取用户通知列表
 * @param {Object} params - 查询参数
 * @param {number} params.userId - 用户ID
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @param {string} params.type - 通知类型筛选
 * @param {boolean} params.unreadOnly - 只查询未读
 */
export const getNotifications = async (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const { userId, page = 1, size = 10, type, unreadOnly } = params
      
      // 筛选用户的通知
      let userNotifications = mockNotifications.value.filter(n => n.userId === userId)
      
      // 类型筛选
      if (type) {
        userNotifications = userNotifications.filter(n => n.type === type)
      }
      
      // 未读筛选
      if (unreadOnly) {
        userNotifications = userNotifications.filter(n => !n.isRead)
      }
      
      // 按创建时间倒序排列
      userNotifications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      
      // 分页
      const total = userNotifications.length
      const start = (page - 1) * size
      const end = start + size
      const list = userNotifications.slice(start, end)
      
      resolve({
        success: true,
        data: {
          list,
          total,
          page,
          size,
          totalPages: Math.ceil(total / size)
        }
      })
    }, 300)
  })
}

/**
 * 获取未读通知数量
 * @param {number} userId - 用户ID
 */
export const getUnreadCount = async (userId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const unreadCount = mockNotifications.value.filter(n => 
        n.userId === userId && !n.isRead
      ).length
      
      resolve({
        success: true,
        data: { count: unreadCount }
      })
    }, 200)
  })
}

/**
 * 标记通知为已读
 * @param {number} notificationId - 通知ID
 */
export const markAsRead = async (notificationId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const notification = mockNotifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.isRead = true
        resolve({
          success: true,
          message: '标记成功'
        })
      } else {
        resolve({
          success: false,
          message: '通知不存在'
        })
      }
    }, 200)
  })
}

/**
 * 批量标记为已读
 * @param {number[]} notificationIds - 通知ID数组
 */
export const batchMarkAsRead = async (notificationIds) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      notificationIds.forEach(id => {
        const notification = mockNotifications.value.find(n => n.id === id)
        if (notification) {
          notification.isRead = true
        }
      })
      
      resolve({
        success: true,
        message: '批量标记成功'
      })
    }, 300)
  })
}

/**
 * 全部标记为已读
 * @param {number} userId - 用户ID
 */
export const markAllAsRead = async (userId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      mockNotifications.value
        .filter(n => n.userId === userId)
        .forEach(n => n.isRead = true)
      
      resolve({
        success: true,
        message: '全部标记成功'
      })
    }, 300)
  })
}

/**
 * 发送通知
 * @param {Object} notification - 通知对象
 * @param {number} notification.userId - 接收用户ID
 * @param {string} notification.type - 通知类型
 * @param {string} notification.title - 标题
 * @param {string} notification.content - 内容
 * @param {number} notification.relatedId - 关联业务ID
 * @param {string} notification.sender - 发送者
 */
export const sendNotification = async (notification) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newNotification = {
        id: mockNotifications.value.length + 1,
        isRead: false,
        createdAt: new Date(),
        ...notification
      }
      
      mockNotifications.value.unshift(newNotification)
      
      resolve({
        success: true,
        data: newNotification,
        message: '通知发送成功'
      })
    }, 200)
  })
}

/**
 * 删除通知
 * @param {number} notificationId - 通知ID
 */
export const deleteNotification = async (notificationId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockNotifications.value.findIndex(n => n.id === notificationId)
      if (index !== -1) {
        mockNotifications.value.splice(index, 1)
        resolve({
          success: true,
          message: '删除成功'
        })
      } else {
        resolve({
          success: false,
          message: '通知不存在'
        })
      }
    }, 200)
  })
}

/**
 * 获取通知类型配置
 */
export const getNotificationTypes = () => {
  return notificationTypes
}

/**
 * 业务触发通知的工具函数
 */

// 代表提交意见时通知工作人员
export const notifyOpinionSubmit = async (opinionData, staffUserId) => {
  const notification = {
    userId: staffUserId,
    type: 'opinion_submit',
    title: '新的待审核意见',
    content: `代表${opinionData.submitterName}提交了一条新的意见建议"${opinionData.title}"，请及时审核处理。`,
    relatedId: opinionData.id,
    sender: `${opinionData.submitterName}代表`
  }
  
  return await sendNotification(notification)
}

// 工作人员审核意见时通知代表
export const notifyOpinionAudit = async (opinionData, representativeUserId, auditResult) => {
  const isApproved = auditResult.status === 'approved'
  const notification = {
    userId: representativeUserId,
    type: 'opinion_audit',
    title: isApproved ? '意见审核通过' : '意见审核未通过',
    content: isApproved 
      ? `您提交的意见"${opinionData.title}"已审核通过，已转交至${auditResult.department}处理。`
      : `您提交的意见"${opinionData.title}"审核未通过，原因：${auditResult.reason}`,
    relatedId: opinionData.id,
    sender: '站点工作人员'
  }
  
  return await sendNotification(notification)
}

// 意见办理结果更新时通知代表
export const notifyOpinionResult = async (opinionData, representativeUserId, resultData) => {
  const notification = {
    userId: representativeUserId,
    type: 'opinion_result',
    title: '意见办理结果更新',
    content: `您的意见"${opinionData.title}"已处理完成，处理结果：${resultData.description}`,
    relatedId: opinionData.id,
    sender: '站点工作人员'
  }
  
  return await sendNotification(notification)
}

// 调解案件提交时通知
export const notifyMediationSubmit = async (caseData, receiverUserId) => {
  const notification = {
    userId: receiverUserId,
    type: 'mediation_submit',
    title: '新的调解案件',
    content: `新增调解案件"${caseData.title}"，案件编号：${caseData.caseNumber}，请及时处理。`,
    relatedId: caseData.id,
    sender: caseData.submitterName
  }
  
  return await sendNotification(notification)
}

// AI分析完成时通知
export const notifyAnalysisComplete = async (caseData, userId, analysisType) => {
  const typeMap = {
    'mediation': '调解案件',
    'annual': '年度履职'
  }
  
  const notification = {
    userId: userId,
    type: 'mediation_analysis',
    title: 'AI分析完成',
    content: `${typeMap[analysisType] || ''}AI分析已完成，您可以查看分析结果了。`,
    relatedId: caseData.id,
    sender: 'AI助手'
  }
  
  return await sendNotification(notification)
}

// 系统通知
export const sendSystemNotification = async (userIds, title, content) => {
  const promises = userIds.map(userId => {
    const notification = {
      userId: userId,
      type: 'system',
      title: title,
      content: content,
      relatedId: null,
      sender: '系统管理员'
    }
    return sendNotification(notification)
  })
  
  return await Promise.all(promises)
} 