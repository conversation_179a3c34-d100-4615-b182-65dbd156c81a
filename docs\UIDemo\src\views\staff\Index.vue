<template>
  <div class="staff-layout">
    <!-- 头部 -->
    <el-header class="layout-header">
      <div class="header-left">
        <el-icon size="24" style="color: var(--china-red); margin-right: 10px;">
          <OfficeBuilding />
        </el-icon>
        <span class="system-title">人大代表履职服务与管理平台</span>
      </div>
      <div class="header-right">
        <!-- 通知图标 -->
        <NotificationIcon />
        
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-avatar :size="32" style="background-color: var(--china-red);">
              {{ userStore.userName.charAt(0) }}
            </el-avatar>
            <span class="user-name">{{ userStore.userName }}</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人信息
              </el-dropdown-item>
              <el-dropdown-item command="logout" divided>
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主体布局 -->
    <el-container class="main-container">
      <!-- 侧边栏 -->
      <el-aside class="layout-aside">
        <el-menu
          :default-active="$route.path"
          router
          class="nav-menu"
        >
          <el-menu-item index="/staff">
            <el-icon><House /></el-icon>
            <span>工作台概览</span>
          </el-menu-item>
          <el-menu-item index="/staff/review">
            <el-icon><Select /></el-icon>
            <span>意见建议审核</span>
          </el-menu-item>
          <el-menu-item index="/staff/work-plan">
            <el-icon><Calendar /></el-icon>
            <span>工作计划管理</span>
          </el-menu-item>
          <el-sub-menu index="work-analysis">
            <template #title>
              <el-icon><DataAnalysis /></el-icon>
              <span>工作分析</span>
            </template>
            <el-menu-item index="/staff/work-analysis/site-summary">
              <el-icon><Document /></el-icon>
              <span>站点工作总结</span>
            </el-menu-item>
            <el-menu-item index="/staff/work-analysis/representative-summary">
              <el-icon><User /></el-icon>
              <span>代表工作总结</span>
            </el-menu-item>
          </el-sub-menu>
          <el-menu-item index="/staff/knowledge-qa">
            <el-icon><DocumentCopy /></el-icon>
            <span>法律政策互动AI问答</span>
          </el-menu-item>
          <el-menu-item index="/staff/account-management">
            <el-icon><UserFilled /></el-icon>
            <span>账号管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 内容区域 -->
      <el-main class="layout-main">
        <!-- 路由出口：如果是根路径显示概览，否则显示子页面 -->
        <div v-if="$route.path === '/staff'" class="dashboard-container">
          <!-- 欢迎卡片 -->
          <div class="welcome-card">
            <h2>欢迎回来，{{ userStore.userName }} 同志</h2>
            <p>{{ currentDate }} | {{ userStore.roleText }}</p>
            <div class="role-info">
              <div class="info-item">
                <span class="label">工作站点：</span>
                <span class="value">{{ userStore.userInfo.department || '某某站点' }}</span>
              </div>
              <div class="info-item">
                <span class="label">职务：</span>
                <span class="value">{{ userStore.userInfo.position || '工作人员' }}</span>
              </div>
              <div class="info-item">
                <span class="label">工作时间：</span>
                <span class="value">周一至周五 8:30-17:30</span>
              </div>
            </div>
          </div>

          <!-- 统计数据 -->
          <el-row :gutter="20" class="stats-row">
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.pendingReview }}</div>
                <div class="stat-label">待审核意见</div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.inProgress }}</div>
                <div class="stat-label">处理中意见</div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.completed }}</div>
                <div class="stat-label">已办结意见</div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="6" :md="6" :lg="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.thisMonthProcessed }}</div>
                <div class="stat-label">本月处理</div>
              </div>
            </el-col>
          </el-row>

          <!-- 工作概览 -->
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <el-card header="待审核意见列表">
                <el-empty v-if="!pendingReviewList.length" description="暂无待审核意见" />
                <div v-else>
                  <div
                    v-for="opinion in pendingReviewList"
                    :key="opinion.id"
                    class="review-item"
                  >
                    <div class="review-header">
                      <span class="review-title">{{ opinion.title }}</span>
                      <el-tag size="small" type="warning">{{ opinion.status }}</el-tag>
                    </div>
                    <div class="review-meta">
                      <span class="submitter">提交人：{{ opinion.submitter }}</span>
                      <span class="submit-date">{{ opinion.date }}</span>
                    </div>
                    <div class="review-actions">
                      <el-button size="small" type="primary" @click="reviewOpinion(opinion.id)">
                        审核
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card header="最近处理记录">
                <el-timeline>
                  <el-timeline-item
                    v-for="record in recentActivities"
                    :key="record.id"
                    :timestamp="record.date"
                    :type="record.type"
                  >
                    <span class="activity-content">{{ record.content }}</span>
                  </el-timeline-item>
                </el-timeline>
              </el-card>
            </el-col>
          </el-row>

          <!-- 快捷操作 -->
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card header="快捷操作">
                <el-row :gutter="15">
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="$router.push('/staff/review')">
                      <el-icon size="24" style="color: var(--china-red);">
                        <Select />
                      </el-icon>
                      <span>意见建议审核</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="$router.push('/staff/work-plan')">
                      <el-icon size="24" style="color: var(--china-red);">
                        <Calendar />
                      </el-icon>
                      <span>工作计划管理</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="$router.push('/staff/work-analysis/site-summary')">
                      <el-icon size="24" style="color: var(--china-red);">
                        <DataAnalysis />
                      </el-icon>
                      <span>站点工作总结</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="$router.push('/staff/work-analysis/representative-summary')">
                      <el-icon size="24" style="color: var(--china-red);">
                        <User />
                      </el-icon>
                      <span>代表工作总结</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="showNotification">
                      <el-icon size="24" style="color: var(--china-red);">
                        <Bell />
                      </el-icon>
                      <span>通知公告</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="exportData">
                      <el-icon size="24" style="color: var(--china-red);">
                        <Download />
                      </el-icon>
                      <span>数据导出</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="$router.push('/staff/knowledge-qa')">
                      <el-icon size="24" style="color: var(--china-red);">
                        <DocumentCopy />
                      </el-icon>
                      <span>法律政策互动AI问答</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="8" :md="6" :lg="4">
                    <div class="quick-action" @click="$router.push('/staff/account-management')">
                      <el-icon size="24" style="color: var(--china-red);">
                        <UserFilled />
                      </el-icon>
                      <span>账号管理</span>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-col>
          </el-row>
        </div>
        
        <!-- 子路由内容 -->
        <router-view v-else />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import NotificationIcon from '@/components/NotificationIcon.vue'
import {
  User,
  ArrowDown,
  SwitchButton,
  House,
  Select,
  Management,
  Bell,
  Download,
  DocumentCopy,
  Calendar,
  DataAnalysis,
  Document,
  UserFilled
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 当前日期
const currentDate = computed(() => {
  const now = new Date()
  return now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

// 统计数据（模拟）
const stats = ref({
  pendingReview: 5,
  inProgress: 12,
  completed: 45,
  thisMonthProcessed: 18,
  totalAccounts: 15,
  activeAccounts: 13
})

// 待审核意见列表（模拟）
const pendingReviewList = ref([
  {
    id: 1,
    title: '关于小区停车难问题',
    submitter: '张三代表',
    status: '待审核',
    date: '2024-01-16'
  },
  {
    id: 2,
    title: '建议增设儿童游乐设施',
    submitter: '王五代表',
    status: '待审核',
    date: '2024-01-15'
  },
  {
    id: 3,
    title: '交通信号灯时间调整建议',
    submitter: '张三代表',
    status: '待审核',
    date: '2024-01-14'
  }
])

// 最近活动记录（模拟）
const recentActivities = ref([
  {
    id: 1,
    content: '审核通过"关于加强食品安全监管的建议"',
    date: '2024-01-15 14:30',
    type: 'success'
  },
  {
    id: 2,
    content: '转交"噪音扰民问题"至环保局',
    date: '2024-01-15 10:20',
    type: 'primary'
  },
  {
    id: 3,
    content: '更新"道路维修申请"办理进度',
    date: '2024-01-14 16:45',
    type: 'info'
  }
])

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    userStore.logout()
    router.push('/login')
  } catch {
    // 用户取消
  }
}

// 审核意见
const reviewOpinion = (opinionId) => {
  router.push(`/staff/review?id=${opinionId}`)
}

// 显示通知
const showNotification = () => {
  ElMessage.info('通知公告功能开发中')
}

// 导出数据
const exportData = () => {
  ElMessage.info('数据导出功能开发中')
}

// 组件挂载时的初始化
onMounted(() => {
  // 这里可以加载实际的统计数据
})
</script>

<style scoped>
.staff-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: white;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
}

.system-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--china-red);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: var(--bg-color);
}

.user-name {
  font-weight: 500;
  color: var(--text-color);
}

.main-container {
  flex: 1;
  height: calc(100vh - 60px);
}

.layout-aside {
  width: 200px;
  background: white;
  border-right: 1px solid var(--border-color);
}

.nav-menu {
  border-right: none;
  height: 100%;
}

.layout-main {
  background: var(--bg-color);
  padding: 0;
  overflow-y: auto;
}

.stats-row {
  margin: 20px 0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: rgba(255, 255, 255, 0.8);
  margin-right: 8px;
  min-width: 80px;
}

.value {
  color: white;
  font-weight: 500;
}

.review-item {
  padding: 15px 0;
  border-bottom: 1px solid var(--border-color);
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.review-title {
  font-weight: 500;
  color: var(--text-color);
}

.review-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 12px;
  color: #666;
}

.review-actions {
  text-align: right;
}

.activity-content {
  font-size: 14px;
  color: var(--text-color);
}

.quick-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.quick-action:hover {
  border-color: var(--china-red);
  box-shadow: 0 2px 8px rgba(200, 16, 46, 0.2);
  transform: translateY(-2px);
}

.quick-action span {
  font-size: 14px;
  color: var(--text-color);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-aside {
    width: 160px;
  }
  
  .system-title {
    display: none;
  }
  
  .stats-row {
    margin: 10px 0;
  }
  
  .review-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .layout-header {
    padding: 0 10px;
  }
  
  .layout-aside {
    width: 120px;
  }
  
  .user-name {
    display: none;
  }
  
  .quick-action {
    padding: 15px 10px;
  }
}
</style> 