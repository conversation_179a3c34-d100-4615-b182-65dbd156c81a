/**
 * 二维码功能演示工具
 * 提供常用的二维码链接更新方法和示例
 */

// 常用链接预设
export const PRESET_URLS = {
  bing: 'https://bing.com',
  baidu: 'https://baidu.com',
  github: 'https://github.com',
  wechat: 'https://weixin.qq.com',
  alipay: 'https://alipay.com',
  taobao: 'https://taobao.com',
  jd: 'https://jd.com',
  bilibili: 'https://bilibili.com'
}

/**
 * 更新二维码链接的通用方法
 * @param {Object} layoutRef - LayoutDemo组件的引用
 * @param {string} url - 新的链接地址
 * @returns {boolean} 是否更新成功
 */
export const updateQRCodeUrl = (layoutRef, url) => {
  try {
    if (!layoutRef || !layoutRef.value) {
      console.warn('⚠️ LayoutDemo组件引用不存在')
      return false
    }

    if (!url || typeof url !== 'string') {
      console.warn('⚠️ 无效的URL参数')
      return false
    }

    // 简单的URL格式验证
    const urlPattern = /^https?:\/\/.+/
    if (!urlPattern.test(url)) {
      console.warn('⚠️ URL格式不正确，请使用http://或https://开头')
      return false
    }

    layoutRef.value.updateQRCode(url)
    console.log('✅ 二维码链接已更新:', url)
    return true
  } catch (error) {
    console.error('❌ 更新二维码链接失败:', error)
    return false
  }
}

/**
 * 使用预设链接更新二维码
 * @param {Object} layoutRef - LayoutDemo组件的引用
 * @param {string} presetKey - 预设链接的键名
 * @returns {boolean} 是否更新成功
 */
export const updateQRCodeWithPreset = (layoutRef, presetKey) => {
  const url = PRESET_URLS[presetKey]
  if (!url) {
    console.warn('⚠️ 未找到预设链接:', presetKey)
    console.log('可用的预设链接:', Object.keys(PRESET_URLS))
    return false
  }

  return updateQRCodeUrl(layoutRef, url)
}

/**
 * 批量测试不同链接的二维码生成
 * @param {Object} layoutRef - LayoutDemo组件的引用
 * @param {number} interval - 切换间隔时间(毫秒)
 */
export const demoQRCodeRotation = (layoutRef, interval = 3000) => {
  const urls = Object.values(PRESET_URLS)
  let currentIndex = 0

  console.log('🔄 开始二维码轮播演示...')
  
  const rotateQRCode = () => {
    const url = urls[currentIndex]
    updateQRCodeUrl(layoutRef, url)
    currentIndex = (currentIndex + 1) % urls.length
  }

  // 立即执行一次
  rotateQRCode()

  // 设置定时器
  const timer = setInterval(rotateQRCode, interval)

  // 返回停止函数
  return () => {
    clearInterval(timer)
    console.log('⏹️ 二维码轮播演示已停止')
  }
}

/**
 * 生成动态链接（示例：带时间戳的链接）
 * @param {string} baseUrl - 基础URL
 * @returns {string} 带时间戳的URL
 */
export const generateDynamicUrl = (baseUrl = 'https://bing.com') => {
  const timestamp = Date.now()
  const separator = baseUrl.includes('?') ? '&' : '?'
  return `${baseUrl}${separator}t=${timestamp}`
}

/**
 * 在浏览器控制台中使用的快捷方法
 * 使用方法：在控制台输入 window.updateQR('https://example.com')
 */
export const setupConsoleHelper = (layoutRef) => {
  if (typeof window !== 'undefined') {
    window.updateQR = (url) => updateQRCodeUrl(layoutRef, url)
    window.presetQR = (key) => updateQRCodeWithPreset(layoutRef, key)
    window.demoQR = (interval) => demoQRCodeRotation(layoutRef, interval)
    
    console.log('🎯 二维码控制台助手已启用:')
    console.log('  - updateQR(url): 更新二维码链接')
    console.log('  - presetQR(key): 使用预设链接 (bing, baidu, github, etc.)')
    console.log('  - demoQR(interval): 开始轮播演示')
    console.log('  - 可用预设:', Object.keys(PRESET_URLS).join(', '))
  }
}

// 默认导出所有功能
export default {
  PRESET_URLS,
  updateQRCodeUrl,
  updateQRCodeWithPreset,
  demoQRCodeRotation,
  generateDynamicUrl,
  setupConsoleHelper
} 