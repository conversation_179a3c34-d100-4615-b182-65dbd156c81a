# 代表AI总结模块

## 模块概述

代表AI总结模块是人大代表履职服务与管理平台的AI分析功能，负责基于代表的履职记录和意见建议生成智能化的年度总结报告。

## 主要功能

### 🎯 核心功能

1. **年度AI总结生成** - 基于履职记录和意见建议生成综合分析报告（支持首次生成和重新生成）
2. **数据收集与分析** - 自动收集指定年份的履职数据并进行统计分析
3. **智能内容生成** - 使用AI技术生成个性化的履职分析报告
4. **状态管理** - 完整的生成状态跟踪和错误处理
5. **权限控制** - 严格的数据访问权限控制

### 🔐 安全特性

- **严格权限控制** - 代表只能生成和查看自己的AI总结
- **数据隔离** - 确保代表数据的安全性和隐私性
- **操作审计** - 完整的操作日志记录
- **状态验证** - 多层次的数据验证机制

## 技术架构

### 数据模型

#### RepresentativeAISummary（代表AI总结表）
- 存储AI总结的生成状态和结果
- 支持JSON格式存储复杂的AI分析数据
- 完整的状态流转管理

### API设计

#### 代表端API
```
POST   /api/v1/ai-summaries/generate/          # 生成AI总结（支持首次生成和重新生成）
GET    /api/v1/ai-summaries/{year}/             # 获取AI总结详情
GET    /api/v1/ai-summaries/{year}/check/       # 检查数据可用性
GET    /api/v1/ai-summaries/                    # AI总结列表
```

#### 工作人员端API
```
GET    /api/v1/ai-summaries/                    # 查看所有AI总结列表
GET    /api/v1/ai-summaries/{year}/             # 查看指定代表的AI总结
```

### 服务类架构

1. **AISummaryService** - AI总结核心服务类
   - 总结生成协调
   - 状态管理
   - 错误处理

2. **DataCollectionService** - 数据收集服务类
   - 履职记录收集
   - 意见建议收集
   - 数据统计分析

3. **AIProviderService** - AI提供商服务类
   - AI分析调用
   - 结果格式化
   - 模拟AI服务（开发用）

## AI分析结果格式

AI总结生成的数据格式完全匹配前端 `/representative/annual-achievements` 页面的需求：

```json
{
  "overview": {
    "subtitle": "年度履职总结概述"
  },
  "coreMetrics": [
    {
      "title": "履职记录总数",
      "value": 25,
      "unit": "条",
      "trend": "up",
      "description": "全年共完成25项履职活动"
    }
  ],
  "activityDistribution": [
    {
      "name": "会议参与",
      "value": 10,
      "percentage": 40.0
    }
  ],
  "timeInvestment": {
    "totalHours": 62.5,
    "avgPerActivity": 2.5,
    "monthlyData": [...]
  },
  "highlights": [
    {
      "title": "履职亮点标题",
      "description": "详细描述",
      "impact": "产生的影响",
      "date": "时间"
    }
  ],
  "keywords": [
    {
      "text": "关键词",
      "weight": 8
    }
  ],
  "aiSummary": {
    "strengths": ["优势1", "优势2"],
    "improvements": ["改进建议1", "改进建议2"],
    "conclusion": "总体评价"
  }
}
```

## 使用说明

### 1. 代表端使用

#### 生成AI总结（首次生成）
```bash
POST /api/v1/ai-summaries/generate/
{
  "analysis_year": 2024,
  "force_regenerate": false
}
```

#### 重新生成AI总结
```bash
POST /api/v1/ai-summaries/generate/
{
  "analysis_year": 2024,
  "force_regenerate": true
}
```

#### 查看AI总结
```bash
GET /api/v1/ai-summaries/2024/
```

#### 检查数据可用性
```bash
GET /api/v1/ai-summaries/2024/check/
```

### 2. 前端集成

前端已实现完整的UI组件，AI总结生成后可直接在年度成就页面展示：

```javascript
// 前端API调用示例
import { aiSummaryAPI } from '@/api/modules/aisummary/api'

// 首次生成AI总结
const generateSummary = async (year) => {
  const response = await aiSummaryAPI.generate({
    analysis_year: year,
    force_regenerate: false
  })
  return response.data
}

// 重新生成AI总结
const regenerateSummary = async (year) => {
  const response = await aiSummaryAPI.generate({
    analysis_year: year,
    force_regenerate: true
  })
  return response.data
}

// 获取AI总结详情
const getSummaryDetail = async (year) => {
  const response = await aiSummaryAPI.getDetail(year)
  return response.data.ai_result_data
}
```

## 开发和测试

### 运行测试
```bash
# 运行单元测试
uv run python manage.py test api.aisummary

# 运行API测试
uv run python manage.py test api.aisummary.tests.AISummaryAPITest
```

### 开发注意事项

1. **AI服务配置** - 实际部署时需要配置真实的AI服务提供商
2. **权限验证** - 确保所有API都有正确的权限控制
3. **数据验证** - 对输入数据进行严格验证
4. **错误处理** - 提供友好的错误信息

## 配置说明

### Django Settings
```python
# settings.py
INSTALLED_APPS = [
    # ...
    'api.aisummary',
]
```

### 环境变量配置
```bash
# AI服务配置
export AI_SUMMARY_DIFY_API_KEY=app-YourRealDifyAppKey123456
export AI_SUMMARY_DIFY_BASE_URL=https://dify.gxaigc.cn/v1
export AI_SUMMARY_TIMEOUT=120
export AI_SUMMARY_MAX_RETRIES=3
```

### Dify应用配置
在Dify平台创建专门的AI总结应用，配置以下输入变量：
- `representative_name`: 代表姓名
- `representative_level`: 代表层级  
- `analysis_year`: 分析年份
- `performance_total`: 履职记录总数
- `opinion_total`: 意见建议总数
- `total_activities`: 总活动数
- `performance_types`: 履职类型统计（JSON）
- `performance_status`: 履职状态统计（JSON）
- `performance_monthly`: 履职月度统计（JSON）
- `opinion_categories`: 意见分类统计（JSON）
- `opinion_monthly`: 意见月度统计（JSON）
- `ai_assisted_rate`: AI辅助使用率
- `performance_details`: 履职记录详情（JSON）
- `opinion_details`: 意见建议详情（JSON）

### URL配置
```python
# urls.py
urlpatterns = [
    path('api/v1/ai-summaries/', include('api.aisummary.urls')),
]
```

## 扩展规划

1. **多AI提供商支持** - 支持多种AI服务提供商
2. **定时生成** - 支持定时自动生成AI总结
3. **模板定制** - 支持自定义AI分析模板
4. **数据导出** - 支持AI总结的多种格式导出
5. **历史对比** - 支持年度间的对比分析

## 许可证

本模块遵循项目整体的许可证协议。 

发送给dify的结构
```
{'姓名': '王代表', '代表层级': '区级', '年份': 2025, '履职数量': 1, '意见建议提交数量': 15, '总活动数量': 16, '履职类型': '[{"走访群众": 1}]', '履职状态': '[{"status": "已完成", "count": 1}]', '履职每月统计': '[{"month": 1, "count": 0}, {"month": 2, "count": 0}, {"month": 3, "count": 0}, {"month": 4, "count": 0}, {"month": 5, "count": 0}, {"month": 6, "count": 1}, {"month": 7, "count": 0}, {"month": 8, "count": 0}, {"month": 9, "count": 0}, {"month": 10, "count": 0}, {"month": 11, "count": 0}, {"month": 12, "count": 0}]', '意见建议分类': '[{"category": "交通出行", "count": 4}, {"category": "教育文化", "count": 3}, {"category": "医疗卫生", "count": 2}, {"category": "经济发展", "count": 2}, {"category": "政务服务", "count": 2}, {"category": "城建环保", "count": 1}, {"category": "其他", "count": 1}]', '意见建议每月统计': '[{"month": 1, "count": 0}, {"month": 2, "count": 0}, {"month": 3, "count": 0}, {"month": 4, "count": 0}, {"month": 5, "count": 0}, {"month": 6, "count": 14}, {"month": 7, "count": 1}, {"month": 8, "count": 0}, {"month": 9, "count": 0}, {"month": 10, "count": 0}, {"month": 11, "count": 0}, {"month": 12, "count": 0}]', '履职记录详情': '[{"date": "2025-06-22", "type": "走访群众", "content": "测试履职内容测试履职内容测试履职内容测试履职内容"}]', '意见建议提交详情': '[{"title": "关于规范电动自行车安全佩戴头盔的建议", "category": "交通出行", "created_at": "2025-07-03T08:08:41.889732+00:00"}, {"title": "关于规范电动自行车安全佩戴头盔的建议", "category": "交通出行", "created_at": "2025-06-30T09:22:06.671137+00:00"}, {"title": "电动车违规上楼充电处理", "category": "交通出行", "created_at": "2025-06-29T09:52:06.640723+00:00"}, {"title": "2222222222222", "category": "教育文化", "created_at": "2025-06-23T11:41:47.807040+00:00"}, {"title": "测试意见标题1", "category": "医疗卫生", "created_at": "2025-06-23T11:19:42.701865+00:00"}, {"title": "关于促进就业创业的建议（第10条）", "category": "经济发展", "created_at": "2025-06-23T10:59:18.508796+00:00"}, {"title": "建议加强电商平台监管（第9条）", "category": "经济发展", "created_at": "2025-06-23T10:59:18.492426+00:00"}, {"title": "建议完善社区图书馆设施（第8条）", "category": "教育文化", "created_at": "2025-06-23T10:59:18.476742+00:00"}, {"title": "建议改善道路交通信号灯配时（第7条）", "category": "交通出行", "created_at": "2025-06-23T10:59:18.464771+00:00"}, {"title": "建议增加幼儿园学位供给（第6条）", "category": "教育文化", "created_at": "2025-06-23T10:59:18.455764+00:00"}, {"title": "建议简化办事流程（第5条）", "category": "政务服务", "created_at": "2025-06-23T10:59:18.443755+00:00"}, {"title": "关于解决老旧小区停车难问题的建议（第4条）", "category": "城建环保", "created_at": "2025-06-23T10:59:18.424670+00:00"}, {"title": "建议开展更多健康教育活动（第3条）", "category": "医疗卫生", "created_at": "2025-06-23T10:59:18.412198+00:00"}, {"title": "建议设立便民服务热线（第2条）", "category": "政务服务", "created_at": "2025-06-23T10:59:18.401925+00:00"}, {"title": "关于加强精神文明建设的建议（第1条）", "category": "其他", "created_at": "2025-06-23T10:59:18.376747+00:00"}]'}
```

dify提示词
```
请严格根据以下输入数据生成JSON格式的履职总结，绝对不可添加任何虚构内容,使用中文，要求：

1. 核心指标：统计履职活动总次数和意见建议总数
   - "履职活动"取'履职数量'字段值
   - "意见建议"取'意见建议提交数量'字段值

2. 履职活动分析：
   -  活动分析包括了履职类型和意见提交类型，不能只包括履职活动类型
   - 活动类型从'履职类型'字段提取（需解析JSON数组）
   - 意见类型从'意见建议分类'字段统计（需解析JSON数组，格式化为"XX意见提交"）
   - 如"履职活动分析": [{"走访群众": 1},{"交通出行意见提交": 4},{"教育文化意见提交": 3},{"医疗卫生意见提交": 2},{"经济发展意见提交": 2}]

3. 履职月度统计：
   - 合并'履职每月统计'和'意见建议每月统计'两个JSON数组
   - 每月数值=履职count+意见建议count
   - 保持1-12月完整序列

4. 关注领域：
   - 取'意见建议分类'中count≥2的category
   - 加上履职类型（如"群众走访"）

5. 履职亮点（需满足以下条件）：
   - 从'履职记录详情'中提取具体活动
   - 从'意见建议提交详情'选择最具代表性的3条（优先选择：a)重复提交的 b)近期提交的 c)分类数量多的）
   - 每条亮点必须包含原始数据中的确切标题/内容

6. AI智能总结要求：
   - 年度表现评价：根据活动时间分布、数量、类型客观描述
   - 突出成就：必须源自原始数据中的高频率/特殊事件
   - 改进建议：仅当每月统计出现连续0值时建议"加强XX月履职"

输出格式必须完全按照示例JSON结构，所有数据必须能在用户发来的原始数据中找到对应字段。

使用说明：
1.此提示词通过明确每个字段的数据来源和计算规则，确保输出真实性
2.特别约束了亮点选择的优先级逻辑，避免主观臆造
3. 对可能存在零值的情况做了明确处理要求
4.最终会生成这样的严格基于事实的输出格式：
{
  "核心指标": [{"履职活动": 1}, {"意见建议": 15}],
  "履职活动分析": [
    {"走访群众": 1},
    {"交通出行意见提交": 4},
    {"教育文化意见提交": 3},
    {"医疗卫生意见提交": 2},
    {"经济发展意见提交": 2},
    {"政务服务意见提交": 2},
    {"城建环保意见提交": 1},
    {"其他意见提交": 1}
  ],
  "履职月度统计": {
    "1": 0, "2": 0, "3": 0, "4": 0, "5": 0,
    "6": 15, "7": 1, "8": 0, "9": 0, "10": 0,
    "11": 0, "12": 0
  },
  "关注领域": ["交通出行", "教育文化", "医疗卫生", "经济发展", "政务服务", "群众走访"],
  "履职亮点": [
    {"走访记录": "2025-06-22走访群众：测试履职内容测试履职内容测试履职内容测试履职内容"},
    {"重点提案": "关于规范电动自行车安全佩戴头盔的建议（7月3日最新提交）"},
    {"高频建议": "教育文化类建议共3条，包括社区图书馆设施、幼儿园学位等"}
  ],
  "ai智能总结": {
    "年度表现评价": "王代表在2025年度主要履职活动集中在6月份，全年提交15条意见建议，其中交通出行类建议占比最高（26.7%），展现了在民生领域的重点关注。",
    "突出成就": [
      "单月高效履职：6月完成全年100%的走访任务并提交14条建议",
      "持续关注交通安全：就电动自行车管理提出重复性建议",
      "多领域建言：意见建议覆盖7个不同分类"
    ],
    "改进建议": [
      "均衡履职时间：避免出现前五个月零履职的情况",
      "加强城建环保领域关注：该类别建议仅1条",
      "拓展履职形式：当前仅见走访群众单一形式"
    ]
  }
}
```

dify返回json数据结构
```
{
  "核心指标": [
    {
      "履职活动": 1
    },
    {
      "意见建议": 15
    }
  ],
  "履职活动分析": [
    {
      "走访群众": 1
    },
    {
      "交通出行意见提交": 4
    },
    {
      "教育文化意见提交": 3
    },
    {
      "医疗卫生意见提交": 2
    },
    {
      "经济发展意见提交": 2
    },
    {
      "政务服务意见提交": 2
    },
    {
      "城建环保意见提交": 1
    },
    {
      "其他意见提交": 1
    }
  ],
  "履职月度统计": {
    "1": 0,
    "2": 0,
    "3": 0,
    "4": 0,
    "5": 0,
    "6": 15,
    "7": 1,
    "8": 0,
    "9": 0,
    "10": 0,
    "11": 0,
    "12": 0
  },
  "关注领域": [
    "交通出行",
    "教育文化",
    "医疗卫生",
    "经济发展",
    "政务服务",
    "群众走访"
  ],
  "履职亮点": [
    {
      "走访记录": "2025-06-22走访群众：测试履职内容测试履职内容测试履职内容测试履职内容"
    },
    {
      "重点提案": "关于规范电动自行车安全佩戴头盔的建议（7月3日最新提交）"
    },
    {
      "高频建议": "教育文化类建议共3条，包括社区图书馆设施、幼儿园学位等"
    }
  ],
  "ai智能总结": {
    "年度表现评价": "王代表在2025年度主要履职活动集中在6月份，全年提交15条意见建议，其中交通出行类建议占比最高（26.7%），展现了在民生领域的重点关注。",
    "突出成就": [
      "单月高效履职：6月完成全年100%的走访任务并提交14条建议",
      "持续关注交通安全：就电动自行车管理提出重复性建议",
      "多领域建言：意见建议覆盖7个不同分类"
    ],
    "改进建议": [
      "均衡履职时间：避免出现前五个月零履职的情况",
      "加强城建环保领域关注：该类别建议仅1条",
      "拓展履职形式：当前仅见走访群众单一形式"
    ]
  }
}
```