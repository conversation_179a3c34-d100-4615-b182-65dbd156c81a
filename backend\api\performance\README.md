# 履职管理模块

## 概述

履职管理模块是人大代表履职服务与管理平台的核心功能之一，提供完整的履职记录管理、多媒体附件支持、统计分析等功能。

## 主要功能

### 1. 履职记录管理
- ✅ 创建、查看、编辑、删除履职记录
- ✅ 支持17种履职类型（会议参与、实地调研、走访群众等）
- ✅ 三种履职状态（已完成、进行中、已暂停）
- ✅ 详细的履职内容和地点记录
- ✅ 时间排序和筛选功能

### 2. 多媒体附件支持
- ✅ 图片附件：支持 JPG、PNG、GIF 格式，最大10MB，最多9张
- ✅ 音频附件：支持 MP3、WAV、AAC 格式，最大50MB，最多3个
- ✅ 视频附件：支持 MP4、AVI、MOV 格式，最大100MB，最多2个
- ✅ 文档附件：支持 PDF、DOC、DOCX、TXT、XLS、XLSX 格式，最大20MB，最多5个
- ✅ 自动生成缩略图（图片和视频）
- ✅ 安全的文件验证和存储

### 3. 统计分析
- ✅ 按类型统计履职记录
- ✅ 按状态统计履职记录
- ✅ 月度、年度履职数量统计
- ✅ 近12个月履职趋势分析
- ✅ 附件统计信息

### 4. 权限控制
- ✅ 严格的用户权限控制
- ✅ 只能查看和操作自己的数据
- ✅ 文件上传权限验证
- ✅ 附件访问权限控制

### 5. 老年用户友好设计
- ✅ 简洁明了的界面设计
- ✅ 清晰的文件类型图标
- ✅ 友好的错误提示信息
- ✅ 便捷的操作流程

## 技术架构

### 后端技术栈
- **Django 5.2** - Web框架
- **Django REST Framework** - API框架
- **PostgreSQL/SQLite** - 数据库
- **Pillow** - 图像处理
- **MoviePy** - 视频处理（可选）

### 数据模型
- `PerformanceRecord` - 履职记录主表
- `PerformanceAttachment` - 履职记录附件表

### API 设计
RESTful API 设计，提供完整的 CRUD 操作：

```
GET    /api/v1/performance/records/          # 获取履职记录列表
POST   /api/v1/performance/records/          # 创建履职记录
GET    /api/v1/performance/records/{id}/     # 获取履职记录详情
PUT    /api/v1/performance/records/{id}/     # 更新履职记录
DELETE /api/v1/performance/records/{id}/     # 删除履职记录
GET    /api/v1/performance/records/stats/    # 获取统计数据
POST   /api/v1/performance/upload/           # 上传附件
```

## 快速开始

### 1. 环境准备

确保已安装必要的Python包：

```bash
# 基础依赖
pip install django djangorestframework pillow

# 可选依赖（视频处理）
pip install moviepy
```

### 2. 数据库迁移

```bash
# 创建迁移文件
python manage.py makemigrations performance

# 执行迁移
python manage.py migrate
```

### 3. 创建测试数据

```bash
# 创建测试用户和履职记录
python manage.py setup_performance --create-test-data --verbose
```

### 4. 启动服务

```bash
# 启动开发服务器
python manage.py runserver
```

### 5. 访问API

访问 `http://localhost:8000/docs/` 查看完整的API文档。

## API 使用示例

### 1. 用户认证

```bash
# 登录获取JWT token
curl -X POST http://localhost:8000/api/v1/users/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "test_rep_1", "password": "testpass123"}'
```

### 2. 获取履职记录列表

```bash
curl -X GET "http://localhost:8000/api/v1/performance/records/" \
  -H "Authorization: Bearer <your_access_token>"
```

### 3. 创建履职记录

```bash
curl -X POST http://localhost:8000/api/v1/performance/records/ \
  -H "Authorization: Bearer <your_access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "performance_date": "2024-12-20",
    "performance_type": "会议参与",
    "performance_content": "参加人大常委会会议",
    "activity_location": "市人大会议室",
    "performance_status": "已完成"
  }'
```

### 4. 上传附件

```bash
curl -X POST http://localhost:8000/api/v1/performance/upload/ \
  -H "Authorization: Bearer <your_access_token>" \
  -F "file=@/path/to/your/image.jpg" \
  -F "file_type=image" \
  -F "performance_record_id=1"
```

### 5. 获取统计数据

```bash
curl -X GET "http://localhost:8000/api/v1/performance/records/stats/" \
  -H "Authorization: Bearer <your_access_token>"
```

## 查询参数

履职记录列表支持多种查询参数：

- `start_date` - 开始日期 (YYYY-MM-DD)
- `end_date` - 结束日期 (YYYY-MM-DD)
- `performance_type` - 履职类型筛选
- `performance_status` - 履职状态筛选
- `has_attachments` - 是否有附件 (true/false)
- `search` - 关键词搜索
- `ordering` - 排序字段
- `page` - 页码
- `page_size` - 每页数量

示例：
```bash
curl "http://localhost:8000/api/v1/performance/records/?start_date=2024-01-01&performance_type=会议参与&page=1&page_size=10" \
  -H "Authorization: Bearer <your_access_token>"
```

## 文件存储

### 存储结构

```
media/
├── performance/
│   └── YYYY/
│       └── MM/
│           └── DD/
│               ├── images/
│               ├── audios/
│               ├── videos/
│               └── documents/
└── thumbnails/
    └── performance/
        └── YYYY/
            └── MM/
                └── DD/
```

### 文件命名

格式：`{uuid}_{timestamp}_{original_name}`

示例：`a1b2c3d4_20241220143000_现场照片.jpg`

### 缩略图命名

格式：`{uuid}_{timestamp}_thumb.jpg`

示例：`a1b2c3d4_20241220143000_thumb.jpg`

## 安全特性

### 文件验证
- ✅ 文件类型验证（扩展名 + MIME类型）
- ✅ 文件大小限制
- ✅ 文件头部魔数检查
- ✅ 恶意文件检测

### 权限控制
- ✅ JWT令牌认证
- ✅ 用户角色验证
- ✅ 数据所有权检查
- ✅ 文件访问权限控制

### 数据完整性
- ✅ 数据库约束
- ✅ 字段验证
- ✅ 事务处理
- ✅ 级联删除

## 测试

运行测试套件：

```bash
# 运行所有测试
python manage.py test api.performance

# 运行特定测试类
python manage.py test api.performance.tests.PerformanceRecordAPITest

# 带详细输出
python manage.py test api.performance --verbosity=2
```

## 监控和日志

### 操作日志
- 创建/更新/删除履职记录
- 文件上传/删除操作
- 错误和异常情况

### 性能监控
- API响应时间
- 数据库查询优化
- 文件上传性能

## 扩展功能

### 计划中的功能
- 📋 Excel导出功能
- 🔍 高级搜索和筛选
- 📊 更丰富的统计图表
- 🎥 AI视频生成支持
- 📱 移动端优化

### 集成建议
- 与工作计划模块集成
- 与通知系统集成
- 与报表系统集成
- 与审批流程集成

## 故障排除

### 常见问题

1. **数据库迁移失败**
   ```bash
   # 重置迁移
   python manage.py migrate performance zero
   python manage.py makemigrations performance
   python manage.py migrate
   ```

2. **文件上传失败**
   - 检查文件大小限制
   - 检查文件类型是否支持
   - 检查媒体目录权限

3. **权限错误**
   - 确认用户角色为 'representative'
   - 检查JWT令牌是否有效
   - 确认操作的是自己的数据

4. **缩略图生成失败**
   - 确认安装了 Pillow
   - 检查媒体目录写入权限
   - 视频缩略图需要 MoviePy

### 日志配置

在 `settings.py` 中添加日志配置：

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'performance.log',
        },
    },
    'loggers': {
        'api.performance': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 编写测试用例
4. 提交代码
5. 创建 Pull Request

## 许可证

MIT License 