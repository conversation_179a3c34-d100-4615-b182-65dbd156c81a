import { mockRequest } from './request.js'

/**
 * 代表履职类型统计API
 * 提供人大代表各种履职活动的统计数据
 */

/**
 * 获取代表履职类型统计数据（真实API接口，暂时注释）
 * @param {Object} params - 查询参数
 * @param {string} params.month - 月份 (格式: YYYY-MM)
 * @param {string} params.year - 年份 (格式: YYYY)
 * @returns {Promise} 返回履职统计数据
 */
// export const getRepresentativeDutiesReal = (params = {}) => {
//   return request({
//     url: '/api/representative/duties',
//     method: 'GET',
//     params
//   })
// }

/**
 * 获取履职类型详情
 * @param {string} dutyType - 履职类型
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回履职详情数据
 */
export const getDutyTypeDetail = (dutyType, params = {}) => {
  return mockRequest(`/api/representative/duties/${dutyType}`, 'GET', params)
}

/**
 * 获取履职趋势数据
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回履职趋势数据
 */
export const getDutyTrend = (params = {}) => {
  return mockRequest('/api/representative/duties/trend', 'GET', params)
}

// ==================== Mock 数据 ====================

/**
 * 模拟代表履职类型统计数据
 * 包含10种常见的人大代表履职类型
 */
const mockDutiesData = {
  // 2024年各月份数据
  '2024': {
    '01': [
      { name: '会议参与', value: 156, color: '#DC143C', percentage: 18.5 },
      { name: '实地调研', value: 142, color: '#B22222', percentage: 16.8 },
      { name: '走访群众', value: 128, color: '#CD5C5C', percentage: 15.2 },
      { name: '议案提交', value: 89, color: '#FFD700', percentage: 10.6 },
      { name: '建议办理', value: 76, color: '#FFA500', percentage: 9.0 },
      { name: '监督检查', value: 68, color: '#FF4500', percentage: 8.1 },
      { name: '法律宣传', value: 54, color: '#8B0000', percentage: 6.4 },
      { name: '信访接待', value: 47, color: '#A0522D', percentage: 5.6 },
      { name: '培训学习', value: 42, color: '#DAA520', percentage: 5.0 },
      { name: '联络活动', value: 38, color: '#9ACD32', percentage: 4.5 },
      { name: '专题调研', value: 35, color: '#32CD32', percentage: 4.1 },
      { name: '视察活动', value: 32, color: '#00CED1', percentage: 3.8 },
      { name: '座谈交流', value: 28, color: '#4169E1', percentage: 3.3 },
      { name: '执法检查', value: 25, color: '#8A2BE2', percentage: 3.0 },
      { name: '民生走访', value: 22, color: '#FF1493', percentage: 2.6 },
      { name: '政策宣讲', value: 19, color: '#FF69B4', percentage: 2.3 },
      { name: '其他活动', value: 16, color: '#DDA0DD', percentage: 1.9 }
    ],
    '02': [
      { name: '会议参与', value: 134, color: '#00D4FF', percentage: 17.8 },
      { name: '实地调研', value: 125, color: '#FF6B6B', percentage: 16.6 },
      { name: '走访群众', value: 118, color: '#4ECDC4', percentage: 15.7 },
      { name: '议案提交', value: 92, color: '#45B7D1', percentage: 12.2 },
      { name: '建议办理', value: 78, color: '#96CEB4', percentage: 10.4 },
      { name: '监督检查', value: 65, color: '#FFEAA7', percentage: 8.6 },
      { name: '法律宣传', value: 48, color: '#DDA0DD', percentage: 6.4 },
      { name: '信访接待', value: 41, color: '#98D8C8', percentage: 5.4 },
      { name: '培训学习', value: 35, color: '#F7DC6F', percentage: 4.7 },
      { name: '联络活动', value: 32, color: '#BB8FCE', percentage: 4.2 },
      { name: '专题调研', value: 29, color: '#85C1E9', percentage: 3.9 },
      { name: '视察活动', value: 26, color: '#F8C471', percentage: 3.5 },
      { name: '座谈交流', value: 23, color: '#82E0AA', percentage: 3.1 },
      { name: '执法检查', value: 20, color: '#F1948A', percentage: 2.7 },
      { name: '民生走访', value: 17, color: '#D7BDE2', percentage: 2.3 },
      { name: '政策宣讲', value: 14, color: '#AED6F1', percentage: 1.9 },
      { name: '其他活动', value: 11, color: '#A9DFBF', percentage: 1.5 }
    ],
    '03': [
      { name: '会议参与', value: 168, color: '#00D4FF', percentage: 19.2 },
      { name: '实地调研', value: 155, color: '#FF6B6B', percentage: 17.7 },
      { name: '走访群众', value: 138, color: '#4ECDC4', percentage: 15.8 },
      { name: '议案提交', value: 95, color: '#45B7D1', percentage: 10.9 },
      { name: '建议办理', value: 82, color: '#96CEB4', percentage: 9.4 },
      { name: '监督检查', value: 71, color: '#FFEAA7', percentage: 8.1 },
      { name: '法律宣传', value: 58, color: '#DDA0DD', percentage: 6.6 },
      { name: '信访接待', value: 49, color: '#98D8C8', percentage: 5.6 },
      { name: '培训学习', value: 44, color: '#F7DC6F', percentage: 5.0 },
      { name: '联络活动', value: 40, color: '#BB8FCE', percentage: 4.6 },
      { name: '专题调研', value: 37, color: '#85C1E9', percentage: 4.2 },
      { name: '视察活动', value: 34, color: '#F8C471', percentage: 3.9 },
      { name: '座谈交流', value: 31, color: '#82E0AA', percentage: 3.5 },
      { name: '执法检查', value: 28, color: '#F1948A', percentage: 3.2 },
      { name: '民生走访', value: 25, color: '#D7BDE2', percentage: 2.9 },
      { name: '政策宣讲', value: 22, color: '#AED6F1', percentage: 2.5 },
      { name: '其他活动', value: 19, color: '#A9DFBF', percentage: 2.2 }
    ],
    '04': [
      { name: '会议参与', value: 145, color: '#00D4FF', percentage: 18.1 },
      { name: '实地调研', value: 132, color: '#FF6B6B', percentage: 16.5 },
      { name: '走访群众', value: 124, color: '#4ECDC4', percentage: 15.5 },
      { name: '议案提交', value: 88, color: '#45B7D1', percentage: 11.0 },
      { name: '建议办理', value: 75, color: '#96CEB4', percentage: 9.4 },
      { name: '监督检查', value: 67, color: '#FFEAA7', percentage: 8.4 },
      { name: '法律宣传', value: 52, color: '#DDA0DD', percentage: 6.5 },
      { name: '信访接待', value: 45, color: '#98D8C8', percentage: 5.6 },
      { name: '培训学习', value: 38, color: '#F7DC6F', percentage: 4.7 },
      { name: '联络活动', value: 35, color: '#BB8FCE', percentage: 4.4 },
      { name: '专题调研', value: 32, color: '#85C1E9', percentage: 4.0 },
      { name: '视察活动', value: 29, color: '#F8C471', percentage: 3.6 },
      { name: '座谈交流', value: 26, color: '#82E0AA', percentage: 3.2 },
      { name: '执法检查', value: 23, color: '#F1948A', percentage: 2.9 },
      { name: '民生走访', value: 20, color: '#D7BDE2', percentage: 2.5 },
      { name: '政策宣讲', value: 17, color: '#AED6F1', percentage: 2.1 },
      { name: '其他活动', value: 14, color: '#A9DFBF', percentage: 1.7 }
    ],
    '05': [
      { name: '会议参与', value: 172, color: '#00D4FF', percentage: 19.5 },
      { name: '实地调研', value: 158, color: '#FF6B6B', percentage: 17.9 },
      { name: '走访群众', value: 141, color: '#4ECDC4', percentage: 16.0 },
      { name: '议案提交', value: 98, color: '#45B7D1', percentage: 11.1 },
      { name: '建议办理', value: 84, color: '#96CEB4', percentage: 9.5 },
      { name: '监督检查', value: 73, color: '#FFEAA7', percentage: 8.3 },
      { name: '法律宣传', value: 59, color: '#DDA0DD', percentage: 6.7 },
      { name: '信访接待', value: 51, color: '#98D8C8', percentage: 5.8 },
      { name: '培训学习', value: 46, color: '#F7DC6F', percentage: 5.2 },
      { name: '联络活动', value: 42, color: '#BB8FCE', percentage: 4.8 },
      { name: '专题调研', value: 39, color: '#85C1E9', percentage: 4.4 },
      { name: '视察活动', value: 36, color: '#F8C471', percentage: 4.1 },
      { name: '座谈交流', value: 33, color: '#82E0AA', percentage: 3.7 },
      { name: '执法检查', value: 30, color: '#F1948A', percentage: 3.4 },
      { name: '民生走访', value: 27, color: '#D7BDE2', percentage: 3.1 },
      { name: '政策宣讲', value: 24, color: '#AED6F1', percentage: 2.7 },
      { name: '其他活动', value: 21, color: '#A9DFBF', percentage: 2.4 }
    ],
    '06': [
      { name: '会议参与', value: 163, color: '#00D4FF', percentage: 18.8 },
      { name: '实地调研', value: 149, color: '#FF6B6B', percentage: 17.2 },
      { name: '走访群众', value: 135, color: '#4ECDC4', percentage: 15.6 },
      { name: '议案提交', value: 91, color: '#45B7D1', percentage: 10.5 },
      { name: '建议办理', value: 79, color: '#96CEB4', percentage: 9.1 },
      { name: '监督检查', value: 69, color: '#FFEAA7', percentage: 8.0 },
      { name: '法律宣传', value: 56, color: '#DDA0DD', percentage: 6.5 },
      { name: '信访接待', value: 48, color: '#98D8C8', percentage: 5.5 },
      { name: '培训学习', value: 43, color: '#F7DC6F', percentage: 5.0 },
      { name: '联络活动', value: 39, color: '#BB8FCE', percentage: 4.5 },
      { name: '专题调研', value: 36, color: '#85C1E9', percentage: 4.2 },
      { name: '视察活动', value: 33, color: '#F8C471', percentage: 3.8 },
      { name: '座谈交流', value: 30, color: '#82E0AA', percentage: 3.5 },
      { name: '执法检查', value: 27, color: '#F1948A', percentage: 3.1 },
      { name: '民生走访', value: 24, color: '#D7BDE2', percentage: 2.8 },
      { name: '政策宣讲', value: 21, color: '#AED6F1', percentage: 2.4 },
      { name: '其他活动', value: 18, color: '#A9DFBF', percentage: 2.1 }
    ],
    '07': [
      { name: '会议参与', value: 178, color: '#00D4FF', percentage: 19.8 },
      { name: '实地调研', value: 164, color: '#FF6B6B', percentage: 18.2 },
      { name: '走访群众', value: 147, color: '#4ECDC4', percentage: 16.3 },
      { name: '议案提交', value: 102, color: '#45B7D1', percentage: 11.3 },
      { name: '建议办理', value: 87, color: '#96CEB4', percentage: 9.7 },
      { name: '监督检查', value: 75, color: '#FFEAA7', percentage: 8.3 },
      { name: '法律宣传', value: 61, color: '#DDA0DD', percentage: 6.8 },
      { name: '信访接待', value: 53, color: '#98D8C8', percentage: 5.9 },
      { name: '培训学习', value: 48, color: '#F7DC6F', percentage: 5.3 },
      { name: '联络活动', value: 44, color: '#BB8FCE', percentage: 4.9 },
      { name: '专题调研', value: 41, color: '#85C1E9', percentage: 4.6 },
      { name: '视察活动', value: 38, color: '#F8C471', percentage: 4.2 },
      { name: '座谈交流', value: 35, color: '#82E0AA', percentage: 3.9 },
      { name: '执法检查', value: 32, color: '#F1948A', percentage: 3.6 },
      { name: '民生走访', value: 29, color: '#D7BDE2', percentage: 3.2 },
      { name: '政策宣讲', value: 26, color: '#AED6F1', percentage: 2.9 },
      { name: '其他活动', value: 23, color: '#A9DFBF', percentage: 2.6 }
    ],
    '08': [
      { name: '会议参与', value: 151, color: '#00D4FF', percentage: 18.3 },
      { name: '实地调研', value: 138, color: '#FF6B6B', percentage: 16.7 },
      { name: '走访群众', value: 129, color: '#4ECDC4', percentage: 15.6 },
      { name: '议案提交', value: 86, color: '#45B7D1', percentage: 10.4 },
      { name: '建议办理', value: 74, color: '#96CEB4', percentage: 9.0 },
      { name: '监督检查', value: 66, color: '#FFEAA7', percentage: 8.0 },
      { name: '法律宣传', value: 54, color: '#DDA0DD', percentage: 6.5 },
      { name: '信访接待', value: 46, color: '#98D8C8', percentage: 5.6 },
      { name: '培训学习', value: 41, color: '#F7DC6F', percentage: 5.0 },
      { name: '联络活动', value: 37, color: '#BB8FCE', percentage: 4.5 },
      { name: '专题调研', value: 34, color: '#85C1E9', percentage: 4.1 },
      { name: '视察活动', value: 31, color: '#F8C471', percentage: 3.8 },
      { name: '座谈交流', value: 28, color: '#82E0AA', percentage: 3.4 },
      { name: '执法检查', value: 25, color: '#F1948A', percentage: 3.0 },
      { name: '民生走访', value: 22, color: '#D7BDE2', percentage: 2.7 },
      { name: '政策宣讲', value: 19, color: '#AED6F1', percentage: 2.3 },
      { name: '其他活动', value: 16, color: '#A9DFBF', percentage: 1.9 }
    ],
    '09': [
      { name: '会议参与', value: 185, color: '#00D4FF', percentage: 20.1 },
      { name: '实地调研', value: 171, color: '#FF6B6B', percentage: 18.6 },
      { name: '走访群众', value: 153, color: '#4ECDC4', percentage: 16.6 },
      { name: '议案提交', value: 105, color: '#45B7D1', percentage: 11.4 },
      { name: '建议办理', value: 89, color: '#96CEB4', percentage: 9.7 },
      { name: '监督检查', value: 77, color: '#FFEAA7', percentage: 8.4 },
      { name: '法律宣传', value: 63, color: '#DDA0DD', percentage: 6.8 },
      { name: '信访接待', value: 55, color: '#98D8C8', percentage: 6.0 },
      { name: '培训学习', value: 50, color: '#F7DC6F', percentage: 5.4 },
      { name: '联络活动', value: 46, color: '#BB8FCE', percentage: 5.0 },
      { name: '专题调研', value: 43, color: '#85C1E9', percentage: 4.7 },
      { name: '视察活动', value: 40, color: '#F8C471', percentage: 4.3 },
      { name: '座谈交流', value: 37, color: '#82E0AA', percentage: 4.0 },
      { name: '执法检查', value: 34, color: '#F1948A', percentage: 3.7 },
      { name: '民生走访', value: 31, color: '#D7BDE2', percentage: 3.4 },
      { name: '政策宣讲', value: 28, color: '#AED6F1', percentage: 3.0 },
      { name: '其他活动', value: 25, color: '#A9DFBF', percentage: 2.7 }
    ],
    '10': [
      { name: '会议参与', value: 192, color: '#00D4FF', percentage: 20.5 },
      { name: '实地调研', value: 178, color: '#FF6B6B', percentage: 19.0 },
      { name: '走访群众', value: 159, color: '#4ECDC4', percentage: 17.0 },
      { name: '议案提交', value: 108, color: '#45B7D1', percentage: 11.5 },
      { name: '建议办理', value: 92, color: '#96CEB4', percentage: 9.8 },
      { name: '监督检查', value: 79, color: '#FFEAA7', percentage: 8.4 },
      { name: '法律宣传', value: 65, color: '#DDA0DD', percentage: 6.9 },
      { name: '信访接待', value: 57, color: '#98D8C8', percentage: 6.1 },
      { name: '培训学习', value: 52, color: '#F7DC6F', percentage: 5.5 },
      { name: '联络活动', value: 48, color: '#BB8FCE', percentage: 5.1 },
      { name: '专题调研', value: 45, color: '#85C1E9', percentage: 4.8 },
      { name: '视察活动', value: 42, color: '#F8C471', percentage: 4.5 },
      { name: '座谈交流', value: 39, color: '#82E0AA', percentage: 4.2 },
      { name: '执法检查', value: 36, color: '#F1948A', percentage: 3.8 },
      { name: '民生走访', value: 33, color: '#D7BDE2', percentage: 3.5 },
      { name: '政策宣讲', value: 30, color: '#AED6F1', percentage: 3.2 },
      { name: '其他活动', value: 27, color: '#A9DFBF', percentage: 2.9 }
    ],
    '11': [
      { name: '会议参与', value: 174, color: '#00D4FF', percentage: 19.2 },
      { name: '实地调研', value: 161, color: '#FF6B6B', percentage: 17.8 },
      { name: '走访群众', value: 144, color: '#4ECDC4', percentage: 15.9 },
      { name: '议案提交', value: 96, color: '#45B7D1', percentage: 10.6 },
      { name: '建议办理', value: 83, color: '#96CEB4', percentage: 9.2 },
      { name: '监督检查', value: 72, color: '#FFEAA7', percentage: 8.0 },
      { name: '法律宣传', value: 58, color: '#DDA0DD', percentage: 6.4 },
      { name: '信访接待', value: 50, color: '#98D8C8', percentage: 5.5 },
      { name: '培训学习', value: 45, color: '#F7DC6F', percentage: 5.0 },
      { name: '联络活动', value: 41, color: '#BB8FCE', percentage: 4.5 },
      { name: '专题调研', value: 38, color: '#85C1E9', percentage: 4.2 },
      { name: '视察活动', value: 35, color: '#F8C471', percentage: 3.9 },
      { name: '座谈交流', value: 32, color: '#82E0AA', percentage: 3.5 },
      { name: '执法检查', value: 29, color: '#F1948A', percentage: 3.2 },
      { name: '民生走访', value: 26, color: '#D7BDE2', percentage: 2.9 },
      { name: '政策宣讲', value: 23, color: '#AED6F1', percentage: 2.5 },
      { name: '其他活动', value: 20, color: '#A9DFBF', percentage: 2.2 }
    ],
    '12': [
      { name: '会议参与', value: 198, color: '#00D4FF', percentage: 21.0 },
      { name: '实地调研', value: 184, color: '#FF6B6B', percentage: 19.5 },
      { name: '走访群众', value: 165, color: '#4ECDC4', percentage: 17.5 },
      { name: '议案提交', value: 112, color: '#45B7D1', percentage: 11.9 },
      { name: '建议办理', value: 95, color: '#96CEB4', percentage: 10.1 },
      { name: '监督检查', value: 81, color: '#FFEAA7', percentage: 8.6 },
      { name: '法律宣传', value: 67, color: '#DDA0DD', percentage: 7.1 },
      { name: '信访接待', value: 59, color: '#98D8C8', percentage: 6.3 },
      { name: '培训学习', value: 54, color: '#F7DC6F', percentage: 5.7 },
      { name: '联络活动', value: 50, color: '#BB8FCE', percentage: 5.3 },
      { name: '专题调研', value: 47, color: '#85C1E9', percentage: 5.0 },
      { name: '视察活动', value: 44, color: '#F8C471', percentage: 4.7 },
      { name: '座谈交流', value: 41, color: '#82E0AA', percentage: 4.3 },
      { name: '执法检查', value: 38, color: '#F1948A', percentage: 4.0 },
      { name: '民生走访', value: 35, color: '#D7BDE2', percentage: 3.7 },
      { name: '政策宣讲', value: 32, color: '#AED6F1', percentage: 3.4 },
      { name: '其他活动', value: 29, color: '#A9DFBF', percentage: 3.1 }
    ]
  }
}

// 模拟API请求延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 重写API函数以使用Mock数据
export const getRepresentativeDuties = async (params = {}) => {
  await delay(800) // 模拟网络延迟
  
  try {
    const currentDate = new Date()
    const year = params.year || currentDate.getFullYear().toString()
    const month = params.month || (currentDate.getMonth() + 1).toString().padStart(2, '0')
    
    console.log(`🏛️ 请求代表履职统计数据 - 年份: ${year}, 月份: ${month}`)
    
    // 获取指定年月的数据
    const yearData = mockDutiesData[year]
    if (!yearData) {
      throw new Error(`没有找到${year}年的数据`)
    }
    
    const monthData = yearData[month]
    if (!monthData) {
      throw new Error(`没有找到${year}年${month}月的数据`)
    }
    
    // 计算总数
    const totalCount = monthData.reduce((sum, item) => sum + item.value, 0)
    
    return {
      code: 200,
      message: '获取成功',
      data: monthData,
      totalCount,
      summary: {
        year,
        month,
        totalDuties: totalCount,
        dutyTypes: monthData.length,
        topDuty: monthData[0]
      },
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    console.error('❌ Mock API 错误:', error.message)
    return {
      code: 500,
      message: error.message,
      data: [],
      totalCount: 0
    }
  }
}

export default {
  getRepresentativeDuties,
  getDutyTypeDetail,
  getDutyTrend
} 