<template>
  <div class="chart4-container">
    <!-- 进度条部分 -->
    <!-- <div class="progress-row">
      <span class="label red">满意度</span>
      <div class="progress-bar">
        <div class="progress-fill red" :style="{ width: '95%' }"></div>
      </div>
      <span class="percent red">95%</span>
    </div>
    <div class="progress-row">
      <span class="label yellow">答复率</span>
      <div class="progress-bar">
        <div class="progress-fill yellow" :style="{ width: '90%' }"></div>
      </div>
      <span class="percent yellow">90%</span>
    </div> -->

    <!-- 建议列表部分 -->
    <div class="advice-container">
      <ul
        class="advice-list"
        ref="adviceList"
        @scroll="handleScroll"
        @wheel="handleWheel"
        @touchstart="handleTouch"
        @touchend="handleTouchEnd"
        @mousedown="handleMouseDown"
        @mouseup="handleMouseUp"
      >
        <li v-for="(item, idx) in data" :key="idx" @click="showAdviceDetail(item)" class="clickable-item">
          <span class="advice-content"> · {{ item.content }} </span>
          <div class="advice-info">
            <span class="advice-date">{{ item.date }}</span>
            <span class="advice-author">{{ item.author }}</span>
          </div>
        </li>
        <!-- 复制列表项实现无缝滚动 -->
        <li v-for="(item, idx) in data" :key="`duplicate-${idx}`" @click="showAdviceDetail(item)" class="clickable-item">
          <span class="advice-content"> · {{ item.content }} </span>
          <div class="advice-info">
            <span class="advice-date">{{ item.date }}</span>
            <span class="advice-author">{{ item.author }}</span>
          </div>
        </li>
      </ul>
    </div>

    <!-- 意见建议详情弹窗 -->
    <ModalDialog
      v-if="showModal"
      :visible="showModal"
      :title="'意见建议详情'"
      @close="closeModal"
    >
      <div class="advice-detail">
        <div class="detail-row">
          <span class="label">标题：</span>
          <span class="value">{{ selectedAdvice?.title || '无标题' }}</span>
        </div>
        <div class="detail-row">
          <span class="label">分类：</span>
          <span class="value">{{ selectedAdvice?.category || '未分类' }}</span>
        </div>
        <div class="detail-row">
          <span class="label">内容：</span>
          <span class="value content-text">{{ selectedAdvice?.original_content || selectedAdvice?.content }}</span>
        </div>
        <div class="detail-row">
          <span class="label">时间：</span>
          <span class="value">{{ selectedAdvice?.date }}</span>
        </div>
        <div class="detail-row">
          <span class="label">代表姓名：</span>
          <span class="value">{{ selectedAdvice?.author }}</span>
        </div>
      </div>
    </ModalDialog>
  </div>
</template>

<script>
import ModalDialog from '../common/ModalDialog.vue'

export default {
  components: {
    ModalDialog
  },
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isManualScrolling: false,
      scrollInterval: null,
      resumeTimeout: null,
      lastScrollTop: 0,
      isMouseDown: false,
      showModal: false,
      selectedAdvice: null,
    }
  },
  mounted() {
    this.startAutoScroll()
  },
  beforeUnmount() {
    this.stopAutoScroll()
    if (this.resumeTimeout) {
      clearTimeout(this.resumeTimeout)
    }
  },
  methods: {
    startAutoScroll() {
      // 清除可能存在的定时器
      this.stopAutoScroll()

      this.scrollInterval = setInterval(() => {
        // 如果正在手动滚动，暂停自动滚动
        if (this.isManualScrolling) {
          return
        }

        const list = this.$refs.adviceList
        if (list) {
          if (list.scrollTop >= list.scrollHeight / 2) {
            // 当滚动到一半时，重置到顶部实现无缝循环
            list.scrollTop = 0
          } else {
            list.scrollTop += 1
          }
        }
      }, 50) // 每50ms滚动1px，控制滚动速度
    },
    stopAutoScroll() {
      if (this.scrollInterval) {
        clearInterval(this.scrollInterval)
        this.scrollInterval = null
      }
    },
    pauseAutoScroll() {
      this.isManualScrolling = true
      this.stopAutoScroll()
    },
    resumeAutoScroll() {
      // 清除之前的恢复定时器
      if (this.resumeTimeout) {
        clearTimeout(this.resumeTimeout)
      }

      // 2秒后恢复自动滚动
      this.resumeTimeout = setTimeout(() => {
        this.isManualScrolling = false
        this.startAutoScroll()
      }, 2000)
    },
    handleScroll() {
      const list = this.$refs.adviceList
      if (!list) return

      // 检测是否为手动滚动（滚动位置发生变化）
      if (Math.abs(list.scrollTop - this.lastScrollTop) > 1) {
        this.pauseAutoScroll()
        this.resumeAutoScroll()
      }

      this.lastScrollTop = list.scrollTop
    },
    handleWheel() {
      // 鼠标滚轮事件
      this.pauseAutoScroll()
      this.resumeAutoScroll()
    },
    handleTouch() {
      // 触摸开始事件
      this.pauseAutoScroll()
    },
    handleTouchEnd() {
      // 触摸结束事件
      this.resumeAutoScroll()
    },
    handleMouseDown() {
      // 鼠标按下事件
      this.isMouseDown = true
      this.pauseAutoScroll()
    },
    handleMouseUp() {
      // 鼠标释放事件
      this.isMouseDown = false
      this.resumeAutoScroll()
    },
    showAdviceDetail(advice) {
      // 暂停自动滚动
      this.pauseAutoScroll()
      // 显示详情弹窗
      this.selectedAdvice = advice
      this.showModal = true
    },
    closeModal() {
      this.showModal = false
      this.selectedAdvice = null
      // 恢复自动滚动
      this.resumeAutoScroll()
    },
  },
}
</script>

<style scoped>
.chart4-container {
  /* background: #0a2343; */
  color: #ecefff;
  padding: 8px;
  border-radius: 10px;
  font-size: 13px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.progress-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.label {
  width: 60px;
}
.label.red {
  color: #ff4d4f;
}
.label.yellow {
  color: #ffd700;
}
.progress-bar {
  flex: 1;
  height: 8px;
  background: #23395d;
  border-radius: 4px;
  margin: 0 10px;
  overflow: hidden;
}
.progress-fill {
  height: 100%;
  border-radius: 4px;
}
.progress-fill.red {
  background: #ff4d4f;
}
.progress-fill.yellow {
  background: #ffd700;
}
.percent {
  width: 40px;
  text-align: right;
}
.percent.red {
  color: #ff4d4f;
}
.percent.yellow {
  color: #ffd700;
}
.advice-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}
.advice-list {
  /* margin-top: 16px; */
  background: rgba(20, 50, 90, 0.8);
  border-radius: 8px;
  padding: 0;
  list-style: none;
  margin: 0;
  height: 100%;
  overflow-y: auto;
  scroll-behavior: smooth;
}
.advice-list::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}
.advice-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  line-height: 18px;
  border-bottom: 1px solid #355992;
  position: relative;
  white-space: nowrap;
}
.advice-list li:last-child {
  border-bottom: none;
}
.advice-list li:hover {
  background: rgba(64, 128, 255, 0.12);
}

.clickable-item {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clickable-item:hover {
  background: rgba(64, 128, 255, 0.2) !important;
}
.advice-content {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.advice-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 16px;
  flex-shrink: 0;
}
.advice-date {
  color: #ecefff;
  font-size: 12px;
  margin-bottom: 2px;
}
.advice-author {
  color: #5b94de;
  font-size: 12px;
}

/* 弹窗内容样式 */
.advice-detail {
  padding: 0;
  color: #ffffff;
}

.detail-row {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid rgba(74, 144, 226, 0.2);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .label {
  font-weight: bold;
  min-width: 100px;
  color: #4a90e2;
  font-size: 14px;
}

.detail-row .value {
  flex: 1;
  word-break: break-word;
  line-height: 1.6;
  color: #ffffff;
  font-size: 14px;
}

.content-text {
  max-height: 150px;
  overflow-y: auto;
  padding: 12px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  border: 1px solid rgba(74, 144, 226, 0.3);
  color: #ffffff;
  line-height: 1.6;
}

.content-text::-webkit-scrollbar {
  width: 6px;
}

.content-text::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.content-text::-webkit-scrollbar-thumb {
  background: #4a90e2;
  border-radius: 3px;
}

.content-text::-webkit-scrollbar-thumb:hover {
  background: #357abd;
}
</style>
