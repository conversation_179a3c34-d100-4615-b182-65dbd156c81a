"""
测试AI配置的Django管理命令

使用方法：
python manage.py test_ai_config
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from api.aisummary.config import get_ai_config, validate_ai_config, should_use_real_ai
from api.aisummary.services import AIProviderService


class Command(BaseCommand):
    help = '测试AI总结服务配置'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--test-api',
            action='store_true',
            help='测试实际API调用（需要真实API密钥）'
        )
    
    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始测试AI配置...'))
        
        # 1. 检查配置
        self.stdout.write('\n1. 检查AI配置:')
        config = get_ai_config()
        for key, value in config.items():
            if 'api_key' in key.lower():
                # 隐藏API密钥的部分内容
                display_value = value[:10] + '...' + value[-4:] if len(value) > 14 else value
                self.stdout.write(f'  {key}: {display_value}')
            else:
                self.stdout.write(f'  {key}: {value}')
        
        # 2. 验证配置
        self.stdout.write('\n2. 验证配置:')
        errors = validate_ai_config()
        if errors:
            for error in errors:
                self.stdout.write(self.style.WARNING(f'  ⚠️  {error}'))
        else:
            self.stdout.write(self.style.SUCCESS('  ✅ 配置验证通过'))
        
        # 3. 检查是否使用真实AI
        self.stdout.write('\n3. AI服务模式:')
        if should_use_real_ai():
            self.stdout.write(self.style.SUCCESS('  ✅ 将使用真实AI服务'))
        else:
            self.stdout.write(self.style.WARNING('  ⚠️  将使用模拟AI服务'))
        
        # 4. 测试API调用（可选）
        if options['test_api'] and should_use_real_ai():
            self.stdout.write('\n4. 测试API调用:')
            self.test_api_call()
        else:
            self.stdout.write('\n4. 跳过API调用测试（使用 --test-api 参数启用）')
        
        self.stdout.write(self.style.SUCCESS('\n✅ AI配置测试完成'))
    
    def test_api_call(self):
        """测试实际的API调用"""
        try:
            # 创建测试数据
            test_data = {
                'representative_name': '测试代表',
                'representative_level': '区级',
                'analysis_year': timezone.now().year,
                'performance_records': {
                    'total_count': 5,
                    'type_statistics': [{'performance_type': '会议参与', 'count': 3}],
                    'status_statistics': [{'performance_status': '已完成', 'count': 5}],
                    'monthly_statistics': [{'month': i, 'count': 1} for i in range(1, 6)],
                    'record_details': []
                },
                'opinion_suggestions': {
                    'total_count': 2,
                    'category_statistics': [{'category': 'urban_construction', 'count': 2}],
                    'ai_assisted_count': 1,
                    'ai_assisted_rate': 50.0,
                    'monthly_statistics': [{'month': i, 'count': 0} for i in range(1, 13)],
                    'opinion_details': []
                },
                'total_activities': 7
            }
            
            self.stdout.write('  开始测试API调用...')
            
            # 创建AI服务实例
            ai_service = AIProviderService()
            
            # 测试数据准备
            inputs_data = ai_service._prepare_analysis_inputs(test_data)
            self.stdout.write(f'  ✅ 数据准备成功，包含 {len(inputs_data)} 个字段')
            
            # 测试查询构建
            query = ai_service._build_analysis_query(test_data)
            self.stdout.write(f'  ✅ 查询构建成功，长度: {len(query)} 字符')
            
            # 实际API调用
            self.stdout.write('  正在调用AI API...')
            result = ai_service._call_dify_api(test_data)
            
            if result:
                self.stdout.write(self.style.SUCCESS('  ✅ API调用成功'))
                self.stdout.write(f'  返回数据包含以下字段: {list(result.keys())}')
                
                # 检查必要字段
                required_fields = ['overview', 'coreMetrics', 'aiSummary']
                missing_fields = [field for field in required_fields if field not in result]
                if missing_fields:
                    self.stdout.write(self.style.WARNING(f'  ⚠️  缺少必要字段: {missing_fields}'))
                else:
                    self.stdout.write(self.style.SUCCESS('  ✅ 返回数据格式正确'))
            else:
                self.stdout.write(self.style.ERROR('  ❌ API调用返回空结果'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  ❌ API调用失败: {str(e)}'))
            self.stdout.write('  建议检查API密钥和网络连接') 