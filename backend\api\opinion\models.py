"""
意见建议管理数据模型

包含以下模型：
1. OpinionSuggestion - 意见建议基础信息模型
2. OpinionReview - 意见审核记录模型

设计理念：
- OpinionSuggestion只保存基础信息，不含状态字段
- OpinionReview记录每次状态变更和审核动作
- 当前状态通过查询最新审核记录获得
"""

from django.db import models
from django.core.validators import RegexValidator
from django.utils import timezone
from api.users.models import Representative, StaffMember


class OpinionSuggestion(models.Model):
    """
    意见建议基础信息模型
    对应数据库设计中的opinion_suggestions表
    """
    
    # 意见分类选择（16个分类，涵盖各个方面）
    CATEGORY_CHOICES = [
        ('urban_construction', '城建环保'),
        ('transportation', '交通出行'),
        ('education', '教育文化'),
        ('healthcare', '医疗卫生'),
        ('social_security', '社会保障'),
        ('economic', '经济发展'),
        ('government_service', '政务服务'),
        ('public_safety', '公共安全'),
        ('community_service', '社区服务'),
        ('housing', '住房保障'),
        ('employment', '就业创业'),
        ('elderly_care', '养老服务'),
        ('food_safety', '食品安全'),
        ('cultural_sports', '文体娱乐'),
        ('digital_governance', '数字政务'),
        ('other', '其他'),
    ]
    
    # 主键ID
    id = models.BigAutoField(
        primary_key=True,
        verbose_name='意见建议ID'
    )
    
    # 提交代表ID（外键关联）
    representative = models.ForeignKey(
        Representative,
        on_delete=models.CASCADE,
        verbose_name='提交代表',
        help_text='提交此意见建议的人大代表'
    )
    
    # 意见建议标题
    title = models.CharField(
        max_length=200,
        verbose_name='意见建议标题',
        help_text='意见建议的标题，最多200个字符'
    )
    
    # 意见建议分类
    category = models.CharField(
        max_length=50,
        choices=CATEGORY_CHOICES,
        verbose_name='意见建议分类',
        help_text='意见建议的分类类型'
    )
    
    # 反映人姓名
    reporter_name = models.CharField(
        max_length=100,
        verbose_name='反映人姓名',
        help_text='提出意见的群众姓名'
    )
    
    # 原始意见内容
    original_content = models.TextField(
        verbose_name='原始意见内容',
        help_text='群众反映的原始意见内容'
    )
    
    # 最终建议内容
    final_suggestion = models.TextField(
        null=True,
        blank=True,
        verbose_name='最终建议内容',
        help_text='代表最终确认的建议内容'
    )
    
    # 是否使用AI辅助
    ai_assisted = models.BooleanField(
        default=False,
        verbose_name='是否使用AI辅助',
        help_text='标识是否使用了AI辅助生成功能'
    )
    
    # AI生成的建议内容
    ai_generated_content = models.TextField(
        null=True,
        blank=True,
        verbose_name='AI生成的建议内容',
        help_text='AI辅助生成的建议内容'
    )
    
    # 创建时间
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )
    
    # 更新时间
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    class Meta:
        db_table = 'opinion_suggestions'
        verbose_name = '意见建议'
        verbose_name_plural = '意见建议'
        indexes = [
            models.Index(fields=['representative']),
            models.Index(fields=['category']),
            models.Index(fields=['created_at']),
            models.Index(fields=['ai_assisted']),
        ]
        ordering = ['-created_at']  # 默认按创建时间倒序
    
    def __str__(self):
        return f"{self.title} - {self.representative.name}"
    
    @property
    def current_status(self):
        """
        获取当前状态
        通过查询ID最大的审核记录获得（ID是自增的，最大ID即最新记录）
        """
        latest_review = self.reviews.order_by('-id').first()
        if latest_review:
            return latest_review.status
        return 'draft'  # 默认为草稿状态
    
    @property
    def current_review(self):
        """
        获取最新的审核记录
        使用ID排序获取最新记录（ID是自增的，最大ID即最新记录）
        """
        return self.reviews.order_by('-id').first()
    
    def get_category_display_name(self):
        """获取分类显示名称"""
        return dict(self.CATEGORY_CHOICES).get(self.category, self.category)


class OpinionReview(models.Model):
    """
    意见审核记录模型
    对应数据库设计中的opinion_reviews表
    记录意见建议的所有审核动作和状态变更
    """
    
    # 操作动作选择
    ACTION_CHOICES = [
        ('create', '创建草稿'),
        ('submit', '代表提交'),
        ('approve', '审核通过'),
        ('reject', '审核驳回'),
        ('transfer', '标记转交'),
        ('update_progress', '更新进展'),
        ('close', '标记办结'),
    ]
    
    # 状态选择
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('submitted', '已提交'),
        ('under_review', '待审核'),
        ('approved', '审核通过'),
        ('rejected', '审核驳回'),
        ('transferred', '已转交'),
        ('in_progress', '处理中'),
        ('completed', '已办结'),
    ]
    
    # 主键ID
    id = models.BigAutoField(
        primary_key=True,
        verbose_name='审核记录ID'
    )
    
    # 关联意见建议表
    opinion = models.ForeignKey(
        OpinionSuggestion,
        on_delete=models.CASCADE,
        related_name='reviews',
        verbose_name='关联意见建议',
        help_text='此审核记录对应的意见建议'
    )
    
    # 审核人ID（工作人员，可为空表示系统操作）
    reviewer = models.ForeignKey(
        StaffMember,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='审核人',
        help_text='执行此操作的工作人员，为空表示系统操作'
    )
    
    # 操作动作
    action = models.CharField(
        max_length=50,
        choices=ACTION_CHOICES,
        verbose_name='操作动作',
        help_text='执行的具体操作类型'
    )
    
    # 操作后状态
    status = models.CharField(
        max_length=50,
        choices=STATUS_CHOICES,
        verbose_name='操作后状态',
        help_text='执行操作后的状态'
    )
    
    # 转交部门名称
    transferred_department = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name='转交部门名称',
        help_text='转交的政府职能部门名称'
    )
    
    # 审核备注/驳回理由
    review_comment = models.TextField(
        null=True,
        blank=True,
        verbose_name='审核备注',
        help_text='审核备注或驳回理由'
    )
    
    # 处理结果描述
    processing_result = models.TextField(
        null=True,
        blank=True,
        verbose_name='处理结果描述',
        help_text='部门处理结果的详细描述'
    )
    
    # 附件文件路径（JSON格式存储）
    attachments = models.TextField(
        null=True,
        blank=True,
        verbose_name='附件路径',
        help_text='相关附件的文件路径，JSON格式存储'
    )
    
    # 操作时间
    action_time = models.DateTimeField(
        default=timezone.now,
        verbose_name='操作时间',
        help_text='执行此操作的时间'
    )
    
    # 创建时间
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )
    
    class Meta:
        db_table = 'opinion_reviews'
        verbose_name = '意见审核记录'
        verbose_name_plural = '意见审核记录'
        indexes = [
            models.Index(fields=['opinion']),
            models.Index(fields=['reviewer']),
            models.Index(fields=['status']),
            models.Index(fields=['action_time']),
            models.Index(fields=['opinion', 'action_time']),  # 复合索引
        ]
        ordering = ['-action_time']  # 默认按操作时间倒序
    
    def __str__(self):
        reviewer_name = self.reviewer.name if self.reviewer else '系统'
        return f"{self.opinion.title} - {self.get_action_display()} ({reviewer_name})"
    
    def save(self, *args, **kwargs):
        """
        保存时验证action与status的映射关系
        确保数据一致性
        """
        # 定义action与status的映射关系
        ACTION_STATUS_MAP = {
            'create': 'draft',
            'submit': 'submitted',
            'approve': 'approved',
            'reject': 'rejected',
            'transfer': 'transferred',
            'update_progress': 'in_progress',
            'close': 'completed'
        }
        
        # 验证映射关系（只有当action在映射表中且当前状态与期望不符时才修正）
        expected_status = ACTION_STATUS_MAP.get(self.action)
        if expected_status and self.status != expected_status:
            # 对于create动作，允许直接使用draft状态
            if self.action == 'create' and self.status == 'draft':
                pass  # 保持draft状态
            else:
                self.status = expected_status
        
        super().save(*args, **kwargs)
    
    @property
    def action_display_name(self):
        """获取操作动作显示名称"""
        return dict(self.ACTION_CHOICES).get(self.action, self.action)
    
    @property
    def status_display_name(self):
        """获取状态显示名称"""
        return dict(self.STATUS_CHOICES).get(self.status, self.status)
