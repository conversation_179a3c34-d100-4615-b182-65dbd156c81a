"""
代表AI总结API视图

包含以下API端点：
1. AISummaryGenerateView - 生成AI总结
2. AISummaryDetailView - 获取AI总结详情
3. AISummaryCheckDataView - 检查数据可用性
4. AISummaryListView - AI总结列表（工作人员查看）

设计原则：
- 遵循现有应用的视图风格
- 严格的权限控制
- 完善的错误处理和日志记录
- RESTful API设计
"""

import logging
from django.utils import timezone
from django.db.models import Q
from django.shortcuts import get_object_or_404
from django.http import Http404
from rest_framework import status, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .models import RepresentativeAISummary
from .serializers import (
    RepresentativeAISummarySerializer,
    AISummaryListSerializer,
    AISummaryGenerateRequestSerializer,
    AISummaryGenerateResponseSerializer,
    AISummaryCheckDataSerializer
)
from .services import AISummaryService
from api.users.permissions import IsRepresentative, IsStaff

# 设置日志
logger = logging.getLogger(__name__)


class AISummaryGenerateView(APIView):
    """
    生成AI总结API
    代表可以为自己生成AI总结，工作人员可以为指定代表生成AI总结
    支持首次生成和重新生成（通过force_regenerate参数控制）
    """
    
    permission_classes = [IsRepresentative | IsStaff]
    
    @swagger_auto_schema(
        operation_summary="生成AI总结",
        operation_description="为指定代表生成指定年份的AI履职总结，支持首次生成和重新生成。代表只能为自己生成，工作人员可以为任意代表生成",
        request_body=AISummaryGenerateRequestSerializer,
        responses={
            201: AISummaryGenerateResponseSerializer,
            400: "参数错误",
            403: "权限不足",
            409: "已存在生成中的任务"
        },
        tags=['AI总结']
    )
    def post(self, request):
        """生成AI总结（包含重新生成功能）"""
        try:
            # 验证请求数据
            serializer = AISummaryGenerateRequestSerializer(
                data=request.data,
                context={'request': request}
            )
            if not serializer.is_valid():
                logger.warning(f"AI总结生成请求参数无效：{serializer.errors}")
                return Response({
                    'error': '请求参数无效',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取参数
            analysis_year = serializer.validated_data['analysis_year']
            force_regenerate = serializer.validated_data.get('force_regenerate', False)
            
            # 确定目标代表
            if hasattr(request.user, 'representative'):
                # 代表用户，只能为自己生成
                representative = request.user.representative
                operator_info = f"代表 {representative.name}"
            elif hasattr(request.user, 'staffmember'):
                # 工作人员，可以为指定代表生成
                representative_id = serializer.validated_data.get('representative_id')
                if not representative_id:
                    return Response({
                        'error': '工作人员生成AI总结需要提供representative_id参数'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                try:
                    from api.users.models import Representative
                    representative = Representative.objects.get(id=representative_id)
                    # 获取工作人员姓名用于日志记录
                    staff_name = getattr(request.user, 'staffmember', None)
                    staff_name = staff_name.name if staff_name else request.user.username
                    operator_info = f"工作人员 {staff_name} 为代表 {representative.name}"
                except Representative.DoesNotExist:
                    return Response({
                        'error': '指定的代表不存在'
                    }, status=status.HTTP_404_NOT_FOUND)
            else:
                return Response({
                    'error': '用户角色错误'
                }, status=status.HTTP_403_FORBIDDEN)
            
            logger.info(f"{operator_info} 请求{'重新' if force_regenerate else ''}生成 {analysis_year}年 AI总结")
            
            # 调用服务生成AI总结
            ai_summary = AISummaryService.generate_summary(
                representative=representative,
                analysis_year=analysis_year,
                force_regenerate=force_regenerate
            )
            
            # 构造响应数据
            response_data = {
                'id': ai_summary.id,
                'analysis_year': ai_summary.analysis_year,
                'status': ai_summary.status,
                'status_display': ai_summary.status_display,
                'created_at': ai_summary.created_at,
            }
            
            if ai_summary.is_generating:
                response_data.update({
                    'message': f'AI总结正在{"重新" if force_regenerate else ""}生成中，请稍后查询结果',
                    'estimated_duration': 30  # 预估30秒
                })
                response_status = status.HTTP_202_ACCEPTED
            elif ai_summary.is_completed:
                response_data['message'] = f'AI总结{"重新" if force_regenerate else ""}生成完成'
                response_status = status.HTTP_201_CREATED
            elif ai_summary.is_failed:
                response_data['message'] = f'AI总结生成失败：{ai_summary.error_message}'
                response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            else:
                response_data['message'] = 'AI总结已创建，等待生成'
                response_status = status.HTTP_201_CREATED
            
            # 序列化响应
            response_serializer = AISummaryGenerateResponseSerializer(response_data)
            
            logger.info(f"AI总结{'重新' if force_regenerate else ''}生成请求处理完成，状态：{ai_summary.status}")
            return Response(response_serializer.data, status=response_status)
            
        except Exception as e:
            error_message = str(e)
            logger.error(f"AI总结生成异常：{error_message}")
            
            # 针对数据不足的情况，返回更友好的错误信息
            if "数据不足" in error_message:
                return Response({
                    'success': False,
                    'error': 'data_insufficient',
                    'message': f'很抱歉，{representative.name}在{analysis_year}年暂无履职记录和意见建议数据，无法生成AI分析报告。请选择其他年份或等待该代表产生履职数据后再试。',
                    'details': {
                        'representative_name': representative.name,
                        'analysis_year': analysis_year,
                        'suggestion': '请选择其他年份或等待履职数据产生'
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 其他错误保持原有处理方式
            return Response({
                'success': False,
                'error': 'generation_failed',
                'message': 'AI总结生成失败，请稍后重试',
                'details': error_message
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RepresentativesWorkSummaryView(APIView):
    """
    代表工作总结API（工作人员专用）
    工作人员可以查看本站点所有代表的AI总结状态和生成总结
    """
    
    permission_classes = [IsStaff]
    
    @swagger_auto_schema(
        operation_summary="获取代表工作总结列表",
        operation_description="工作人员查看本站点所有代表的AI总结状态，用于代表工作总结功能",
        manual_parameters=[
            openapi.Parameter(
                'year',
                openapi.IN_QUERY,
                description="分析年份",
                type=openapi.TYPE_INTEGER,
                required=True
            )
        ],
        responses={
            200: openapi.Response(
                description="代表工作总结列表",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'data': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='代表ID'),
                                    'name': openapi.Schema(type=openapi.TYPE_STRING, description='代表姓名'),
                                    'level': openapi.Schema(type=openapi.TYPE_STRING, description='代表层级'),
                                    'department': openapi.Schema(type=openapi.TYPE_STRING, description='所属部门'),
                                    'phone': openapi.Schema(type=openapi.TYPE_STRING, description='联系电话'),
                                    'recordCount': openapi.Schema(type=openapi.TYPE_INTEGER, description='履职记录数量'),
                                    'opinionCount': openapi.Schema(type=openapi.TYPE_INTEGER, description='意见建议数量'),
                                    'analysisStatus': openapi.Schema(type=openapi.TYPE_STRING, description='分析状态'),
                                    'lastAnalysisTime': openapi.Schema(type=openapi.TYPE_STRING, description='最后分析时间')
                                }
                            )
                        )
                    }
                )
            ),
            400: "年份参数无效",
            403: "权限不足"
        },
        tags=['工作分析']
    )
    def get(self, request):
        """获取代表工作总结列表"""
        try:
            # 获取年份参数
            year = request.query_params.get('year')
            if not year:
                return Response({
                    'success': False,
                    'message': '年份参数不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                year = int(year)
            except ValueError:
                return Response({
                    'success': False,
                    'message': '年份参数必须是数字'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取所有代表
            from api.users.models import Representative
            representatives = Representative.objects.select_related('user').all()
            
            # 获取代表AI总结状态
            result_data = []
            for rep in representatives:
                try:
                    # 查询AI总结状态
                    ai_summary = RepresentativeAISummary.objects.filter(
                        representative=rep,
                        analysis_year=year
                    ).first()
                    
                    # 获取履职记录数量
                    from api.performance.models import PerformanceRecord
                    record_count = PerformanceRecord.objects.filter(
                        representative=rep,
                        performance_date__year=year
                    ).count()
                    
                    # 获取意见建议数量
                    from api.opinion.models import OpinionSuggestion
                    opinion_count = OpinionSuggestion.objects.filter(
                        representative=rep,
                        created_at__year=year
                    ).count()
                    
                    representative_data = {
                        'id': rep.id,
                        'name': rep.name,
                        'level': rep.level,
                        'department': rep.current_position,  # 使用现任职务作为部门信息
                        'phone': rep.mobile_phone,
                        'avatar': rep.avatar,  # 添加头像字段
                        'recordCount': record_count,
                        'opinionCount': opinion_count,
                        'analysisStatus': 'analyzed' if ai_summary and ai_summary.is_completed else 'not_analyzed',
                        'lastAnalysisTime': ai_summary.completed_at.strftime('%Y-%m-%d %H:%M:%S') if ai_summary and ai_summary.completed_at else None
                    }
                    
                    result_data.append(representative_data)
                    
                except Exception as e:
                    logger.error(f"获取代表 {rep.name} 工作总结数据失败：{str(e)}")
                    # 继续处理其他代表，不中断整个流程
                    continue
            
            # 获取工作人员姓名用于日志记录
            staff_name = getattr(request.user, 'staffmember', None)
            staff_name = staff_name.name if staff_name else request.user.username
            logger.info(f"工作人员 {staff_name} 查看 {year}年 代表工作总结列表，共 {len(result_data)} 位代表")
            
            return Response({
                'success': True,
                'data': result_data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取代表工作总结列表异常：{str(e)}")
            return Response({
                'success': False,
                'message': '获取代表工作总结列表失败',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AISummaryDetailView(APIView):
    """
    获取AI总结详情API
    代表可以查看自己的AI总结，工作人员可以查看所有AI总结
    """
    
    permission_classes = [IsRepresentative | IsStaff]
    
    @swagger_auto_schema(
        operation_summary="获取AI总结详情",
        operation_description="获取指定年份的AI总结详情",
        manual_parameters=[
            openapi.Parameter(
                'representative_id',
                openapi.IN_QUERY,
                description="代表ID（工作人员查询时使用）",
                type=openapi.TYPE_INTEGER,
                required=False
            )
        ],
        responses={
            200: RepresentativeAISummarySerializer,
            404: "AI总结不存在",
            403: "权限不足"
        },
        tags=['AI总结']
    )
    def get(self, request, year):
        """获取AI总结详情"""
        try:
            # 权限控制
            if hasattr(request.user, 'representative'):
                # 代表用户，只能查看自己的AI总结
                representative = request.user.representative
            elif hasattr(request.user, 'staffmember'):
                # 工作人员，可以查看指定代表的AI总结
                representative_id = request.query_params.get('representative_id')
                if not representative_id:
                    return Response({
                        'error': '工作人员查询需要提供representative_id参数'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                try:
                    from api.users.models import Representative
                    representative = Representative.objects.get(id=representative_id)
                except Representative.DoesNotExist:
                    return Response({
                        'error': '指定的代表不存在'
                    }, status=status.HTTP_404_NOT_FOUND)
            else:
                return Response({
                    'error': '权限不足'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # 查询AI总结
            try:
                ai_summary = RepresentativeAISummary.objects.get(
                    representative=representative,
                    analysis_year=year
                )
            except RepresentativeAISummary.DoesNotExist:
                logger.warning(f"AI总结不存在，代表：{representative.name}，年份：{year}")
                return Response({
                    'success': False,
                    'message': f'{year}年的AI总结尚未生成',
                    'error': 'AI总结不存在'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # 只返回AI分析报告数据，用于前端展示
            logger.info(f"获取AI总结详情成功：{ai_summary.id}")
            return Response({
                'success': True,
                'data': {
                    'representative_id': representative.id,
                    'representative_name': representative.name,
                    'analysis_year': year,
                    'ai_result_data': ai_summary.get_ai_result_data(),
                    'is_completed': ai_summary.is_completed,
                    'completed_at': ai_summary.completed_at.strftime('%Y-%m-%d %H:%M:%S') if ai_summary.completed_at else None,
                    # 添加完整的代表信息供AIReportRenderer使用
                    'representative_info': {
                        'name': representative.name,
                        'level': representative.level,
                        'mobile_phone': representative.mobile_phone,
                        'current_position': representative.current_position,
                        'avatar': representative.avatar  # 添加头像字段
                    }
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取AI总结详情异常：{str(e)}")
            return Response({
                'error': '获取AI总结详情失败',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AISummaryCheckDataView(APIView):
    """
    检查数据可用性API
    代表可以检查自己指定年份的数据情况
    """
    
    permission_classes = [IsRepresentative]
    
    @swagger_auto_schema(
        operation_summary="检查数据可用性",
        operation_description="检查指定年份的履职数据是否足够生成AI总结",
        manual_parameters=[
            openapi.Parameter(
                'year',
                openapi.IN_PATH,
                description="分析年份",
                type=openapi.TYPE_INTEGER,
                required=True
            )
        ],
        responses={
            200: AISummaryCheckDataSerializer,
            400: "年份参数无效",
            403: "权限不足"
        },
        tags=['AI总结']
    )
    def get(self, request, year):
        """检查数据可用性"""
        try:
            # 验证年份参数
            current_year = timezone.now().year
            if year < 2020 or year > current_year:
                return Response({
                    'error': '年份参数无效',
                    'details': f'年份必须在2020到{current_year}之间'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            representative = request.user.representative
            
            logger.info(f"检查代表 {representative.name} {year}年 数据可用性")
            
            # 检查数据
            check_result = AISummaryService.check_data_availability(
                representative=representative,
                analysis_year=year
            )
            
            # 序列化响应
            serializer = AISummaryCheckDataSerializer(check_result)
            
            logger.info(f"数据检查完成：{check_result['message']}")
            return Response(serializer.data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"数据检查异常：{str(e)}")
            return Response({
                'error': '数据检查失败',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AISummaryListView(APIView):
    """
    AI总结列表API
    工作人员可以查看所有代表的AI总结列表
    代表可以查看自己的AI总结历史
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    @swagger_auto_schema(
        operation_summary="获取AI总结列表",
        operation_description="获取AI总结列表，支持分页和筛选",
        manual_parameters=[
            openapi.Parameter(
                'page',
                openapi.IN_QUERY,
                description="页码，默认为1",
                type=openapi.TYPE_INTEGER,
                required=False
            ),
            openapi.Parameter(
                'page_size',
                openapi.IN_QUERY,
                description="每页数量，默认为20",
                type=openapi.TYPE_INTEGER,
                required=False
            ),
            openapi.Parameter(
                'year',
                openapi.IN_QUERY,
                description="筛选年份",
                type=openapi.TYPE_INTEGER,
                required=False
            ),
            openapi.Parameter(
                'status',
                openapi.IN_QUERY,
                description="筛选状态",
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'representative_id',
                openapi.IN_QUERY,
                description="筛选代表ID（仅工作人员可用）",
                type=openapi.TYPE_INTEGER,
                required=False
            )
        ],
        responses={
            200: openapi.Response(
                description="AI总结列表",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'count': openapi.Schema(type=openapi.TYPE_INTEGER, description='总数量'),
                        'next': openapi.Schema(type=openapi.TYPE_STRING, description='下一页URL'),
                        'previous': openapi.Schema(type=openapi.TYPE_STRING, description='上一页URL'),
                        'results': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(type=openapi.TYPE_OBJECT)
                        )
                    }
                )
            ),
            403: "权限不足"
        },
        tags=['AI总结']
    )
    def get(self, request):
        """获取AI总结列表"""
        try:
            # 基础查询
            queryset = RepresentativeAISummary.objects.select_related('representative__user')
            
            # 权限控制
            if request.user.role == 'representative':
                # 代表只能查看自己的
                queryset = queryset.filter(representative=request.user.representative)
            elif request.user.role == 'staff':
                # 工作人员可以查看所有，支持按代表筛选
                representative_id = request.query_params.get('representative_id')
                if representative_id:
                    queryset = queryset.filter(representative_id=representative_id)
            else:
                return Response({
                    'error': '权限不足'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # 筛选条件
            year = request.query_params.get('year')
            if year:
                queryset = queryset.filter(analysis_year=year)
            
            status_filter = request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)
            
            # 排序
            queryset = queryset.order_by('-analysis_year', '-created_at')
            
            # 分页
            page = int(request.query_params.get('page', 1))
            page_size = min(int(request.query_params.get('page_size', 20)), 100)
            
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            
            total_count = queryset.count()
            results = queryset[start_index:end_index]
            
            # 序列化数据
            serializer = AISummaryListSerializer(results, many=True)
            
            # 构造分页响应
            response_data = {
                'count': total_count,
                'next': None,
                'previous': None,
                'results': serializer.data
            }
            
            # 计算分页链接
            if end_index < total_count:
                next_page = page + 1
                response_data['next'] = f"?page={next_page}&page_size={page_size}"
                
            if page > 1:
                prev_page = page - 1
                response_data['previous'] = f"?page={prev_page}&page_size={page_size}"
            
            logger.info(f"查询AI总结列表成功，返回 {len(results)} 条记录")
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"查询AI总结列表异常：{str(e)}")
            return Response({
                'error': '查询失败',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
