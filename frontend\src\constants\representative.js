/**
 * 人大代表相关配置常量
 */

// 代表层级选项
export const REPRESENTATIVE_LEVELS = [
  { label: '乡镇人大代表', value: '乡镇人大代表' },
  { label: '县区人大代表', value: '县区人大代表' },
  { label: '市人大代表', value: '市人大代表' },
  { label: '自治区人大代表', value: '自治区人大代表' },
  { label: '全国人大代表', value: '全国人大代表' }
]

// 代表构成选项
export const REPRESENTATIVE_COMPOSITIONS = [
  // 基层代表5种
  { label: '一线工人', value: '一线工人' },
  { label: '农民', value: '农民' },
  { label: '村委会村党支部组成人员', value: '村委会村党支部组成人员' },
  { label: '专业技术人员', value: '专业技术人员' },
  { label: '其他基层代表', value: '其他基层代表' },
  // 其他6种
  { label: '公务员', value: '公务员' },
  { label: '国有和集体企业负责人', value: '国有和集体企业负责人' },
  { label: '非共有制经济人士', value: '非共有制经济人士' },
  { label: '事业单位负责人', value: '事业单位负责人' },
  { label: '解放军和武警部队', value: '解放军和武警部队' },
  { label: '其他', value: '其他' }
]

// 所属片区选项
export const DISTRICT_OPTIONS = [
  { label: '那洪片区', value: '那洪片区' },
  { label: '那历片区', value: '那历片区' },
  { label: '沛鸿片区', value: '沛鸿片区' }
]

// 性别选项
export const GENDER_OPTIONS = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' }
]

/**
 * 层级数据处理工具函数
 */

// 将层级字符串转换为数组
export const parseLevelString = (levelString) => {
  if (!levelString) return []
  return levelString.split(',').map(level => level.trim()).filter(level => level)
}

// 将层级数组转换为字符串
export const formatLevelArray = (levelArray) => {
  if (!Array.isArray(levelArray)) return ''
  return levelArray.filter(level => level).join(',')
}

// 获取层级显示文本（用于界面显示）
export const getLevelDisplayText = (levelString) => {
  const levels = parseLevelString(levelString)
  return levels.length > 0 ? levels.join('、') : '未设置'
}

// 验证层级选择是否有效
export const validateLevels = (levelArray) => {
  if (!Array.isArray(levelArray) || levelArray.length === 0) {
    return { valid: false, message: '请至少选择一个代表层级' }
  }

  const validLevels = REPRESENTATIVE_LEVELS.map(item => item.value)
  const invalidLevels = levelArray.filter(level => !validLevels.includes(level))

  if (invalidLevels.length > 0) {
    return { valid: false, message: `无效的层级选择: ${invalidLevels.join('、')}` }
  }

  // 检查重复选择
  const uniqueLevels = [...new Set(levelArray)]
  if (uniqueLevels.length !== levelArray.length) {
    return { valid: false, message: '不能重复选择相同的层级' }
  }

  return { valid: true, message: '验证通过' }
}

/**
 * 构成数据处理工具函数
 */

// 将构成字符串转换为数组
export const parseCompositionString = (compositionString) => {
  if (!compositionString) return []
  return compositionString.split(',').map(comp => comp.trim()).filter(comp => comp)
}

// 将构成数组转换为字符串
export const formatCompositionArray = (compositionArray) => {
  if (!Array.isArray(compositionArray)) return ''
  return compositionArray.filter(comp => comp).join(',')
}

// 获取构成显示文本（用于界面显示）
export const getCompositionDisplayText = (compositionString) => {
  const compositions = parseCompositionString(compositionString)
  return compositions.length > 0 ? compositions.join('、') : '未设置'
}

// 验证构成选择是否有效
export const validateCompositions = (compositionArray) => {
  // 构成字段可以为空
  if (!Array.isArray(compositionArray) || compositionArray.length === 0) {
    return { valid: true, message: '验证通过' }
  }

  const validCompositions = REPRESENTATIVE_COMPOSITIONS.map(item => item.value)
  const invalidCompositions = compositionArray.filter(comp => !validCompositions.includes(comp))

  if (invalidCompositions.length > 0) {
    return { valid: false, message: `无效的构成选择: ${invalidCompositions.join('、')}` }
  }

  // 检查重复选择
  const uniqueCompositions = [...new Set(compositionArray)]
  if (uniqueCompositions.length !== compositionArray.length) {
    return { valid: false, message: '不能重复选择相同的构成' }
  }

  return { valid: true, message: '验证通过' }
}

// 民族选项
export const NATIONALITY_OPTIONS = [
  '汉族', '蒙古族', '回族', '藏族', '维吾尔族', '苗族', '彝族', '壮族', '布依族', '朝鲜族',
  '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族', '哈萨克族', '傣族', '黎族', '傈僳族',
  '佤族', '畲族', '高山族', '拉祜族', '水族', '东乡族', '纳西族', '景颇族', '柯尔克孜族',
  '土族', '达斡尔族', '仫佬族', '羌族', '布朗族', '撒拉族', '毛南族', '仡佬族', '锡伯族',
  '阿昌族', '普米族', '塔吉克族', '怒族', '乌孜别克族', '俄罗斯族', '鄂温克族', '德昂族',
  '保安族', '裕固族', '京族', '塔塔尔族', '独龙族', '鄂伦春族', '赫哲族', '门巴族',
  '珞巴族', '基诺族'
]

// 党派选项
export const PARTY_OPTIONS = [
  '中国共产党', '中国国民党革命委员会', '中国民主同盟', '中国民主建国会', '中国民主促进会',
  '中国农工民主党', '中国致公党', '九三学社', '台湾民主自治同盟', '群众'
]

// 学历选项
export const EDUCATION_OPTIONS = [
  '博士研究生', '硕士研究生', '大学本科', '大学专科', '中等专业学校', '技工学校',
  '高中', '初中', '小学', '其他'
]

// 工作人员职位选项
export const STAFF_POSITION_OPTIONS = [
  { label: '站点主任', value: '站点主任' },
  { label: '副站长', value: '副站长' },
  { label: '工作人员', value: '工作人员' },
  { label: '联络员', value: '联络员' },
  { label: '秘书', value: '秘书' }
]
