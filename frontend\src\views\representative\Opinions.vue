<template>
  <div class="opinions-container">
    <div class="page-header">
      <h2>意见建议</h2>
      <p>收集、处理和跟踪群众意见建议</p>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <div>
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          录入新意见建议
        </el-button>
        <el-button @click="loadOpinions" :loading="loading">
          刷新列表
        </el-button>
      </div>
      <div class="filter-group">
        <span>总数: {{ total }}</span>
        <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;" @change="loadOpinions">
          <el-option label="全部" value="" />
          <el-option 
            v-for="option in opinionUtils.getStatusOptions()" 
            :key="option.value"
            :label="option.label" 
            :value="option.value" 
          />
        </el-select>
      </div>
    </div>



    <!-- 意见列表 -->
    <el-card>
      <el-table :data="opinionsList" v-loading="loading">
        <template #empty>
          <div style="padding: 40px;">
            <p v-if="!loading">暂无意见建议数据</p>
            <p v-if="!loading">点击"录入新意见建议"按钮开始添加</p>
          </div>
        </template>
        <el-table-column prop="title" label="意见建议标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            <el-tag :type="getCategoryColor(row.category)">{{ row.category_display || getCategoryLabel(row.category) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="current_status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.current_status)">{{ row.current_status_display || getStatusText(row.current_status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ opinionUtils.formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="last_updated_time" label="更新时间" width="160">
          <template #default="{ row }">
            {{ opinionUtils.formatDateTime(row.last_updated_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="table-action-buttons">
              <el-button size="small" @click="viewOpinion(row)">
                查看
              </el-button>
              <el-button 
                v-if="row.current_status === 'draft' || row.current_status === 'rejected'" 
                size="small" 
                type="primary" 
                @click="editOpinion(row)"
              >
                编辑
              </el-button>
              <el-button 
                v-if="row.current_status === 'draft' || row.current_status === 'rejected'" 
                size="small" 
                type="success" 
                @click="submitOpinion(row)"
              >
                {{ row.current_status === 'rejected' ? '重新提交' : '提交' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <div class="pagination-info">
          <span class="pagination-total">
            共 {{ total }} 条记录，当前第 {{ currentPage }} / {{ Math.ceil(total / pageSize) || 1 }} 页
          </span>
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          :pager-count="7"
          :disabled="loading"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
          small
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '录入意见建议' : '编辑意见建议'"
      width="800px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="opinionForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="意见建议标题" prop="title">
          <div class="input-with-voice">
            <el-input
              v-model="opinionForm.title"
              placeholder="请输入意见建议标题"
              maxlength="100"
              show-word-limit
              :disabled="isRecording"
            />
            <el-button
              :type="isRecording ? 'danger' : 'default'"
              @click="toggleVoiceRecording"
              :loading="isProcessingVoice"
              :disabled="aiLoading"
              class="voice-button"
              circle
              :title="isRecording ? '停止录音' : '语音录入'"
            >
              <el-icon>
                <component :is="isRecording ? 'VideoPlay' : 'Microphone'" />
              </el-icon>
            </el-button>
          </div>
          <div v-if="isRecording" class="voice-tip">
            <el-text type="danger" size="small">
              <el-icon><Microphone /></el-icon>
              正在录音中，请说出您的意见建议...
            </el-text>
          </div>
          <div v-if="!isRecording && !isProcessingVoice" class="voice-options">
            <el-checkbox v-model="aiSummarizeContent" size="small">
              让AI总结优化意见建议内容
            </el-checkbox>
            <el-text type="info" size="small" class="voice-option-tip">
              不勾选则直接使用语音转换的原始内容
            </el-text>
          </div>
        </el-form-item>
        
        <el-form-item label="意见建议分类" prop="category">
          <el-select v-model="opinionForm.category" placeholder="请选择意见建议分类" style="width: 100%">
            <el-option 
              v-for="option in opinionUtils.getCategoryOptions()" 
              :key="option.value"
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </el-form-item>

        <el-form-item label="反映人" prop="reporter_name">
          <el-input
            v-model="opinionForm.reporter_name"
            placeholder="请输入反映人姓名（如：张三）"
          />
        </el-form-item>

        <el-form-item label="意见建议内容" prop="original_content">
          <el-input
            v-model="opinionForm.original_content"
            type="textarea"
            :rows="6"
            placeholder="请详细描述群众反映的意见建议或问题"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="AI辅助生成">
          <el-button 
            type="info" 
            @click="generateAISuggestion" 
            :loading="aiLoading"
            :disabled="!isAIButtonEnabled"
          >
            <el-icon><MagicStick /></el-icon>
            AI辅助生成高质量意见建议
          </el-button>
          <div v-if="!isAIButtonEnabled" class="ai-button-tip">
            <el-text type="info" size="small">
              请先输入10-1000字的意见建议内容并选择分类
            </el-text>
          </div>
        </el-form-item>

        <!-- AI生成内容区域 - 支持流式显示 -->
        <el-form-item v-if="opinionForm.ai_generated_content || isAIStreaming || aiLoading || aiStreamingContent" label="AI生成内容">
          <div class="ai-content-container">

            <!-- 深度思考区域 -->
            <div v-if="aiThinkingContent && aiThinkingContent.length > 0" class="thinking-section">
              <div class="thinking-header" @click="showAIThinking = !showAIThinking">
                <el-icon class="thinking-icon">
                  <ArrowDown v-if="showAIThinking" />
                  <ArrowRight v-else />
                </el-icon>
                <span class="thinking-label">
                  💭 深度思考
                  <span v-if="isAIStreaming" class="thinking-indicator">
                    <el-icon class="is-loading"><Loading /></el-icon>
                    思考中...
                  </span>
                </span>
                <span class="thinking-status">({{ showAIThinking ? '展开' : '收起' }})</span>
              </div>
              <div v-if="showAIThinking" class="thinking-content">
                <div class="thinking-text">{{ aiThinkingContent }}</div>
              </div>
            </div>

            <!-- AI生成的主要内容 -->
            <div class="ai-suggestion">
              <div v-if="aiLoading && !aiStreamingContent && !aiThinkingContent" class="ai-loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                AI正在生成高质量意见建议...
              </div>
              <div v-else-if="aiStreamingContent || opinionForm.ai_generated_content" class="ai-content">
                {{ aiStreamingContent || opinionForm.ai_generated_content }}
                <span v-if="isAIStreaming || (aiLoading && aiStreamingContent)" class="streaming-cursor">|</span>
              </div>
            </div>

            <!-- 应用按钮 -->
            <div v-if="opinionForm.ai_generated_content && !isAIStreaming" style="margin-top: 10px;">
              <el-button
                size="small"
                type="success"
                @click="applyAIContent"
                :disabled="!opinionForm.ai_generated_content"
              >
                <el-icon><Check /></el-icon>
                一键应用到最终意见建议
              </el-button>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="最终意见建议" prop="final_suggestion">
          <el-input
            v-model="opinionForm.final_suggestion"
            type="textarea"
            :rows="8"
            placeholder="基于AI生成内容或您的判断，完善最终的意见建议"
            maxlength="2000"
            show-word-limit
            :autosize="{ minRows: 8, maxRows: 15 }"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button @click="saveOpinion">保存草稿</el-button>
        <el-button type="primary" @click="saveAndSubmitOpinion">保存并提交</el-button>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="意见详情"
      width="800px"
    >
      <div v-if="currentOpinion" class="opinion-detail">
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="detail-item">
            <span class="label">意见建议标题：</span>
            <span class="value">{{ currentOpinion.title }}</span>
          </div>
          <div class="detail-item">
            <span class="label">意见建议分类：</span>
            <el-tag :type="getCategoryColor(currentOpinion.category)">{{ currentOpinion.category_display || getCategoryLabel(currentOpinion.category) }}</el-tag>
          </div>
          <div class="detail-item">
            <span class="label">当前状态：</span>
            <el-tag :type="getStatusColor(currentOpinion.current_status)">{{ currentOpinion.current_status_display || getStatusText(currentOpinion.current_status) }}</el-tag>
          </div>
          <div class="detail-item">
            <span class="label">反映人：</span>
            <span class="value">{{ currentOpinion.reporter_name }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h3>意见建议内容</h3>
          <div class="content-box">{{ currentOpinion.original_content }}</div>
        </div>

        <div v-if="currentOpinion.ai_generated_content" class="detail-section">
          <h3>AI生成内容</h3>
          <div class="ai-suggestion">{{ currentOpinion.ai_generated_content }}</div>
        </div>

        <div v-if="currentOpinion.final_suggestion" class="detail-section">
          <h3>最终意见建议</h3>
          <div class="content-box">{{ currentOpinion.final_suggestion }}</div>
        </div>

        <div v-if="currentOpinion.feedback" class="detail-section">
          <h3>处理反馈</h3>
          <div class="content-box">{{ currentOpinion.feedback }}</div>
        </div>

        <!-- 处理流程 -->
        <div class="detail-section">
          <h3>处理流程</h3>
          <el-timeline>
            <el-timeline-item
              v-for="step in getProcessSteps(currentOpinion)"
              :key="step.id"
              :timestamp="step.timestamp"
              :type="step.type"
            >
              <div class="timeline-content">
                <div class="timeline-title">{{ step.description }}</div>
                <div v-if="step.reviewer" class="reviewer-info">
                  操作人：{{ step.reviewer }}
                </div>
                <div v-if="step.comment" class="review-comment">
                  <strong>{{ getCommentLabel(step.action) }}：</strong>{{ step.comment }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, MagicStick, Check, Microphone, VideoPlay, Loading, ArrowDown, ArrowRight } from '@element-plus/icons-vue'
import { opinionAPI, opinionAIAPI, opinionUtils } from '@/api/modules/opinion'
import { aiKnowledgeAPI } from '@/api/modules/aiknowledge'

// 数据状态
const loading = ref(false)
const aiLoading = ref(false)
const opinionsList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const statusFilter = ref('')

// 语音录制状态
const isRecording = ref(false)
const isProcessingVoice = ref(false)
const mediaRecorder = ref(null)
const audioChunks = ref([])
const aiSummarizeContent = ref(true) // 默认开启AI总结

// AI流式生成状态
const aiStreamingContent = ref('') // 流式生成的内容
const aiThinkingContent = ref('') // 深度思考内容
const isAIStreaming = ref(false) // 是否正在流式生成
const showAIThinking = ref(true) // 是否显示深度思考

// 对话框状态
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit'

// 表单引用
const formRef = ref()

// 当前意见
const currentOpinion = ref(null)

// 表单数据
const opinionForm = reactive({
  id: null,
  title: '',
  category: '',
  reporter_name: '',
  original_content: '',
  ai_generated_content: '',
  final_suggestion: '',
  ai_assisted: false,
  status: 'draft'
})

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入意见建议标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择意见建议分类', trigger: ['change', 'blur'] }
  ],
  reporter_name: [
    { required: true, message: '请输入反映人姓名', trigger: 'blur' }
  ],
  original_content: [
    { required: true, message: '请输入意见建议内容', trigger: 'blur' },
    { min: 10, max: 1000, message: '内容长度在 10 到 1000 个字符', trigger: 'blur' }
  ],
  final_suggestion: [
    { max: 2000, message: '最终意见建议长度不能超过 2000 个字符', trigger: 'blur' }
  ]
}

// 获取分类颜色
const getCategoryColor = (category) => {
  return opinionUtils.getCategoryColor(category)
}

// 获取分类标签
const getCategoryLabel = (category) => {
  return opinionUtils.getCategoryLabel(category)
}

// 获取状态颜色
const getStatusColor = (status) => {
  return opinionUtils.getStatusColor(status)
}

// 获取状态文本
const getStatusText = (status) => {
  return opinionUtils.getStatusLabel(status)
}

// 计算AI按钮是否可用
const isAIButtonEnabled = computed(() => {
  const content = opinionForm.original_content?.trim() || ''
  const hasCategory = !!opinionForm.category
  const validLength = content.length >= 10 && content.length <= 1000
  return hasCategory && validLength
})

// 获取处理步骤
const getProcessSteps = (opinion) => {
  const steps = []
  
  // 如果有完整的审核历史，使用审核历史
  if (opinion.review_history && opinion.review_history.length > 0) {
    opinion.review_history.forEach((review) => {
      steps.push({
        id: `review-${review.id}`,
        reviewId: review.id, // 保存原始ID用于排序
        timestamp: opinionUtils.formatDateTime(review.action_time),
        rawTimestamp: review.action_time, // 保存原始时间戳用于排序
        description: getActionDescription(review.action, review.status, review),
        type: getStepType(review.status),
        reviewer: review.operator_name || (review.reviewer_info ? review.reviewer_info.name : '系统'),
        comment: getReviewComment(review),
        action: review.action // 添加action信息用于获取标签
      })
    })
  } else {
    // 如果没有审核历史，使用基础信息构建步骤
    steps.push({
      id: 'create',
      reviewId: 0, // 创建操作设为最小ID
      timestamp: opinionUtils.formatDateTime(opinion.created_at),
      rawTimestamp: opinion.created_at,
      description: '创建意见草稿',
      type: 'primary',
      action: 'create' // 添加action信息
    })
    
    // 如果有最新审核记录，添加最新状态
    if (opinion.latest_review && opinion.latest_review.id) {
      const review = opinion.latest_review
      steps.push({
        id: `latest-${review.id}`,
        reviewId: review.id,
        timestamp: opinionUtils.formatDateTime(review.action_time),
        rawTimestamp: review.action_time,
        description: getActionDescription(review.action, review.status, review),
        type: getStepType(review.status),
        reviewer: review.operator_name || (review.reviewer_info ? review.reviewer_info.name : '系统'),
        comment: getReviewComment(review),
        action: review.action // 添加action信息用于获取标签
      })
    }
  }
  
  // 后端已经按正确顺序返回数据，前端不需要再次排序
  return steps
}

// 获取操作描述
const getActionDescription = (action, status, review) => {
  const actionMap = {
    'create': '创建意见草稿',
    'submit': '代表提交意见建议',
    'approve': '工作人员审核通过',
    'reject': '工作人员审核驳回',
    'transfer': `转交至${review.transferred_department || '相关部门'}`,
    'update_progress': '更新处理进度',
    'close': '标记办结'
  }
  
  return actionMap[action] || review.action_display || action
}

// 获取步骤类型颜色
const getStepType = (status) => {
  const typeMap = {
    'draft': 'info',
    'submitted': 'primary', 
    'approved': 'success',
    'rejected': 'danger',
    'transferred': 'warning',
    'in_progress': 'warning',
    'completed': 'success'
  }
  return typeMap[status] || 'info'
}

// 获取审核评论内容（优先显示处理结果，回退到审核备注）
const getReviewComment = (review) => {
  // 对于更新进度和办结操作，优先显示processing_result
  if (review.action === 'update_progress' || review.action === 'close') {
    return review.processing_result || review.review_comment
  }
  // 对于其他操作，优先显示review_comment
  return review.review_comment || review.processing_result
}

// 获取评论标签（根据操作类型显示不同的标签）
const getCommentLabel = (action) => {
  const labelMap = {
    'create': '说明',
    'submit': '提交说明',
    'approve': '审核意见',
    'reject': '驳回理由',
    'transfer': '转交说明',
    'update_progress': '处理进度',
    'close': '办结结果'
  }
  return labelMap[action] || '备注'
}

// 显示新增对话框
const showAddDialog = () => {
  dialogMode.value = 'add'
  resetForm()
  dialogVisible.value = true
}

// 关闭对话框
const closeDialog = () => {
  resetForm()
  dialogVisible.value = false
}

// 验证表单必填字段
const validateRequiredFields = () => {
  const validations = [
    { field: 'title', value: opinionForm.title.trim(), message: '请输入意见建议标题' },
    { field: 'category', value: opinionForm.category, message: '请选择意见建议分类' },
    { field: 'reporter_name', value: opinionForm.reporter_name.trim(), message: '请输入反映人姓名' },
    { field: 'original_content', value: opinionForm.original_content.trim(), message: '请输入意见建议内容' }
  ]
  
  for (const validation of validations) {
    if (!validation.value) {
      ElMessage.warning(validation.message)
      // 尝试聚焦到对应字段
      setTimeout(() => {
        const fieldElement = document.querySelector(`[prop="${validation.field}"] input, [prop="${validation.field}"] textarea, [prop="${validation.field}"] .el-select`)
        if (fieldElement) {
          fieldElement.focus()
        }
      }, 100)
      return false
    }
  }
  return true
}



// 编辑意见
const editOpinion = async (row) => {
  try {
    loading.value = true
    
    // 获取完整的意见详情，确保包含AI生成内容
    const response = await opinionAPI.getOpinionDetail(row.id)
    
    if (response.data && response.data.success && response.data.data) {
      const detailData = response.data.data
      
      // 使用详情数据填充表单，确保AI内容不丢失
      Object.assign(opinionForm, {
        id: detailData.id,
        title: detailData.title,
        category: detailData.category,
        reporter_name: detailData.reporter_name,
        original_content: detailData.original_content,
        ai_generated_content: detailData.ai_generated_content || '',
        final_suggestion: detailData.final_suggestion,
        ai_assisted: detailData.ai_assisted || false
      })
      
      console.log('编辑模式 - 加载的AI内容:', detailData.ai_generated_content)
    } else {
      // 降级使用列表数据
      Object.assign(opinionForm, row)
      ElMessage.warning('获取详细信息失败，可能缺少AI生成内容')
    }
    
    dialogMode.value = 'edit'
    dialogVisible.value = true
  } catch (error) {
    console.error('获取编辑数据失败:', error)
    // 降级使用列表数据
    Object.assign(opinionForm, row)
    dialogMode.value = 'edit'
    dialogVisible.value = true
    ElMessage.warning('获取详细信息失败，可能缺少AI生成内容')
  } finally {
    loading.value = false
  }
}

// 查看意见
const viewOpinion = async (row) => {
  try {
    loading.value = true
    // 获取完整的意见详情，包含审核历史
    const response = await opinionAPI.getOpinionDetail(row.id)
    if (response.data && response.data.data) {
      currentOpinion.value = response.data.data
    } else {
      // 如果详情API失败，使用列表数据
      currentOpinion.value = row
    }
    viewDialogVisible.value = true
  } catch (error) {
    console.error('获取意见详情失败:', error)
    // 如果获取详情失败，仍然使用列表数据显示
    currentOpinion.value = row
    viewDialogVisible.value = true
    ElMessage.warning('获取详细信息失败，显示基础信息')
  } finally {
    loading.value = false
  }
}

// 提交意见
const submitOpinion = async (row) => {
  try {
    const isResubmit = row.current_status === 'rejected'
    const confirmText = isResubmit 
      ? '确定要重新提交该意见吗？重新提交后将进入审核流程。' 
      : '确定要提交该意见吗？提交后将无法修改。'
    const confirmTitle = isResubmit ? '确认重新提交' : '确认提交'
    
    await ElMessageBox.confirm(confirmText, confirmTitle, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    loading.value = true
    await opinionAPI.submitOpinion(row.id)
    ElMessage.success(isResubmit ? '意见重新提交成功' : '意见提交成功')
    
    // 重新加载列表
    await loadOpinions()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交意见失败:', error)
      ElMessage.error('提交失败：' + (error.response?.data?.message || error.message))
    }
  } finally {
    loading.value = false
  }
}

// 解析思考内容（参考KnowledgeQA的实现）
const parseThinkingContent = (content) => {
  if (!content) {
    return { thinking: '', answer: '', isThinking: false }
  }

  // 检查是否有完整的思考标签对
  const completeThinkMatch = content.match(/<think>([\s\S]*?)<\/think>/)

  if (completeThinkMatch) {
    // 找到完整的思考内容
    const thinkingContent = completeThinkMatch[1]
    const cleanAnswer = content.replace(/<think>[\s\S]*?<\/think>/g, '')

    return {
      thinking: thinkingContent,
      answer: cleanAnswer,
      isThinking: false // 思考已完成
    }
  }

  // 检查是否正在思考中（只有开始标签）
  const thinkStartMatch = content.match(/<think>([\s\S]*)$/)

  if (thinkStartMatch) {
    // 正在接收思考内容
    const thinkingContent = thinkStartMatch[1]
    const answerBeforeThink = content.substring(0, content.indexOf('<think>'))

    return {
      thinking: thinkingContent,
      answer: answerBeforeThink,
      isThinking: true // 正在思考中
    }
  }

  // 没有思考标签，全部作为回答内容
  return {
    thinking: '',
    answer: content,
    isThinking: false
  }
}

// AI辅助生成高质量意见建议（流式输出）
const generateAISuggestion = async () => {
  // 验证必要字段
  if (!opinionForm.original_content.trim()) {
    ElMessage.warning('请先输入意见建议内容')
    document.querySelector('textarea[placeholder*="意见建议内容"]')?.focus()
    return
  }

  if (!opinionForm.category) {
    ElMessage.warning('请先选择意见建议分类')
    document.querySelector('.el-select input')?.focus()
    return
  }

  // 重置流式生成状态
  aiStreamingContent.value = ''
  aiThinkingContent.value = ''
  isAIStreaming.value = false // 先设为false，等收到数据时再设为true
  aiLoading.value = true
  showAIThinking.value = true

  // 清空之前的AI生成内容，确保显示流式内容
  opinionForm.ai_generated_content = ''

  try {
    const categoryText = opinionUtils.getCategoryLabel(opinionForm.category)

    const aiData = {
      original_content: opinionForm.original_content,
      category: opinionForm.category,
      context: `分类：${categoryText}${opinionForm.title ? `，标题：${opinionForm.title}` : ''}`
    }

    console.log('🤖 开始AI流式生成，请求数据:', aiData)

    let fullContent = '' // 累积的完整内容

    await aiKnowledgeAPI.generateOpinionSSE(
      aiData,
      (data) => {
        console.log('🔄 收到SSE数据:', data)

        if (data.event === 'message' || data.answer) {
          const newContent = data.answer || data.text || ''
          if (newContent) {
            // 第一次收到数据时，切换状态
            if (aiLoading.value) {
              aiLoading.value = false
              isAIStreaming.value = true
              console.log('🚀 开始流式显示')
            }

            // 累积内容
            fullContent += newContent

            // 解析思考和回答内容
            const parsedContent = parseThinkingContent(fullContent)

            // 更新状态
            aiThinkingContent.value = parsedContent.thinking
            aiStreamingContent.value = parsedContent.answer

            // 如果正在思考中，保持isAIStreaming为true
            if (parsedContent.isThinking) {
              isAIStreaming.value = true
            }

            console.log('� AI流式解析结果:', {
              thinking: parsedContent.thinking.substring(0, 50) + '...',
              answer: parsedContent.answer.substring(0, 50) + '...',
              isThinking: parsedContent.isThinking,
              fullContentLength: fullContent.length
            })

            // 触发自动滚动（稍微延迟确保DOM更新完成）
            setTimeout(() => {
              autoScrollToAIContent()
            }, 50)
          }
        }
      },
      (error) => {
        console.error('AI流式生成错误:', error)
        aiLoading.value = false
        isAIStreaming.value = false
        ElMessage.error('AI生成失败，请稍后重试')
      },
      () => {
        console.log('🤖 AI流式生成完成')
        aiLoading.value = false
        isAIStreaming.value = false

        // 将最终的回答内容设置为AI生成内容
        if (aiStreamingContent.value && aiStreamingContent.value.trim()) {
          opinionForm.ai_generated_content = aiStreamingContent.value.trim()
          opinionForm.ai_assisted = true
          ElMessage.success('AI高质量意见建议生成完成')

          // 最终滚动到应用按钮
          nextTick(() => {
            autoScrollToAIContent()
          })
        } else {
          ElMessage.warning('AI生成内容为空，请重试')
        }
      }
    )

  } catch (error) {
    console.error('AI生成失败:', error)
    aiLoading.value = false
    isAIStreaming.value = false
    ElMessage.error('AI生成失败：' + (error.message || '未知错误'))
  }
}

// 自动滚动到AI内容区域的最新内容
const autoScrollToAIContent = () => {
  nextTick(() => {
    try {
      // 查找AI建议区域（这是真正有滚动条的元素）
      const aiSuggestion = document.querySelector('.ai-suggestion')
      if (aiSuggestion) {
        // 滚动到AI建议内容的底部
        aiSuggestion.scrollTop = aiSuggestion.scrollHeight
      }

      // 如果在对话框中，确保对话框也滚动到合适位置
      const dialogBody = document.querySelector('.el-dialog__body')
      if (dialogBody) {
        // 温和地滚动，让AI内容区域可见
        const aiContainer = document.querySelector('.ai-content-container')
        if (aiContainer) {
          aiContainer.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
          })
        }
      }
    } catch (error) {
      console.warn('自动滚动失败:', error)
    }
  })
}

// 语音录制相关方法
// 切换录音状态
const toggleVoiceRecording = () => {
  if (isRecording.value) {
    stopVoiceRecording()
  } else {
    startVoiceRecording()
  }
}

// 开始录音
const startVoiceRecording = async () => {
  try {
    // 检查浏览器是否支持录音
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      ElMessage.error('您的浏览器不支持录音功能')
      return
    }

    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        sampleRate: 44100
      }
    })

    // 创建MediaRecorder
    const mimeType = MediaRecorder.isTypeSupported('audio/webm;codecs=opus')
      ? 'audio/webm;codecs=opus'
      : 'audio/webm'

    mediaRecorder.value = new MediaRecorder(stream, {
      mimeType: mimeType
    })

    audioChunks.value = []

    // 监听数据可用事件
    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.value.push(event.data)
      }
    }

    // 监听录音停止事件
    mediaRecorder.value.onstop = async () => {
      const audioBlob = new Blob(audioChunks.value, { type: mimeType })
      await processVoiceAudio(audioBlob)

      // 停止所有音频轨道
      stream.getTracks().forEach(track => track.stop())
    }

    // 开始录音
    mediaRecorder.value.start()
    isRecording.value = true
    ElMessage.success('开始录音，请说出您的意见建议...')

  } catch (error) {
    console.error('开始录音失败:', error)
    if (error.name === 'NotAllowedError') {
      ElMessage.error('请允许访问麦克风权限')
    } else if (error.name === 'NotFoundError') {
      ElMessage.error('未找到麦克风设备')
    } else {
      ElMessage.error('录音失败，请检查设备权限')
    }
  }
}

// 停止录音
const stopVoiceRecording = () => {
  if (mediaRecorder.value && mediaRecorder.value.state === 'recording') {
    mediaRecorder.value.stop()
    isRecording.value = false
    ElMessage.info('录音结束，正在AI解析中...')
  }
}

// 处理语音音频文件
const processVoiceAudio = async (audioBlob) => {
  if (audioBlob.size === 0) {
    ElMessage.warning('录音时间太短，请重新录音')
    return
  }

  isProcessingVoice.value = true

  try {
    // 创建文件对象
    const audioFile = new File([audioBlob], 'voice_recording.webm', {
      type: audioBlob.type
    })

    console.log('处理语音音频文件:', {
      size: audioFile.size,
      type: audioFile.type,
      name: audioFile.name
    })

    // 创建FormData
    const formData = new FormData()
    formData.append('file', audioFile)
    formData.append('summarize_content', aiSummarizeContent.value.toString())

    // 调用语音转意见建议API
    const response = await aiKnowledgeAPI.voiceToOpinion(formData, opinionForm.category)

    console.log('语音转意见建议响应:', response)

    // 从response.data中获取实际数据
    const result = response.data || response

    if (result.success) {
      // 自动填充标题、内容和分类
      if (result.title && result.title.trim()) {
        opinionForm.title = result.title.trim()
      }
      if (result.content && result.content.trim()) {
        opinionForm.original_content = result.content.trim()
      }
      if (result.category && result.category.trim()) {
        opinionForm.category = result.category.trim()
        console.log('AI推荐的分类:', result.category)
      }

      ElMessage.success('语音录入成功，已自动识别标题、内容和分类')
    } else {
      ElMessage.error(result.error || '语音解析失败')
    }

  } catch (error) {
    console.error('语音解析失败:', error)
    ElMessage.error('语音解析失败: ' + (error.response?.data?.message || error.message))
  } finally {
    isProcessingVoice.value = false
  }
}

// 一键应用AI内容到最终意见建议
const applyAIContent = () => {
  if (!opinionForm.ai_generated_content) {
    ElMessage.warning('没有可应用的AI生成内容')
    return
  }
  
  // 如果最终建议已有内容，先确认
  if (opinionForm.final_suggestion && opinionForm.final_suggestion.trim()) {
    ElMessageBox.confirm(
      '当前最终意见建议已有内容，确定要替换为AI生成的内容吗？',
      '确认应用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      opinionForm.final_suggestion = opinionForm.ai_generated_content
      ElMessage.success('AI内容已应用到最终意见建议')
    }).catch(() => {
      // 用户取消，不做任何操作
    })
  } else {
    // 直接应用
    opinionForm.final_suggestion = opinionForm.ai_generated_content
    ElMessage.success('AI内容已应用到最终意见建议')
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(opinionForm, {
    id: null,
    title: '',
    category: '',
    reporter_name: '',
    original_content: '',
    ai_generated_content: '',
    final_suggestion: '',
    ai_assisted: false,
    status: 'draft'
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 保存意见
const saveOpinion = async () => {
  if (!formRef.value) return
  
  // 先进行简单的必填字段检查
  if (!validateRequiredFields()) {
    return
  }
  
  // 直接保存，跳过Element Plus验证避免控制台警告
  await saveOpinionData('draft')
}

// 保存并提交意见
const saveAndSubmitOpinion = async () => {
  if (!formRef.value) return
  
  // 先进行简单的必填字段检查
  if (!validateRequiredFields()) {
    return
  }
  
  // 直接保存，跳过Element Plus验证避免控制台警告
  await saveOpinionData('submitted')
}

// 保存意见数据
const saveOpinionData = async (status) => {
  try {
    loading.value = true
    
    const opinionData = {
      title: opinionForm.title,
      category: opinionForm.category,
      reporter_name: opinionForm.reporter_name,
      original_content: opinionForm.original_content,
      final_suggestion: opinionForm.final_suggestion || opinionForm.original_content,
      ai_assisted: opinionForm.ai_assisted,
      ai_generated_content: opinionForm.ai_generated_content
    }

    let response
    if (dialogMode.value === 'add') {
      // 创建新意见建议
      response = await opinionAPI.createOpinion(opinionData)
      
      // 如果需要立即提交
      if (status === 'submitted' && response.data?.data?.id) {
        await opinionAPI.submitOpinion(response.data.data.id)
      }
      
      ElMessage.success(status === 'submitted' ? '意见提交成功' : '草稿保存成功')
    } else {
      // 更新现有意见建议
      response = await opinionAPI.updateOpinion(opinionForm.id, opinionData)
      
      // 如果需要提交
      if (status === 'submitted') {
        await opinionAPI.submitOpinion(opinionForm.id)
      }
      
      ElMessage.success(status === 'submitted' ? '意见提交成功' : '意见更新成功')
    }

    dialogVisible.value = false
    resetForm()
    
    // 重新加载列表
    console.log('🔄 重新加载意见列表...')
    await loadOpinions()
  } catch (error) {
    console.error('保存意见失败:', error)
    ElMessage.error('操作失败：' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  loadOpinions()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadOpinions()
}

// 加载意见列表
const loadOpinions = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ordering: '-created_at'
    }
    
    // 状态筛选
    if (statusFilter.value) {
      params.status = statusFilter.value
    }
    
    const response = await opinionAPI.getOpinionsList(params)
    
    if (response.data && response.data.data) {
      const results = response.data.data.results || []
      const count = response.data.data.count || 0
      
      opinionsList.value = results
      total.value = count
      
      console.log('✅ 意见列表加载成功:', {
        count: total.value,
        results: opinionsList.value.length
      })
    } else {
      console.log('❌ 响应数据结构不正确:', response.data)
    }
  } catch (error) {
    console.error('加载意见列表失败:', error)
    ElMessage.error('加载数据失败：' + (error.response?.data?.message || error.message))
    opinionsList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  loadOpinions()
})

// 组件卸载时清理录音资源
onBeforeUnmount(() => {
  if (mediaRecorder.value && mediaRecorder.value.state === 'recording') {
    mediaRecorder.value.stop()
  }
})
</script>

<style scoped>
.opinions-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

.page-header {
  flex-shrink: 0;
  margin-bottom: 20px;
}

.page-header h2 {
  color: var(--china-red);
  margin: 0 0 5px 0;
}

.page-header p {
  color: #666;
  margin: 0;
}

.toolbar {
  flex-shrink: 0;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 表格卡片样式 - 占用剩余空间 */
.opinions-container :deep(.el-card) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.opinions-container :deep(.el-card .el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 20px;
  overflow: hidden;
}

/* 表格容器 - 占用剩余空间并允许内部滚动 */
.opinions-container :deep(.el-table) {
  flex: 1;
  overflow: hidden;
  border-radius: 8px;
}

/* 表格体滚动 - 使用flex自动计算高度 */
.opinions-container :deep(.el-table .el-table__body-wrapper) {
  overflow-y: auto;
  flex: 1;
}

.opinions-container :deep(.el-table__body) {
  padding-bottom: 10px;
}

.opinions-container :deep(.el-table__row:last-child td) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

/* 分页容器 - 固定在底部 */
.pagination {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  padding: 20px 0 0 0;
  border-top: 1px solid var(--el-border-color-lighter);
  margin-top: 20px;
  min-height: 60px;
}

.pagination-info {
  flex: 1;
  min-width: 200px;
}

.pagination-total {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.pagination .el-pagination {
  flex-shrink: 0;
}

/* 表格样式优化 */
.opinions-container :deep(.el-table th) {
  background-color: var(--el-fill-color-light);
  font-weight: 600;
}

.opinions-container :deep(.el-table tbody tr:hover > td) {
  background-color: var(--el-fill-color-extra-light);
}

/* 状态标签样式优化 */
.opinions-container :deep(.el-tag) {
  font-weight: 500;
  border-radius: 12px;
  padding: 2px 8px;
}

/* 空数据状态优化 */
.opinions-container :deep(.el-table__empty-block) {
  padding: 60px 0;
}

.opinions-container :deep(.el-table__empty-text) {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

/* 加载状态优化 */
.opinions-container :deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 防止表格内容换行 */
.opinions-container :deep(.el-table .el-table__cell) {
  white-space: nowrap;
}

/* 标题列特殊处理 - 允许鼠标悬浮查看完整内容 */
.opinions-container :deep(.el-table .el-table__cell:first-child .cell) {
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 时间列确保能够完整显示 */
.opinions-container :deep(.el-table .el-table__cell:nth-child(4) .cell),
.opinions-container :deep(.el-table .el-table__cell:nth-child(5) .cell) {
  white-space: nowrap;
}

/* 表格操作按钮对齐 */
.table-action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.table-action-buttons .el-button {
  margin: 0;
}

.ai-suggestion {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 15px;
  color: #0369a1;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-wrap; /* 保留换行和空格 */
  word-wrap: break-word; /* 长单词自动换行 */
  overflow-wrap: break-word; /* 现代浏览器的换行处理 */
  max-height: 400px; /* 限制最大高度 */
  overflow-y: auto; /* 内容过多时显示滚动条 */
  scroll-behavior: smooth; /* 添加平滑滚动 */
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
}

/* AI生成内容的滚动条样式 */
.ai-suggestion::-webkit-scrollbar {
  width: 6px;
}

.ai-suggestion::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 3px;
}

.ai-suggestion::-webkit-scrollbar-thumb {
  background: rgba(3, 105, 161, 0.3);
  border-radius: 3px;
}

.ai-suggestion::-webkit-scrollbar-thumb:hover {
  background: rgba(3, 105, 161, 0.5);
}

/* AI生成内容前添加小图标标识 */
.ai-suggestion::before {
  content: "🤖";
  position: absolute;
  top: 8px;
  right: 12px;
  font-size: 16px;
  opacity: 0.6;
}

.ai-button-tip {
  margin-top: 8px;
  padding: 6px 12px;
  background: #fef3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  font-size: 12px;
}

/* 语音录入相关样式 */
.input-with-voice {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  width: 100%;
}

.input-with-voice .el-input {
  flex: 1;
  min-width: 0; /* 确保输入框能够正确收缩 */
}

.voice-button {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  margin-top: 0;
  transition: all 0.3s ease;
}

.voice-button.el-button--danger {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 108, 108, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}

.voice-tip {
  margin-top: 8px;
  padding: 6px 12px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  font-size: 12px;
  color: #f56c6c;
  display: flex;
  align-items: center;
  gap: 4px;
}

.voice-tip .el-icon {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.voice-options {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 12px;
}

.voice-option-tip {
  margin-left: 8px;
  color: #6c757d;
}

/* AI流式生成相关样式 */
.ai-content-container {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
  overflow: visible; /* 确保容器本身不产生滚动条 */
}

.thinking-section {
  margin-bottom: 16px;
  border: 1px solid #d4edda;
  border-radius: 6px;
  background: #f8f9fa;
}

.thinking-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  background: #e9f7ef;
  border-radius: 6px 6px 0 0;
  transition: background-color 0.3s;
}

.thinking-header:hover {
  background: #d4edda;
}

.thinking-icon {
  margin-right: 8px;
  color: #28a745;
}

.thinking-label {
  flex: 1;
  font-weight: 500;
  color: #155724;
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6c757d;
}

.thinking-status {
  font-size: 12px;
  color: #6c757d;
}

.thinking-content {
  padding: 12px;
  border-top: 1px solid #d4edda;
}

.thinking-text {
  color: #495057;
  line-height: 1.6;
  white-space: pre-wrap;
  font-size: 14px;
}

.ai-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6c757d;
  font-style: italic;
  padding: 12px 0;
}

.ai-content {
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  /* 移除这里的滚动设置，使用ai-suggestion的滚动 */
}

.streaming-cursor {
  animation: blink 1s infinite;
  color: #409eff;
  font-weight: bold;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 确保AI内容区域可见 */
.ai-content-container {
  min-height: 60px;
  transition: all 0.3s ease;
}

/* 最终意见建议文本框样式优化 */
.opinions-container :deep(.el-form-item:last-of-type .el-textarea__inner) {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  resize: vertical;
  min-height: 200px;
}

.opinion-detail {
  line-height: 1.6;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  color: var(--china-red);
  margin-bottom: 12px;
  font-size: 16px;
  border-bottom: 2px solid var(--china-red);
  padding-bottom: 4px;
}

.detail-item {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.label {
  font-weight: bold;
  color: var(--text-color);
  min-width: 100px;
  margin-right: 10px;
}

.value {
  flex: 1;
  color: var(--text-color);
}

.content-box {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid var(--china-red);
  line-height: 1.6;
  white-space: pre-wrap;
}

.reviewer-info {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

/* 时间线样式 */
.timeline-content {
  padding: 5px 0;
}

.timeline-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 4px;
}

.review-comment {
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid var(--china-red);
  margin-top: 8px;
  font-size: 13px;
  line-height: 1.5;
}

.review-comment strong {
  color: var(--china-red);
}

/* 分页响应式 */
@media (max-width: 768px) {
  .pagination {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .pagination-info {
    order: 2;
    margin-top: 10px;
    text-align: center;
  }
  
  .pagination .el-pagination {
    order: 1;
    justify-content: center;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .opinions-container {
    padding: 10px;
    height: 100vh;
    overflow: hidden;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .filter-group {
    flex-direction: column;
    gap: 10px;
  }
  
  .filter-group .el-select,
  .filter-group .el-input {
    width: 100% !important;
  }
  
  /* 移动端隐藏部分列 */
  :deep(.el-table .el-table__cell:nth-child(3)),
  :deep(.el-table .el-table__cell:nth-child(4)),
  :deep(.el-table .el-table__cell:nth-child(5)) {
    display: none;
  }
}
</style> 