"""
人大代表履职服务与管理平台主URL配置

包含以下路由：
1. API路由 - 所有业务API端点
2. Swagger文档 - API文档和调试界面
3. Django管理后台（仅用于开发调试）
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

# Swagger文档配置
schema_view = get_schema_view(
    openapi.Info(
        title="人大代表履职服务与管理平台 API",
        default_version='v1',
        description="""
        人大代表履职服务与管理平台后端API文档
        
        ## 主要功能模块
        
        ### 用户认证与管理
        - 用户登录/登出
        - JWT令牌认证
        - 密码修改
        - 用户信息管理
        
        ### 人大代表管理
        - 代表信息查看
        - 代表信息更新
        - 代表列表查询
        
        ### 工作人员管理
        - 工作人员信息管理
        - 用户账号管理
        - 权限控制
        
        ## 认证方式
        
        API使用JWT令牌进行认证，请在请求头中包含：
        ```
        Authorization: Bearer <access_token>
        ```
        
        ## 响应格式
        
        所有API响应都采用统一格式：
        ```json
        {
            "success": true|false,
            "message": "响应消息",
            "data": {}, // 响应数据（成功时）
            "errors": {} // 错误信息（失败时）
        }
        ```
        """,
        terms_of_service="https://www.example.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="MIT License"),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],
)

urlpatterns = [
    # Django管理后台（仅用于开发和调试）
    #path('admin/', admin.site.urls),
    
    # API路由
    path('api/v1/users/', include('api.users.urls')),
    path('api/v1/performance/', include('api.performance.urls')),
    path('api/v1/opinions/', include('api.opinion.urls')),
    path('api/v1/aiknowledge/', include('api.aiknowledge.urls')),
    path('api/v1/ai-summaries/', include('api.aisummary.urls')),
    path('api/v1/workplan/', include('api.workplan.urls')),
    path('api/v1/people-opinions/', include('api.peopleopnion.urls')),
    path('api/v1/bigscreen/', include('api.bigscreen.urls')),
    # 后续会添加其他模块的API路由：
    # path('api/v1/notifications/', include('api.notifications.urls')),
    
    # Swagger文档
    path('docs/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    path('api/schema/', schema_view.without_ui(cache_timeout=0), name='schema-json'),
]

# 开发环境下提供静态文件服务（媒体文件通过安全API访问）
if settings.DEBUG:
    # 移除了不安全的媒体文件直接访问：static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    # 所有媒体文件现在通过安全API访问：/api/v1/performance/secure-file/{attachment_id}/
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
