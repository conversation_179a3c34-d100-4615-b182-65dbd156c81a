from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch, MagicMock
import json

User = get_user_model()


class OpinionGenerateAPITest(TestCase):
    """意见建议AI生成API测试"""
    
    def setUp(self):
        """设置测试数据"""
        self.client = APIClient()
        
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            role='representative'
        )
        
        # 设置认证
        self.client.force_authenticate(user=self.user)
        
        # API端点
        self.url = '/api/v1/aiknowledge/opinion/generate/'
        
        # 测试数据
        self.valid_data = {
            'original_content': '关于改善社区环境的建议',
            'category': '城建环保',
            'context': '该社区存在垃圾分类不规范的问题'
        }
    
    @patch('api.aiknowledge.services.requests.post')
    def test_generate_suggestion_success(self, mock_post):
        """测试成功生成意见建议"""
        # 模拟Dify API成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'answer': '经过调研发现，该社区垃圾分类不规范主要原因是...'
        }
        mock_post.return_value = mock_response
        
        # 发送请求
        response = self.client.post(self.url, self.valid_data, format='json')
        
        # 验证响应
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('generated_content', response.data)
        self.assertEqual(response.data['message'], '意见建议生成成功')
    
    def test_generate_suggestion_missing_content(self):
        """测试缺少原始内容"""
        invalid_data = self.valid_data.copy()
        del invalid_data['original_content']
        
        response = self.client.post(self.url, invalid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertEqual(response.data['error'], '原始意见内容不能为空')
    
    def test_generate_suggestion_missing_category(self):
        """测试缺少分类"""
        invalid_data = self.valid_data.copy()
        del invalid_data['category']
        
        response = self.client.post(self.url, invalid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertEqual(response.data['error'], '意见类别不能为空')
    
    @patch('api.aiknowledge.services.requests.post')
    def test_generate_suggestion_api_error(self, mock_post):
        """测试Dify API错误"""
        # 模拟Dify API错误响应
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = 'Internal Server Error'
        mock_post.return_value = mock_response
        
        response = self.client.post(self.url, self.valid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertFalse(response.data['success'])
        self.assertEqual(response.data['error'], '意见建议生成失败')
    
    def test_generate_suggestion_unauthorized(self):
        """测试未认证访问"""
        # 移除认证
        self.client.force_authenticate(user=None)
        
        response = self.client.post(self.url, self.valid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    @patch('api.aiknowledge.services.requests.post')
    def test_generate_suggestion_with_context(self, mock_post):
        """测试带上下文信息的生成"""
        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'answer': '针对垃圾分类不规范问题，建议采取以下措施...'
        }
        mock_post.return_value = mock_response
        
        response = self.client.post(self.url, self.valid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # 验证调用参数 - 新的实现方式直接传递原始数据
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        request_data = call_args[1]['json']
        
        # 验证inputs包含所有必要字段
        self.assertIn('inputs', request_data)
        inputs = request_data['inputs']
        self.assertEqual(inputs['original_content'], self.valid_data['original_content'])
        self.assertEqual(inputs['category'], self.valid_data['category'])
        self.assertEqual(inputs['context'], self.valid_data['context'])
        
        # 验证query字段是原始内容
        self.assertEqual(request_data['query'], self.valid_data['original_content'])
    
    @patch('api.aiknowledge.services.requests.post')
    def test_generate_suggestion_without_context(self, mock_post):
        """测试不带上下文信息的生成"""
        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'answer': '根据您的意见建议，我们建议采取以下措施...'
        }
        mock_post.return_value = mock_response
        
        # 不包含context的测试数据
        data_without_context = {
            'original_content': '关于改善社区环境的建议',
            'category': '城建环保'
        }
        
        response = self.client.post(self.url, data_without_context, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # 验证调用参数 - 不包含context字段
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        request_data = call_args[1]['json']
        
        # 验证inputs只包含必要字段
        self.assertIn('inputs', request_data)
        inputs = request_data['inputs']
        self.assertEqual(inputs['original_content'], data_without_context['original_content'])
        self.assertEqual(inputs['category'], data_without_context['category'])
        self.assertNotIn('context', inputs)  # 不应该包含空的context字段
    
    @patch('api.aiknowledge.services.requests.post')
    def test_generate_suggestion_with_structured_text(self, mock_post):
        """测试带结构化文本的生成"""
        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'answer': '基于您提供的结构化信息，建议采取以下措施...'
        }
        mock_post.return_value = mock_response
        
        # 包含结构化文本的测试数据
        data_with_structured = {
            'original_content': '关于改善社区环境的建议',
            'category': '城建环保',
            'title': '关于改善社区环境卫生的建议',
            'structured_text': '标题：关于改善社区环境卫生的建议\n分类：城建环保\n内容：关于改善社区环境的建议'
        }
        
        response = self.client.post(self.url, data_with_structured, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # 验证调用参数 - 应该使用结构化文本作为query
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        request_data = call_args[1]['json']
        
        # 验证inputs包含所有字段
        self.assertIn('inputs', request_data)
        inputs = request_data['inputs']
        self.assertEqual(inputs['original_content'], data_with_structured['original_content'])
        self.assertEqual(inputs['category'], data_with_structured['category'])
        self.assertEqual(inputs['title'], data_with_structured['title'])
        self.assertEqual(inputs['structured_text'], data_with_structured['structured_text'])
        
        # 验证query字段是结构化文本
        self.assertEqual(request_data['query'], data_with_structured['structured_text'])
