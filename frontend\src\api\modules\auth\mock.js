/**
 * 用户认证模块 Mock 数据
 * 用于开发和测试阶段模拟后端API响应
 */

/**
 * 模拟用户数据
 */
export const mockUsers = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    name: '系统管理员',
    phone: '13800000001',
    role: 'staff',
    is_active: true,
    is_staff: true,
    date_joined: '2024-01-01T08:00:00Z',
    last_login: '2024-01-15T10:30:00Z',
    avatar: null,
    department: '管理部门'
  },
  {
    id: 2,
    username: 'rep001',
    email: '<EMAIL>',
    name: '张代表',
    phone: '13800000002',
    role: 'representative',
    is_active: true,
    is_staff: false,
    date_joined: '2024-01-01T08:00:00Z',
    last_login: '2024-01-15T09:15:00Z',
    avatar: null,
    constituency: '第一选区',
    term: '第十五届',
    contact_address: '南宁市江南区某某街道'
  },
  {
    id: 3,
    username: 'staff001',
    email: '<EMAIL>',
    name: '李工作人员',
    phone: '13800000003',
    role: 'staff',
    is_active: true,
    is_staff: true,
    date_joined: '2024-01-01T08:00:00Z',
    last_login: '2024-01-15T08:45:00Z',
    avatar: null,
    department: '办公室'
  }
]

/**
 * 模拟登录响应
 */
export const mockLoginResponse = {
  access: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
  refresh: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
  user: {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    name: '系统管理员',
    role: 'staff',
    is_staff: true,
    avatar: null
  },
  message: '登录成功'
}

/**
 * 模拟用户资料响应
 */
export const mockUserProfile = {
  id: 1,
  username: 'admin',
  email: '<EMAIL>',
  name: '系统管理员',
  phone: '13800000001',
  role: 'staff',
  is_active: true,
  is_staff: true,
  date_joined: '2024-01-01T08:00:00Z',
  last_login: '2024-01-15T10:30:00Z',
  avatar: null,
  department: '管理部门',
  permissions: [
    'view_user',
    'add_user',
    'change_user',
    'delete_user',
    'view_representative',
    'change_representative'
  ]
}

/**
 * 模拟用户列表响应
 */
export const mockUserListResponse = {
  count: 50,
  next: null,
  previous: null,
  results: mockUsers
}

/**
 * 模拟代表列表响应
 */
export const mockRepresentativeListResponse = {
  count: 33,
  next: null,
  previous: null,
  results: mockUsers.filter(user => user.role === 'representative')
}

/**
 * 模拟工作人员列表响应
 */
export const mockStaffListResponse = {
  count: 15,
  next: null,
  previous: null,
  results: mockUsers.filter(user => user.role === 'staff')
}

/**
 * 模拟API响应工具函数
 */
export const createMockResponse = (data, message = '操作成功', success = true) => {
  return {
    success,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * 模拟登录验证
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Object} 登录结果
 */
export const mockLogin = (username, password) => {
  // 模拟登录验证逻辑
  const validCredentials = [
    { username: 'admin', password: 'admin123' },
    { username: 'rep001', password: 'rep123' },
    { username: 'staff001', password: 'staff123' }
  ]

  const isValid = validCredentials.some(
    cred => cred.username === username && cred.password === password
  )

  if (isValid) {
    const user = mockUsers.find(u => u.username === username)
    return createMockResponse({
      access: 'mock_access_token_' + Date.now(),
      refresh: 'mock_refresh_token_' + Date.now(),
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        role: user.role,
        is_staff: user.is_staff,
        avatar: user.avatar
      }
    }, '登录成功')
  } else {
    return createMockResponse(
      null, 
      '用户名或密码错误', 
      false
    )
  }
}

/**
 * 模拟密码修改
 * @param {Object} passwordData - 密码数据
 * @param {string} passwordData.old_password - 旧密码
 * @param {string} passwordData.new_password - 新密码
 * @param {string} passwordData.confirm_password - 确认新密码
 * @returns {Object} 修改结果
 */
export const mockChangePassword = (passwordData) => {
  const { old_password, new_password, confirm_password } = passwordData
  
  // 模拟当前密码验证
  const validOldPasswords = ['admin123', 'rep123', 'staff123']
  if (!validOldPasswords.includes(old_password)) {
    return createMockResponse(
      null,
      '当前密码错误',
      false
    )
  }
  
  // 模拟新密码强度检查
  if (new_password.length < 6) {
    return createMockResponse(
      null,
      '密码长度至少6位',
      false
    )
  }
  
  // 模拟确认密码验证
  if (new_password !== confirm_password) {
    return createMockResponse(
      null,
      '两次输入的密码不一致',
      false
    )
  }
  
  // 模拟新密码不能与旧密码相同
  if (new_password === old_password) {
    return createMockResponse(
      null,
      '新密码不能与当前密码相同',
      false
    )
  }
  
  return createMockResponse(
    {
      timestamp: new Date().toISOString()
    },
    '密码修改成功'
  )
}

/**
 * 模拟用户创建
 * @param {Object} userData - 用户数据
 * @returns {Object} 创建结果
 */
export const mockCreateUser = (userData) => {
  const newUser = {
    id: Math.max(...mockUsers.map(u => u.id)) + 1,
    ...userData,
    date_joined: new Date().toISOString(),
    last_login: null,
    is_active: true
  }

  return createMockResponse(
    newUser,
    '用户创建成功'
  )
}

/**
 * 模拟延迟函数
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise} Promise对象
 */
export const delay = (ms = 1000) => {
  return new Promise(resolve => setTimeout(resolve, ms))
} 