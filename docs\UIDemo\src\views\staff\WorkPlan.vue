<template>
  <div class="workplan-container">
    <div class="page-header">
      <h2>工作计划管理</h2>
      <p>录入和管理年度、季度、月度工作计划，设置提醒时间</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-container">
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.total }}</div>
              <div class="stat-label">总计划数</div>
            </div>
            <el-icon class="stat-icon" style="color: #409eff;"><Document /></el-icon>
          </el-card>
        </div>
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pending }}</div>
              <div class="stat-label">待开始</div>
            </div>
            <el-icon class="stat-icon" style="color: #909399;"><Clock /></el-icon>
          </el-card>
        </div>
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.inProgress }}</div>
              <div class="stat-label">进行中</div>
            </div>
            <el-icon class="stat-icon" style="color: #e6a23c;"><Loading /></el-icon>
          </el-card>
        </div>
        <div class="stat-item">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <el-icon class="stat-icon" style="color: #67c23a;"><Select /></el-icon>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button
            type="primary"
            @click="showCreateDialog"
            :icon="Plus"
          >
            新增计划
          </el-button>
          <el-button
            type="success"
            @click="batchUpdateStatus"
            :disabled="selectedIds.length === 0"
            :icon="Check"
          >
            批量完成
          </el-button>
        </div>
        <div class="toolbar-right">
          <div class="search-filters">
            <el-select
              v-model="filters.type"
              placeholder="计划类型"
              style="width: 120px; margin-right: 10px;"
              @change="loadWorkPlans"
            >
              <el-option label="全部" value="全部" />
              <el-option label="年度" value="年度" />
              <el-option label="季度" value="季度" />
              <el-option label="月度" value="月度" />
            </el-select>
            <el-select
              v-model="filters.status"
              placeholder="状态"
              style="width: 120px; margin-right: 10px;"
              @change="loadWorkPlans"
            >
              <el-option label="全部" value="全部" />
              <el-option label="待开始" value="待开始" />
              <el-option label="进行中" value="进行中" />
              <el-option label="已完成" value="已完成" />
              <el-option label="已延期" value="已延期" />
            </el-select>
            <el-input
              v-model="filters.keyword"
              placeholder="搜索计划标题、内容、负责人"
              style="width: 250px;"
              @keyup.enter="loadWorkPlans"
              @clear="loadWorkPlans"
              clearable
            >
              <template #suffix>
                <el-icon @click="loadWorkPlans" style="cursor: pointer;">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 计划列表 -->
    <el-card class="list-card">
      <el-table
        v-loading="loading"
        :data="workPlans"
        @selection-change="handleSelectionChange"
        empty-text="暂无工作计划"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="title" label="计划标题" min-width="200">
          <template #default="{ row }">
            <div class="plan-title">
              <el-text tag="b">{{ row.title }}</el-text>
              <el-tag
                :type="getTypeTagType(row.type)"
                size="small"
                style="margin-left: 8px;"
              >
                {{ row.type }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="responsible" label="负责人" width="100" />
        <el-table-column prop="startDate" label="开始时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.startDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="endDate" label="结束时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.endDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reminderDays" label="提醒时间" width="100">
          <template #default="{ row }">
            {{ row.reminderDays }}天前
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                size="small"
                @click="showDetailDialog(row)"
              >
                查看
              </el-button>
              <el-button
                size="small"
                type="primary"
                @click="showEditDialog(row)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deletePlan(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadWorkPlans"
          @current-change="loadWorkPlans"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑工作计划' : '新增工作计划'"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="large"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="计划标题" prop="title">
              <el-input
                v-model="formData.title"
                placeholder="请输入计划标题"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划类型" prop="type">
              <el-select v-model="formData.type" placeholder="请选择计划类型" style="width: 100%;">
                <el-option label="年度" value="年度" />
                <el-option label="季度" value="季度" />
                <el-option label="月度" value="月度" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="responsible">
              <el-input v-model="formData.responsible" placeholder="请输入负责人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="date"
                placeholder="选择开始时间"
                style="width: 100%;"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="date"
                placeholder="选择结束时间"
                style="width: 100%;"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="计划内容" prop="content">
              <el-input
                v-model="formData.content"
                type="textarea"
                :rows="4"
                placeholder="请详细描述计划内容"
                maxlength="2000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="计划目标" prop="goal">
              <el-input
                v-model="formData.goal"
                type="textarea"
                :rows="3"
                placeholder="请描述计划目标（可选）"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划状态" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%;">
                <el-option label="待开始" value="待开始" />
                <el-option label="进行中" value="进行中" />
                <el-option label="已完成" value="已完成" />
                <el-option label="已延期" value="已延期" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提醒时间" prop="reminderDays">
              <el-input-number
                v-model="formData.reminderDays"
                :min="0"
                :max="30"
                style="width: 100%;"
                placeholder="提前几天提醒"
              />
              <span style="margin-left: 8px; color: #666;">天前提醒</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="formLoading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="工作计划详情" width="600px">
      <div v-if="currentPlan" class="plan-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="计划标题" :span="2">
            <el-text tag="b">{{ currentPlan.title }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="计划类型">
            <el-tag :type="getTypeTagType(currentPlan.type)">{{ currentPlan.type }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="负责人">
            {{ currentPlan.responsible }}
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ formatDate(currentPlan.startDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ formatDate(currentPlan.endDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="计划状态">
            <el-tag :type="getStatusTagType(currentPlan.status)">{{ currentPlan.status }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="提醒设置">
            {{ currentPlan.reminderDays }}天前 ({{ currentPlan.reminderType }})
          </el-descriptions-item>
          <el-descriptions-item label="计划内容" :span="2">
            <div class="content-text">{{ currentPlan.content }}</div>
          </el-descriptions-item>
          <el-descriptions-item v-if="currentPlan.goal" label="计划目标" :span="2">
            <div class="content-text">{{ currentPlan.goal }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(currentPlan.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDate(currentPlan.updatedAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Check,
  Document,
  Clock,
  Loading,
  Select
} from '@element-plus/icons-vue'
import {
  getWorkPlans,
  createWorkPlan,
  updateWorkPlan,
  deleteWorkPlan,
  getWorkPlanStatistics,
  batchUpdatePlanStatus
} from '@/api/workplan'

// 数据响应式变量
const loading = ref(false)
const formLoading = ref(false)
const workPlans = ref([])
const statistics = ref({
  total: 0,
  pending: 0,
  inProgress: 0,
  completed: 0,
  delayed: 0
})

// 对话框控制
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const currentPlan = ref(null)

// 表单相关
const formRef = ref()
const formData = reactive({
  title: '',
  type: '',
  startDate: '',
  endDate: '',
  content: '',
  goal: '',
  responsible: '',
  status: '待开始',
  reminderDays: 3
})

// 筛选和分页
const filters = reactive({
  type: '全部',
  status: '全部',
  keyword: ''
})

const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 选择的行
const selectedIds = ref([])

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入计划标题', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择计划类型', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入计划内容', trigger: 'blur' }
  ],
  responsible: [
    { required: true, message: '请输入负责人', trigger: 'blur' }
  ]
}

// 加载工作计划列表
const loadWorkPlans = async () => {
  try {
    loading.value = true
    const response = await getWorkPlans({
      ...filters,
      page: pagination.page,
      pageSize: pagination.pageSize
    })
    
    if (response.success) {
      workPlans.value = response.data.list
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('加载工作计划失败:', error)
    ElMessage.error('加载工作计划失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response = await getWorkPlanStatistics()
    if (response.success) {
      statistics.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 显示新建对话框
const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (plan) => {
  isEdit.value = true
  Object.assign(formData, plan)
  dialogVisible.value = true
}

// 显示详情对话框
const showDetailDialog = (plan) => {
  currentPlan.value = plan
  detailDialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    title: '',
    type: '',
    startDate: '',
    endDate: '',
    content: '',
    goal: '',
    responsible: '',
    status: '待开始',
    reminderDays: 3
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    formLoading.value = true

    if (isEdit.value) {
      const response = await updateWorkPlan(formData.id, formData)
      if (response.success) {
        ElMessage.success('工作计划更新成功')
        dialogVisible.value = false
        loadWorkPlans()
        loadStatistics()
      }
    } else {
      const response = await createWorkPlan(formData)
      if (response.success) {
        ElMessage.success('工作计划创建成功')
        dialogVisible.value = false
        loadWorkPlans()
        loadStatistics()
      }
    }
  } catch (error) {
    console.error('提交失败:', error)
    if (error.response && error.response.data) {
      ElMessage.error(error.response.data.message || '操作失败')
    } else {
      ElMessage.error('网络错误，请重试')
    }
  } finally {
    formLoading.value = false
  }
}

// 删除计划
const deletePlan = async (plan) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除计划"${plan.title}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteWorkPlan(plan.id)
    if (response.success) {
      ElMessage.success('删除成功')
      loadWorkPlans()
      loadStatistics()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量更新状态
const batchUpdateStatus = async () => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning('请选择要更新的计划')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${selectedIds.value.length} 个计划标记为已完成吗？`,
      '批量操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const response = await batchUpdatePlanStatus(selectedIds.value, '已完成')
    if (response.success) {
      ElMessage.success(response.message)
      selectedIds.value = []
      loadWorkPlans()
      loadStatistics()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量更新失败:', error)
      ElMessage.error('批量更新失败')
    }
  }
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map(item => item.id)
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 获取类型标签样式
const getTypeTagType = (type) => {
  const typeMap = {
    '年度': 'danger',
    '季度': 'warning',
    '月度': 'success'
  }
  return typeMap[type] || ''
}

// 获取状态标签样式
const getStatusTagType = (status) => {
  const statusMap = {
    '待开始': 'info',
    '进行中': 'warning',
    '已完成': 'success',
    '已延期': 'danger'
  }
  return statusMap[status] || ''
}

// 组件挂载时执行
onMounted(() => {
  loadWorkPlans()
  loadStatistics()
})
</script>

<style scoped>
.workplan-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  color: var(--china-red);
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.stats-section {
  margin-bottom: 20px;
}

.stats-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  flex: 1;
  min-width: 200px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 16px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(200, 16, 46, 0.15);
}

.stat-card .el-card__body {
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.stat-content {
  text-align: center;
  margin-bottom: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: var(--china-red);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  white-space: nowrap;
}

.stat-icon {
  font-size: 28px;
  opacity: 0.8;
}

/* 响应式调整 */
@media (min-width: 1200px) {
  .stat-item {
    min-width: 220px;
  }
  
  .stat-card .el-card__body {
    padding: 20px;
  }
  
  .stat-content {
    margin-bottom: 12px;
  }
  
  .stat-number {
    font-size: 28px;
  }
  
  .stat-label {
    font-size: 14px;
    margin-top: 5px;
  }
  
  .stat-icon {
    font-size: 32px;
  }
}

@media (max-width: 992px) {
  .stat-item {
    min-width: 180px;
  }
}

@media (max-width: 768px) {
  .stats-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .stat-item {
    flex: none;
    min-width: auto;
  }
  
  .stat-card {
    margin-bottom: 0;
  }
  
  .stat-card .el-card__body {
    padding: 12px;
  }
  
  .stat-content {
    margin-bottom: 6px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .stat-icon {
    font-size: 24px;
  }
}

@media (max-width: 576px) {
  .stats-container {
    gap: 8px;
  }
  
  .stat-card .el-card__body {
    padding: 10px;
  }
  
  .stat-content {
    margin-bottom: 4px;
  }
}

.toolbar-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-left .el-button {
  margin-right: 12px;
}

.search-filters {
  display: flex;
  align-items: center;
}

.list-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.plan-title {
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.plan-detail .content-text {
  line-height: 1.6;
  white-space: pre-wrap;
}

.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: flex-start;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  min-width: 50px;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-button--primary) {
  background-color: var(--china-red);
  border-color: var(--china-red);
}

:deep(.el-button--primary:hover) {
  background-color: #a52525;
  border-color: #a52525;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .workplan-container {
    padding: 16px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .search-filters {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-filters .el-select,
  .search-filters .el-input {
    width: 100% !important;
  }
  
  .page-header h2 {
    font-size: 20px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;
  }
  
  .action-buttons .el-button {
    width: 100%;
    min-width: auto;
  }
}
</style> 