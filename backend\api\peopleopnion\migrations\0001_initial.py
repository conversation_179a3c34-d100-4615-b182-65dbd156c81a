# Generated by Django 4.2.16 on 2025-01-17 12:00

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PeopleOpinion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.<PERSON>r<PERSON><PERSON>(max_length=200, verbose_name='标题')),
                ('content', models.TextField(verbose_name='内容')),
                ('contact_info', models.CharField(blank=True, max_length=100, null=True, verbose_name='联系方式')),
                ('name', models.CharField(blank=True, max_length=50, null=True, verbose_name='姓名')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='提交时间')),
            ],
            options={
                'verbose_name': '群众意见',
                'verbose_name_plural': '群众意见',
                'db_table': 'people_opinion',
                'ordering': ['-created_at'],
            },
        ),
    ] 