<template>
  <div class="ranking-container">
    <div class="ranking-columns">
      <div class="ranking-column">
        <div
          class="ranking-item"
          v-for="(item, index) in leftColumnData"
          :key="'left-' + index"
        >
          <div class="avatar">
            <img :src="item.avatar" :alt="item.name" />
          </div>
          <div class="info">
            <div class="name">{{ item.name }}</div>
            <div class="score">{{ item.score }}</div>
          </div>
        </div>
      </div>
      <div class="ranking-column">
        <div
          class="ranking-item"
          v-for="(item, index) in rightColumnData"
          :key="'right-' + index"
        >
          <div class="avatar">
            <img :src="item.avatar" :alt="item.name" />
          </div>
          <div class="info">
            <div class="name">{{ item.name }}</div>
            <div class="score">{{ item.score }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    rankingData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    leftColumnData() {
      return this.rankingData.filter((_, index) => index % 2 === 0)
    },
    rightColumnData() {
      return this.rankingData.filter((_, index) => index % 2 === 1)
    },
  },
  methods: {},
}
</script>

<style scoped>
.ranking-container {
  width: 100%;
  height: 95%;
  /* background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); */
  border-radius: 8px;
  padding: 20px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.ranking-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
}

.ranking-columns {
  display: flex;
  justify-content: space-between;
  height: 100%;
  position: relative;
  z-index: 1;
  overflow-y: auto;
}

.ranking-columns::-webkit-scrollbar {
  display: none;
}

.ranking-columns {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.ranking-column {
  width: 48%;
  display: flex;
  flex-direction: column;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.ranking-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.name {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.score {
  color: #ffd700;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
</style>
