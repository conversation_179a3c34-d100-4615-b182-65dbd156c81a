import { get } from './request.js'

/**
 * 代表履职榜单API
 * 提供人大代表履职数量排行榜数据
 */

/**
 * 获取代表履职榜单数据（真实API接口，暂时注释）
 * @param {Object} params - 查询参数
 * @param {number} params.pageSize - 页面大小，默认20
 * @param {number} params.pageNum - 页码，默认1
 * @returns {Promise} 返回履职榜单数据
 */
// export const getDutyRankingReal = (params = {}) => {
//   return get('/api/representative/duty-ranking', params)
// }

/**
 * 获取代表履职详情
 * @param {string} representativeId - 代表ID
 * @returns {Promise} 返回代表履职详情
 */
export const getRepresentativeDutyDetail = (representativeId) => {
  return get(`/api/representative/duty-detail/${representativeId}`)
}

// ==================== Mock 数据 ====================

/**
 * 模拟代表履职榜单数据
 * 包含代表头像、姓名、履职总数等信息
 */
const mockRankingData = {
  list: [
    { id: '001', name: '张明华', avatar: '👨', dutyCount: 156, department: '市人大常委' },
    { id: '002', name: '李秀英', avatar: '👩', dutyCount: 142, department: '县人大代表' },
    { id: '003', name: '王建国', avatar: '👨', dutyCount: 138, department: '镇人大代表' },
    { id: '004', name: '陈丽娟', avatar: '👩', dutyCount: 135, department: '市人大代表' },
    { id: '005', name: '刘志强', avatar: '👨', dutyCount: 128, department: '省人大代表' },
    { id: '006', name: '赵美玲', avatar: '👩', dutyCount: 125, department: '县人大代表' },
    { id: '007', name: '孙德华', avatar: '👨', dutyCount: 122, department: '镇人大代表' },
    { id: '008', name: '周晓燕', avatar: '👩', dutyCount: 118, department: '市人大代表' },
    { id: '009', name: '吴国庆', avatar: '👨', dutyCount: 115, department: '县人大代表' },
    { id: '010', name: '郑红梅', avatar: '👩', dutyCount: 112, department: '镇人大代表' },
    { id: '011', name: '冯立军', avatar: '👨', dutyCount: 108, department: '市人大代表' },
    { id: '012', name: '何春花', avatar: '👩', dutyCount: 105, department: '县人大代表' },
    { id: '013', name: '蒋文斌', avatar: '👨', dutyCount: 102, department: '省人大代表' },
    { id: '014', name: '韩雨晴', avatar: '👩', dutyCount: 98, department: '镇人大代表' },
    { id: '015', name: '魏振东', avatar: '👨', dutyCount: 95, department: '市人大代表' },
    { id: '016', name: '邓慧敏', avatar: '👩', dutyCount: 92, department: '县人大代表' },
    { id: '017', name: '曹永红', avatar: '👨', dutyCount: 89, department: '镇人大代表' },
    { id: '018', name: '袁淑华', avatar: '👩', dutyCount: 86, department: '市人大代表' },
    { id: '019', name: '彭国强', avatar: '👨', dutyCount: 83, department: '县人大代表' },
    { id: '020', name: '薛丽萍', avatar: '👩', dutyCount: 80, department: '镇人大代表' },
    { id: '021', name: '贺志明', avatar: '👨', dutyCount: 77, department: '省人大代表' },
    { id: '022', name: '苏晓琳', avatar: '👩', dutyCount: 74, department: '市人大代表' },
    { id: '023', name: '龚建华', avatar: '👨', dutyCount: 71, department: '县人大代表' },
    { id: '024', name: '谭秀珍', avatar: '👩', dutyCount: 68, department: '镇人大代表' },
    { id: '025', name: '段文军', avatar: '👨', dutyCount: 65, department: '市人大代表' },
    { id: '026', name: '罗春燕', avatar: '👩', dutyCount: 62, department: '县人大代表' },
    { id: '027', name: '黄德明', avatar: '👨', dutyCount: 59, department: '镇人大代表' },
    { id: '028', name: '林雅琴', avatar: '👩', dutyCount: 56, department: '市人大代表' },
    { id: '029', name: '梁国华', avatar: '👨', dutyCount: 53, department: '县人大代表' },
    { id: '030', name: '叶美霞', avatar: '👩', dutyCount: 50, department: '镇人大代表' }
  ],
  pagination: {
    total: 30,
    pageSize: 20,
    pageNum: 1,
    pages: 2
  }
}

// 模拟API请求延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 随机排列函数
const shuffleArray = (array) => {
  const newArray = [...array]
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[newArray[i], newArray[j]] = [newArray[j], newArray[i]]
  }
  return newArray
}

// 重写API函数以使用Mock数据
export const getDutyRanking = async (params = {}) => {
  await delay(600) // 模拟网络延迟
  
  try {
    const { pageSize = 20, pageNum = 1 } = params
    
    console.log('🏆 请求代表履职榜单数据 - 页面大小:', pageSize, '页码:', pageNum)
    
    // 随机排列数据（每次请求都会重新排列）
    const shuffledList = shuffleArray(mockRankingData.list)
    
    // 分页处理
    const startIndex = (pageNum - 1) * pageSize
    const endIndex = startIndex + pageSize
    const pageData = shuffledList.slice(startIndex, endIndex)
    
    return {
      code: 200,
      message: '获取成功',
      data: {
        list: pageData,
        pagination: {
          total: mockRankingData.list.length,
          pageSize,
          pageNum,
          pages: Math.ceil(mockRankingData.list.length / pageSize)
        }
      },
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    console.error('❌ Mock API 错误:', error.message)
    return {
      code: 500,
      message: error.message,
      data: {
        list: [],
        pagination: { total: 0, pageSize: 20, pageNum: 1, pages: 0 }
      }
    }
  }
}

export default {
  getDutyRanking,
  getRepresentativeDutyDetail
} 