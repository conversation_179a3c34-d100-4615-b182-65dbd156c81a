import request from '../../http/client'

/**
 * 群众意见API服务
 */

// 群众提交意见（匿名访问）
const submitPeopleOpinion = async (opinionData) => {
  try {
    console.log('📤 提交群众意见:', opinionData)
    
    const response = await request.post('/people-opinions/submit/', opinionData)
    
    console.log('✅ 群众意见提交成功:', response.data)
    return response.data
  } catch (error) {
    console.error('❌ 群众意见提交失败:', error)
    throw error
  }
}

// 获取群众意见列表（工作人员使用）
const getPeopleOpinionList = async (params = {}) => {
  try {
    console.log('📋 获取群众意见列表:', params)
    
    const response = await request.get('/people-opinions/', { params })
    
    console.log('✅ 群众意见列表获取成功:', response.data)
    return response.data
  } catch (error) {
    console.error('❌ 群众意见列表获取失败:', error)
    throw error
  }
}

// 获取群众意见详情
const getPeopleOpinionDetail = async (opinionId) => {
  try {
    console.log('📄 获取群众意见详情:', opinionId)
    
    const response = await request.get(`/people-opinions/${opinionId}/`)
    
    console.log('✅ 群众意见详情获取成功:', response.data)
    return response.data
  } catch (error) {
    console.error('❌ 群众意见详情获取失败:', error)
    throw error
  }
}

// 更新群众意见
const updatePeopleOpinion = async (opinionId, opinionData) => {
  try {
    console.log('📝 更新群众意见:', opinionId, opinionData)
    
    const response = await request.put(`/people-opinions/${opinionId}/`, opinionData)
    
    console.log('✅ 群众意见更新成功:', response.data)
    return response.data
  } catch (error) {
    console.error('❌ 群众意见更新失败:', error)
    throw error
  }
}

// 删除群众意见
const deletePeopleOpinion = async (opinionId) => {
  try {
    console.log('🗑️ 删除群众意见:', opinionId)
    
    const response = await request.delete(`/people-opinions/${opinionId}/`)
    
    console.log('✅ 群众意见删除成功:', response.data)
    return response.data
  } catch (error) {
    console.error('❌ 群众意见删除失败:', error)
    throw error
  }
}

// 删除统计API

/**
 * 群众意见API对象
 */
export const peopleOpinionAPI = {
  submitPeopleOpinion,
  getPeopleOpinionList,
  getPeopleOpinionDetail,
  updatePeopleOpinion,
  deletePeopleOpinion
}

// 为了向后兼容，同时导出单个函数
export {
  submitPeopleOpinion,
  getPeopleOpinionList,
  getPeopleOpinionDetail,
  updatePeopleOpinion,
  deletePeopleOpinion
} 