import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/datav'
  },
  {
    path: '/datav',
    name: 'Datav',
    component: () => import('@/components/indexscreen/DataScreenIndex.vue'),
    meta: {
      title: '数据可视化',
      requiresAuth: false,
      useApi: true  // 使用后端API数据
    }
  },
  {
    path: '/datav2',
    name: 'Datav2',
    component: () => import('@/components/indexscreen/DataScreenIndex.vue'),
    meta: {
      title: '数据可视化(静态)',
      requiresAuth: false,
      useApi: false  // 使用静态data.json数据
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { 
      title: '登录',
      requiresAuth: false 
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { 
      title: '工作台',
      requiresAuth: true 
    }
  },
  {
    path: '/representative',
    name: 'Representative',
    component: () => import('@/views/representative/Index.vue'),
    meta: { 
      title: '人大代表工作台',
      requiresAuth: true,
      roles: ['representative']
    },
    children: [
      {
        path: 'profile',
        name: 'RepresentativeProfile',
        component: () => import('@/views/representative/Profile.vue'),
        meta: { 
          title: '个人信息管理',
          requiresAuth: true,
          roles: ['representative']
        }
      },
      {
        path: 'records',
        name: 'RepresentativeRecords',
        component: () => import('@/views/representative/Records.vue'),
        meta: { 
          title: '履职记录管理',
          requiresAuth: true,
          roles: ['representative']
        }
      },
      {
        path: 'annual-achievements',
        name: 'RepresentativeAnnualAchievements',
        component: () => import('@/views/representative/AnnualAchievements.vue'),
        meta: { 
          title: '年度履职AI分析展示',
          requiresAuth: true,
          roles: ['representative']
        }
      },
      {
        path: 'opinions',
        name: 'RepresentativeOpinions',
        component: () => import('@/views/representative/Opinions.vue'),
        meta: { 
          title: '意见建议',
          requiresAuth: true,
          roles: ['representative']
        }
      },

      {
        path: 'knowledge-qa',
        name: 'RepresentativeKnowledgeQA',
        component: () => import('@/views/KnowledgeQA.vue'),
        meta: { 
          title: '法律政策互动AI问答',
          requiresAuth: true,
          roles: ['representative']
        }
      },
      {
        path: 'password-change',
        name: 'RepresentativePasswordChange',
        component: () => import('@/views/representative/PasswordChange.vue'),
        meta: { 
          title: '账号密码修改',
          requiresAuth: true,
          roles: ['representative']
        }
      }
    ]
  },
  {
    path: '/staff',
    name: 'Staff',
    component: () => import('@/views/staff/Index.vue'),
    meta: { 
      title: '站点工作人员工作台',
      requiresAuth: true,
      roles: ['staff']
    },
    children: [
      {
        path: 'review',
        name: 'StaffReview',
        component: () => import('@/views/staff/Review.vue'),
        meta: { 
          title: '意见建议审核',
          requiresAuth: true,
          roles: ['staff']
        }
      },
      {
        path: 'work-plan',
        name: 'StaffWorkPlan',
        component: () => import('@/views/staff/WorkPlan.vue'),
        meta: { 
          title: '工作计划管理',
          requiresAuth: true,
          roles: ['staff']
        }
      },
      {
        path: 'work-analysis/site-summary',
        name: 'StaffSiteSummary',
        component: () => import('@/views/staff/SiteSummary.vue'),
        meta: { 
          title: '站点工作总结',
          requiresAuth: true,
          roles: ['staff']
        }
      },
      {
        path: 'work-analysis/representative-summary',
        name: 'StaffRepresentativeSummary',
        component: () => import('@/views/staff/RepresentativeSummary.vue'),
        meta: { 
          title: '代表工作总结',
          requiresAuth: true,
          roles: ['staff']
        }
      },
      {
        path: 'knowledge-qa',
        name: 'StaffKnowledgeQA',
        component: () => import('@/views/KnowledgeQA.vue'),
        meta: { 
          title: '法律政策互动AI问答',
          requiresAuth: true,
          roles: ['staff']
        }
      },
      {
        path: 'account-management',
        name: 'StaffAccountManagement',
        component: () => import('@/views/staff/AccountManagement.vue'),
        meta: { 
          title: '账号管理',
          requiresAuth: true,
          roles: ['staff']
        }
      },
      {
        path: 'people-opinion-list',
        name: 'StaffPeopleOpinionList',
        component: () => import('@/views/staff/PeopleOpinionList.vue'),
        meta: { 
          title: '群众意见管理',
          requiresAuth: true,
          roles: ['staff']
        }
      }
    ]
  },
  {
    path: '/notifications',
    name: 'NotificationCenter',
    component: () => import('@/views/NotificationCenter.vue'),
    meta: { 
      title: '通知中心',
      requiresAuth: true 
    }
  },
  // 群众意见提交页面（无需认证）
  {
    path: '/people-opinion',
    name: 'PeopleOpinion',
    component: () => import('@/views/PeopleOpinion.vue'),
    meta: { 
      title: '群众意见提交',
      requiresAuth: false 
    }
  },
  // 系统错误页面
  {
    path: '/network-error',
    name: 'NetworkError',
    component: () => import('@/views/system/NetworkError.vue'),
    meta: {
      title: '网络连接失败',
      requiresAuth: false
    }
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/system/NotFound.vue'),
    meta: {
      title: '页面不存在',
      requiresAuth: false
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 人大代表履职服务与管理平台`
  }
  
  // 不需要认证的页面直接通过
  if (!to.meta.requiresAuth) {
    next()
    return
  }

  // 对于需要认证的页面，确保用户状态已正确初始化
  if (!userStore.isLoggedIn) {
    // 尝试从本地存储恢复认证状态（不跳过验证）
    userStore.initializeAuth(false)

    // 给initializeAuth一点时间来恢复状态
    await new Promise(resolve => setTimeout(resolve, 50))
  }

  console.log('🔐 路由守卫检查认证状态:', {
    to: to.path,
    isLoggedIn: userStore.isLoggedIn,
    isAuthValid: userStore.isAuthValid(),
    userRole: userStore.userInfo.role
  })

  // 检查用户是否已登录且认证状态有效
  if (!userStore.isLoggedIn || !userStore.isAuthValid()) {
    console.log('❌ 认证状态无效，重定向到登录页')

    // 如果当前不在登录页，保存目标路由用于登录后跳转
    if (to.path !== '/login') {
      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`)
    } else {
      next('/login')
    }
    return
  }
  
  // 检查角色权限
  if (to.meta.roles && !to.meta.roles.includes(userStore.userInfo.role)) {
    console.log('❌ 角色权限不足:', {
      required: to.meta.roles,
      current: userStore.userInfo.role
    })
    
    // 根据用户角色重定向到对应的工作台
    if (userStore.userInfo.role === 'representative') {
      next('/representative')
    } else if (userStore.userInfo.role === 'staff') {
      next('/staff')
    } else {
      next('/dashboard')
    }
    return
  }
  
  console.log('✅ 路由守卫通过')
  next()
})

export default router 