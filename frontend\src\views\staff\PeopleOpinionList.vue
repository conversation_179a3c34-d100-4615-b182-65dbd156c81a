<template>
  <div class="people-opinion-container">
    <div class="page-header">
      <h2>群众意见管理</h2>
      <p>管理和处理群众反馈的意见建议</p>
    </div>

    <!-- 工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-form
            :model="searchForm"
            inline
            @submit.prevent="handleSearch"
          >
            <el-form-item>
              <el-input
                v-model="searchForm.search"
                placeholder="输入标题、内容或姓名进行搜索"
                style="width: 300px"
                clearable
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            

            
            <el-form-item>
              <el-button type="primary" @click="handleSearch" :icon="Search">
                搜索
              </el-button>
              <el-button @click="handleResetSearch" :icon="Refresh">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-card>

    <!-- 意见列表 -->
    <el-card class="list-card">
      <el-table
        v-loading="loading"
        :data="opinions"
        empty-text="暂无群众意见"
      >
        <el-table-column prop="title" label="意见标题" min-width="200">
          <template #default="{ row }">
            <div class="opinion-title">
              <el-text tag="b">{{ row.title }}</el-text>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="反馈人" width="100" />
        <el-table-column prop="contact_info" label="联系方式" width="140" />
        <el-table-column prop="content" label="意见内容" min-width="250">
          <template #default="{ row }">
            <el-text class="content-preview" line-clamp="2">
              {{ row.content }}
            </el-text>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="提交时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                size="small"
                @click="handleViewDetail(row)"
              >
                查看
              </el-button>
              <el-button
                size="small"
                type="primary"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :total="pagination.total_count"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="意见详情"
      width="600px"
    >
      <div v-if="currentOpinion" class="opinion-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="意见标题" :span="2">
            <el-text tag="b">{{ currentOpinion.title }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="反馈人">
            {{ currentOpinion.name }}
          </el-descriptions-item>
          <el-descriptions-item label="联系方式">
            {{ currentOpinion.contact_info }}
          </el-descriptions-item>
          <el-descriptions-item label="提交时间" :span="2">
            {{ formatTime(currentOpinion.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="详细内容" :span="2">
            <div class="content-text">{{ currentOpinion.content }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑意见"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="120px"
        size="large"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="意见标题" prop="title">
              <el-input
                v-model="editForm.title"
                placeholder="请输入意见标题"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="反馈人" prop="name">
              <el-input
                v-model="editForm.name"
                placeholder="请输入反馈人姓名"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="contact_info">
              <el-input
                v-model="editForm.contact_info"
                placeholder="请输入联系方式"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="详细内容" prop="content">
              <el-input
                v-model="editForm.content"
                type="textarea"
                :rows="4"
                placeholder="请输入详细内容"
                maxlength="2000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit" :loading="saving">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh
} from '@element-plus/icons-vue'
import {
  getPeopleOpinionList,
  getPeopleOpinionDetail,
  updatePeopleOpinion,
  deletePeopleOpinion
} from '@/api/modules/people-opinion/api'

console.log('📋 群众意见列表页面加载完成')

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const opinions = ref([])

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total_count: 0,
  total_pages: 0
})

// 搜索表单
const searchForm = reactive({
  search: ''
})

// 弹窗状态
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const currentOpinion = ref(null)

// 编辑表单
const editFormRef = ref()
const editForm = reactive({
  id: null,
  title: '',
  name: '',
  contact_info: '',
  content: ''
})

// 编辑表单验证规则
const editRules = {
  title: [
    { required: true, message: '请输入意见标题', trigger: 'blur' },
    { min: 5, message: '标题至少需要5个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入反馈人姓名', trigger: 'blur' }
  ],
  contact_info: [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入详细内容', trigger: 'blur' },
    { min: 10, message: '内容至少需要10个字符', trigger: 'blur' }
  ]
}

// 加载意见列表
const loadOpinionList = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      page_size: pagination.page_size,
      ...searchForm
    }
    
    const response = await getPeopleOpinionList(params)
    
    if (response.success) {
      opinions.value = response.data.opinions
      Object.assign(pagination, response.data.pagination)
    } else {
      ElMessage.error(response.message || '获取意见列表失败')
    }
  } catch (error) {
    console.error('❌ 加载意见列表失败:', error)
    ElMessage.error('加载失败，请重试')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadOpinionList()
}

// 重置搜索
const handleResetSearch = () => {
  Object.assign(searchForm, {
    search: ''
  })
  pagination.page = 1
  loadOpinionList()
}

// 分页处理
const handlePageChange = (page) => {
  pagination.page = page
  loadOpinionList()
}

const handlePageSizeChange = (pageSize) => {
  pagination.page_size = pageSize
  pagination.page = 1
  loadOpinionList()
}

// 查看详情
const handleViewDetail = async (opinion) => {
  try {
    const response = await getPeopleOpinionDetail(opinion.id)
    if (response.success) {
      currentOpinion.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取详情失败')
    }
  } catch (error) {
    console.error('❌ 获取详情失败:', error)
    ElMessage.error('获取详情失败，请重试')
  }
}

// 编辑
const handleEdit = (opinion) => {
  Object.assign(editForm, {
    id: opinion.id,
    title: opinion.title,
    name: opinion.name,
    contact_info: opinion.contact_info,
    content: opinion.content
  })
  editDialogVisible.value = true
}

// 保存编辑
const handleSaveEdit = async () => {
  try {
    await editFormRef.value?.validate()
    
    saving.value = true
    
    const { id, ...updateData } = editForm
    const response = await updatePeopleOpinion(id, updateData)
    
    if (response.success) {
      ElMessage.success('更新成功')
      editDialogVisible.value = false
      loadOpinionList()
    } else {
      ElMessage.error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('❌ 更新失败:', error)
    if (typeof error === 'string') {
      // 表单验证错误
      return
    }
    ElMessage.error('更新失败，请重试')
  } finally {
    saving.value = false
  }
}

// 删除
const handleDelete = async (opinion) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除"${opinion.title}"这条意见吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await deletePeopleOpinion(opinion.id)
    
    if (response.success) {
      ElMessage.success('删除成功')
      loadOpinionList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error === 'cancel') {
      return
    }
    console.error('❌ 删除失败:', error)
    ElMessage.error('删除失败，请重试')
  }
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''
  const date = new Date(timeString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 页面挂载时加载数据
onMounted(() => {
  loadOpinionList()
})
</script>

<style scoped>
.people-opinion-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  color: var(--china-red);
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.toolbar-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.toolbar-left .el-form {
  margin: 0;
}

.toolbar-left .el-form-item {
  margin-right: 12px;
  margin-bottom: 0;
}

.list-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.opinion-title {
  display: flex;
  align-items: center;
}

.content-preview {
  color: #666;
  line-height: 1.4;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.opinion-detail .content-text {
  line-height: 1.6;
  white-space: pre-wrap;
}

.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: flex-start;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  min-width: 50px;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-button--primary) {
  background-color: var(--china-red);
  border-color: var(--china-red);
}

:deep(.el-button--primary:hover) {
  background-color: #a52525;
  border-color: #a52525;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .people-opinion-container {
    padding: 16px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .toolbar-left .el-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .page-header h2 {
    font-size: 20px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;
  }
  
  .action-buttons .el-button {
    width: 100%;
    min-width: auto;
  }
}
</style> 