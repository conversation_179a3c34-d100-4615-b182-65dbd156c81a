"""
用户管理序列化器

包含以下序列化器：
1. UserLoginSerializer - 用户登录序列化器
2. UserSerializer - 用户基础信息序列化器
3. RepresentativeSerializer - 人大代表信息序列化器
4. StaffMemberSerializer - 站点工作人员信息序列化器
5. PasswordChangeSerializer - 密码修改序列化器
6. UserProfileSerializer - 用户完整信息序列化器
"""

from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import User, Representative, StaffMember
import re


class UserLoginSerializer(serializers.Serializer):
    """用户登录序列化器"""
    
    username = serializers.CharField(
        max_length=50,
        required=True,
        help_text='用户名',
        error_messages={
            'required': '用户名不能为空',
            'max_length': '用户名长度不能超过50个字符'
        }
    )
    
    password = serializers.CharField(
        max_length=128,
        required=True,
        write_only=True,
        style={'input_type': 'password'},
        help_text='密码',
        error_messages={
            'required': '密码不能为空'
        }
    )
    
    def validate(self, attrs):
        """验证用户名和密码"""
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            # 尝试认证用户
            user = authenticate(
                request=self.context.get('request'),
                username=username,
                password=password
            )
            
            if not user:
                raise serializers.ValidationError(
                    '用户名或密码错误',
                    code='authorization'
                )
            
            if not user.is_active:
                raise serializers.ValidationError(
                    '用户账号已被禁用',
                    code='authorization'
                )
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError(
                '必须包含用户名和密码',
                code='authorization'
            )


class UserSerializer(serializers.ModelSerializer):
    """用户基础信息序列化器"""

    # 密码字段只写不读
    password = serializers.CharField(
        write_only=True,
        required=False,
        style={'input_type': 'password'},
        help_text='密码'
    )

    # 角色显示名称
    role_display = serializers.CharField(
        source='get_role_display',
        read_only=True,
        help_text='用户角色显示名称'
    )

    # 角色相关信息（用于显示真实姓名等基本信息）
    representative_info = serializers.SerializerMethodField(
        help_text='人大代表基本信息（仅代表用户有此字段）'
    )

    staff_info = serializers.SerializerMethodField(
        help_text='工作人员基本信息（仅工作人员用户有此字段）'
    )

    def get_representative_info(self, obj):
        """获取代表基本信息"""
        if obj.role == 'representative' and hasattr(obj, 'representative'):
            return {
                'name': obj.representative.name,
                'level': obj.representative.level,
                'mobile_phone': obj.representative.mobile_phone
            }
        return None

    def get_staff_info(self, obj):
        """获取工作人员基本信息"""
        if obj.role == 'staff' and hasattr(obj, 'staffmember'):
            return {
                'name': obj.staffmember.name,
                'position': obj.staffmember.position,
                'mobile_phone': obj.staffmember.mobile_phone,
                'station_name': obj.staffmember.station_name
            }
        return None

    class Meta:
        model = User
        fields = [
            'id', 'username', 'password', 'role', 'role_display',
            'is_active', 'last_login_at', 'created_at', 'updated_at',
            'representative_info', 'staff_info'
        ]
        read_only_fields = ['id', 'last_login_at', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        """创建用户时加密密码"""
        password = validated_data.pop('password', None)
        user = super().create(validated_data)
        
        if password:
            user.set_password(password)
            user.save()
        
        return user
    
    def update(self, instance, validated_data):
        """更新用户时处理密码"""
        password = validated_data.pop('password', None)
        user = super().update(instance, validated_data)
        
        if password:
            user.set_password(password)
            user.save()
        
        return user


class RepresentativeListSerializer(serializers.ModelSerializer):
    """人大代表列表序列化器（不包含头像，用于列表显示优化性能）"""

    # 用户信息（嵌套序列化）
    user = UserSerializer(read_only=True)

    # 性别显示名称
    gender_display = serializers.CharField(
        source='get_gender_display',
        read_only=True,
        help_text='性别显示名称'
    )

    # 年龄（计算属性）
    age = serializers.ReadOnlyField(help_text='根据出生日期计算的年龄')

    # 层级列表（用于前端多选处理）
    level_list = serializers.SerializerMethodField(help_text='层级列表')

    class Meta:
        model = Representative
        fields = [
            'id', 'user', 'level', 'level_list', 'name', 'gender', 'gender_display',
            'nationality', 'district', 'birth_date', 'birthplace', 'party',
            'current_position', 'mobile_phone', 'education',
            'graduated_school', 'major', 'age', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_level_list(self, obj):
        """获取层级列表"""
        return obj.get_level_list()


class RepresentativeSerializer(serializers.ModelSerializer):
    """人大代表信息序列化器（包含头像，用于详情显示）"""

    # 用户信息（嵌套序列化）
    user = UserSerializer(read_only=True)

    # 性别显示名称
    gender_display = serializers.CharField(
        source='get_gender_display',
        read_only=True,
        help_text='性别显示名称'
    )

    # 年龄（计算属性）
    age = serializers.ReadOnlyField(help_text='根据出生日期计算的年龄')

    # 层级列表（用于前端多选处理）
    level_list = serializers.SerializerMethodField(help_text='层级列表')

    # 构成列表（用于前端多选处理）
    composition_list = serializers.SerializerMethodField(help_text='构成列表')

    class Meta:
        model = Representative
        fields = [
            'id', 'user', 'level', 'level_list', 'composition', 'composition_list',
            'name', 'gender', 'gender_display', 'nationality', 'district',
            'birth_date', 'birthplace', 'party', 'current_position', 'mobile_phone',
            'education', 'graduated_school', 'major', 'avatar', 'age',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_level_list(self, obj):
        """获取层级列表"""
        return obj.get_level_list()

    def get_composition_list(self, obj):
        """获取构成列表"""
        return obj.get_composition_list()
    
    def validate_mobile_phone(self, value):
        """验证手机号码格式"""
        import re
        if not re.match(r'^1[3-9]\d{9}$', value):
            raise serializers.ValidationError('请输入有效的手机号码格式')
        return value

    def validate_avatar(self, value):
        """验证头像数据格式"""
        if value is None or value == '':
            return value

        # 检查是否是base64格式
        if not value.startswith('data:image/'):
            raise serializers.ValidationError('头像必须是base64格式的图片数据')

        # 检查数据大小（限制为2MB）
        import base64
        try:
            # 提取base64数据部分
            if ',' in value:
                base64_data = value.split(',')[1]
            else:
                base64_data = value

            # 计算解码后的大小
            decoded_size = len(base64.b64decode(base64_data))
            max_size = 2 * 1024 * 1024  # 2MB

            if decoded_size > max_size:
                raise serializers.ValidationError('头像文件大小不能超过2MB')

        except Exception:
            raise serializers.ValidationError('无效的base64图片数据')

        return value


class StaffMemberSerializer(serializers.ModelSerializer):
    """站点工作人员信息序列化器"""
    
    # 用户信息（嵌套序列化）
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = StaffMember
        fields = [
            'id', 'user', 'name', 'position', 'mobile_phone',
            'email', 'station_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_mobile_phone(self, value):
        """验证手机号码格式"""
        import re
        if not re.match(r'^1[3-9]\d{9}$', value):
            raise serializers.ValidationError('请输入有效的手机号码格式')
        return value


class PasswordChangeSerializer(serializers.Serializer):
    """密码修改序列化器"""
    
    old_password = serializers.CharField(
        required=True,
        write_only=True,
        style={'input_type': 'password'},
        help_text='当前密码',
        error_messages={
            'required': '当前密码不能为空'
        }
    )
    
    new_password = serializers.CharField(
        required=True,
        write_only=True,
        style={'input_type': 'password'},
        help_text='新密码',
        error_messages={
            'required': '新密码不能为空'
        }
    )
    
    confirm_password = serializers.CharField(
        required=True,
        write_only=True,
        style={'input_type': 'password'},
        help_text='确认新密码',
        error_messages={
            'required': '确认密码不能为空'
        }
    )
    
    def validate_old_password(self, value):
        """验证当前密码"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('当前密码错误')
        return value
    
    def validate(self, attrs):
        """验证新密码和确认密码是否一致"""
        new_password = attrs.get('new_password')
        confirm_password = attrs.get('confirm_password')
        
        if new_password != confirm_password:
            raise serializers.ValidationError({
                'confirm_password': '两次输入的密码不一致'
            })
        
        # 自定义密码验证器，返回中文错误信息
        try:
            validate_password(new_password, self.context['request'].user)
        except ValidationError as e:
            # 将Django的英文错误信息转换为中文
            chinese_errors = []
            for error in e.messages:
                if 'too short' in error:
                    chinese_errors.append('密码长度至少需要8个字符')
                elif 'too common' in error:
                    chinese_errors.append('这个密码太常见了，请设置更复杂的密码')
                elif 'entirely numeric' in error:
                    chinese_errors.append('密码不能全部为数字')
                elif 'too similar' in error:
                    chinese_errors.append('密码不能与个人信息过于相似')
                else:
                    chinese_errors.append(error)
            
            raise serializers.ValidationError({
                'new_password': chinese_errors
            })
        
        return attrs
    
    def save(self):
        """保存新密码"""
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user


class UserProfileSerializer(serializers.ModelSerializer):
    """用户完整信息序列化器（包含角色相关信息）"""
    
    # 用户基础信息
    role_display = serializers.CharField(
        source='get_role_display',
        read_only=True,
        help_text='用户角色显示名称'
    )
    
    # 角色相关信息（根据用户角色动态包含）
    representative_info = RepresentativeSerializer(
        source='representative',
        read_only=True,
        help_text='人大代表信息（仅代表用户有此字段）'
    )
    
    staff_info = StaffMemberSerializer(
        source='staffmember',
        read_only=True,
        help_text='工作人员信息（仅工作人员用户有此字段）'
    )
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'role', 'role_display', 'is_active',
            'last_login_at', 'created_at', 'updated_at',
            'representative_info', 'staff_info'
        ]
        read_only_fields = [
            'id', 'username', 'role', 'last_login_at',
            'created_at', 'updated_at'
        ]
    
    def to_representation(self, instance):
        """自定义序列化输出，根据用户角色只返回相关信息"""
        data = super().to_representation(instance)
        
        # 根据用户角色过滤不相关的字段
        if instance.role == 'representative':
            # 代表用户移除工作人员信息
            data.pop('staff_info', None)
        elif instance.role == 'staff':
            # 工作人员用户移除代表信息
            data.pop('representative_info', None)
        else:
            # 其他情况移除所有角色信息
            data.pop('representative_info', None)
            data.pop('staff_info', None)
        
        return data
    
    def update(self, instance, validated_data):
        """更新用户信息，包括相关联的代表或工作人员信息"""
        # 更新User模型的字段
        for attr, value in validated_data.items():
            if hasattr(instance, attr):
                setattr(instance, attr, value)
        instance.save()

        # 如果是代表用户，检查是否有代表信息需要更新
        if (instance.role == 'representative' and
            hasattr(instance, 'representative') and
            self.initial_data):

            # 从请求数据中提取代表信息字段
            representative_data = {}
            representative_fields = [
                'level', 'composition', 'name', 'gender', 'nationality', 'birth_date',
                'birthplace', 'party', 'current_position', 'mobile_phone',
                'education', 'graduated_school', 'major'
            ]

            for field in representative_fields:
                if field in self.initial_data:
                    representative_data[field] = self.initial_data[field]

            # 如果有代表信息需要更新
            if representative_data:
                representative = instance.representative
                for attr, value in representative_data.items():
                    if hasattr(representative, attr):
                        setattr(representative, attr, value)
                representative.save()

        # 如果是工作人员用户，检查是否有工作人员信息需要更新
        elif (instance.role == 'staff' and
              hasattr(instance, 'staffmember') and
              self.initial_data):

            # 从请求数据中提取工作人员信息字段
            staff_data = {}
            staff_fields = [
                'name', 'position', 'mobile_phone', 'email', 'station_name'
            ]

            for field in staff_fields:
                if field in self.initial_data:
                    staff_data[field] = self.initial_data[field]

            # 如果有工作人员信息需要更新
            if staff_data:
                staff_member = instance.staffmember
                for attr, value in staff_data.items():
                    if hasattr(staff_member, attr):
                        setattr(staff_member, attr, value)
                staff_member.save()

        return instance


class UserCreateSerializer(serializers.ModelSerializer):
    """用户创建序列化器（管理员使用）"""
    
    password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'},
        help_text='密码'
    )
    
    confirm_password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'},
        help_text='确认密码'
    )
    
    class Meta:
        model = User
        fields = [
            'username', 'password', 'confirm_password', 'role', 'is_active'
        ]
    
    def validate(self, attrs):
        """验证密码和确认密码是否一致"""
        password = attrs.get('password')
        confirm_password = attrs.pop('confirm_password', None)
        
        if password != confirm_password:
            raise serializers.ValidationError({
                'confirm_password': '两次输入的密码不一致'
            })
        
        # 使用Django的密码验证器
        try:
            validate_password(password)
        except ValidationError as e:
            raise serializers.ValidationError({
                'password': list(e.messages)
            })
        
        return attrs
    
    def create(self, validated_data):
        """创建用户"""
        password = validated_data.pop('password')
        user = User.objects.create_user(password=password, **validated_data)
        return user


class AccountCreateSerializer(serializers.Serializer):
    """账号创建序列化器（包含角色相关信息）"""
    
    # 用户基础信息
    username = serializers.CharField(
        max_length=50,
        required=True,
        help_text='用户名'
    )
    
    password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'},
        help_text='密码'
    )
    
    confirm_password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'},
        help_text='确认密码'
    )
    
    role = serializers.ChoiceField(
        choices=User.ROLE_CHOICES,
        required=True,
        help_text='用户角色'
    )
    
    is_active = serializers.BooleanField(
        default=True,
        required=False,
        help_text='是否激活'
    )
    
    # 人大代表信息（当role=representative时需要）
    representative_level = serializers.CharField(
        max_length=50,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='代表层级'
    )

    representative_district = serializers.CharField(
        max_length=20,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='所属片区'
    )
    
    representative_name = serializers.CharField(
        max_length=50,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='代表姓名'
    )
    
    representative_gender = serializers.ChoiceField(
        choices=Representative.GENDER_CHOICES,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='性别'
    )
    
    representative_nationality = serializers.CharField(
        max_length=50,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='民族'
    )
    
    representative_birth_date = serializers.DateField(
        required=False,
        allow_null=True,
        help_text='出生日期'
    )
    
    representative_birthplace = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='籍贯'
    )
    
    representative_party = serializers.CharField(
        max_length=50,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='党派'
    )
    
    representative_current_position = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='现任职务'
    )
    
    representative_mobile_phone = serializers.CharField(
        max_length=11,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='手机号码'
    )
    
    representative_education = serializers.CharField(
        max_length=50,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='学历'
    )
    
    representative_graduated_school = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='毕业院校'
    )
    
    representative_major = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='所学专业'
    )
    
    # 工作人员信息（当role=staff时需要）
    staff_name = serializers.CharField(
        max_length=50,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='工作人员姓名'
    )
    
    staff_position = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='职位'
    )
    
    staff_mobile_phone = serializers.CharField(
        max_length=11,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='手机号码'
    )
    
    staff_email = serializers.EmailField(
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='邮箱地址'
    )
    
    staff_station_name = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        allow_null=True,
        help_text='所属站点名称'
    )
    
    def validate_username(self, value):
        """验证用户名唯一性"""
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError('该用户名已存在，请选择其他用户名')
        return value
    
    def validate(self, attrs):
        """验证数据"""
        role = attrs.get('role')
        password = attrs.get('password')
        confirm_password = attrs.pop('confirm_password', None)
        
        # 验证密码
        if password != confirm_password:
            raise serializers.ValidationError({
                'confirm_password': '两次输入的密码不一致'
            })
        
        try:
            validate_password(password)
        except ValidationError as e:
            raise serializers.ValidationError({
                'password': list(e.messages)
            })
        
        # 验证角色相关必填字段
        if role == 'representative':
            # 必填字段：代表层级、姓名、性别、籍贯、出生日期、党派、现任职务、电话
            required_fields = [
                'representative_level', 'representative_name', 'representative_gender',
                'representative_birth_date', 'representative_birthplace', 'representative_party',
                'representative_current_position', 'representative_mobile_phone'
            ]
            
            for field in required_fields:
                if not attrs.get(field):
                    field_name = field.replace('representative_', '')
                    raise serializers.ValidationError({
                        field: f'创建代表账号时{field_name}为必填字段'
                    })
        
        elif role == 'staff':
            required_fields = [
                'staff_name', 'staff_position', 'staff_mobile_phone', 'staff_station_name'
            ]
            
            for field in required_fields:
                if not attrs.get(field):
                    field_name = field.replace('staff_', '')
                    raise serializers.ValidationError({
                        field: f'创建工作人员账号时{field_name}为必填字段'
                    })
        
        return attrs
    
    def create(self, validated_data):
        """创建用户及相关信息"""
        from django.db import transaction
        from api.users.models import Representative, StaffMember
        
        # 提取用户基础信息
        user_data = {
            'username': validated_data['username'],
            'role': validated_data['role'],
            'is_active': validated_data.get('is_active', True)
        }
        
        password = validated_data['password']
        role = validated_data['role']
        
        with transaction.atomic():
            # 创建用户
            user = User.objects.create_user(password=password, **user_data)
            
            # 根据角色创建相关信息
            if role == 'representative':
                Representative.objects.create(
                    user=user,
                    level=validated_data['representative_level'],
                    name=validated_data['representative_name'],
                    gender=validated_data['representative_gender'],
                    nationality=validated_data['representative_nationality'],
                    district=validated_data['representative_district'],
                    birth_date=validated_data['representative_birth_date'],
                    birthplace=validated_data['representative_birthplace'],
                    party=validated_data['representative_party'],
                    current_position=validated_data['representative_current_position'],
                    mobile_phone=validated_data['representative_mobile_phone'],
                    education=validated_data['representative_education'],
                    graduated_school=validated_data.get('representative_graduated_school', ''),
                    major=validated_data.get('representative_major', '')
                )
            
            elif role == 'staff':
                StaffMember.objects.create(
                    user=user,
                    name=validated_data['staff_name'],
                    position=validated_data['staff_position'],
                    mobile_phone=validated_data['staff_mobile_phone'],
                    email=validated_data.get('staff_email', ''),
                    station_name=validated_data['staff_station_name']
                )
        
        return user


class UserImportSerializer(serializers.Serializer):
    """用户批量导入序列化器"""

    # 基础字段
    username = serializers.CharField(max_length=50, help_text='用户名')
    password = serializers.CharField(max_length=128, help_text='密码')
    role = serializers.ChoiceField(choices=User.ROLE_CHOICES, help_text='角色')
    is_active = serializers.BooleanField(default=True, help_text='是否启用')

    # 代表字段
    representative_name = serializers.CharField(max_length=50, required=False, allow_blank=True, help_text='代表姓名')
    representative_level = serializers.CharField(max_length=50, required=False, allow_blank=True, help_text='代表层级')
    representative_gender = serializers.CharField(max_length=10, required=False, allow_blank=True, help_text='性别')
    representative_nationality = serializers.CharField(max_length=50, required=False, allow_blank=True, help_text='民族')
    representative_district = serializers.CharField(max_length=20, required=False, allow_blank=True, help_text='所属片区')
    representative_birth_date = serializers.DateField(required=False, allow_null=True, help_text='出生日期')
    representative_birthplace = serializers.CharField(max_length=100, required=False, allow_blank=True, help_text='籍贯')
    representative_party = serializers.CharField(max_length=50, required=False, allow_blank=True, help_text='党派')
    representative_current_position = serializers.CharField(max_length=100, required=False, allow_blank=True, help_text='现任职务')
    representative_mobile_phone = serializers.CharField(max_length=11, required=False, allow_blank=True, help_text='手机号码')
    representative_education = serializers.CharField(max_length=50, required=False, allow_blank=True, help_text='学历')
    representative_graduated_school = serializers.CharField(max_length=100, required=False, allow_blank=True, help_text='毕业院校')
    representative_major = serializers.CharField(max_length=100, required=False, allow_blank=True, help_text='所学专业')

    # 工作人员字段
    staff_name = serializers.CharField(max_length=50, required=False, allow_blank=True, help_text='工作人员姓名')
    staff_position = serializers.CharField(max_length=100, required=False, allow_blank=True, help_text='职位')
    staff_mobile_phone = serializers.CharField(max_length=11, required=False, allow_blank=True, help_text='手机号码')
    staff_email = serializers.EmailField(required=False, allow_blank=True, help_text='邮箱地址')
    staff_station_name = serializers.CharField(max_length=100, required=False, allow_blank=True, help_text='工作站点')

    def validate_username(self, value):
        """验证用户名唯一性"""
        if not value or value.strip() == '':
            raise serializers.ValidationError('用户名不能为空')

        # 检查用户名格式
        if len(value.strip()) < 3:
            raise serializers.ValidationError('用户名长度不能少于3个字符')

        if len(value.strip()) > 50:
            raise serializers.ValidationError('用户名长度不能超过50个字符')

        # 检查用户名是否已存在
        if User.objects.filter(username=value.strip()).exists():
            raise serializers.ValidationError(f'用户名 "{value}" 已存在，请使用其他用户名')

        return value.strip()

    def validate_representative_mobile_phone(self, value):
        """验证代表手机号码格式"""
        if value and not re.match(r'^1[3-9]\d{9}$', value):
            raise serializers.ValidationError('请输入有效的手机号码格式')
        return value

    def validate_staff_mobile_phone(self, value):
        """验证工作人员手机号码格式"""
        if value and not re.match(r'^1[3-9]\d{9}$', value):
            raise serializers.ValidationError('请输入有效的手机号码格式')
        return value

    def validate_representative_gender(self, value):
        """验证性别值"""
        if value and value not in ['male', 'female']:
            raise serializers.ValidationError('性别只能是 male 或 female')
        return value

    def validate_representative_level(self, value):
        """验证代表层级（支持多层级）"""
        if not value:
            raise serializers.ValidationError('代表层级不能为空')

        from .models import Representative
        is_valid, message = Representative.validate_levels(value)
        if not is_valid:
            raise serializers.ValidationError(message)

        return value

    def validate_representative_district(self, value):
        """验证所属片区"""
        valid_districts = ['那洪片区', '那历片区', '沛鸿片区']
        if value and value not in valid_districts:
            raise serializers.ValidationError(f'所属片区必须是以下之一：{", ".join(valid_districts)}')
        return value

    def validate_representative_party(self, value):
        """验证党派"""
        valid_parties = [
            '中国共产党', '中国国民党革命委员会', '中国民主同盟', '中国民主建国会',
            '中国民主促进会', '中国农工民主党', '中国致公党', '九三学社',
            '台湾民主自治同盟', '群众'
        ]
        if value and value not in valid_parties:
            raise serializers.ValidationError(f'党派必须是以下之一：{", ".join(valid_parties)}')
        return value

    def validate(self, attrs):
        """验证整体数据"""
        role = attrs.get('role')

        # 根据角色验证必填字段
        if role == 'representative':
            required_fields = [
                'representative_name', 'representative_level', 'representative_gender',
                'representative_birth_date', 'representative_birthplace', 'representative_party',
                'representative_current_position', 'representative_mobile_phone'
            ]

            for field in required_fields:
                if not attrs.get(field):
                    field_name = field.replace('representative_', '')
                    raise serializers.ValidationError({
                        field: f'代表角色的{field_name}为必填字段'
                    })

        elif role == 'staff':
            required_fields = [
                'staff_name', 'staff_position', 'staff_mobile_phone', 'staff_station_name'
            ]

            for field in required_fields:
                if not attrs.get(field):
                    field_name = field.replace('staff_', '')
                    raise serializers.ValidationError({
                        field: f'工作人员角色的{field_name}为必填字段'
                    })

        return attrs


class UserExportSerializer(serializers.ModelSerializer):
    """用户导出序列化器"""

    role_display = serializers.CharField(source='get_role_display', read_only=True)
    is_active_display = serializers.SerializerMethodField()

    # 代表信息
    representative_name = serializers.SerializerMethodField()
    representative_level = serializers.SerializerMethodField()
    representative_gender = serializers.SerializerMethodField()
    representative_nationality = serializers.SerializerMethodField()
    representative_district = serializers.SerializerMethodField()
    representative_birth_date = serializers.SerializerMethodField()
    representative_birthplace = serializers.SerializerMethodField()
    representative_party = serializers.SerializerMethodField()
    representative_current_position = serializers.SerializerMethodField()
    representative_mobile_phone = serializers.SerializerMethodField()
    representative_education = serializers.SerializerMethodField()
    representative_graduated_school = serializers.SerializerMethodField()
    representative_major = serializers.SerializerMethodField()

    # 工作人员信息
    staff_name = serializers.SerializerMethodField()
    staff_position = serializers.SerializerMethodField()
    staff_mobile_phone = serializers.SerializerMethodField()
    staff_email = serializers.SerializerMethodField()
    staff_station_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'username', 'role', 'role_display', 'is_active', 'is_active_display',
            'created_at', 'representative_name', 'representative_level', 'representative_gender',
            'representative_nationality', 'representative_district', 'representative_birth_date',
            'representative_birthplace', 'representative_party', 'representative_current_position',
            'representative_mobile_phone', 'representative_education', 'representative_graduated_school',
            'representative_major', 'staff_name', 'staff_position', 'staff_mobile_phone',
            'staff_email', 'staff_station_name'
        ]

    def get_is_active_display(self, obj):
        return 'TRUE' if obj.is_active else 'FALSE'

    def get_created_at(self, obj):
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S') if obj.created_at else ''

    # 代表信息获取方法
    def get_representative_name(self, obj):
        return obj.representative.name if hasattr(obj, 'representative') else ''

    def get_representative_level(self, obj):
        return obj.representative.level if hasattr(obj, 'representative') else ''

    def get_representative_gender(self, obj):
        return obj.representative.gender if hasattr(obj, 'representative') else ''

    def get_representative_nationality(self, obj):
        return obj.representative.nationality if hasattr(obj, 'representative') else ''

    def get_representative_district(self, obj):
        return obj.representative.district if hasattr(obj, 'representative') else ''

    def get_representative_birth_date(self, obj):
        return obj.representative.birth_date.strftime('%Y-%m-%d') if hasattr(obj, 'representative') and obj.representative.birth_date else ''

    def get_representative_birthplace(self, obj):
        return obj.representative.birthplace if hasattr(obj, 'representative') else ''

    def get_representative_party(self, obj):
        return obj.representative.party if hasattr(obj, 'representative') else ''

    def get_representative_current_position(self, obj):
        return obj.representative.current_position if hasattr(obj, 'representative') else ''

    def get_representative_mobile_phone(self, obj):
        return obj.representative.mobile_phone if hasattr(obj, 'representative') else ''

    def get_representative_education(self, obj):
        return obj.representative.education if hasattr(obj, 'representative') else ''

    def get_representative_graduated_school(self, obj):
        return obj.representative.graduated_school if hasattr(obj, 'representative') else ''

    def get_representative_major(self, obj):
        return obj.representative.major if hasattr(obj, 'representative') else ''

    # 工作人员信息获取方法
    def get_staff_name(self, obj):
        return obj.staffmember.name if hasattr(obj, 'staffmember') else ''

    def get_staff_position(self, obj):
        return obj.staffmember.position if hasattr(obj, 'staffmember') else ''

    def get_staff_mobile_phone(self, obj):
        return obj.staffmember.mobile_phone if hasattr(obj, 'staffmember') else ''

    def get_staff_email(self, obj):
        return obj.staffmember.email if hasattr(obj, 'staffmember') else ''

    def get_staff_station_name(self, obj):
        return obj.staffmember.station_name if hasattr(obj, 'staffmember') else ''