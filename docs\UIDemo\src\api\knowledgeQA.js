// 法律政策知识问答模块API接口
import { ref } from 'vue'

// 模拟知识库数据
const mockKnowledgeData = ref([
  {
    id: 1,
    title: '人大代表的基本职责和权利',
    content: '根据《中华人民共和国宪法》和《中华人民共和国全国人民代表大会和地方各级人民代表大会代表法》规定，人大代表享有以下基本权利：1. 审议各项议案、报告的权利；2. 提出议案、质询案、建议、批评和意见的权利；3. 选举权和决定权；4. 询问和质询权；5. 监督权等。代表应当与原选区选民或者原选举单位保持密切联系，听取和反映他们的意见和要求。',
    category: '人大代表法',
    keywords: ['人大代表', '职责', '权利', '代表法', '监督权'],
    source: '《中华人民共和国全国人民代表大会和地方各级人民代表大会代表法》',
    relevantLaws: ['宪法', '代表法'],
    updateTime: '2024-01-15',
    viewCount: 156,
    favoriteCount: 23
  },
  {
    id: 2,
    title: '意见建议的处理程序',
    content: '意见建议的处理应当遵循以下程序：1. 接收登记：对群众反映的意见建议进行登记编号；2. 初步审查：核实意见建议的真实性和合理性；3. 分类处理：根据问题性质和职责分工，转交相关部门处理；4. 跟踪督办：定期了解处理进展情况；5. 反馈回复：将处理结果及时反馈给反映人；6. 归档备案：将处理过程和结果归档保存。',
    category: '工作程序',
    keywords: ['意见建议', '处理程序', '分类处理', '跟踪督办', '反馈回复'],
    source: '《信访工作条例》',
    relevantLaws: ['信访工作条例', '行政程序法'],
    updateTime: '2024-01-14',
    viewCount: 89,
    favoriteCount: 15
  },
  {
    id: 3,
    title: '调解案件的法律依据和程序',
    content: '人民调解是在人民调解委员会主持下，以国家法律、法规、规章和社会公德为准则，对民间纠纷双方当事人进行调解、劝说，促使他们互相谅解，平等协商，自愿达成协议，消除纷争的活动。调解程序包括：1. 受理纠纷；2. 调查了解；3. 调解准备；4. 主持调解；5. 制作调解协议书；6. 跟踪回访。',
    category: '调解程序',
    keywords: ['人民调解', '调解程序', '民间纠纷', '调解协议', '法律依据'],
    source: '《人民调解法》',
    relevantLaws: ['人民调解法', '民事诉讼法'],
    updateTime: '2024-01-13',
    viewCount: 234,
    favoriteCount: 45
  },
  {
    id: 4,
    title: '代表建议的提出和办理',
    content: '人大代表有权依照法律规定的程序，向本级人民代表大会提出对各方面工作的建议、批评和意见。代表建议应当明确具体，有情况、有分析、有具体的建议。承办单位收到代表建议后，应当认真研究办理，并在规定期限内答复代表。对代表建议的办理情况，应当向代表大会或者其常务委员会报告。',
    category: '代表建议',
    keywords: ['代表建议', '提出程序', '办理程序', '答复期限', '情况报告'],
    source: '《代表法》第九条',
    relevantLaws: ['代表法', '监督法'],
    updateTime: '2024-01-12',
    viewCount: 178,
    favoriteCount: 32
  },
  {
    id: 5,
    title: '信访工作的基本原则',
    content: '信访工作应当遵循以下原则：1. 属地管理、分级负责；2. 谁主管、谁负责；3. 依法、及时、就地解决问题与疏导教育相结合；4. 公开、公正、便民、高效。信访工作应当在各级党委领导下，坚持党统一领导、政府组织实施、部门各负其责、社会协同参与的工作格局。',
    category: '信访工作',
    keywords: ['信访工作', '基本原则', '属地管理', '分级负责', '依法解决'],
    source: '《信访工作条例》第三条',
    relevantLaws: ['信访工作条例'],
    updateTime: '2024-01-11',
    viewCount: 134,
    favoriteCount: 28
  }
])

// 模拟用户收藏的知识
const userFavorites = ref(new Set())

// 模拟查询历史
const queryHistory = ref([
  {
    id: 1,
    query: '人大代表权利',
    resultCount: 3,
    queryTime: new Date('2024-01-15 14:30:00')
  },
  {
    id: 2,
    query: '意见建议处理',
    resultCount: 2,
    queryTime: new Date('2024-01-14 10:20:00')
  }
])

/**
 * 搜索法律政策知识
 * @param {Object} params - 搜索参数
 * @param {string} params.query - 搜索关键词
 * @param {string} params.category - 分类筛选
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 */
export const searchKnowledge = async (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const { query, category, page = 1, size = 10 } = params
      
      if (!query || query.trim() === '') {
        resolve({
          success: false,
          message: '搜索关键词不能为空',
          errorCode: 'KQ_ERR_001'
        })
        return
      }
      
      // 模拟搜索逻辑
      let results = mockKnowledgeData.value.filter(item => {
        const queryLower = query.toLowerCase()
        const titleMatch = item.title.toLowerCase().includes(queryLower)
        const contentMatch = item.content.toLowerCase().includes(queryLower)
        const keywordMatch = item.keywords.some(keyword => 
          keyword.toLowerCase().includes(queryLower)
        )
        return titleMatch || contentMatch || keywordMatch
      })
      
      // 分类筛选
      if (category) {
        results = results.filter(item => item.category === category)
      }
      
      // 模拟查询失败
      if (Math.random() < 0.05) {
        resolve({
          success: false,
          message: '查询失败，请稍后再试',
          errorCode: 'KQ_ERR_002'
        })
        return
      }
      
      // 记录查询历史
      const historyItem = {
        id: queryHistory.value.length + 1,
        query,
        resultCount: results.length,
        queryTime: new Date()
      }
      queryHistory.value.unshift(historyItem)
      
      // 分页处理
      const total = results.length
      const start = (page - 1) * size
      const end = start + size
      const list = results.slice(start, end)
      
      // 更新浏览次数
      list.forEach(item => {
        const original = mockKnowledgeData.value.find(k => k.id === item.id)
        if (original) {
          original.viewCount++
        }
      })
      
      resolve({
        success: true,
        data: {
          list,
          total,
          page,
          size,
          totalPages: Math.ceil(total / size),
          query
        }
      })
    }, 800) // 模拟AI接口响应时间
  })
}

/**
 * 获取知识详情
 * @param {number} knowledgeId - 知识ID
 */
export const getKnowledgeDetail = async (knowledgeId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const knowledge = mockKnowledgeData.value.find(item => item.id === knowledgeId)
      
      if (!knowledge) {
        resolve({
          success: false,
          message: '未找到相关法律政策知识',
          errorCode: 'KQ_ERR_003'
        })
        return
      }
      
      // 增加浏览次数
      knowledge.viewCount++
      
      resolve({
        success: true,
        data: knowledge
      })
    }, 300)
  })
}

/**
 * 收藏/取消收藏知识
 * @param {number} knowledgeId - 知识ID
 * @param {boolean} isFavorite - 是否收藏
 */
export const toggleFavorite = async (knowledgeId, isFavorite) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const knowledge = mockKnowledgeData.value.find(item => item.id === knowledgeId)
      
      if (!knowledge) {
        resolve({
          success: false,
          message: '知识不存在'
        })
        return
      }
      
      if (isFavorite) {
        userFavorites.value.add(knowledgeId)
        knowledge.favoriteCount++
      } else {
        userFavorites.value.delete(knowledgeId)
        knowledge.favoriteCount = Math.max(0, knowledge.favoriteCount - 1)
      }
      
      resolve({
        success: true,
        message: isFavorite ? '收藏成功' : '取消收藏成功',
        data: { isFavorite }
      })
    }, 300)
  })
}

/**
 * 获取用户收藏的知识列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 */
export const getFavoriteKnowledge = async (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const { page = 1, size = 10 } = params
      
      const favoriteIds = Array.from(userFavorites.value)
      const favorites = mockKnowledgeData.value.filter(item => 
        favoriteIds.includes(item.id)
      )
      
      // 按收藏时间倒序（这里简化为按ID倒序）
      favorites.sort((a, b) => b.id - a.id)
      
      // 分页
      const total = favorites.length
      const start = (page - 1) * size
      const end = start + size
      const list = favorites.slice(start, end)
      
      resolve({
        success: true,
        data: {
          list,
          total,
          page,
          size,
          totalPages: Math.ceil(total / size)
        }
      })
    }, 400)
  })
}

/**
 * 获取查询历史
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 */
export const getQueryHistory = async (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const { page = 1, size = 10 } = params
      
      // 按查询时间倒序
      const sortedHistory = [...queryHistory.value].sort((a, b) => 
        new Date(b.queryTime) - new Date(a.queryTime)
      )
      
      // 分页
      const total = sortedHistory.length
      const start = (page - 1) * size
      const end = start + size
      const list = sortedHistory.slice(start, end)
      
      resolve({
        success: true,
        data: {
          list,
          total,
          page,
          size,
          totalPages: Math.ceil(total / size)
        }
      })
    }, 300)
  })
}

/**
 * 获取知识分类列表
 */
export const getKnowledgeCategories = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const categories = [
        { id: 1, name: '人大代表法', count: 45 },
        { id: 2, name: '工作程序', count: 32 },
        { id: 3, name: '调解程序', count: 28 },
        { id: 4, name: '代表建议', count: 23 },
        { id: 5, name: '信访工作', count: 19 },
        { id: 6, name: '监督法', count: 15 },
        { id: 7, name: '其他法规', count: 12 }
      ]
      
      resolve({
        success: true,
        data: categories
      })
    }, 200)
  })
}

/**
 * 获取热门搜索关键词
 */
export const getHotKeywords = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const hotKeywords = [
        '人大代表权利',
        '意见建议处理',
        '调解程序',
        '代表建议',
        '监督权',
        '履职职责',
        '信访工作',
        '法律依据'
      ]
      
      resolve({
        success: true,
        data: hotKeywords
      })
    }, 200)
  })
}

/**
 * 清除查询历史
 */
export const clearQueryHistory = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      queryHistory.value = []
      
      resolve({
        success: true,
        message: '查询历史已清除'
      })
    }, 300)
  })
}

/**
 * 检查知识是否已收藏
 * @param {number} knowledgeId - 知识ID
 */
export const checkIsFavorite = (knowledgeId) => {
  return userFavorites.value.has(knowledgeId)
}

/**
 * 获取统计信息
 */
export const getKnowledgeStats = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const stats = {
        totalKnowledge: mockKnowledgeData.value.length,
        totalViews: mockKnowledgeData.value.reduce((sum, item) => sum + item.viewCount, 0),
        totalFavorites: mockKnowledgeData.value.reduce((sum, item) => sum + item.favoriteCount, 0),
        userFavoriteCount: userFavorites.value.size,
        queryCount: queryHistory.value.length
      }
      
      resolve({
        success: true,
        data: stats
      })
    }, 200)
  })
}

export default {
  searchKnowledge,
  getKnowledgeDetail,
  toggleFavorite,
  getFavoriteKnowledge,
  getQueryHistory,
  getKnowledgeCategories,
  getHotKeywords,
  clearQueryHistory,
  checkIsFavorite,
  getKnowledgeStats
} 