# 履职管理设计文档

## 1. 文档说明

本文档详细描述了人大代表履职记录管理功能的设计方案，特别是针对多媒体附件支持的完整设计。考虑到使用者年龄较大的特点，采用简单明了的界面设计和操作流程。

## 2. 设计原则

### 2.1 老年用户友好原则
- **大字体、大按钮**：适合老年用户的视觉需求
- **清晰分组**：功能区域明确分离，减少认知负担
- **详细提示**：提供充分的操作指导和说明
- **容错设计**：友好的错误提示和操作引导

### 2.2 功能设计原则
- **渐进增强**：在现有文字录入基础上增加多媒体支持
- **可选附件**：多媒体内容作为可选项，不强制使用
- **向后兼容**：保持现有数据结构和操作流程

## 3. 界面设计方案

### 3.1 整体布局结构

```
┌─────────────────────────────────────┐
│           履职记录录入              │
├─────────────────────────────────────┤
│ 基本信息区域                        │
│ - 履职日期                         │
│ - 履职类型                         │
│ - 活动地点                         │
├─────────────────────────────────────┤
│ 履职内容区域                        │
│ - 主要内容（文本框）                │
│ - 详细情况（文本框）                │
├─────────────────────────────────────┤
│ 相关资料区域（可选）                │
│ - 现场照片                         │
│ - 录音文件                         │
│ - 视频文件                         │
│ - 相关文档                         │
├─────────────────────────────────────┤
│ 操作按钮区域                        │
│ [取消] [保存记录]                   │
└─────────────────────────────────────┘
```

### 3.2 详细界面设计

#### A. 基本信息区域
- **履职日期**：日期选择器，大尺寸按钮
- **履职类型**：下拉选择，预设选项清晰
- **活动地点**：文本输入框，支持地点描述

#### B. 履职内容区域（核心）
- **主要内容**：
  - 文本域，4行高度
  - 字数限制200字，显示字数统计
  - 必填项，用于简要描述履职活动
  
- **详细情况**：
  - 文本域，6行高度
  - 字数限制1000字，显示字数统计
  - 可选项，用于详细记录履职过程

#### C. 相关资料区域（附件）
- **现场照片**：
  - 支持多张上传（最多9张）
  - 图片预览卡片式布局
  - 支持删除和重新排序
  
- **录音文件**：
  - 支持多个录音（最多3个）
  - 显示文件名和时长
  - 支持播放预览
  
- **视频文件**：
  - 支持多个视频（最多2个）
  - 显示缩略图和时长
  - 支持播放预览
  
- **相关文档**：
  - 支持多个文档（最多5个）
  - 显示文件名和大小
  - 支持预览（PDF等）

## 4. 技术实现方案

### 4.1 数据库设计

#### 履职记录表（主表）
```sql
CREATE TABLE performance_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    representative_id BIGINT NOT NULL,
    performance_date DATE NOT NULL,
    performance_type VARCHAR(50) NOT NULL,
    activity_location VARCHAR(200) NOT NULL,
    performance_content TEXT NOT NULL,           -- 主要内容
    detailed_description TEXT,                   -- 详细描述
    performance_status VARCHAR(50) DEFAULT '已完成',
    attachment_count INT DEFAULT 0,              -- 附件总数
    has_multimedia BOOLEAN DEFAULT FALSE,       -- 是否包含多媒体
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (representative_id) REFERENCES representatives(id) ON DELETE CASCADE,
    INDEX idx_representative_date (representative_id, performance_date),
    INDEX idx_performance_type (performance_type)
) COMMENT='履职记录表';
```

#### 附件表（关联表）
```sql
CREATE TABLE performance_attachments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    performance_record_id BIGINT NOT NULL,
    file_type ENUM('image', 'audio', 'video', 'document') NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    stored_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_extension VARCHAR(10) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    thumbnail_path VARCHAR(500) NULL,
    duration INT NULL COMMENT '音视频时长(秒)',
    sort_order INT DEFAULT 0,                    -- 排序字段
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (performance_record_id) REFERENCES performance_records(id) ON DELETE CASCADE,
    INDEX idx_performance_record (performance_record_id),
    INDEX idx_file_type (file_type),
    INDEX idx_sort_order (performance_record_id, sort_order)
) COMMENT='履职记录附件表';
```

### 4.2 文件存储方案

#### 存储目录结构
```
project_root/
├── backend/
│   ├── media/                    # Django MEDIA_ROOT
│   │   ├── performance/          # 履职记录文件
│   │   │   ├── 2024/
│   │   │   │   ├── 12/
│   │   │   │   │   ├── 20/       # 按日期分目录
│   │   │   │   │   │   ├── images/
│   │   │   │   │   │   │   ├── a1b2c3d4_20241220143000_现场照片.jpg
│   │   │   │   │   │   │   └── e5f6g7h8_20241220143010_调研图片.png
│   │   │   │   │   │   ├── videos/
│   │   │   │   │   │   │   └── i9j0k1l2_20241220143030_履职视频.mp4
│   │   │   │   │   │   ├── audios/
│   │   │   │   │   │   │   └── m3n4o5p6_20241220143020_现场录音.mp3
│   │   │   │   │   │   ├── documents/
│   │   │   │   │   │   │   └── q7r8s9t0_20241220143040_相关文档.pdf
│   │   │   │   │   │   └── thumbnails/
│   │   │   │   │   │       ├── i9j0k1l2_20241220143030_thumb.jpg
│   │   │   │   │   │       └── a1b2c3d4_20241220143000_thumb.jpg
```

#### 文件命名规则
- **格式**：`{uuid}_{timestamp}_{original_name}`
- **示例**：`a1b2c3d4_20241220143000_现场照片.jpg`
- **优点**：避免重名、便于管理、保留原始信息

### 4.3 文件类型和限制

```javascript
const FILE_LIMITS = {
  image: {
    max_count: 9,                    // 最多9张照片
    max_size: 10 * 1024 * 1024,     // 单张最大10MB
    allowed_types: ['jpg', 'jpeg', 'png', 'gif'],
    mime_types: ['image/jpeg', 'image/png', 'image/gif'],
    description: '现场照片'
  },
  audio: {
    max_count: 3,                    // 最多3个录音
    max_size: 50 * 1024 * 1024,     // 单个最大50MB
    allowed_types: ['mp3', 'wav', 'aac', 'm4a'],
    mime_types: ['audio/mpeg', 'audio/wav', 'audio/aac'],
    description: '录音文件'
  },
  video: {
    max_count: 2,                    // 最多2个视频
    max_size: 100 * 1024 * 1024,    // 单个最大100MB
    allowed_types: ['mp4', 'avi', 'mov', 'wmv'],
    mime_types: ['video/mp4', 'video/avi', 'video/quicktime'],
    description: '视频文件'
  },
  document: {
    max_count: 5,                    // 最多5个文档
    max_size: 20 * 1024 * 1024,     // 单个最大20MB
    allowed_types: ['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx'],
    mime_types: ['application/pdf', 'application/msword', 'text/plain'],
    description: '相关文档'
  }
}
```

## 5. 前端组件设计

### 5.1 主表单组件结构

```vue
<template>
  <el-form :model="recordForm" :rules="formRules" label-width="100px">
    <!-- 基本信息区域 -->
    <BasicInfoSection v-model="recordForm" />
    
    <!-- 履职内容区域 -->
    <ContentSection v-model="recordForm" />
    
    <!-- 附件上传区域 -->
    <AttachmentsSection 
      v-model:images="imageList"
      v-model:audios="audioList"
      v-model:videos="videoList"
      v-model:documents="documentList"
      @upload-success="onAttachmentUpload"
      @remove-file="onAttachmentRemove"
    />
    
    <!-- 提交按钮区域 -->
    <SubmitSection 
      @save="saveRecord" 
      @cancel="cancelForm"
      :loading="saving"
    />
  </el-form>
</template>
```

### 5.2 附件上传组件

```vue
<!-- AttachmentsSection.vue -->
<template>
  <div class="attachments-section">
    <h3>相关资料</h3>
    <p class="section-tip">您可以上传现场照片、录音、视频等资料（可选）</p>
    
    <!-- 照片上传 -->
    <el-form-item label="现场照片">
      <ImageUploader 
        v-model="images"
        :max-count="9"
        :max-size="10"
        @upload-success="$emit('upload-success', $event)"
        @remove="$emit('remove-file', $event)"
      />
    </el-form-item>

    <!-- 录音上传 -->
    <el-form-item label="录音文件">
      <AudioUploader 
        v-model="audios"
        :max-count="3"
        :max-size="50"
        @upload-success="$emit('upload-success', $event)"
        @remove="$emit('remove-file', $event)"
      />
    </el-form-item>

    <!-- 视频上传 -->
    <el-form-item label="视频文件">
      <VideoUploader 
        v-model="videos"
        :max-count="2"
        :max-size="100"
        @upload-success="$emit('upload-success', $event)"
        @remove="$emit('remove-file', $event)"
      />
    </el-form-item>

    <!-- 文档上传 -->
    <el-form-item label="相关文档">
      <DocumentUploader 
        v-model="documents"
        :max-count="5"
        :max-size="20"
        @upload-success="$emit('upload-success', $event)"
        @remove="$emit('remove-file', $event)"
      />
    </el-form-item>
  </div>
</template>
```

### 5.3 老年用户友好样式

```scss
<style scoped>
/* 基础区域样式 */
.basic-info-section,
.content-section,
.attachments-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

/* 区域标题 */
.basic-info-section h3,
.content-section h3,
.attachments-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

/* 提示文字 */
.section-tip {
  color: #909399;
  font-size: 14px;
  margin-bottom: 15px;
  line-height: 1.5;
}

/* 老年人友好设计 */
:deep(.el-form-item__label) {
  font-size: 16px !important;
  font-weight: 500;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  font-size: 16px;
  line-height: 1.5;
}

:deep(.el-button--large) {
  padding: 12px 24px;
  font-size: 16px;
  min-height: 44px;
  border-radius: 6px;
}

/* 上传区域样式 */
.upload-area {
  margin-bottom: 20px;
}

.upload-trigger {
  text-align: center;
  color: #8c939d;
  font-size: 14px;
  padding: 20px;
}

.upload-tip {
  margin-top: 8px;
  font-size: 14px;
  color: #909399;
  line-height: 1.6;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

/* 图片上传特殊样式 */
.photo-uploader :deep(.el-upload--picture-card) {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
  transition: border-color 0.3s;
}

.photo-uploader :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
}

/* 提交按钮区域 */
.submit-section {
  text-align: center;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background: #ffffff;
}

.submit-section .el-button {
  min-width: 120px;
  margin: 0 15px;
}
</style>
```

## 6. 后端API设计

### 6.1 文件上传API

```python
# views.py
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import PerformanceRecord, PerformanceAttachment
from .utils.file_handler import FileHandler

class PerformanceAttachmentUploadAPIView(APIView):
    """履职记录附件上传"""
    permission_classes = [IsAuthenticated, IsRepresentativeOnly]
    
    def post(self, request):
        performance_record_id = request.data.get('performance_record_id')
        file_type = request.data.get('file_type')
        uploaded_file = request.FILES.get('file')
        
        # 参数验证
        if not all([performance_record_id, file_type, uploaded_file]):
            return Response({
                'success': False,
                'message': '缺少必要参数'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # 验证履职记录存在且属于当前用户
            performance_record = PerformanceRecord.objects.get(
                id=performance_record_id,
                representative__user=request.user
            )
            
            # 验证文件类型和大小
            validation_result = FileHandler.validate_file(uploaded_file, file_type)
            if not validation_result['valid']:
                return Response({
                    'success': False,
                    'message': validation_result['message']
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查文件数量限制
            current_count = PerformanceAttachment.objects.filter(
                performance_record=performance_record,
                file_type=file_type
            ).count()
            
            max_counts = {'image': 9, 'audio': 3, 'video': 2, 'document': 5}
            if current_count >= max_counts.get(file_type, 5):
                return Response({
                    'success': False,
                    'message': f'{file_type}文件数量已达到上限'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 处理文件上传
            file_info = FileHandler.upload_performance_file(
                uploaded_file, file_type, performance_record_id
            )
            
            # 保存附件记录
            attachment = PerformanceAttachment.objects.create(
                performance_record=performance_record,
                file_type=file_type,
                original_name=uploaded_file.name,
                stored_name=file_info['stored_name'],
                file_path=file_info['file_path'],
                file_url=file_info['file_url'],
                file_size=file_info['file_size'],
                file_extension=file_info['file_extension'],
                mime_type=file_info['mime_type'],
                thumbnail_path=file_info.get('thumbnail_path'),
                duration=file_info.get('duration'),
                sort_order=current_count + 1
            )
            
            # 更新履职记录的附件统计
            performance_record.attachment_count = PerformanceAttachment.objects.filter(
                performance_record=performance_record
            ).count()
            performance_record.has_multimedia = True
            performance_record.save(update_fields=['attachment_count', 'has_multimedia'])
            
            return Response({
                'success': True,
                'data': {
                    'id': attachment.id,
                    'url': attachment.file_url,
                    'thumbnail_url': attachment.thumbnail_path,
                    'original_name': attachment.original_name,
                    'file_size': attachment.file_size,
                    'duration': attachment.duration
                }
            })
            
        except PerformanceRecord.DoesNotExist:
            return Response({
                'success': False,
                'message': '履职记录不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'上传失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
```

### 6.2 文件处理工具类

```python
# utils/file_handler.py
import os
import uuid
from datetime import datetime
from PIL import Image
import moviepy.editor as mp
from django.conf import settings
from django.core.files.storage import default_storage

class FileHandler:
    
    # 文件类型配置
    FILE_CONFIGS = {
        'image': {
            'max_size': 10 * 1024 * 1024,  # 10MB
            'allowed_extensions': ['jpg', 'jpeg', 'png', 'gif'],
            'allowed_mime_types': ['image/jpeg', 'image/png', 'image/gif']
        },
        'audio': {
            'max_size': 50 * 1024 * 1024,  # 50MB
            'allowed_extensions': ['mp3', 'wav', 'aac', 'm4a'],
            'allowed_mime_types': ['audio/mpeg', 'audio/wav', 'audio/aac']
        },
        'video': {
            'max_size': 100 * 1024 * 1024,  # 100MB
            'allowed_extensions': ['mp4', 'avi', 'mov', 'wmv'],
            'allowed_mime_types': ['video/mp4', 'video/avi', 'video/quicktime']
        },
        'document': {
            'max_size': 20 * 1024 * 1024,  # 20MB
            'allowed_extensions': ['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx'],
            'allowed_mime_types': ['application/pdf', 'application/msword', 'text/plain']
        }
    }
    
    @classmethod
    def validate_file(cls, uploaded_file, file_type):
        """验证上传文件"""
        config = cls.FILE_CONFIGS.get(file_type)
        if not config:
            return {'valid': False, 'message': '不支持的文件类型'}
        
        # 检查文件大小
        if uploaded_file.size > config['max_size']:
            max_size_mb = config['max_size'] // (1024 * 1024)
            return {'valid': False, 'message': f'文件大小不能超过{max_size_mb}MB'}
        
        # 检查文件扩展名
        file_extension = uploaded_file.name.split('.')[-1].lower()
        if file_extension not in config['allowed_extensions']:
            return {'valid': False, 'message': f'不支持的文件格式，请选择{"/".join(config["allowed_extensions"])}格式'}
        
        # 检查MIME类型
        if uploaded_file.content_type not in config['allowed_mime_types']:
            return {'valid': False, 'message': '文件格式验证失败'}
        
        return {'valid': True, 'message': '验证通过'}
    
    @classmethod
    def upload_performance_file(cls, uploaded_file, file_type, performance_record_id):
        """处理履职记录文件上传"""
        # 生成唯一文件名
        file_uuid = str(uuid.uuid4())
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        file_extension = uploaded_file.name.split('.')[-1].lower()
        stored_name = f"{file_uuid}_{timestamp}_{uploaded_file.name}"
        
        # 生成存储路径
        now = datetime.now()
        relative_dir = f"performance/{now.year}/{now.month:02d}/{now.day:02d}/{file_type}s"
        file_path = os.path.join(relative_dir, stored_name)
        
        # 保存文件
        full_path = default_storage.save(file_path, uploaded_file)
        file_url = f"{settings.MEDIA_URL}{full_path}"
        
        # 生成附加信息
        thumbnail_path = None
        duration = None
        
        try:
            if file_type == 'image':
                thumbnail_path = cls._generate_image_thumbnail(full_path)
            elif file_type == 'video':
                thumbnail_path = cls._generate_video_thumbnail(full_path)
                duration = cls._get_video_duration(full_path)
            elif file_type == 'audio':
                duration = cls._get_audio_duration(full_path)
        except Exception as e:
            # 缩略图生成失败不影响主流程
            print(f"生成缩略图失败: {e}")
        
        return {
            'stored_name': stored_name,
            'file_path': full_path,
            'file_url': file_url,
            'file_extension': file_extension,
            'thumbnail_path': thumbnail_path,
            'duration': duration
        }
    
    @classmethod
    def _generate_image_thumbnail(cls, image_path):
        """生成图片缩略图"""
        try:
            full_path = os.path.join(settings.MEDIA_ROOT, image_path)
            thumbnail_dir = os.path.join(
                os.path.dirname(full_path).replace('/images/', '/thumbnails/')
            )
            os.makedirs(thumbnail_dir, exist_ok=True)
            
            # 根据原文件名生成缩略图名：{uuid}_{timestamp}_thumb.jpg
            original_basename = os.path.basename(image_path)
            if '_' in original_basename:
                # 假设原文件名是 uuid_timestamp_originalname.ext 格式
                parts = original_basename.split('_', 2)
                if len(parts) >= 2:
                    thumbnail_name = f"{parts[0]}_{parts[1]}_thumb.jpg"
                else:
                    thumbnail_name = f"thumb_{original_basename}"
            else:
                thumbnail_name = f"thumb_{original_basename}"
            thumbnail_path = os.path.join(thumbnail_dir, thumbnail_name)
            
            with Image.open(full_path) as img:
                img.thumbnail((300, 300), Image.Resampling.LANCZOS)
                img.save(thumbnail_path, optimize=True, quality=85)
            
            relative_thumbnail_path = thumbnail_path.replace(settings.MEDIA_ROOT, '').lstrip('/')
            return f"{settings.MEDIA_URL}{relative_thumbnail_path}"
        except Exception:
            return None
    
    @classmethod
    def _generate_video_thumbnail(cls, video_path):
        """生成视频缩略图"""
        try:
            full_path = os.path.join(settings.MEDIA_ROOT, video_path)
            thumbnail_dir = os.path.join(
                os.path.dirname(full_path).replace('/videos/', '/thumbnails/')
            )
            os.makedirs(thumbnail_dir, exist_ok=True)
            
            # 根据原文件名生成缩略图名：{uuid}_{timestamp}_thumb.jpg
            original_basename = os.path.splitext(os.path.basename(video_path))[0]
            if '_' in original_basename:
                # 假设原文件名是 uuid_timestamp_originalname 格式
                parts = original_basename.split('_', 2)
                if len(parts) >= 2:
                    thumbnail_name = f"{parts[0]}_{parts[1]}_thumb.jpg"
                else:
                    thumbnail_name = f"thumb_{original_basename}.jpg"
            else:
                thumbnail_name = f"thumb_{original_basename}.jpg"
            thumbnail_path = os.path.join(thumbnail_dir, thumbnail_name)
            
            with mp.VideoFileClip(full_path) as clip:
                # 提取第1秒的帧作为缩略图
                frame_time = min(1.0, clip.duration / 2)
                clip.save_frame(thumbnail_path, t=frame_time)
            
            relative_thumbnail_path = thumbnail_path.replace(settings.MEDIA_ROOT, '').lstrip('/')
            return f"{settings.MEDIA_URL}{relative_thumbnail_path}"
        except Exception:
            return None
    
    @classmethod
    def _get_video_duration(cls, video_path):
        """获取视频时长"""
        try:
            full_path = os.path.join(settings.MEDIA_ROOT, video_path)
            with mp.VideoFileClip(full_path) as clip:
                return int(clip.duration)
        except Exception:
            return None
    
    @classmethod
    def _get_audio_duration(cls, audio_path):
        """获取音频时长"""
        try:
            full_path = os.path.join(settings.MEDIA_ROOT, audio_path)
            with mp.AudioFileClip(full_path) as clip:
                return int(clip.duration)
        except Exception:
            return None
```

## 7. AI视频生成支持

### 7.1 素材准备服务

```python
# services/video_generator.py
class PerformanceVideoGenerator:
    
    def prepare_materials(self, performance_record_id):
        """准备AI视频生成素材"""
        record = PerformanceRecord.objects.get(id=performance_record_id)
        attachments = record.attachments.all()
        
        materials = {
            "basic_info": {
                "id": record.id,
                "date": record.performance_date.strftime('%Y-%m-%d'),
                "type": record.get_performance_type_display(),
                "location": record.activity_location,
                "status": record.get_performance_status_display()
            },
            "text_content": {
                "main_content": record.performance_content,
                "detailed_description": record.detailed_description or "",
                "word_count": len(record.performance_content + (record.detailed_description or ""))
            },
            "multimedia_assets": {
                "images": [],
                "videos": [],
                "audios": [],
                "documents": []
            },
            "statistics": {
                "total_attachments": attachments.count(),
                "has_multimedia": record.has_multimedia
            }
        }
        
        # 分类整理附件
        for attachment in attachments:
            asset_info = {
                "id": attachment.id,
                "original_name": attachment.original_name,
                "url": attachment.file_url,
                "file_size": attachment.file_size,
                "created_at": attachment.created_at.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            if attachment.file_type == 'image':
                asset_info["thumbnail_url"] = attachment.thumbnail_path
                materials["multimedia_assets"]["images"].append(asset_info)
                
            elif attachment.file_type == 'video':
                asset_info["duration"] = attachment.duration
                asset_info["thumbnail_url"] = attachment.thumbnail_path
                materials["multimedia_assets"]["videos"].append(asset_info)
                
            elif attachment.file_type == 'audio':
                asset_info["duration"] = attachment.duration
                materials["multimedia_assets"]["audios"].append(asset_info)
                
            elif attachment.file_type == 'document':
                materials["multimedia_assets"]["documents"].append(asset_info)
        
        return materials
    
    def prepare_annual_materials(self, representative_id, year):
        """准备年度履职素材"""
        records = PerformanceRecord.objects.filter(
            representative_id=representative_id,
            performance_date__year=year
        ).prefetch_related('attachments').order_by('performance_date')
        
        annual_materials = {
            "summary": {
                "year": year,
                "total_records": records.count(),
                "total_attachments": sum(record.attachment_count for record in records),
                "multimedia_records": records.filter(has_multimedia=True).count()
            },
            "records": []
        }
        
        for record in records:
            record_materials = self.prepare_materials(record.id)
            annual_materials["records"].append(record_materials)
        
        return annual_materials
```

### 7.2 AI视频生成API

```python
# views.py
class PerformanceVideoGenerateAPIView(APIView):
    """履职记录视频生成"""
    permission_classes = [IsAuthenticated, IsRepresentativeOnly]
    
    def post(self, request):
        performance_record_id = request.data.get('performance_record_id')
        video_type = request.data.get('video_type', 'summary')  # summary | annual
        
        try:
            # 验证权限
            record = PerformanceRecord.objects.get(
                id=performance_record_id,
                representative__user=request.user
            )
            
            # 准备素材
            generator = PerformanceVideoGenerator()
            
            if video_type == 'annual':
                year = request.data.get('year', datetime.now().year)
                materials = generator.prepare_annual_materials(
                    record.representative_id, year
                )
            else:
                materials = generator.prepare_materials(performance_record_id)
            
            # 调用AI视频生成服务
            video_service = AIVideoService()
            video_result = video_service.generate_performance_video(
                materials, video_type
            )
            
            return Response({
                'success': True,
                'data': {
                    'video_url': video_result['video_url'],
                    'thumbnail_url': video_result['thumbnail_url'],
                    'duration': video_result['duration'],
                    'status': video_result['status']
                }
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'视频生成失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
```

## 8. 用户体验优化

### 8.1 操作提示和帮助

#### 文字提示
- **上传区域**：清晰说明支持的文件格式和大小限制
- **操作按钮**：使用明确的动词，如"上传照片"、"添加录音"
- **状态反馈**：上传进度、成功提示、错误说明

#### 图标设计
- **照片**：📷 相机图标
- **录音**：🎤 麦克风图标  
- **视频**：📹 摄像机图标
- **文档**：📄 文档图标

### 8.2 错误处理和容错

#### 常见错误提示
```javascript
const ERROR_MESSAGES = {
  file_too_large: '文件太大了，请选择更小的文件',
  invalid_format: '不支持这种文件格式，请选择正确的格式',
  upload_failed: '上传失败，请检查网络后重试',
  quota_exceeded: '文件数量已达到上限',
  network_error: '网络连接出现问题，请稍后再试'
}
```

#### 重试机制
```javascript
// 上传失败自动重试
const uploadWithRetry = async (file, retryCount = 3) => {
  for (let i = 0; i < retryCount; i++) {
    try {
      return await uploadFile(file)
    } catch (error) {
      if (i === retryCount - 1) throw error
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}
```

### 8.3 性能优化

#### 文件压缩
```javascript
// 图片自动压缩
const compressImage = (file, maxWidth = 1920, quality = 0.8) => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      const ratio = Math.min(maxWidth / img.width, maxWidth / img.height)
      canvas.width = img.width * ratio
      canvas.height = img.height * ratio
      
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
      canvas.toBlob(resolve, 'image/jpeg', quality)
    }
    
    img.src = URL.createObjectURL(file)
  })
}
```

#### 懒加载
```vue
<!-- 大量附件时使用懒加载 -->
<div class="attachment-list">
  <div 
    v-for="attachment in attachments" 
    :key="attachment.id"
    v-loading="!attachment.loaded"
    class="attachment-item"
  >
    <img 
      v-if="attachment.type === 'image'"
      :src="attachment.thumbnail_url"
      @load="attachment.loaded = true"
      loading="lazy"
    />
  </div>
</div>
```

## 9. 部署和运维考虑

### 9.1 存储空间管理

#### 定期清理策略
```python
# management/commands/cleanup_old_files.py
from django.core.management.base import BaseCommand
from datetime import datetime, timedelta
from apps.performance.models import PerformanceAttachment

class Command(BaseCommand):
    def handle(self, *args, **options):
        # 清理6个月前的临时文件
        cutoff_date = datetime.now() - timedelta(days=180)
        old_attachments = PerformanceAttachment.objects.filter(
            created_at__lt=cutoff_date,
            performance_record__isnull=True  # 未关联的临时文件
        )
        
        for attachment in old_attachments:
            # 删除文件
            if default_storage.exists(attachment.file_path):
                default_storage.delete(attachment.file_path)
            
            # 删除缩略图
            if attachment.thumbnail_path and default_storage.exists(attachment.thumbnail_path):
                default_storage.delete(attachment.thumbnail_path)
            
            # 删除记录
            attachment.delete()
        
        self.stdout.write(f'清理了 {old_attachments.count()} 个过期文件')
```

#### 存储监控
```python
# 监控存储使用情况
def get_storage_stats():
    total_size = PerformanceAttachment.objects.aggregate(
        total=models.Sum('file_size')
    )['total'] or 0
    
    stats_by_type = PerformanceAttachment.objects.values('file_type').annotate(
        count=models.Count('id'),
        size=models.Sum('file_size')
    )
    
    return {
        'total_size_mb': total_size / (1024 * 1024),
        'by_type': list(stats_by_type)
    }
```

### 9.2 备份策略

#### 文件备份
```bash
#!/bin/bash
# 每日备份媒体文件
BACKUP_DIR="/backup/media/$(date +%Y%m%d)"
SOURCE_DIR="/var/www/npc/backend/media"

mkdir -p $BACKUP_DIR
rsync -av --progress $SOURCE_DIR/ $BACKUP_DIR/

# 保留最近30天的备份
find /backup/media -type d -mtime +30 -exec rm -rf {} +
```

#### 数据库备份
```bash
#!/bin/bash
# 备份附件相关表
mysqldump -u username -p database_name \
  performance_records performance_attachments \
  > /backup/db/performance_$(date +%Y%m%d).sql
```

## 10. 总结

本设计方案实现了以下目标：

### 10.1 功能完整性
✅ **多媒体支持**：图片、音频、视频、文档全面支持  
✅ **文件管理**：上传、预览、删除、排序功能完整  
✅ **AI友好**：为AI视频生成提供丰富的素材支持  
✅ **向后兼容**：保持现有文字录入功能不变  

### 10.2 用户体验
✅ **老年友好**：大字体、大按钮、清晰分组  
✅ **操作简单**：文字内容与附件分离，层次清晰  
✅ **提示详细**：每个操作都有明确的说明和反馈  
✅ **容错性强**：友好的错误提示和重试机制  

### 10.3 技术可靠
✅ **数据安全**：文件存储与数据库分离，支持备份  
✅ **性能优良**：图片压缩、懒加载、缩略图生成  
✅ **扩展性好**：支持云存储、CDN加速  
✅ **运维友好**：自动清理、监控统计、备份策略  

### 10.4 AI集成
✅ **素材丰富**：为AI提供文字、图片、音视频等多维度素材  
✅ **结构化数据**：标准化的数据格式便于AI处理  
✅ **年度汇总**：支持跨时间段的数据聚合分析  
✅ **生成支持**：完整的AI视频生成工作流  

这个设计方案在保持需求文档不变的前提下，为履职记录管理提供了强大的多媒体支持能力，特别考虑了老年用户的使用习惯，为后续的AI视频生成功能奠定了坚实的基础。 