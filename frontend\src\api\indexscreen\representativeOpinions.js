/**
 * 代表意见列表数据相关API
 * 包含人大代表提交的意见建议列表数据接口
 */

import { get, post } from './request.js'

/**
 * 获取代表意见列表数据
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，默认1
 * @param {number} params.pageSize - 每页数量，默认10
 * @param {string} params.status - 状态筛选，可选值：all, pending, processing, completed
 * @returns {Promise} 返回代表意见列表数据
 */
export const getRepresentativeOpinions = async (params = {}) => {
  try {
    const { page = 1, pageSize = 10, status = 'all' } = params
    
    // 模拟数据 - 后期替换为真实API调用
    const mockData = {
      code: 200,
      message: 'success',
      data: {
        list: [
          {
            id: 1,
            opinionTitle: '关于加强基层医疗卫生服务体系建设的建议',
            representativeName: '张委员',
            submitTime: '2024-01-15 14:30',
            status: 'processing',
            statusText: '处理中',
            priority: 'high',
            category: '医疗卫生',
            description: '建议加强基层医疗卫生服务体系建设，提高医疗服务质量'
          },
          {
            id: 2,
            opinionTitle: '关于完善城市公共交通网络的提案',
            representativeName: '李代表',
            submitTime: '2024-01-14 16:45',
            status: 'pending',
            statusText: '待处理',
            priority: 'medium',
            category: '交通运输',
            description: '完善城市公共交通网络，提高市民出行便利性'
          },
          {
            id: 3,
            opinionTitle: '关于推进教育信息化建设的意见',
            representativeName: '王委员',
            submitTime: '2024-01-13 10:20',
            status: 'completed',
            statusText: '已完成',
            priority: 'high',
            category: '教育科技',
            description: '推进教育信息化建设，提升教育教学质量'
          },
          {
            id: 4,
            opinionTitle: '关于加强环境保护和生态建设的建议',
            representativeName: '陈代表',
            submitTime: '2024-01-12 09:15',
            status: 'processing',
            statusText: '处理中',
            priority: 'high',
            category: '环境保护',
            description: '加强环境保护和生态建设，推动绿色发展'
          },
          {
            id: 5,
            opinionTitle: '关于促进中小企业发展的政策建议',
            representativeName: '刘委员',
            submitTime: '2024-01-11 15:30',
            status: 'pending',
            statusText: '待处理',
            priority: 'medium',
            category: '经济发展',
            description: '制定促进中小企业发展的政策措施'
          },
          {
            id: 6,
            opinionTitle: '关于完善社会保障体系的提案',
            representativeName: '赵代表',
            submitTime: '2024-01-10 11:45',
            status: 'processing',
            statusText: '处理中',
            priority: 'high',
            category: '社会保障',
            description: '完善社会保障体系，提高保障水平'
          },
          {
            id: 7,
            opinionTitle: '关于推动数字经济发展的意见',
            representativeName: '孙委员',
            submitTime: '2024-01-09 14:20',
            status: 'completed',
            statusText: '已完成',
            priority: 'medium',
            category: '数字经济',
            description: '推动数字经济发展，培育新的经济增长点'
          },
          {
            id: 8,
            opinionTitle: '关于加强食品安全监管的建议',
            representativeName: '周代表',
            submitTime: '2024-01-08 16:10',
            status: 'pending',
            statusText: '待处理',
            priority: 'high',
            category: '食品安全',
            description: '加强食品安全监管，保障人民群众身体健康'
          }
        ],
        pagination: {
          current: page,
          pageSize: pageSize,
          total: 156,
          totalPages: Math.ceil(156 / pageSize)
        },
        statistics: {
          total: 156,
          pending: 45,
          processing: 67,
          completed: 44
        }
      },
      timestamp: Date.now()
    }

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 400))
    
    console.log('📝 获取代表意见列表数据成功:', mockData.data)
    return mockData
    
    // 后期替换为真实API调用：
    // return await get('/opinions/list', params)
    
  } catch (error) {
    console.error('❌ 获取代表意见列表数据失败:', error)
    throw error
  }
}

/**
 * 获取意见详情
 * @param {number} id - 意见ID
 * @returns {Promise} 返回意见详情数据
 */
export const getOpinionDetail = async (id) => {
  try {
    // 模拟详情数据
    const mockData = {
      code: 200,
      message: 'success',
      data: {
        id: id,
        opinionTitle: '关于加强基层医疗卫生服务体系建设的建议',
        representativeName: '张委员',
        representativeInfo: {
          name: '张委员',
          level: '省人大代表',
          constituency: '医疗卫生界',
          contact: '138****5678'
        },
        submitTime: '2024-01-15 14:30',
        status: 'processing',
        statusText: '处理中',
        priority: 'high',
        category: '医疗卫生',
        content: '当前基层医疗卫生服务体系还存在一些问题，建议从以下几个方面加强建设：1. 增加基层医疗机构投入；2. 提高基层医务人员待遇；3. 完善医疗设备配置；4. 加强人才培养。',
        attachments: [
          { name: '调研报告.pdf', size: '2.5MB', url: '/files/report.pdf' }
        ],
        processHistory: [
          { time: '2024-01-15 14:30', action: '提交意见', operator: '张委员' },
          { time: '2024-01-16 09:00', action: '接收处理', operator: '办公室' },
          { time: '2024-01-17 15:20', action: '转交相关部门', operator: '协调科' }
        ]
      },
      timestamp: Date.now()
    }

    await new Promise(resolve => setTimeout(resolve, 300))
    
    console.log(`📋 获取意见详情成功 (ID: ${id}):`, mockData.data)
    return mockData
    
    // 后期替换为：
    // return await get(`/opinions/${id}`)
    
  } catch (error) {
    console.error(`❌ 获取意见详情失败 (ID: ${id}):`, error)
    throw error
  }
}

/**
 * 获取意见统计数据
 * @returns {Promise} 返回意见统计数据
 */
export const getOpinionStatistics = async () => {
  try {
    const mockData = {
      code: 200,
      message: 'success',
      data: {
        total: 156,
        thisMonth: 23,
        pending: 45,
        processing: 67,
        completed: 44,
        categories: [
          { name: '医疗卫生', count: 28 },
          { name: '教育科技', count: 24 },
          { name: '交通运输', count: 19 },
          { name: '环境保护', count: 17 },
          { name: '经济发展', count: 15 },
          { name: '其他', count: 53 }
        ],
        trends: [
          { month: '2023-07', count: 12 },
          { month: '2023-08', count: 15 },
          { month: '2023-09', count: 18 },
          { month: '2023-10', count: 21 },
          { month: '2023-11', count: 19 },
          { month: '2023-12', count: 25 },
          { month: '2024-01', count: 23 }
        ]
      },
      timestamp: Date.now()
    }

    await new Promise(resolve => setTimeout(resolve, 250))
    
    console.log('📊 获取意见统计数据成功:', mockData.data)
    return mockData
    
    // 后期替换为：
    // return await get('/opinions/statistics')
    
  } catch (error) {
    console.error('❌ 获取意见统计数据失败:', error)
    throw error
  }
} 