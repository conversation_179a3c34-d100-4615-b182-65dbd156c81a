/**
 * 履职记录相关API接口
 */

export const recordsAPI = {
  /**
   * 获取年度履职统计数据
   * @param {number} year 年度
   * @returns {Promise} 年度统计数据
   */
  async getYearlyStatistics(year) {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟不同年度的数据差异
    const currentYear = new Date().getFullYear()
    const isCurrentYear = year === currentYear
    const isPastYear = year < currentYear
    
    // 基础数据，根据年度调整
    let baseData = {
      totalRecords: 0,
      meetingCount: 0,
      proposalCount: 0,
      researchCount: 0,
      contactCount: 0,
      otherCount: 0
    }
    
    if (isCurrentYear) {
      // 当前年度：模拟正在进行中的数据
      baseData = {
        totalRecords: Math.floor(15 + Math.random() * 25), // 15-40条记录
        meetingCount: Math.floor(8 + Math.random() * 12),   // 8-20次会议
        proposalCount: Math.floor(3 + Math.random() * 8),   // 3-11个建议
        researchCount: Math.floor(5 + Math.random() * 10),  // 5-15次调研
        contactCount: Math.floor(10 + Math.random() * 20),  // 10-30次联系群众
        otherCount: Math.floor(2 + Math.random() * 5)       // 2-7次其他活动
      }
    } else if (isPastYear) {
      // 过去年度：模拟完整年度数据，数据量更大
      const yearDiff = currentYear - year
      const reduction = Math.min(yearDiff * 0.1, 0.3) // 越早的年份数据越少
      
      baseData = {
        totalRecords: Math.floor((45 + Math.random() * 25) * (1 - reduction)), // 45-70条记录
        meetingCount: Math.floor((15 + Math.random() * 15) * (1 - reduction)),   // 15-30次会议
        proposalCount: Math.floor((8 + Math.random() * 12) * (1 - reduction)),   // 8-20个建议
        researchCount: Math.floor((12 + Math.random() * 18) * (1 - reduction)),  // 12-30次调研
        contactCount: Math.floor((25 + Math.random() * 25) * (1 - reduction)),   // 25-50次联系群众
        otherCount: Math.floor((5 + Math.random() * 10) * (1 - reduction))       // 5-15次其他活动
      }
    } else {
      // 未来年度：没有数据
      baseData = {
        totalRecords: 0,
        meetingCount: 0,
        proposalCount: 0,
        researchCount: 0,
        contactCount: 0,
        otherCount: 0
      }
    }
    
    // 确保总数一致
    baseData.totalRecords = Math.max(
      baseData.totalRecords,
      baseData.meetingCount + baseData.proposalCount + baseData.researchCount + 
      baseData.contactCount + baseData.otherCount
    )
    
    return baseData
  },

  /**
   * 获取履职记录列表
   * @param {Object} params 查询参数
   * @param {number} params.year 年度
   * @param {string} params.type 活动类型
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页大小
   * @returns {Promise} 履职记录列表
   */
  async getRecordsList(params = {}) {
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const { year = new Date().getFullYear(), type = '', page = 1, pageSize = 10 } = params
    
    // 模拟记录数据
    const mockRecords = []
    const recordTypes = ['视察调研', '学习培训', '接待走访', '执法检查', '主题活动', '述职', '法规征询意见', '政策法规宣传', '议案建议办理督办', '会议', '其它']
    const statusList = ['已完成', '进行中', '已提交']
    
    for (let i = 0; i < 50; i++) {
      const recordType = type || recordTypes[Math.floor(Math.random() * recordTypes.length)]
      mockRecords.push({
        id: `record_${year}_${i + 1}`,
        title: `${recordType}活动${i + 1}`,
        type: recordType,
        date: new Date(year, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
        status: statusList[Math.floor(Math.random() * statusList.length)],
        content: `这是一个关于${recordType}的详细记录内容...`,
        location: ['市政府', '社区', '企业', '学校', '医院'][Math.floor(Math.random() * 5)],
        participants: Math.floor(5 + Math.random() * 20),
        duration: Math.floor(1 + Math.random() * 4) + '小时'
      })
    }
    
    // 过滤和分页
    let filteredRecords = mockRecords
    if (type) {
      filteredRecords = mockRecords.filter(record => record.type === type)
    }
    
    const total = filteredRecords.length
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const records = filteredRecords.slice(start, end)
    
    return {
      records,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  },

  /**
   * 获取履职记录详情
   * @param {string} recordId 记录ID
   * @returns {Promise} 记录详情
   */
  async getRecordDetail(recordId) {
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 模拟记录详情
    return {
      id: recordId,
      title: '参与市政府常务会议',
      type: '会议参与',
      date: new Date().toISOString().split('T')[0],
      status: '已完成',
      content: '本次会议主要讨论了城市基础设施建设、民生改善等重要议题。我在会上就教育资源均衡配置问题提出了具体建议...',
      location: '市政府会议室',
      participants: 25,
      duration: '3小时',
      attachments: [
        { name: '会议纪要.pdf', url: '#' },
        { name: '发言稿.docx', url: '#' }
      ],
      tags: ['会议', '基础设施', '教育', '民生'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  },

  /**
   * 创建履职记录
   * @param {Object} recordData 记录数据
   * @returns {Promise} 创建结果
   */
  async createRecord(recordData) {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟创建成功
    return {
      id: `record_${Date.now()}`,
      ...recordData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  },

  /**
   * 更新履职记录
   * @param {string} recordId 记录ID
   * @param {Object} recordData 更新的记录数据
   * @returns {Promise} 更新结果
   */
  async updateRecord(recordId, recordData) {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟更新成功
    return {
      id: recordId,
      ...recordData,
      updatedAt: new Date().toISOString()
    }
  },

  /**
   * 删除履职记录
   * @param {string} recordId 记录ID
   * @returns {Promise} 删除结果
   */
  async deleteRecord(recordId) {
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 模拟删除成功
    return {
      success: true,
      message: '记录删除成功'
    }
  },

  /**
   * 获取履职类型选项
   * @returns {Promise} 类型选项列表
   */
  async getRecordTypes() {
    await new Promise(resolve => setTimeout(resolve, 100))
    
    return [
      { value: '视察调研', label: '视察调研', icon: 'search' },
      { value: '学习培训', label: '学习培训', icon: 'academic-cap' },
      { value: '接待走访', label: '接待走访', icon: 'user-group' },
      { value: '执法检查', label: '执法检查', icon: 'shield-check' },
      { value: '主题活动', label: '主题活动', icon: 'calendar' },
      { value: '述职', label: '述职', icon: 'presentation-chart-line' },
      { value: '法规征询意见', label: '法规征询意见', icon: 'document-text' },
      { value: '政策法规宣传', label: '政策法规宣传', icon: 'speakerphone' },
      { value: '议案建议办理督办', label: '议案建议办理督办', icon: 'clipboard-check' },
      { value: '会议', label: '会议', icon: 'office-building' },
      { value: '其它', label: '其它', icon: 'more' }
    ]
  }
}

export default recordsAPI 