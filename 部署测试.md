# 后端django部署

## 打包backend文件


## sqlite数据库版本部署测试

```
pip install uv
cd backend
1.删除.venv目录
2.设置.env文件
3.uv sync
.venv\Scripts\activate.bat

http方式
# uv run manage.py runserver 0.0.0.0:8000



# https,DEBUG设置为false的时候（特殊情况）
uv run manage.py runserver_plus 0.0.0.0:8000 --cert-file cert.crt
```

# 前端部署

```
# http/https  修改vite.config.js配置
# 修改后端地址config.js,部署相同机器则不用修改
// 开发环境配置
  development: {
    // 后端实际地址（用于代理目标）
    BACKEND_URL: 'http://localhost:8000',
    // 前端使用的API地址（通过代理访问）
    API_BASE_URL: '/api/v1',  // 使用相对路径，通过Vite代理
    // 前端地址
    FRONTEND_URL: 'http://localhost:3000',
    USE_PROXY: true  // 开发环境使用代理
  },


# npm run dev 运行在0.0.0.0
```
