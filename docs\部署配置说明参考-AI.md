# 部署配置说明

## 🎯 解决方案概述

本项目采用代理方式解决 CORS 和 SSL 证书信任问题：

- **开发环境**：Vite 代理 `https://************:3000/api/v1` → `https://************:8000`
- **生产环境**：Nginx 代理 `https://yourdomain.com/api/v1` → `https://127.0.0.1:8000`

## 🔧 开发环境配置

### 1. 前端配置
```javascript
// src/api/http/config.js
development: {
  BACKEND_URL: 'https://************:8000',     // 后端实际地址
  API_BASE_URL: '/api/v1',                      // 通过代理访问
  FRONTEND_URL: 'https://************:3000',   // 前端地址
  USE_PROXY: true                               // 启用Vite代理
}
```

### 2. Vite 代理配置
```javascript
// vite.config.js - 自动从 config.js 导入
proxy: {
  '/api': {
    target: 'https://************:8000',
    changeOrigin: true,
    secure: false  // 忽略SSL证书验证
  }
}
```

### 3. 访问方式
- 前端：`https://************:3000`
- API请求：`https://************:3000/api/v1/xxx` → 自动代理到后端

### 4. 优势
✅ 浏览器只需信任前端证书（3000端口）
✅ 避免直接访问后端的 CORS 问题
✅ 统一的域名和端口访问

## 🚀 生产环境部署

### 1. Nginx 配置
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    # 前端静态文件
    location / {
        root /var/www/npc-frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理到后端
    location /api/ {
        proxy_pass https://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. 部署步骤

#### 步骤1：构建前端
```bash
cd frontend
npm run build
```

#### 步骤2：部署到 Nginx
```bash
# 复制构建文件到 Nginx 目录
sudo cp -r dist/* /var/www/npc-frontend/

# 复制 Nginx 配置
sudo cp nginx.conf /etc/nginx/sites-available/npc
sudo ln -s /etc/nginx/sites-available/npc /etc/nginx/sites-enabled/

# 重启 Nginx
sudo nginx -t
sudo systemctl reload nginx
```

#### 步骤3：启动后端
```bash
cd backend
python manage.py runserver 127.0.0.1:8000
```

### 3. 访问方式
- 生产环境：`https://yourdomain.com`
- API请求：`https://yourdomain.com/api/v1/xxx` → Nginx代理到后端

## 🔒 SSL 证书配置

### 开发环境
- 使用自签名证书或开发证书
- 浏览器手动信任前端证书即可

### 生产环境
- 使用 Let's Encrypt 或购买的 SSL 证书
- 配置在 Nginx 中

## 📋 配置检查清单

### 开发环境
- [ ] `config.js` 中 `API_BASE_URL` 设置为 `/api/v1`
- [ ] `vite.config.js` 代理配置正确
- [ ] 后端 `ALLOWED_HOSTS` 包含 `************`
- [ ] 后端 `CORS_ALLOWED_ORIGINS` 包含前端地址

### 生产环境
- [ ] Nginx 配置文件正确
- [ ] SSL 证书配置完成
- [ ] 前端构建文件部署到正确目录
- [ ] 后端服务正常运行
- [ ] 域名 DNS 解析正确

## 🐛 常见问题

### 1. CORS 错误
- 检查后端 `CORS_ALLOWED_ORIGINS` 配置
- 确认代理配置是否生效

### 2. SSL 证书错误
- 开发环境：在浏览器中手动信任证书
- 生产环境：检查证书文件路径和权限

### 3. 代理不生效
- 重启 Vite 开发服务器
- 检查 `vite.config.js` 配置
- 查看浏览器网络面板确认请求路径

### 4. 静态文件 404
- 检查 Nginx 配置中的 `root` 路径
- 确认文件权限和所有权
