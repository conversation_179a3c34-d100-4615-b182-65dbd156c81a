/**
 * 代表履职统计数据相关API
 * 包含代表履职情况统计、月度履职数据等接口
 */

import { get, post } from './request.js'

/**
 * 获取月度代表履职统计数据
 * @param {Object} params - 查询参数
 * @param {number} params.year - 年份，默认当前年份
 * @returns {Promise} 返回月度履职统计数据
 */
export const getMonthlyPerformanceStats = async (params = {}) => {
  try {
    const { year = new Date().getFullYear() } = params
    
    // 模拟月度履职数据 - 后期替换为真实API调用
    const mockData = {
      code: 200,
      message: 'success',
      data: {
        year: year,
        monthlyData: [
          { month: 1, count: 89, representatives: 45, activities: ['调研活动', '座谈会议', '实地考察'] },
          { month: 2, count: 76, representatives: 38, activities: ['春节慰问', '企业走访'] },
          { month: 3, count: 145, representatives: 72, activities: ['两会期间', '提案建议', '专题调研'] },
          { month: 4, count: 123, representatives: 61, activities: ['教育调研', '医疗考察', '环保检查'] },
          { month: 5, count: 167, representatives: 83, activities: ['五一调研', '劳动保障', '企业服务'] },
          { month: 6, count: 134, representatives: 67, activities: ['儿童节活动', '教育视察', '安全检查'] },
          { month: 7, count: 98, representatives: 49, activities: ['暑期调研', '学生安全', '防汛检查'] },
          { month: 8, count: 112, representatives: 56, activities: ['环境保护', '生态建设', '绿色发展'] },
          { month: 9, count: 156, representatives: 78, activities: ['开学调研', '教师节活动', '校园安全'] },
          { month: 10, count: 143, representatives: 71, activities: ['国庆调研', '旅游安全', '市容环境'] },
          { month: 11, count: 178, representatives: 89, activities: ['年末冲刺', '民生调研', '社会保障'] },
          { month: 12, count: 134, representatives: 67, activities: ['年终总结', '来年规划', '述职汇报'] }
        ],
        summary: {
          totalActivities: 1555,
          totalRepresentatives: 156,
          averagePerMonth: 129.6,
          peakMonth: { month: 11, count: 178 },
          lowMonth: { month: 2, count: 76 }
        },
        trends: {
          growthRate: 8.5, // 同比增长率
          participationRate: 94.2, // 代表参与率
          satisfactionRate: 96.8 // 满意度
        }
      },
      timestamp: Date.now()
    }

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 600))
    
    console.log(`📊 获取${year}年月度履职统计数据成功:`, mockData.data)
    return mockData
    
    // 后期替换为真实API调用：
    // return await get('/representatives/monthly-stats', params)
    
  } catch (error) {
    console.error('❌ 获取月度履职统计数据失败:', error)
    throw error
  }
}

/**
 * 获取代表履职详细统计
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回详细统计数据
 */
export const getPerformanceDetailStats = async (params = {}) => {
  try {
    const mockData = {
      code: 200,
      message: 'success',
      data: {
        categories: [
          { name: '调研活动', count: 456, percentage: 29.3 },
          { name: '意见建议', count: 387, percentage: 24.9 },
          { name: '监督检查', count: 234, percentage: 15.0 },
          { name: '座谈交流', count: 198, percentage: 12.7 },
          { name: '实地考察', count: 167, percentage: 10.7 },
          { name: '其他活动', count: 113, percentage: 7.3 }
        ],
        regions: [
          { name: '城区', count: 678, percentage: 43.6 },
          { name: '郊区', count: 456, percentage: 29.3 },
          { name: '农村', count: 421, percentage: 27.1 }
        ]
      },
      timestamp: Date.now()
    }

    await new Promise(resolve => setTimeout(resolve, 400))
    
    console.log('📈 获取履职详细统计数据成功:', mockData.data)
    return mockData
    
  } catch (error) {
    console.error('❌ 获取履职详细统计数据失败:', error)
    throw error
  }
} 