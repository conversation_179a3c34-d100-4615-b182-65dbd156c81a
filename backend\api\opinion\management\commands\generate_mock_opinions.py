"""
生成模拟意见建议数据的Django管理命令

使用方法：
python manage.py generate_mock_opinions --username rep001 --count 20
uv run manage.py generate_mock_opinions --username rep001 --count 20
功能：
1. 为指定代表账号生成模拟意见建议数据
2. 包含不同分类、状态的意见建议
3. 部分使用AI辅助功能
4. 自动创建对应的审核记录
"""

import random
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.contrib.auth import get_user_model
from api.users.models import Representative
from api.opinion.models import OpinionSuggestion, OpinionReview

User = get_user_model()


class Command(BaseCommand):
    help = '为指定代表生成模拟意见建议数据'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            required=True,
            help='代表账号用户名'
        )
        parser.add_argument(
            '--count',
            type=int,
            default=20,
            help='生成意见建议数量，默认20条'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='清除该代表的所有现有意见建议数据'
        )
    
    def handle(self, *args, **options):
        username = options['username']
        count = options['count']
        clear_existing = options['clear']
        
        try:
            # 获取代表用户
            user = User.objects.get(username=username, role='representative')
            representative = Representative.objects.get(user=user)
            
            self.stdout.write(
                self.style.SUCCESS(f'找到代表用户: {representative.name} ({username})')
            )
            
            # 清除现有数据（如果指定）
            if clear_existing:
                existing_count = OpinionSuggestion.objects.filter(representative=representative).count()
                OpinionSuggestion.objects.filter(representative=representative).delete()
                self.stdout.write(
                    self.style.WARNING(f'已清除 {existing_count} 条现有意见建议数据')
                )
            
            # 生成模拟数据
            self.generate_mock_opinions(representative, count)
            
            self.stdout.write(
                self.style.SUCCESS(f'成功为代表 {representative.name} 生成 {count} 条模拟意见建议数据')
            )
            
        except User.DoesNotExist:
            raise CommandError(f'未找到用户名为 {username} 的代表用户')
        except Representative.DoesNotExist:
            raise CommandError(f'用户 {username} 不是代表用户')
        except Exception as e:
            raise CommandError(f'生成数据时发生错误: {str(e)}')
    
    def generate_mock_opinions(self, representative, count):
        """生成模拟意见建议数据"""
        
        # 模拟数据模板
        mock_data_templates = [
            {
                'category': 'urban_construction',
                'titles': [
                    '关于改善小区绿化环境的建议',
                    '建议加强垃圾分类设施建设',
                    '关于解决老旧小区停车难问题的建议',
                    '建议增设公园健身器材',
                    '关于改善市区空气质量的建议'
                ],
                'reporters': ['张三', '李四', '王五', '赵六', '孙七'],
                'original_contents': [
                    '我们小区的绿化面积太少，希望能够增加一些绿植和花坛，改善居住环境。',
                    '现在垃圾分类政策推行，但是我们小区的垃圾分类设施不够完善，希望能够改进。',
                    '老旧小区停车位严重不足，居民停车困难，希望政府能够统筹解决。',
                    '公园里的健身器材老化严重，希望能够更新换代，满足居民健身需求。',
                    '最近空气质量不太好，希望政府加强环保治理，改善空气质量。'
                ]
            },
            {
                'category': 'transportation',
                'titles': [
                    '建议优化公交线路设置',
                    '关于增设人行天桥的建议',
                    '建议改善道路交通信号灯配时',
                    '关于解决上下班高峰期拥堵的建议',
                    '建议增加共享单车投放点'
                ],
                'reporters': ['陈八', '周九', '吴十', '郑一', '钱二'],
                'original_contents': [
                    '现有公交线路不够合理，有些地方需要换乘多次才能到达，希望优化线路设置。',
                    '某路段车流量大，行人过马路不安全，建议增设人行天桥。',
                    '路口红绿灯配时不合理，经常造成交通拥堵，希望能够调整。',
                    '上下班高峰期某路段严重拥堵，影响市民出行，希望采取措施缓解。',
                    '共享单车投放点太少，有时找不到车，希望增加投放点。'
                ]
            },
            {
                'category': 'education',
                'titles': [
                    '建议增加幼儿园学位供给',
                    '关于改善学校周边交通安全的建议',
                    '建议开设老年大学课程',
                    '关于加强青少年课外活动场所建设的建议',
                    '建议完善社区图书馆设施'
                ],
                'reporters': ['何三', '卢四', '韩五', '邓六', '彭七'],
                'original_contents': [
                    '我们片区幼儿园学位紧张，孩子入园困难，希望能够增加学位供给。',
                    '学校门口上下学时间交通混乱，存在安全隐患，希望改善交通秩序。',
                    '社区老年人希望能够继续学习，建议开设更多老年大学课程。',
                    '青少年缺乏课外活动场所，希望能够建设更多文体活动中心。',
                    '社区图书馆设施简陋，图书更新不及时，希望能够完善。'
                ]
            },
            {
                'category': 'healthcare',
                'titles': [
                    '建议增加社区卫生服务站',
                    '关于改善医院就诊环境的建议',
                    '建议加强老年人健康管理',
                    '关于完善急救医疗体系的建议',
                    '建议开展更多健康教育活动'
                ],
                'reporters': ['冯八', '陈九', '褚十', '卫一', '蒋二'],
                'original_contents': [
                    '我们片区只有一个卫生服务站，居民看病不方便，希望能够增加。',
                    '医院候诊环境较差，希望能够改善就诊环境，提升服务质量。',
                    '社区老年人较多，希望能够加强健康管理和定期体检。',
                    '遇到紧急情况时，急救响应时间较长，希望完善急救体系。',
                    '居民健康意识不强，希望多开展健康教育和宣传活动。'
                ]
            },
            {
                'category': 'social_security',
                'titles': [
                    '建议完善养老服务设施',
                    '关于提高低保标准的建议',
                    '建议加强残疾人无障碍设施建设',
                    '关于改善农民工就业环境的建议',
                    '建议建立社区互助服务体系'
                ],
                'reporters': ['沈三', '韩四', '杨五', '朱六', '秦七'],
                'original_contents': [
                    '社区养老设施不足，老年人生活不便，希望能够完善养老服务。',
                    '现在物价上涨，低保标准偏低，希望能够适当提高。',
                    '残疾人出行不便，希望加强无障碍设施建设。',
                    '农民工就业环境较差，权益保障不足，希望改善。',
                    '邻里互助意识不强，希望建立社区互助服务体系。'
                ]
            },
            {
                'category': 'economic',
                'titles': [
                    '建议支持小微企业发展',
                    '关于促进就业创业的建议',
                    '建议完善市场监管体系',
                    '关于发展夜间经济的建议',
                    '建议加强电商平台监管'
                ],
                'reporters': ['尤八', '许九', '何十', '吕一', '施二'],
                'original_contents': [
                    '小微企业融资困难，经营成本高，希望政府给予更多支持。',
                    '就业机会不多，希望政府出台更多促进就业创业的政策。',
                    '市场上存在一些不规范经营行为，希望加强监管。',
                    '夜间经济发展潜力大，建议制定相关政策促进发展。',
                    '网购时遇到假货问题，希望加强对电商平台的监管。'
                ]
            },
            {
                'category': 'government_service',
                'titles': [
                    '建议简化办事流程',
                    '关于提升政务服务效率的建议',
                    '建议完善网上办事平台',
                    '关于加强政务信息公开的建议',
                    '建议设立便民服务热线'
                ],
                'reporters': ['张三', '李四', '王五', '赵六', '孙七'],
                'original_contents': [
                    '办理证件需要跑多个部门，流程复杂，希望能够简化。',
                    '政务服务效率不高，等待时间长，希望能够提升。',
                    '网上办事平台功能不完善，使用不便，希望改进。',
                    '政府信息公开不够及时，希望加强信息公开工作。',
                    '遇到问题时不知道找哪个部门，希望设立统一的便民热线。'
                ]
            },
            {
                'category': 'other',
                'titles': [
                    '建议加强社区文化建设',
                    '关于改善居民生活环境的建议',
                    '建议完善社区安全防范',
                    '关于加强精神文明建设的建议',
                    '建议开展更多社区活动'
                ],
                'reporters': ['陈八', '周九', '吴十', '郑一', '钱二'],
                'original_contents': [
                    '社区文化氛围不浓，希望能够加强文化建设，丰富居民精神生活。',
                    '居民生活环境有待改善，希望政府关注民生问题。',
                    '社区安全防范措施不够完善，希望加强安全管理。',
                    '社会风气有待改善，希望加强精神文明建设。',
                    '社区活动较少，邻里关系疏远，希望多开展社区活动。'
                ]
            }
        ]
        
        # 状态权重（用于随机选择状态）
        status_weights = [
            ('draft', 0.15),        # 15% 草稿
            ('submitted', 0.20),    # 20% 已提交
            ('approved', 0.25),     # 25% 审核通过
            ('transferred', 0.20),  # 20% 已转交
            ('in_progress', 0.15),  # 15% 处理中
            ('completed', 0.05),    # 5% 已办结
        ]
        
        # 生成意见建议
        for i in range(count):
            # 随机选择分类模板
            template = random.choice(mock_data_templates)
            
            # 随机选择具体数据
            title = random.choice(template['titles'])
            reporter_name = random.choice(template['reporters'])
            original_content = random.choice(template['original_contents'])
            
            # 随机决定是否使用AI辅助
            ai_assisted = random.choice([True, False])
            ai_generated_content = None
            final_suggestion = None
            
            if ai_assisted:
                # 模拟AI生成的建议内容
                ai_generated_content = f"经过AI分析，针对「{title}」的问题，建议采取以下措施：\n\n1. 深入调研现状，了解具体问题根源\n2. 制定针对性解决方案\n3. 建立长效管理机制\n4. 加强部门协调配合\n5. 定期评估改进效果\n\n通过系统性的治理措施，相信能够有效解决群众反映的问题，提升民生服务水平。"
                final_suggestion = ai_generated_content
            else:
                final_suggestion = f"针对群众反映的问题，建议相关部门高度重视，及时研究解决方案，切实改善民生服务质量。"
            
            # 创建意见建议
            opinion = OpinionSuggestion.objects.create(
                representative=representative,
                title=f"{title}（第{i+1}条）",
                category=template['category'],
                reporter_name=reporter_name,
                original_content=original_content,
                final_suggestion=final_suggestion,
                ai_assisted=ai_assisted,
                ai_generated_content=ai_generated_content,
                created_at=self.get_random_datetime()
            )
            
            # 根据权重随机选择最终状态
            final_status = self.weighted_random_choice(status_weights)
            
            # 创建审核记录链
            self.create_review_chain(opinion, final_status)
            
            # 显示进度
            if (i + 1) % 5 == 0:
                self.stdout.write(f'已生成 {i + 1}/{count} 条意见建议...')
    
    def get_random_datetime(self):
        """生成随机的创建时间（最近3个月内）"""
        now = timezone.now()
        start_date = now - timedelta(days=90)
        random_days = random.randint(0, 90)
        random_hours = random.randint(0, 23)
        random_minutes = random.randint(0, 59)
        
        return start_date + timedelta(
            days=random_days,
            hours=random_hours,
            minutes=random_minutes
        )
    
    def weighted_random_choice(self, choices):
        """根据权重随机选择"""
        total_weight = sum(weight for _, weight in choices)
        random_num = random.uniform(0, total_weight)
        
        current_weight = 0
        for choice, weight in choices:
            current_weight += weight
            if random_num <= current_weight:
                return choice
        
        return choices[0][0]  # 默认返回第一个选择
    
    def create_review_chain(self, opinion, final_status):
        """创建审核记录链"""
        base_time = opinion.created_at
        
        # 状态转换链
        status_chains = {
            'draft': [
                ('create', 'draft')
            ],
            'submitted': [
                ('create', 'draft'),
                ('submit', 'submitted')
            ],
            'approved': [
                ('create', 'draft'),
                ('submit', 'submitted'),
                ('approve', 'approved')
            ],
            'transferred': [
                ('create', 'draft'),
                ('submit', 'submitted'),
                ('approve', 'approved'),
                ('transfer', 'transferred')
            ],
            'in_progress': [
                ('create', 'draft'),
                ('submit', 'submitted'),
                ('approve', 'approved'),
                ('transfer', 'transferred'),
                ('update_progress', 'in_progress')
            ],
            'completed': [
                ('create', 'draft'),
                ('submit', 'submitted'),
                ('approve', 'approved'),
                ('transfer', 'transferred'),
                ('update_progress', 'in_progress'),
                ('close', 'completed')
            ]
        }
        
        chain = status_chains.get(final_status, status_chains['draft'])
        
        # 创建审核记录
        for i, (action, status) in enumerate(chain):
            # 计算时间间隔
            time_offset = timedelta(
                days=random.randint(0, 3),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59)
            )
            action_time = base_time + time_offset * (i + 1)
            
            # 生成审核备注
            review_comment = self.get_review_comment(action, status)
            
            # 生成转交部门（如果是转交操作）
            transferred_department = None
            if action == 'transfer':
                departments = ['城建局', '交通局', '教育局', '卫健委', '民政局', '发改委', '市场监管局']
                transferred_department = random.choice(departments)
            
            # 生成处理结果（如果是办结操作）
            processing_result = None
            if action == 'close':
                processing_result = "经过相关部门认真研究和实地调研，已制定具体解决方案并组织实施。目前问题已得到有效解决，群众反映良好。"
            
            OpinionReview.objects.create(
                opinion=opinion,
                reviewer=None,  # 暂时不关联具体审核人
                action=action,
                status=status,
                transferred_department=transferred_department,
                review_comment=review_comment,
                processing_result=processing_result,
                action_time=action_time
            )
    
    def get_review_comment(self, action, status):
        """根据操作类型生成审核备注"""
        comments = {
            'create': '代表创建意见建议草稿',
            'submit': '代表提交意见建议，等待站点审核',
            'approve': '站点审核通过，意见建议具有较高价值，建议转交相关部门处理',
            'transfer': '已转交至相关职能部门，请及时跟进处理进展',
            'update_progress': '相关部门正在积极处理中，已制定初步解决方案',
            'close': '问题已得到妥善解决，群众满意度较高，予以办结'
        }
        
        return comments.get(action, '系统操作')