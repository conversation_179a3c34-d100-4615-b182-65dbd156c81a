/**
 * 账号管理相关API接口
 * 实现站点工作人员管理所有用户账号的功能
 */

// 模拟账号数据
const mockAccounts = [
  {
    id: 1,
    username: 'representative1',
    realName: '张明',
    role: 'representative',
    roleText: '人大代表',
    department: '第一选区',
    phone: '***********',
    email: '<EMAIL>',
    status: 'active',
    statusText: '启用',
    lastLoginTime: '2024-12-20 09:30:00',
    createTime: '2024-01-15 10:00:00',
    remark: '市级人大代表'
  },
  {
    id: 2,
    username: 'representative2',
    realName: '李华',
    role: 'representative',
    roleText: '人大代表',
    department: '第二选区',
    phone: '***********',
    email: '<EMAIL>',
    status: 'active',
    statusText: '启用',
    lastLoginTime: '2024-12-19 14:20:00',
    createTime: '2024-01-20 11:00:00',
    remark: '区级人大代表'
  },
  {
    id: 3,
    username: 'staff1',
    realName: '王芳',
    role: 'staff',
    roleText: '站点工作人员',
    department: '某某街道工作站',
    phone: '***********',
    email: '<EMAIL>',
    status: 'active',
    statusText: '启用',
    lastLoginTime: '2024-12-20 08:45:00',
    createTime: '2024-02-01 09:00:00',
    remark: '站点负责人'
  },
  {
    id: 4,
    username: 'staff2',
    realName: '刘强',
    role: 'staff',
    roleText: '站点工作人员',
    department: '某某街道工作站',
    phone: '13800138004',
    email: '<EMAIL>',
    status: 'disabled',
    statusText: '禁用',
    lastLoginTime: '2024-12-10 16:30:00',
    createTime: '2024-02-15 10:30:00',
    remark: '临时停用'
  },
  {
    id: 5,
    username: 'representative3',
    realName: '陈敏',
    role: 'representative',
    roleText: '人大代表',
    department: '第三选区',
    phone: '***********',
    email: '<EMAIL>',
    status: 'active',
    statusText: '启用',
    lastLoginTime: '2024-12-18 11:15:00',
    createTime: '2024-03-01 14:00:00',
    remark: '县级人大代表'
  }
]

// 模拟延迟函数
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

// 生成新的ID
let nextId = Math.max(...mockAccounts.map(a => a.id)) + 1

export const accountManagementAPI = {
  /**
   * 获取账号列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.keyword - 搜索关键词
   * @returns {Promise} 账号列表数据
   */
  async getAccountList(params = {}) {
    await delay()
    
    const { page = 1, pageSize = 20, keyword = '' } = params
    
    // 搜索过滤
    let filteredAccounts = mockAccounts
    if (keyword) {
      filteredAccounts = mockAccounts.filter(account => 
        account.username.toLowerCase().includes(keyword.toLowerCase()) ||
        account.realName.toLowerCase().includes(keyword.toLowerCase())
      )
    }
    
    // 分页
    const total = filteredAccounts.length
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const list = filteredAccounts.slice(start, end)
    
    return {
      code: 200,
      message: '获取成功',
      data: {
        list,
        total,
        page,
        pageSize
      }
    }
  },

  /**
   * 创建新账号
   * @param {Object} accountData - 账号数据
   * @returns {Promise} 创建结果
   */
  async createAccount(accountData) {
    await delay()
    
    // 检查用户名是否已存在
    const existingAccount = mockAccounts.find(account => 
      account.username === accountData.username
    )
    
    if (existingAccount) {
      throw new Error('用户名已存在')
    }
    
    // 创建新账号
    const newAccount = {
      id: nextId++,
      username: accountData.username,
      realName: accountData.realName,
      role: accountData.role,
      roleText: accountData.role === 'representative' ? '人大代表' : '站点工作人员',
      department: accountData.department,
      phone: accountData.phone,
      email: accountData.email || '',
      status: accountData.status,
      statusText: accountData.status === 'active' ? '启用' : '禁用',
      lastLoginTime: '从未登录',
      createTime: new Date().toLocaleString('zh-CN'),
      remark: accountData.remark || ''
    }
    
    mockAccounts.unshift(newAccount)
    
    return {
      code: 200,
      message: '账号创建成功',
      data: newAccount
    }
  },

  /**
   * 更新账号信息
   * @param {number} accountId - 账号ID
   * @param {Object} accountData - 更新的账号数据
   * @returns {Promise} 更新结果
   */
  async updateAccount(accountId, accountData) {
    await delay()
    
    const accountIndex = mockAccounts.findIndex(account => account.id === accountId)
    
    if (accountIndex === -1) {
      throw new Error('账号不存在')
    }
    
    // 更新账号信息
    const updatedAccount = {
      ...mockAccounts[accountIndex],
      realName: accountData.realName,
      role: accountData.role,
      roleText: accountData.role === 'representative' ? '人大代表' : '站点工作人员',
      department: accountData.department,
      phone: accountData.phone,
      email: accountData.email || '',
      status: accountData.status,
      statusText: accountData.status === 'active' ? '启用' : '禁用',
      remark: accountData.remark || ''
    }
    
    mockAccounts[accountIndex] = updatedAccount
    
    return {
      code: 200,
      message: '账号更新成功',
      data: updatedAccount
    }
  },

  /**
   * 重置账号密码
   * @param {number} accountId - 账号ID
   * @param {Object} passwordData - 密码数据
   * @param {string} passwordData.newPassword - 新密码
   * @returns {Promise} 重置结果
   */
  async resetPassword(accountId, passwordData) {
    await delay()
    
    const account = mockAccounts.find(account => account.id === accountId)
    
    if (!account) {
      throw new Error('账号不存在')
    }
    
    // 模拟密码重置（实际应用中会更新数据库中的密码哈希）
    console.log(`账号 ${account.username} 的密码已重置为: ${passwordData.newPassword}`)
    
    return {
      code: 200,
      message: '密码重置成功',
      data: {
        accountId,
        username: account.username,
        resetTime: new Date().toLocaleString('zh-CN')
      }
    }
  },

  /**
   * 更新账号状态
   * @param {number} accountId - 账号ID
   * @param {string} status - 新状态 ('active' | 'disabled')
   * @returns {Promise} 更新结果
   */
  async updateAccountStatus(accountId, status) {
    await delay()
    
    const accountIndex = mockAccounts.findIndex(account => account.id === accountId)
    
    if (accountIndex === -1) {
      throw new Error('账号不存在')
    }
    
    // 更新账号状态
    mockAccounts[accountIndex].status = status
    mockAccounts[accountIndex].statusText = status === 'active' ? '启用' : '禁用'
    
    return {
      code: 200,
      message: `账号${status === 'active' ? '启用' : '禁用'}成功`,
      data: {
        accountId,
        status,
        updateTime: new Date().toLocaleString('zh-CN')
      }
    }
  },

  /**
   * 删除账号（软删除）
   * @param {number} accountId - 账号ID
   * @returns {Promise} 删除结果
   */
  async deleteAccount(accountId) {
    await delay()
    
    const accountIndex = mockAccounts.findIndex(account => account.id === accountId)
    
    if (accountIndex === -1) {
      throw new Error('账号不存在')
    }
    
    // 软删除：标记为已删除状态
    mockAccounts[accountIndex].status = 'deleted'
    mockAccounts[accountIndex].statusText = '已删除'
    
    return {
      code: 200,
      message: '账号删除成功',
      data: {
        accountId,
        deleteTime: new Date().toLocaleString('zh-CN')
      }
    }
  },

  /**
   * 批量操作账号
   * @param {Array} accountIds - 账号ID数组
   * @param {string} action - 操作类型 ('enable' | 'disable' | 'delete')
   * @returns {Promise} 批量操作结果
   */
  async batchUpdateAccounts(accountIds, action) {
    await delay()
    
    const results = []
    
    for (const accountId of accountIds) {
      const accountIndex = mockAccounts.findIndex(account => account.id === accountId)
      
      if (accountIndex !== -1) {
        switch (action) {
          case 'enable':
            mockAccounts[accountIndex].status = 'active'
            mockAccounts[accountIndex].statusText = '启用'
            break
          case 'disable':
            mockAccounts[accountIndex].status = 'disabled'
            mockAccounts[accountIndex].statusText = '禁用'
            break
          case 'delete':
            mockAccounts[accountIndex].status = 'deleted'
            mockAccounts[accountIndex].statusText = '已删除'
            break
        }
        
        results.push({
          accountId,
          success: true,
          message: '操作成功'
        })
      } else {
        results.push({
          accountId,
          success: false,
          message: '账号不存在'
        })
      }
    }
    
    return {
      code: 200,
      message: '批量操作完成',
      data: {
        results,
        successCount: results.filter(r => r.success).length,
        failCount: results.filter(r => !r.success).length
      }
    }
  },

  /**
   * 获取账号详情
   * @param {number} accountId - 账号ID
   * @returns {Promise} 账号详情
   */
  async getAccountDetail(accountId) {
    await delay()
    
    const account = mockAccounts.find(account => account.id === accountId)
    
    if (!account) {
      throw new Error('账号不存在')
    }
    
    return {
      code: 200,
      message: '获取成功',
      data: account
    }
  },

  /**
   * 获取角色统计信息
   * @returns {Promise} 角色统计数据
   */
  async getRoleStatistics() {
    await delay()
    
    const stats = {
      total: mockAccounts.length,
      representative: mockAccounts.filter(a => a.role === 'representative').length,
      staff: mockAccounts.filter(a => a.role === 'staff').length,
      active: mockAccounts.filter(a => a.status === 'active').length,
      disabled: mockAccounts.filter(a => a.status === 'disabled').length
    }
    
    return {
      code: 200,
      message: '获取成功',
      data: stats
    }
  }
} 