# 意见建议管理前端API模块

## 📋 概述

本模块提供了完整的意见建议管理前端API接口，支持代表录入、AI辅助生成、提交审核等功能。模块采用现代化的Vue 3 + Element Plus技术栈，提供优秀的用户体验。

## 📁 文件结构

```
frontend/src/api/modules/opinion/
├── api.js          # 核心API接口定义
├── index.js        # 模块入口文件
├── test.js         # API测试工具
└── README.md       # 本文档
```

## 🔧 核心功能

### 1. opinionAPI - 意见建议API
提供完整的CRUD操作接口：

```javascript
import { opinionAPI } from '@/api/modules/opinion'

// 获取意见建议列表
const response = await opinionAPI.getOpinionsList({
  page: 1,
  page_size: 10,
  status: 'draft',
  category: 'urban_construction'
})

// 创建意见建议
const createResponse = await opinionAPI.createOpinion({
  title: '意见建议标题',
  category: 'urban_construction',
  reporter_name: '反映人姓名',
  original_content: '原始意见内容',
  final_suggestion: '最终建议内容'
})

// 提交意见建议
await opinionAPI.submitOpinion(opinionId)
```

### 2. opinionAIAPI - AI辅助功能
提供AI辅助生成高质量意见建议：

```javascript
import { opinionAIAPI } from '@/api/modules/opinion'

// AI辅助生成建议
const aiResponse = await opinionAIAPI.generateSuggestion({
  original_content: '原始意见内容',
  category: 'urban_construction',
  context: '补充背景信息'
})
```

### 3. opinionUtils - 工具函数
提供数据处理和验证工具：

```javascript
import { opinionUtils } from '@/api/modules/opinion'

// 获取分类选项
const categories = opinionUtils.getCategoryOptions()

// 获取状态选项
const statuses = opinionUtils.getStatusOptions()

// 格式化日期时间
const formatted = opinionUtils.formatDateTime(dateString)

// 表单验证
const validation = opinionUtils.validateOpinionForm(formData)
```

## 🎨 Vue组件集成

### 在Vue组件中使用

```vue
<template>
  <div>
    <!-- 意见建议列表 -->
    <el-table :data="opinionsList" v-loading="loading">
      <el-table-column prop="title" label="标题" />
      <el-table-column prop="category" label="分类">
        <template #default="{ row }">
          <el-tag :type="getCategoryColor(row.category)">
            {{ getCategoryLabel(row.category) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <el-tag :type="getStatusColor(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { opinionAPI, opinionUtils } from '@/api/modules/opinion'

const loading = ref(false)
const opinionsList = ref([])

// 获取分类标签和颜色
const getCategoryLabel = (category) => opinionUtils.getCategoryLabel(category)
const getCategoryColor = (category) => opinionUtils.getCategoryColor(category)
const getStatusText = (status) => opinionUtils.getStatusLabel(status)
const getStatusColor = (status) => opinionUtils.getStatusColor(status)

// 加载数据
const loadOpinions = async () => {
  try {
    loading.value = true
    const response = await opinionAPI.getOpinionsList()
    opinionsList.value = response.data.results || []
  } catch (error) {
    console.error('加载失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadOpinions()
})
</script>
```

## 📊 数据结构

### 意见建议对象结构
```javascript
{
  id: 1,                           // 意见建议ID
  title: "意见建议标题",            // 标题
  category: "urban_construction",   // 分类（枚举值）
  reporter_name: "反映人姓名",      // 反映人
  original_content: "原始内容",     // 原始意见内容
  final_suggestion: "最终建议",     // 最终建议内容
  ai_assisted: true,               // 是否使用AI辅助
  ai_generated_content: "AI内容",   // AI生成的内容
  status: "draft",                 // 状态
  created_at: "2024-12-22T10:00:00Z", // 创建时间
  updated_at: "2024-12-22T10:30:00Z", // 更新时间
  representative: {                // 代表信息
    id: 1,
    name: "代表姓名"
  }
}
```

### 分类枚举值
```javascript
const categories = [
  { label: '城建环保', value: 'urban_construction' },
  { label: '交通出行', value: 'transportation' },
  { label: '教育文化', value: 'education' },
  { label: '医疗卫生', value: 'healthcare' },
  { label: '社会保障', value: 'social_security' },
  { label: '经济发展', value: 'economic' },
  { label: '政务服务', value: 'government_service' },
  { label: '其他', value: 'other' }
]
```

### 状态枚举值
```javascript
const statuses = [
  { label: '草稿', value: 'draft', color: 'info' },
  { label: '已提交', value: 'submitted', color: 'primary' },
  { label: '审核通过', value: 'approved', color: 'success' },
  { label: '审核驳回', value: 'rejected', color: 'danger' },
  { label: '已转交', value: 'transferred', color: 'warning' },
  { label: '处理中', value: 'in_progress', color: 'warning' },
  { label: '已办结', value: 'completed', color: 'success' }
]
```

## 🔍 API端点配置

API端点在 `frontend/src/api/http/config.js` 中配置：

```javascript
export const OPINION_ENDPOINTS = {
  LIST: '/opinions/suggestions/',
  DETAIL: '/opinions/suggestions/:id/',
  CREATE: '/opinions/suggestions/create/',
  UPDATE: '/opinions/suggestions/:id/update/',
  DELETE: '/opinions/suggestions/:id/delete/',
  SUBMIT: '/opinions/suggestions/:id/submit/',
  REVIEW: '/opinions/suggestions/:id/review/',
  AI_GENERATE: '/opinions/ai/generate/',
  STATISTICS: '/opinions/statistics/'
}
```

## 🧪 测试和调试

### 1. 使用测试工具
```javascript
import testOpinionAPI from '@/api/modules/opinion/test'

// 运行所有测试
await testOpinionAPI.runAllTests()

// 单独测试某个功能
await testOpinionAPI.testCreateOpinion()
await testOpinionAPI.testAIGeneration()
```

### 2. 浏览器控制台测试
在浏览器控制台中运行：
```javascript
// 测试所有API接口
testOpinionAPI.runAllTests()

// 测试工具函数
testOpinionAPI.testUtils()
```

## 🔐 权限和安全

### 身份验证
所有API请求都会自动携带用户身份验证token：

```javascript
// HTTP客户端会自动添加Authorization头
Authorization: Bearer <access_token>
```

### 权限控制
- 代表只能操作自己创建的意见建议
- 所有敏感操作都有权限验证
- 错误处理包含权限验证失败的情况

### 数据验证
前端提供完整的数据验证：

```javascript
const validation = opinionUtils.validateOpinionForm({
  title: '标题',
  category: 'urban_construction',
  reporter_name: '反映人',
  original_content: '内容'
})

if (!validation.valid) {
  console.log('验证错误:', validation.errors)
}
```

## 🎯 最佳实践

### 1. 错误处理
```javascript
try {
  const response = await opinionAPI.createOpinion(data)
  ElMessage.success('创建成功')
} catch (error) {
  console.error('创建失败:', error)
  ElMessage.error('创建失败：' + (error.response?.data?.message || error.message))
}
```

### 2. Loading状态管理
```javascript
const loading = ref(false)

const handleSubmit = async () => {
  try {
    loading.value = true
    await opinionAPI.createOpinion(formData)
  } finally {
    loading.value = false
  }
}
```

### 3. 数据更新
```javascript
// 操作完成后重新加载列表
const handleDelete = async (id) => {
  await opinionAPI.deleteOpinion(id)
  await loadOpinions() // 重新加载列表
}
```

## 🔄 与后端API对接

### 请求格式
所有请求都使用JSON格式：
```javascript
Content-Type: application/json
```

### 响应格式
后端统一返回格式：
```javascript
{
  "success": true,
  "data": {...},
  "message": "操作成功",
  "code": 200
}
```

### 分页响应
列表接口返回分页数据：
```javascript
{
  "success": true,
  "data": {
    "results": [...],
    "count": 100,
    "next": "http://api/url?page=2",
    "previous": null
  }
}
```

## 📱 移动端适配

组件已针对移动端进行优化：
- 响应式布局设计
- 触摸友好的交互
- 移动端隐藏部分表格列
- 优化的表单输入体验

## 🚀 性能优化

### 1. 请求优化
- 支持请求缓存
- 自动重试机制
- 请求去重处理

### 2. 数据处理
- 分页加载大量数据
- 延迟加载详情信息
- 本地数据缓存

### 3. 用户体验
- Loading状态提示
- 操作成功/失败反馈
- 表单验证实时提示

## 📈 更新日志

- **v1.0.0** (2024-12-22): 完成基础API模块开发
- **功能完成**: 意见建议CRUD、AI辅助、状态管理、权限控制
- **测试完成**: 完整的API测试套件
- **文档完成**: 详细的使用说明和示例代码

## 🤝 贡献指南

如需扩展或修改功能，请遵循以下规范：

1. **代码风格**: 遵循项目ESLint配置
2. **注释规范**: 使用JSDoc格式注释
3. **测试覆盖**: 新增功能需要相应的测试用例
4. **文档更新**: 更新相关文档和示例

## 📞 技术支持

如有问题或建议，请联系开发团队或提交Issue。 