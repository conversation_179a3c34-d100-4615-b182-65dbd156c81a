// 工作分析模块API接口
// 集成真实的后端API，支持站点工作分析和代表工作总结功能

import httpClient from './http/client'

/**
 * 站点工作分析API
 * 提供站点工作总结和代表工作总结相关的接口
 */

/**
 * 生成站点工作分析报告
 * @param {string} year - 分析年度
 * @returns {Promise} 站点分析结果
 */
export const generateSiteAnalysis = async (year) => {
  // TODO: 实现站点工作分析API调用
  // 当前使用模拟数据，后续需要连接真实的站点工作分析接口
  const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))
  await delay(2000) // 模拟AI分析时间

  return {
    success: true,
    message: '站点工作分析生成成功',
    data: {
      year,
      analysisId: `site_analysis_${year}_${Date.now()}`,
      generatedAt: new Date().toISOString(),
      // 分析结果数据与getSiteWorkData保持一致
      totalOpinions: 156,
      processedOpinions: 144,
      pendingOpinions: 12,
      completionRate: 92.3,
      averageProcessTime: 7.2,
      representativeCount: 25,
      satisfactionRate: 95.6,
      monthlyData: [
        { month: '1月', opinions: 12, processed: 11 },
        { month: '2月', opinions: 8, processed: 8 },
        { month: '3月', opinions: 15, processed: 14 },
        { month: '4月', opinions: 18, processed: 17 },
        { month: '5月', opinions: 14, processed: 13 },
        { month: '6月', opinions: 16, processed: 15 },
        { month: '7月', opinions: 13, processed: 12 },
        { month: '8月', opinions: 19, processed: 18 },
        { month: '9月', opinions: 11, processed: 10 },
        { month: '10月', opinions: 15, processed: 14 },
        { month: '11月', opinions: 17, processed: 16 },
        { month: '12月', opinions: 9, processed: 8 }
      ],
      workPlans: {
        total: 45,
        completed: 43,
        inProgress: 2,
        delayed: 0
      },
      highlights: [
        {
          title: '意见建议处理效率显著提升',
          description: '通过优化工作流程和引入AI辅助工具，意见建议平均处理时长较去年缩短2.1天，处理效率提升22%。',
          tags: ['效率提升', '流程优化', 'AI应用']
        },
        {
          title: '代表服务质量持续改善',
          description: '建立了完善的代表服务体系，代表满意度达到95.6%，获得了代表们的一致好评。',
          tags: ['服务质量', '满意度', '持续改善']
        },
        {
          title: '工作计划执行率创新高',
          description: '年度工作计划执行率达到96.8%，各项重点工作均按时保质完成，展现了团队的高效执行力。',
          tags: ['计划执行', '团队协作', '目标达成']
        }
      ],
      issues: [
        {
          level: '中等',
          title: '部分代表履职记录更新不及时',
          description: '发现约20%的代表履职记录存在更新滞后的情况，影响了数据统计的及时性和准确性。',
          impact: '可能影响年度履职分析的准确性，需要加强督促和指导'
        },
        {
          level: '轻微',
          title: '工作计划制定的前瞻性有待加强',
          description: '部分季度和月度工作计划制定时对突发情况的预估不足，导致计划调整频繁。',
          impact: '影响工作的连续性和稳定性，需要提高计划制定的科学性'
        }
      ],
      suggestions: [
        {
          title: '建立履职记录定期提醒机制',
          description: '设置自动提醒功能，每周向履职记录更新不及时的代表发送提醒通知，并提供简化的录入流程。',
          priority: '高',
          expectedEffect: '提高履职记录更新及时率至95%以上'
        },
        {
          title: '完善工作计划制定培训',
          description: '组织专题培训，提高工作计划制定的前瞻性和科学性，建立计划评估和调整机制。',
          priority: '中',
          expectedEffect: '减少计划调整频次，提高工作效率'
        },
        {
          title: '引入更多AI辅助工具',
          description: '在意见建议处理、数据分析等环节引入更多AI工具，进一步提高工作效率和质量。',
          priority: '中',
          expectedEffect: '工作效率再提升15%以上'
        }
      ],
      aiSummary: {
        overall: `${year}年度，本站点在各项工作中表现出色，意见建议处理效率显著提升，代表服务质量持续改善。通过AI技术的应用和工作流程的优化，站点整体工作效能得到了明显提升。`,
        achievements: '在意见建议处理、代表服务、工作计划执行等关键指标上均实现了显著改善，特别是AI辅助工具的应用为工作效率提升做出了重要贡献。',
        outlook: '建议在保持现有优势的基础上，重点关注履职记录管理和工作计划制定的科学性，通过持续优化和创新，力争在新的一年实现更大突破。'
      }
    }
  }
}

/**
 * 获取站点年度工作数据
 * @param {string} year - 分析年度
 * @returns {Promise} 站点工作数据
 */
export const getSiteWorkData = async (year) => {
  // TODO: 实现站点工作总结API调用
  // 当前使用模拟数据，后续需要连接真实的站点工作分析接口
  const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))
  await delay(1000)
  
  return {
    success: true,
    data: {
      year,
      totalOpinions: 156,
      processedOpinions: 144,
      pendingOpinions: 12,
      completionRate: 92.3,
      averageProcessTime: 7.2,
      representativeCount: 25,
      satisfactionRate: 95.6,
      monthlyData: [
        { month: '1月', opinions: 12, processed: 11 },
        { month: '2月', opinions: 8, processed: 8 },
        { month: '3月', opinions: 15, processed: 14 },
        { month: '4月', opinions: 18, processed: 17 },
        { month: '5月', opinions: 14, processed: 13 },
        { month: '6月', opinions: 16, processed: 15 },
        { month: '7月', opinions: 13, processed: 12 },
        { month: '8月', opinions: 19, processed: 18 },
        { month: '9月', opinions: 11, processed: 10 },
        { month: '10月', opinions: 15, processed: 14 },
        { month: '11月', opinions: 17, processed: 16 },
        { month: '12月', opinions: 9, processed: 8 }
      ],
      workPlans: {
        total: 45,
        completed: 43,
        inProgress: 2,
        delayed: 0
      },
      highlights: [
        {
          title: '意见建议处理效率显著提升',
          description: '通过优化工作流程和引入AI辅助工具，意见建议平均处理时长较去年缩短2.1天，处理效率提升22%。',
          tags: ['效率提升', '流程优化', 'AI应用']
        },
        {
          title: '代表服务质量持续改善',
          description: '建立了完善的代表服务体系，代表满意度达到95.6%，获得了代表们的一致好评。',
          tags: ['服务质量', '满意度', '持续改善']
        },
        {
          title: '工作计划执行率创新高',
          description: '年度工作计划执行率达到96.8%，各项重点工作均按时保质完成，展现了团队的高效执行力。',
          tags: ['计划执行', '团队协作', '目标达成']
        }
      ],
      issues: [
        {
          level: '中等',
          title: '部分代表履职记录更新不及时',
          description: '发现约20%的代表履职记录存在更新滞后的情况，影响了数据统计的及时性和准确性。',
          impact: '可能影响年度履职分析的准确性，需要加强督促和指导'
        },
        {
          level: '轻微',
          title: '工作计划制定的前瞻性有待加强',
          description: '部分季度和月度工作计划制定时对突发情况的预估不足，导致计划调整频繁。',
          impact: '影响工作的连续性和稳定性，需要提高计划制定的科学性'
        }
      ],
      suggestions: [
        {
          title: '建立履职记录定期提醒机制',
          description: '设置自动提醒功能，每周向履职记录更新不及时的代表发送提醒通知，并提供简化的录入流程。',
          priority: '高',
          expectedEffect: '提高履职记录更新及时率至95%以上'
        },
        {
          title: '完善工作计划制定培训',
          description: '组织专题培训，提高工作计划制定的前瞻性和科学性，建立计划评估和调整机制。',
          priority: '中',
          expectedEffect: '减少计划调整频次，提高工作效率'
        },
        {
          title: '引入更多AI辅助工具',
          description: '在意见建议处理、数据分析等环节引入更多AI工具，进一步提高工作效率和质量。',
          priority: '中',
          expectedEffect: '工作效率再提升15%以上'
        }
      ],
      aiSummary: {
        overall: `${year}年度，本站点在各项工作中表现出色，意见建议处理效率显著提升，代表服务质量持续改善。通过AI技术的应用和工作流程的优化，站点整体工作效能得到了明显提升。`,
        achievements: '在意见建议处理、代表服务、工作计划执行等关键指标上均实现了显著改善，特别是AI辅助工具的应用为工作效率提升做出了重要贡献。',
        outlook: '建议在保持现有优势的基础上，重点关注履职记录管理和工作计划制定的科学性，通过持续优化和创新，力争在新的一年实现更大突破。'
      }
    }
  }
}

/**
 * 获取站点代表列表（用于工作人员查看代表工作总结）
 * @param {string} year - 年度
 * @returns {Promise} 代表列表数据
 */
export const getRepresentativesList = async (year) => {
  try {
    // 使用专门的代表工作总结API - 不需要/api/v1前缀，因为httpClient.baseURL已包含
    const response = await httpClient.get('/ai-summaries/representatives/', {
      params: { year: year }
    })
    
    return response.data
  } catch (error) {
    console.error('获取代表列表失败：', error)
    return {
      success: false,
      message: error.response?.data?.message || '获取代表列表失败'
    }
  }
}

/**
 * 为代表生成履职分析（工作人员操作）
 * @param {number} representativeId - 代表ID
 * @param {string} year - 分析年度
 * @returns {Promise} 分析结果
 */
export const generateRepresentativeAnalysis = async (representativeId, year) => {
  try {
    const response = await httpClient.post('/ai-summaries/generate/', {
      analysis_year: parseInt(year),
      force_regenerate: true,  // 工作人员端默认支持重新生成
      // 工作人员为指定代表生成
      representative_id: parseInt(representativeId)
    }, {
      timeout: 120000  // 2分钟超时，与代表端一致
    })
    
    return response.data
  } catch (error) {
    console.error('生成代表分析失败：', error)
    console.error('错误详情：', error.response?.data)
    
    const errorData = error.response?.data || {}
    return {
      success: false,
      message: errorData.message || errorData.error || '生成代表分析失败',
      details: {
        error: errorData.error,
        ...errorData.details
      }
    }
  }
}

/**
 * 批量生成代表履职分析
 * @param {Array} representativeIds - 代表ID数组
 * @param {string} year - 分析年度
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise} 批量分析结果
 */
export const batchGenerateAnalysis = async (representativeIds, year, onProgress) => {
  const results = []
  
  try {
    for (let i = 0; i < representativeIds.length; i++) {
      const id = representativeIds[i]
      
      // 通知进度
      if (onProgress) {
        onProgress({
          current: i,
          total: representativeIds.length,
          currentId: id,
          progress: Math.round((i / representativeIds.length) * 100)
        })
      }
      
      // 为每个代表生成分析
      try {
        const result = await generateRepresentativeAnalysis(id, year)
        results.push({
          representativeId: id,
          success: result.success,
          message: result.message || '生成成功',
          generateTime: new Date().toLocaleString()
        })
      } catch (error) {
        results.push({
          representativeId: id,
          success: false,
          message: '生成失败',
          error: error.message
        })
      }
    }
    
    // 最终进度
    if (onProgress) {
      onProgress({
        current: representativeIds.length,
        total: representativeIds.length,
        currentId: null,
        progress: 100
      })
    }
    
    return {
      success: true,
      data: results
    }
  } catch (error) {
    console.error('批量生成分析失败：', error)
    return {
      success: false,
      message: '批量生成分析失败',
      data: results
    }
  }
}

/**
 * 获取代表履职分析结果
 * @param {number} representativeId - 代表ID
 * @param {string} year - 年度
 * @returns {Promise} 分析结果数据
 */
export const getRepresentativeAnalysis = async (representativeId, year) => {
  try {
    const response = await httpClient.get(`/ai-summaries/${year}/`, {
      params: { representative_id: representativeId }
    })
    
    if (response.data.success) {
      // 直接返回后端数据，包含ai_result_data
      return {
        success: true,
        data: response.data.data
      }
    }
    
    return response.data
  } catch (error) {
    console.error('获取代表分析失败：', error)
    return {
      success: false,
      message: error.response?.data?.message || '获取代表分析失败'
    }
  }
}

/**
 * 导出分析报告
 * @param {string} type - 导出类型 ('site' | 'representative')
 * @param {Object} params - 导出参数
 * @returns {Promise} 导出结果
 */
export const exportAnalysisReport = async (type, params) => {
  // TODO: 实现真实的导出功能
  const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))
  await delay(1000)
  
  return {
    success: true,
    message: '报告导出成功',
    data: {
      downloadUrl: `/exports/${type}-analysis-${params.year}.pdf`,
      fileName: `${type === 'site' ? '站点' : '代表'}工作分析报告_${params.year}.pdf`
    }
  }
}

/**
 * 分享分析报告
 * @param {string} type - 分享类型
 * @param {Object} params - 分享参数
 * @returns {Promise} 分享结果
 */
export const shareAnalysisReport = async (type, params) => {
  // TODO: 实现真实的分享功能
  const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))
  await delay(500)
  
  return {
    success: true,
    message: '分享链接生成成功',
    data: {
      shareUrl: `${window.location.origin}/share/${type}-analysis/${params.year}`,
      expireTime: '2024-12-27 23:59:59'
    }
  }
} 