/**
 * 统一错误处理模块
 * 提供各种错误情况的处理方法
 */
import { ElMessage, ElMessageBox } from 'element-plus'
import { API_CONFIG, HTTP_STATUS } from './config'

/**
 * 网络错误处理
 * @param {Error} error - 错误对象
 */
export const handleNetworkError = (error) => {
  if (error.code === 'ECONNABORTED') {
    ElMessage.error(API_CONFIG.ERROR_MESSAGES.TIMEOUT_ERROR)
  } else if (error.message === 'Network Error') {
    ElMessage.error(API_CONFIG.ERROR_MESSAGES.NETWORK_ERROR)
  } else {
    ElMessage.error(error.message || '未知网络错误')
  }
}

/**
 * HTTP状态码错误处理
 * @param {Object} response - 响应对象
 */
export const handleHttpError = (response) => {
  const { status, data } = response
  const message = data?.message || data?.detail || data?.error

  switch (status) {
    case HTTP_STATUS.BAD_REQUEST:
      ElMessage.error(message || '请求参数错误')
      break
    case HTTP_STATUS.UNAUTHORIZED:
      ElMessage.error(API_CONFIG.ERROR_MESSAGES.UNAUTHORIZED)
      break
    case HTTP_STATUS.FORBIDDEN:
      ElMessage.error(API_CONFIG.ERROR_MESSAGES.FORBIDDEN)
      break
    case HTTP_STATUS.NOT_FOUND:
      ElMessage.error(API_CONFIG.ERROR_MESSAGES.NOT_FOUND)
      break
    case HTTP_STATUS.VALIDATION_ERROR:
      handleValidationError(data)
      break
    case HTTP_STATUS.INTERNAL_SERVER_ERROR:
      ElMessage.error(API_CONFIG.ERROR_MESSAGES.SERVER_ERROR)
      break
    case HTTP_STATUS.BAD_GATEWAY:
      ElMessage.error('网关错误，请稍后重试')
      break
    case HTTP_STATUS.SERVICE_UNAVAILABLE:
      ElMessage.error('服务暂时不可用，请稍后重试')
      break
    default:
      ElMessage.error(message || `请求失败 (${status})`)
  }
}

/**
 * 表单验证错误处理
 * @param {Object} data - 错误数据
 */
export const handleValidationError = (data) => {
  if (data?.errors) {
    // 处理字段级别的验证错误
    const messages = Object.entries(data.errors).map(([field, errors]) => {
      return `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`
    })
    ElMessage.error(messages.join('; '))
  } else if (data?.non_field_errors) {
    // 处理非字段级别的验证错误
    ElMessage.error(data.non_field_errors.join('; '))
  } else {
    ElMessage.error(data?.message || data?.detail || API_CONFIG.ERROR_MESSAGES.VALIDATION_ERROR)
  }
}

/**
 * 业务逻辑错误处理
 * @param {string} errorCode - 错误代码
 * @param {string} errorMessage - 错误消息
 */
export const handleBusinessError = (errorCode, errorMessage) => {
  // 根据业务错误代码进行不同处理
  switch (errorCode) {
    case 'INSUFFICIENT_PERMISSION':
      ElMessage.error('权限不足，无法执行此操作')
      break
    case 'RESOURCE_NOT_FOUND':
      ElMessage.error('请求的资源不存在')
      break
    case 'OPERATION_FAILED':
      ElMessage.error('操作失败，请稍后重试')
      break
    default:
      ElMessage.error(errorMessage || '业务处理失败')
  }
}

/**
 * 确认对话框错误处理
 * @param {string} title - 标题
 * @param {string} message - 消息
 * @param {string} type - 类型
 */
export const showConfirmError = (title = '错误', message, type = 'error') => {
  return ElMessageBox.confirm(message, title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type
  })
}

/**
 * 警告消息处理
 * @param {string} message - 警告消息
 */
export const handleWarning = (message) => {
  ElMessage.warning(message)
}

/**
 * 成功消息处理
 * @param {string} message - 成功消息
 */
export const handleSuccess = (message) => {
  ElMessage.success(message)
}

/**
 * 信息消息处理
 * @param {string} message - 信息消息
 */
export const handleInfo = (message) => {
  ElMessage.info(message)
} 