import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/datav'
  },
  {
    path: '/datav',
    name: 'Datav',
    component: () => import('@/components/indexscreen/DataScreenIndex.vue'),
    meta: { 
      title: '数据可视化',
      requiresAuth: false 
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { 
      title: '登录',
      requiresAuth: false 
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { 
      title: '工作台',
      requiresAuth: true 
    }
  },
  {
    path: '/representative',
    name: 'Representative',
    component: () => import('@/views/representative/Index.vue'),
    meta: { 
      title: '人大代表工作台',
      requiresAuth: true,
      roles: ['representative']
    },
    children: [
      {
        path: 'profile',
        name: 'RepresentativeProfile',
        component: () => import('@/views/representative/Profile.vue'),
        meta: { 
          title: '个人信息管理',
          requiresAuth: true,
          roles: ['representative']
        }
      },
      {
        path: 'records',
        name: 'RepresentativeRecords',
        component: () => import('@/views/representative/Records.vue'),
        meta: { 
          title: '履职记录管理',
          requiresAuth: true,
          roles: ['representative']
        }
      },
      {
        path: 'annual-achievements',
        name: 'RepresentativeAnnualAchievements',
        component: () => import('@/views/representative/AnnualAchievements.vue'),
        meta: { 
          title: '年度履职AI分析展示',
          requiresAuth: true,
          roles: ['representative']
        }
      },
      {
        path: 'opinions',
        name: 'RepresentativeOpinions',
        component: () => import('@/views/representative/Opinions.vue'),
        meta: { 
          title: '意见建议',
          requiresAuth: true,
          roles: ['representative']
        }
      },

      {
        path: 'knowledge-qa',
        name: 'RepresentativeKnowledgeQA',
        component: () => import('@/views/KnowledgeQA.vue'),
        meta: { 
          title: '法律政策互动AI问答',
          requiresAuth: true,
          roles: ['representative']
        }
      },
      {
        path: 'password-change',
        name: 'RepresentativePasswordChange',
        component: () => import('@/views/representative/PasswordChange.vue'),
        meta: { 
          title: '账号密码修改',
          requiresAuth: true,
          roles: ['representative']
        }
      }
    ]
  },
  {
    path: '/staff',
    name: 'Staff',
    component: () => import('@/views/staff/Index.vue'),
    meta: { 
      title: '站点工作人员工作台',
      requiresAuth: true,
      roles: ['staff']
    },
    children: [
      {
        path: 'review',
        name: 'StaffReview',
        component: () => import('@/views/staff/Review.vue'),
        meta: { 
          title: '意见建议审核',
          requiresAuth: true,
          roles: ['staff']
        }
      },
      {
        path: 'work-plan',
        name: 'StaffWorkPlan',
        component: () => import('@/views/staff/WorkPlan.vue'),
        meta: { 
          title: '工作计划管理',
          requiresAuth: true,
          roles: ['staff']
        }
      },
      {
        path: 'work-analysis/site-summary',
        name: 'StaffSiteSummary',
        component: () => import('@/views/staff/SiteSummary.vue'),
        meta: { 
          title: '站点工作总结',
          requiresAuth: true,
          roles: ['staff']
        }
      },
      {
        path: 'work-analysis/representative-summary',
        name: 'StaffRepresentativeSummary',
        component: () => import('@/views/staff/RepresentativeSummary.vue'),
        meta: { 
          title: '代表工作总结',
          requiresAuth: true,
          roles: ['staff']
        }
      },
      {
        path: 'knowledge-qa',
        name: 'StaffKnowledgeQA',
        component: () => import('@/views/KnowledgeQA.vue'),
        meta: { 
          title: '法律政策互动AI问答',
          requiresAuth: true,
          roles: ['staff']
        }
      },
      {
        path: 'account-management',
        name: 'StaffAccountManagement',
        component: () => import('@/views/staff/AccountManagement.vue'),
        meta: { 
          title: '账号管理',
          requiresAuth: true,
          roles: ['staff']
        }
      }
    ]
  },
  {
    path: '/notifications',
    name: 'NotificationCenter',
    component: () => import('@/views/NotificationCenter.vue'),
    meta: { 
      title: '通知中心',
      requiresAuth: true 
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 人大代表履职服务与管理平台`
  }
  
  // 不需要认证的页面直接通过
  if (!to.meta.requiresAuth) {
    next()
    return
  }
  
  // 检查用户是否已登录
  if (!userStore.isLoggedIn) {
    next('/login')
    return
  }
  
  // 检查角色权限
  if (to.meta.roles && !to.meta.roles.includes(userStore.userInfo.role)) {
    // 根据用户角色重定向到对应的工作台
    if (userStore.userInfo.role === 'representative') {
      next('/representative')
    } else if (userStore.userInfo.role === 'staff') {
      next('/staff')
    } else {
      next('/dashboard')
    }
    return
  }
  
  next()
})

export default router 