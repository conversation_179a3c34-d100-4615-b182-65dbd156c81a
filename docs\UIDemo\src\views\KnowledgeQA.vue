<template>
  <div class="knowledge-qa-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- AI助手介绍 - 仅在无对话时显示 -->
      <div v-if="conversationHistory.length === 0" class="ai-intro">
        <div class="ai-avatar-large">
          <el-icon><ChatDotRound /></el-icon>
        </div>
        <h1 class="ai-title">您好，我是AI法律政策助手</h1>
        <p class="ai-subtitle">基于专业法律政策知识库，为您提供智能问答服务</p>
      </div>

      <!-- 对话历史 -->
      <div v-if="conversationHistory.length > 0" class="conversation-history" ref="conversationArea">
        <div 
          v-for="(item, index) in conversationHistory" 
          :key="index"
          class="conversation-item"
        >
          <!-- 用户问题 -->
          <div class="message user-message">
            <div class="message-content">{{ item.question }}</div>
          </div>

          <!-- AI回答 -->
          <div class="message ai-message">
            <div class="ai-avatar-small">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="message-content">
              <!-- 加载状态 -->
              <div v-if="item.loading" class="ai-thinking">
                <el-icon class="is-loading"><Loading /></el-icon>
                正在思考...
              </div>
              <!-- AI回答内容 -->
              <div v-else>
                <div class="answer-content" v-html="item.answer"></div>
                <div class="answer-actions">
                  <el-button 
                    text 
                    size="small"
                    @click="copyAnswer(item.answer)"
                    icon="CopyDocument"
                  >
                    复制
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部输入区域 -->
    <div class="input-container">
      <div class="input-wrapper">
        <el-input
          v-model="currentQuestion"
          type="textarea"
          :rows="1"
          :autosize="{ minRows: 1, maxRows: 4 }"
          placeholder="请输入您的法律政策相关问题..."
          @keydown.enter.exact.prevent="submitQuestion"
          :disabled="isAIThinking"
          class="question-input"
          resize="none"
        />
        <el-button 
          type="primary" 
          @click="submitQuestion"
          :loading="isAIThinking"
          :disabled="!currentQuestion.trim()"
          class="send-button"
          circle
          icon="Position"
        />
      </div>
      
      <!-- 快速提问建议 -->
      <div v-if="conversationHistory.length === 0" class="quick-suggestions">
        <el-tag
          v-for="question in quickQuestions"
          :key="question"
          size="small"
          class="suggestion-tag"
          @click="askQuickQuestion(question)"
          :disabled="isAIThinking"
        >
          {{ question }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  ChatDotRound, Loading, CopyDocument, Position
} from '@element-plus/icons-vue'
import { useKnowledgeQAStore } from '@/stores/knowledgeQA'

const knowledgeStore = useKnowledgeQAStore()

// 本地状态
const currentQuestion = ref('')
const isAIThinking = ref(false)
const conversationHistory = ref([])
const conversationArea = ref(null)

// 快速提问选项
const quickQuestions = ref([
  '人大代表的基本职责',
          '意见建议处理流程', 
  '调解案件程序'
])

// 提交问题
const submitQuestion = async () => {
  if (!currentQuestion.value.trim() || isAIThinking.value) {
    return
  }

  const question = currentQuestion.value.trim()
  currentQuestion.value = ''
  
  // 添加用户问题到对话历史
  const conversationItem = {
    id: Date.now(),
    question,
    answer: '',
    loading: true,
    timestamp: new Date()
  }
  
  conversationHistory.value.push(conversationItem)
  isAIThinking.value = true

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  try {
    // 调用AI问答API（模拟）
    const response = await askAI(question)
    
    // 更新对话项
    conversationItem.loading = false
    conversationItem.answer = response.answer
    
    ElMessage.success('AI回答完成')
  } catch (error) {
    conversationItem.loading = false
    conversationItem.answer = '抱歉，AI服务暂时不可用，请稍后重试。'
    ElMessage.error('AI回答失败，请重试')
  } finally {
    isAIThinking.value = false
    await nextTick()
    scrollToBottom()
  }
}

// 快速提问
const askQuickQuestion = (question) => {
  if (isAIThinking.value) return
  currentQuestion.value = question
  submitQuestion()
}

// 模拟AI问答API
const askAI = async (question) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let answer = ''
      
      if (question.includes('人大代表') && (question.includes('职责') || question.includes('权利'))) {
        answer = `
          <p><strong>人大代表的基本职责和权利包括：</strong></p>
          <ol>
            <li><strong>审议权：</strong>审议各项议案、报告的权利</li>
            <li><strong>提案权：</strong>提出议案、质询案、建议、批评和意见的权利</li>
            <li><strong>选举权：</strong>参与选举和决定重大事项</li>
            <li><strong>监督权：</strong>对政府工作进行监督</li>
            <li><strong>询问质询权：</strong>向有关部门询问和质询</li>
          </ol>
          <p><strong>履职要求：</strong></p>
          <ul>
            <li>与原选区选民保持密切联系</li>
            <li>听取和反映群众意见和要求</li>
            <li>模范遵守法律法规</li>
            <li>积极参加代表活动</li>
          </ul>
        `
      } else if (question.includes('意见建议') || question.includes('处理流程')) {
        answer = `
          <p><strong>群众意见建议的处理程序：</strong></p>
          <ol>
            <li><strong>接收登记：</strong>对群众反映的意见建议进行登记编号</li>
            <li><strong>初步审查：</strong>核实意见建议的真实性和合理性</li>
            <li><strong>分类处理：</strong>根据问题性质和职责分工，转交相关部门处理</li>
            <li><strong>跟踪督办：</strong>定期了解处理进展情况</li>
            <li><strong>反馈回复：</strong>将处理结果及时反馈给反映人</li>
            <li><strong>归档备案：</strong>将处理过程和结果归档保存</li>
          </ol>
          <p><strong>处理原则：</strong>属地管理、分级负责、依法及时、就地解决</p>
        `
      } else if (question.includes('调解') || question.includes('程序')) {
        answer = `
          <p><strong>人民调解的基本程序：</strong></p>
          <ol>
            <li><strong>受理纠纷：</strong>了解纠纷情况，决定是否受理</li>
            <li><strong>调查了解：</strong>深入了解纠纷的事实和争议焦点</li>
            <li><strong>调解准备：</strong>确定调解方案和策略</li>
            <li><strong>主持调解：</strong>组织双方进行协商调解</li>
            <li><strong>制作协议书：</strong>达成协议后制作调解协议书</li>
            <li><strong>跟踪回访：</strong>了解协议履行情况</li>
          </ol>
          <p><strong>调解原则：</strong>在法律法规范围内，遵循自愿平等、不违背法律法规和国家政策的原则</p>
        `
      } else {
        answer = `
          <p>感谢您的提问。根据您的问题，我为您提供以下相关信息：</p>
          <p>如需更详细的法律政策解答，建议您：</p>
          <ul>
            <li>明确具体的法律条款或政策文件</li>
            <li>描述具体的应用场景</li>
            <li>说明遇到的具体问题</li>
          </ul>
          <p>这样我可以为您提供更准确和详细的专业解答。</p>
        `
      }
      
      resolve({ answer })
    }, 2000 + Math.random() * 2000)
  })
}

// 滚动到底部
const scrollToBottom = () => {
  if (conversationArea.value) {
    conversationArea.value.scrollTop = conversationArea.value.scrollHeight
  }
}

// 复制回答
const copyAnswer = async (answer) => {
  try {
    const textContent = answer.replace(/<[^>]*>/g, '').replace(/\n\s*/g, '\n')
    await navigator.clipboard.writeText(textContent)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 初始化
onMounted(() => {
  knowledgeStore.fetchStats()
})
</script>

<style scoped>
.knowledge-qa-container {
  height: calc(100vh - 60px);
  background: #ffffff;
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.main-content {
  flex: 1;
  padding: 40px 40px 20px;
  overflow-y: auto;
  min-height: 0;
}

/* AI介绍样式 */
.ai-intro {
  text-align: center;
  padding: 80px 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.ai-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--china-red), #e04545);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30px;
  color: white;
  font-size: 32px;
}

.ai-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.ai-subtitle {
  font-size: 16px;
  color: #666666;
  line-height: 1.6;
  margin: 0;
}

/* 对话历史样式 */
.conversation-history {
  max-height: none;
  overflow-y: visible;
  padding-bottom: 20px;
}

.conversation-item {
  margin-bottom: 32px;
}

.message {
  margin-bottom: 16px;
}

.user-message {
  display: flex;
  justify-content: flex-end;
}

.user-message .message-content {
  max-width: 70%;
  background: var(--china-red);
  color: white;
  padding: 12px 18px;
  border-radius: 18px;
  border-bottom-right-radius: 4px;
  font-size: 15px;
  line-height: 1.5;
}

.ai-message {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  width: 100%;
}

.ai-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--china-red), #e04545);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.ai-message .message-content {
  flex: 1;
  max-width: none;
  background: #f5f5f5;
  padding: 12px 18px;
  border-radius: 18px;
  border-bottom-left-radius: 4px;
  font-size: 15px;
  line-height: 1.6;
  color: #303133;
}

.ai-thinking {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666666;
}

.answer-content :deep(p) {
  margin: 0 0 8px 0;
}

.answer-content :deep(ol),
.answer-content :deep(ul) {
  margin: 8px 0;
  padding-left: 20px;
}

.answer-content :deep(li) {
  margin: 4px 0;
}

.answer-content :deep(strong) {
  color: var(--china-red);
  font-weight: 600;
}

.answer-actions {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #eeeeee;
}

/* 底部输入区域 */
.input-container {
  flex-shrink: 0;
  background: #ffffff;
  padding: 20px 40px 30px;
  border-top: 1px solid #e0e0e0;
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  background: #f8f9fa;
  border-radius: 24px;
  padding: 8px 8px 8px 20px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s;
  width: 100%;
}

.input-wrapper:focus-within {
  border-color: var(--china-red);
  box-shadow: 0 0 0 3px rgba(198, 45, 45, 0.1);
}

.question-input {
  flex: 1;
  border: none;
  background: transparent;
}

.question-input :deep(.el-textarea__inner) {
  border: none;
  background: transparent;
  box-shadow: none;
  padding: 8px 0;
  font-size: 15px;
  line-height: 1.5;
  resize: none;
}

.question-input :deep(.el-textarea__inner):focus {
  box-shadow: none;
}

.send-button {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.quick-suggestions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;
}

.suggestion-tag {
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #e0e0e0;
  background: #ffffff;
  color: #666666;
}

.suggestion-tag:hover {
  border-color: var(--china-red);
  background: var(--china-red);
  color: white;
  transform: translateY(-1px);
}

/* 自定义滚动条 */
.conversation-history::-webkit-scrollbar,
.main-content::-webkit-scrollbar {
  width: 4px;
}

.conversation-history::-webkit-scrollbar-track,
.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.conversation-history::-webkit-scrollbar-thumb,
.main-content::-webkit-scrollbar-thumb {
  background: #dcdfe6;
  border-radius: 2px;
}

.conversation-history::-webkit-scrollbar-thumb:hover,
.main-content::-webkit-scrollbar-thumb:hover {
  background: #c0c4cc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-qa-container {
    height: calc(100vh - 60px);
  }
  
  .main-content {
    padding: 20px 20px 20px;
  }
  
  .ai-intro {
    padding: 40px 16px;
  }
  
  .ai-title {
    font-size: 24px;
  }
  
  .ai-subtitle {
    font-size: 14px;
  }
  
  .input-container {
    padding: 16px 20px 20px;
  }
  
  .user-message .message-content {
    max-width: 80%;
    font-size: 14px;
  }
  
  .ai-message .message-content {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 16px 16px 20px;
  }
  
  .ai-intro {
    padding: 20px 12px;
  }
  
  .ai-avatar-large {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
  
  .ai-title {
    font-size: 20px;
  }
  
  .input-container {
    padding: 12px 16px 16px;
  }
  
  .input-wrapper {
    padding: 6px 6px 6px 16px;
  }
  
  .send-button {
    width: 36px;
    height: 36px;
  }
  
  .user-message .message-content {
    max-width: 85%;
  }
}
</style> 