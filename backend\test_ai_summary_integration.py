#!/usr/bin/env python
"""
AI总结功能集成测试脚本

测试代表履职年度分析功能的前后端对接
包括：
1. 数据收集和验证
2. AI总结生成（模拟）
3. API接口调用
4. 调试信息输出

运行方式：
cd backend
uv run python test_ai_summary_integration.py
"""

import os
import sys
import django
import json
from datetime import datetime

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'npcsite.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from api.users.models import Representative
from api.performance.models import PerformanceRecord
from api.opinion.models import OpinionSuggestion
from api.aisummary.models import RepresentativeAISummary
from api.aisummary.services import DataCollectionService, AISummaryService

User = get_user_model()

def get_existing_data():
    """获取现有测试账号数据"""
    print("📝 使用现有账号数据...")
    
    # 查找现有用户和代表
    try:
        user = User.objects.get(username='rep001')
        print(f"✅ 找到用户: {user.username} (角色: {user.role})")
        
        representative = Representative.objects.get(user=user)
        print(f"✅ 找到代表: {representative.name}")
        
        # 查看现有履职记录
        current_year = datetime.now().year
        performance_records = PerformanceRecord.objects.filter(
            representative=representative,
            performance_date__year=current_year
        )
        
        print(f"📊 {current_year}年履职记录统计:")
        print(f"   总计: {performance_records.count()} 条")
        
        # 按类型统计
        for perf_type in performance_records.values_list('performance_type', flat=True).distinct():
            count = performance_records.filter(performance_type=perf_type).count()
            print(f"   {perf_type}: {count} 条")
        
        # 查看现有意见建议
        opinions = OpinionSuggestion.objects.filter(
            representative=representative,
            created_at__year=current_year
        )
        
        print(f"💡 {current_year}年意见建议统计:")
        print(f"   总计: {opinions.count()} 条")
        
        # 按类别统计
        for category in opinions.values_list('category', flat=True).distinct():
            count = opinions.filter(category=category).count()
            print(f"   {category}: {count} 条")
        
        # 按状态统计
        for status in opinions.values_list('status', flat=True).distinct():
            count = opinions.filter(status=status).count()
            print(f"   状态-{status}: {count} 条")
        
        print(f"\n✅ 现有数据统计完成:")
        print(f"   代表: {representative.name}")
        print(f"   履职记录: {performance_records.count()} 条")
        print(f"   意见建议: {opinions.count()} 条")
        
        return representative
        
    except User.DoesNotExist:
        print("❌ 找不到用户 rep001")
        print("   请确认账号已存在")
        return None
    except Representative.DoesNotExist:
        print("❌ 找不到代表信息")
        print("   请确认代表资料已创建")
        return None

def test_data_collection(representative):
    """测试数据收集功能"""
    print("\n📊 测试数据收集功能...")
    
    current_year = datetime.now().year
    
    try:
        source_data = DataCollectionService.collect_representative_data(
            representative, current_year
        )
        
        print("✅ 数据收集成功")
        print(f"   收集的数据结构: {list(source_data.keys())}")
        print(f"   履职记录数量: {source_data.get('performance_records', {}).get('total_count', 0)}")
        print(f"   意见建议数量: {source_data.get('opinion_suggestions', {}).get('total_count', 0)}")
        
        return source_data
        
    except Exception as e:
        print(f"❌ 数据收集失败: {e}")
        return None

def test_ai_summary_generation(representative):
    """测试AI总结生成功能"""
    print("\n🤖 测试AI总结生成功能...")
    
    current_year = datetime.now().year
    
    try:
        # 删除已存在的总结
        RepresentativeAISummary.objects.filter(
            representative=representative,
            analysis_year=current_year
        ).delete()
        
        # 生成新的AI总结
        ai_summary = AISummaryService.generate_summary(
            representative=representative,
            analysis_year=current_year,
            force_regenerate=False
        )
        
        print("✅ AI总结生成成功")
        print(f"   总结ID: {ai_summary.id}")
        print(f"   状态: {ai_summary.status}")
        print(f"   是否完成: {ai_summary.is_completed}")
        print(f"   错误信息: {ai_summary.error_message or '无'}")
        
        if ai_summary.is_completed and ai_summary.ai_result:
            result_keys = list(ai_summary.ai_result.keys())
            print(f"   AI结果结构: {result_keys}")
            
            if 'overview' in ai_summary.ai_result:
                overview = ai_summary.ai_result['overview']
                print(f"   概览副标题: {overview.get('subtitle', '无')}")
            
            if 'coreMetrics' in ai_summary.ai_result:
                metrics_count = len(ai_summary.ai_result['coreMetrics'])
                print(f"   核心指标数量: {metrics_count}")
        
        return ai_summary
        
    except Exception as e:
        print(f"❌ AI总结生成失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_api_endpoints(representative):
    """测试API端点"""
    print("\n🔌 测试API端点...")
    
    from django.test import Client
    from django.urls import reverse
    import json
    
    client = Client()
    
    # 登录
    client.login(username='rep001', password='test123456')
    
    current_year = datetime.now().year
    
    try:
        # 测试数据检查API
        print("🔍 测试数据检查API...")
        response = client.get(f'/api/v1/ai-summaries/{current_year}/check/')
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {data}")
        
        # 测试AI总结生成API
        print("🚀 测试AI总结生成API...")
        response = client.post('/api/v1/ai-summaries/generate/', {
            'analysis_year': current_year,
            'force_regenerate': True
        }, content_type='application/json')
        print(f"   状态码: {response.status_code}")
        if response.status_code in [200, 201, 202]:
            data = response.json()
            print(f"   响应数据: {data}")
        
        # 测试AI总结详情API
        print("📄 测试AI总结详情API...")
        response = client.get(f'/api/v1/ai-summaries/{current_year}/')
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据键: {list(data.keys())}")
            if 'ai_result_data' in data and data['ai_result_data']:
                ai_data = data['ai_result_data']
                print(f"   AI结果结构: {list(ai_data.keys())}")
        
        print("✅ API端点测试完成")
        
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🧪 代表履职AI分析功能集成测试")
    print("=" * 60)
    
    # 1. 获取现有测试数据
    representative = get_existing_data()
    
    if not representative:
        print("❌ 无法获取测试代表数据，测试中断")
        return
    
    # 2. 测试数据收集
    source_data = test_data_collection(representative)
    
    # 3. 测试AI总结生成
    ai_summary = test_ai_summary_generation(representative)
    
    # 4. 测试API端点
    test_api_endpoints(representative)
    
    print("\n" + "=" * 60)
    print("🎉 集成测试完成!")
    
    if ai_summary and ai_summary.is_completed:
        print("✅ 所有功能测试通过，AI总结功能已就绪")
        print("\n📋 测试结果摘要:")
        print(f"   代表: {representative.name}")
        print(f"   AI总结状态: {ai_summary.status}")
        print(f"   生成耗时: {ai_summary.generation_duration}秒" if ai_summary.generation_duration else "   生成耗时: 未记录")
        
        if ai_summary.ai_result:
            result = ai_summary.ai_result
            print(f"   数据完整性: {len(result.keys())} 个主要部分")
            if 'coreMetrics' in result:
                print(f"   核心指标: {len(result['coreMetrics'])} 个")
            if 'highlights' in result:
                print(f"   履职亮点: {len(result['highlights'])} 个")
                
        print("\n🚀 前端可以开始对接测试!")
        print("   前端API地址: http://localhost:8000/api/v1/ai-summaries/")
        print("   测试代表: rep001 / test123456")
        
    else:
        print("⚠️  部分功能存在问题，请检查日志")
    
if __name__ == '__main__':
    main() 