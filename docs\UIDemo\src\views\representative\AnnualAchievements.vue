<template>
  <div class="annual-achievements">
    <!-- 年度选择 -->
    <el-card class="year-selector" shadow="hover">
      <div class="selector-header">
        <h2>年度履职AI分析展示</h2>
        <p>AI智能分析您的年度履职记录，生成图文并茂的精美报告</p>
      </div>
      <div class="year-controls">
        <el-select 
          v-model="selectedYear" 
          placeholder="请选择年度" 
          size="large"
          @change="onYearChange"
        >
          <el-option
            v-for="year in availableYears"
            :key="year"
            :label="year + '年'"
            :value="year"
          />
        </el-select>
        <el-button 
          type="primary" 
          :loading="loadingAchievements"
          @click="generateAchievements"
          size="large"
          :disabled="!selectedYear || !hasAnalysisData"
        >
          <el-icon><TrendCharts /></el-icon>
          生成AI分析展示
        </el-button>
      </div>
      <div v-if="selectedYear && !hasAnalysisData" class="no-analysis-tip">
        <el-alert 
          title="暂无AI分析数据" 
          description="请先完成该年度的AI履职分析，然后再生成成果展示"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <div>
              <p>暂无AI分析数据</p>
              <p>请先完成该年度的履职记录，系统将自动进行AI分析并生成展示</p>
              <el-button 
                type="primary" 
                size="small" 
                @click="goToRecords"
                style="margin-top: 10px;"
              >
                录入履职记录
              </el-button>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- 成果展示区域 -->
    <div v-if="achievements" class="achievements-display">
      <!-- 个性化头部 -->
      <el-card class="achievement-header" shadow="never">
        <div class="header-content">
          <div class="user-avatar">
            <el-avatar :size="80" style="background-color: #c62d2d; font-size: 32px;">
              {{ userStore.userName?.[0] || '代' }}
            </el-avatar>
          </div>
          <div class="header-info">
            <h1>{{ userStore.userName }}代表</h1>
            <h2>{{ selectedYear }}年度履职成果</h2>
            <p class="subtitle">{{ achievements.overview.subtitle }}</p>
          </div>
          <div class="header-decoration">
            <div class="year-badge">{{ selectedYear }}</div>
          </div>
        </div>
      </el-card>

      <!-- 核心数据指标卡片 -->
      <div class="metrics-section">
        <h3 class="section-title">核心成就指标</h3>
        <div class="metrics-grid">
          <el-card 
            v-for="metric in achievements.coreMetrics" 
            :key="metric.label"
            class="metric-card"
            shadow="hover"
          >
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon :size="24" :color="metric.color">
                  <component :is="metric.icon" />
                </el-icon>
              </div>
              <div class="metric-data">
                <div class="metric-value">{{ metric.value }}</div>
                <div class="metric-label">{{ metric.label }}</div>
                <div v-if="metric.trend" class="metric-trend" :class="metric.trendType">
                  {{ metric.trend }}
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 履职活动数据可视化 -->
      <div class="visualization-section">
        <h3 class="section-title">履职活动统计</h3>
        <div class="charts-container">
          <!-- 活动类型分布图 -->
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <div class="chart-header">
                <h4>活动类型分布</h4>
                <span class="chart-subtitle">各类履职活动占比</span>
              </div>
            </template>
            <div class="activity-chart">
              <div class="chart-bars">
                <div 
                  v-for="item in achievements.activityDistribution" 
                  :key="item.type"
                  class="bar-item"
                >
                  <div class="bar-label">{{ item.type }}</div>
                  <div class="bar-container">
                    <div 
                      class="bar-fill" 
                      :style="{ width: item.percentage + '%', backgroundColor: item.color }"
                    ></div>
                    <span class="bar-value">{{ item.count }}次</span>
                  </div>
                  <div class="bar-percentage">{{ item.percentage }}%</div>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 履职时间投入 -->
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <div class="chart-header">
                <h4>时间投入分析</h4>
                <span class="chart-subtitle">月度履职时间分布</span>
              </div>
            </template>
            <div class="time-chart">
              <div class="time-stats">
                <div class="time-item">
                  <span class="time-label">总投入时间</span>
                  <span class="time-value">{{ achievements.timeInvestment.total }}天</span>
                </div>
                <div class="time-item">
                  <span class="time-label">月均时间</span>
                  <span class="time-value">{{ achievements.timeInvestment.monthly }}天</span>
                </div>
                <div class="time-item">
                  <span class="time-label">活跃度</span>
                  <span class="time-value">{{ achievements.timeInvestment.activity }}%</span>
                </div>
              </div>
              <div class="month-bars">
                <div 
                  v-for="month in achievements.timeInvestment.monthlyData" 
                  :key="month.month"
                  class="month-bar"
                >
                  <div 
                    class="month-fill" 
                    :style="{ height: month.percentage + '%' }"
                  ></div>
                  <span class="month-label">{{ month.month }}月</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 履职亮点/典型事例 -->
      <div class="highlights-section">
        <h3 class="section-title">履职亮点</h3>
        <div class="highlights-grid">
          <el-card 
            v-for="(highlight, index) in achievements.highlights" 
            :key="index"
            class="highlight-card"
            shadow="hover"
          >
            <div class="highlight-content">
              <div class="highlight-icon">
                <el-icon :size="20" color="#c62d2d">
                  <Star />
                </el-icon>
              </div>
              <div class="highlight-text">
                <h4>{{ highlight.title }}</h4>
                <p>{{ highlight.description }}</p>
                <div class="highlight-metrics">
                  <span 
                    v-for="metric in highlight.metrics" 
                    :key="metric.label"
                    class="highlight-metric"
                  >
                    {{ metric.label }}: <strong>{{ metric.value }}</strong>
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 关键词云展示 -->
      <div class="keywords-section">
        <h3 class="section-title">关注领域</h3>
        <el-card class="keywords-card" shadow="hover">
          <div class="keywords-cloud">
            <span 
              v-for="keyword in achievements.keywords" 
              :key="keyword.word"
              class="keyword-tag"
              :class="'weight-' + Math.ceil(keyword.weight * 5)"
            >
              {{ keyword.word }}
            </span>
          </div>
        </el-card>
      </div>

      <!-- AI总结与展望 -->
      <div class="summary-section">
        <h3 class="section-title">AI智能总结</h3>
        <el-card class="summary-card" shadow="hover">
          <div class="summary-content">
            <div class="summary-text">
              <h4>年度表现评价</h4>
              <p>{{ achievements.aiSummary.evaluation }}</p>
              
              <h4>突出成就</h4>
              <ul class="achievement-list">
                <li v-for="achievement in achievements.aiSummary.achievements" :key="achievement">
                  {{ achievement }}
                </li>
              </ul>
              
              <h4>改进建议</h4>
              <ul class="suggestion-list">
                <li v-for="suggestion in achievements.aiSummary.suggestions" :key="suggestion">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
            <div class="summary-decoration">
              <el-icon :size="60" color="#c62d2d" style="opacity: 0.1;">
                <TrendCharts />
              </el-icon>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 操作按钮 -->
      <div class="actions-section">
        <el-card class="actions-card" shadow="never">
          <div class="action-buttons">
            <el-button 
              type="primary" 
              size="large"
              @click="regenerateAchievements"
              :loading="loadingAchievements"
            >
              <el-icon><Refresh /></el-icon>
              重新生成
            </el-button>
            <el-button 
              size="large"
              @click="exportAchievements"
            >
              <el-icon><Download /></el-icon>
              导出PDF
            </el-button>
            <el-button 
              size="large"
              @click="shareAchievements"
            >
              <el-icon><Share /></el-icon>
              分享报告
            </el-button>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 生成中的加载状态 -->
    <div v-if="loadingAchievements && !achievements" class="loading-container">
      <el-card class="loading-card" shadow="hover">
        <div class="loading-content">
          <el-icon class="loading-icon" :size="48" color="#c62d2d">
            <Loading />
          </el-icon>
          <h3>AI正在生成您的成果展示</h3>
          <p>{{ loadingText }}</p>
          <el-progress 
            :percentage="loadingProgress" 
            :show-text="false"
            style="margin-top: 20px;"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  TrendCharts, 
  Star, 
  Refresh, 
  Download, 
  Share, 
  Loading,
  Trophy,
  DataLine,
  User,
  Timer
} from '@element-plus/icons-vue'
import achievementAPI from '@/api/achievement'

// 状态管理
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const selectedYear = ref('')
const loadingAchievements = ref(false)
const loadingProgress = ref(0)
const loadingText = ref('正在分析数据...')
const achievements = ref(null)
const hasAnalysisData = ref(true)

// 可选年份
const availableYears = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = 0; i < 6; i++) {
    years.push(currentYear - i)
  }
  return years
})

// 监听年度变化
watch(selectedYear, async (newYear) => {
  if (newYear) {
    await checkAnalysisData(newYear)
  }
})

// 检查分析数据是否存在
const checkAnalysisData = async (year) => {
  try {
    const hasData = await achievementAPI.checkAnalysisData({ year })
    hasAnalysisData.value = hasData
  } catch (error) {
    console.error('检查分析数据失败:', error)
    hasAnalysisData.value = false
  }
}

// 年度变化处理
const onYearChange = (year) => {
  achievements.value = null
}

// 生成成果展示
const generateAchievements = async () => {
  if (!selectedYear.value) {
    ElMessage.warning('请选择年度')
    return
  }

  if (!hasAnalysisData.value) {
    ElMessage.warning('请先完成该年度的AI分析')
    return
  }

  loadingAchievements.value = true
  loadingProgress.value = 0
  
  // 模拟生成进度
  const progressSteps = [
    { progress: 20, text: '正在分析数据...' },
    { progress: 40, text: '生成可视化图表...' },
    { progress: 60, text: '提取履职亮点...' },
    { progress: 80, text: 'AI智能总结...' },
    { progress: 100, text: '生成完成！' }
  ]

  for (const step of progressSteps) {
    await new Promise(resolve => setTimeout(resolve, 800))
    loadingProgress.value = step.progress
    loadingText.value = step.text
  }

  try {
    const result = await achievementAPI.generateAchievements({
      year: selectedYear.value,
      userId: userStore.userInfo?.id || userStore.userName
    })
    
    achievements.value = result
    ElMessage.success('成果展示生成完成！')
  } catch (error) {
    console.error('生成成果展示失败:', error)
    ElMessage.error(error.message || '生成失败，请稍后重试')
  } finally {
    loadingAchievements.value = false
    loadingProgress.value = 0
  }
}

// 重新生成
const regenerateAchievements = async () => {
  const confirm = await ElMessageBox.confirm(
    '重新生成将覆盖当前内容，确定继续吗？',
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).catch(() => false)

  if (confirm) {
    achievements.value = null
    await generateAchievements()
  }
}

// 导出PDF
const exportAchievements = () => {
  ElMessage.info('导出功能开发中...')
}

// 分享报告
const shareAchievements = () => {
  ElMessage.info('分享功能开发中...')
}

// 前往履职记录
const goToRecords = () => {
  router.push('/representative/records')
}

// 初始化
onMounted(() => {
  const currentYear = new Date().getFullYear()
  selectedYear.value = currentYear
})
</script>

<style scoped>
.annual-achievements {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 年度选择器 */
.year-selector {
  margin-bottom: 30px;
}

.selector-header {
  text-align: center;
  margin-bottom: 30px;
}

.selector-header h2 {
  color: #c62d2d;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: bold;
}

.selector-header p {
  color: #666;
  font-size: 16px;
}

.year-controls {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.no-analysis-tip {
  margin-top: 20px;
}

/* 成果展示头部 */
.achievement-header {
  margin-bottom: 30px;
  background: linear-gradient(135deg, #c62d2d 0%, #8b1e1e 100%);
  color: white;
  border: none;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 30px;
  position: relative;
  min-height: 120px;
}

.user-avatar {
  flex-shrink: 0;
}

.header-info h1 {
  font-size: 32px;
  margin: 0 0 8px 0;
  font-weight: bold;
}

.header-info h2 {
  font-size: 24px;
  margin: 0 0 8px 0;
  opacity: 0.9;
}

.subtitle {
  font-size: 16px;
  opacity: 0.8;
  margin: 0;
}

.header-decoration {
  margin-left: auto;
  align-self: flex-start;
}

.year-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 18px;
  font-weight: bold;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

/* 核心指标 */
.metrics-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 24px;
  color: #c62d2d;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #c62d2d;
  font-weight: bold;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.metric-card {
  border: 2px solid #f5f5f5;
  transition: all 0.3s ease;
}

.metric-card:hover {
  border-color: #c62d2d;
  transform: translateY(-5px);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(198, 45, 45, 0.2);
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #c62d2d;
  line-height: 1;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.metric-trend {
  font-size: 12px;
  margin-top: 5px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: bold;
}

.metric-trend.positive {
  background: #e6f7e6;
  color: #52c41a;
}

.metric-trend.negative {
  background: #ffe6e6;
  color: #ff4d4f;
}

/* 可视化图表 */
.visualization-section {
  margin-bottom: 40px;
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.chart-card {
  min-height: 400px;
}

.chart-header {
  text-align: center;
}

.chart-header h4 {
  color: #c62d2d;
  font-size: 18px;
  margin: 0 0 5px 0;
  font-weight: bold;
}

.chart-subtitle {
  color: #999;
  font-size: 14px;
}

/* 活动分布图表 */
.activity-chart {
  padding: 20px 0;
}

.bar-item {
  display: grid;
  grid-template-columns: 80px 1fr 60px;
  gap: 15px;
  align-items: center;
  margin-bottom: 15px;
}

.bar-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.bar-container {
  position: relative;
  height: 30px;
  background: #f5f5f5;
  border-radius: 15px;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.bar-fill {
  height: 100%;
  border-radius: 15px;
  transition: width 0.8s ease;
  background: linear-gradient(90deg, #c62d2d, #e85555);
}

.bar-value {
  position: absolute;
  right: 10px;
  font-size: 12px;
  color: white;
  font-weight: bold;
}

.bar-percentage {
  font-size: 14px;
  color: #c62d2d;
  font-weight: bold;
  text-align: center;
}

/* 时间投入图表 */
.time-chart {
  padding: 20px;
}

.time-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.time-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
}

.time-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.time-value {
  display: block;
  font-size: 24px;
  color: #c62d2d;
  font-weight: bold;
}

.month-bars {
  display: flex;
  justify-content: space-between;
  align-items: end;
  height: 150px;
  gap: 8px;
}

.month-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.month-fill {
  width: 100%;
  background: linear-gradient(to top, #c62d2d, #e85555);
  border-radius: 4px 4px 0 0;
  transition: height 0.8s ease;
  margin-bottom: 8px;
}

.month-label {
  font-size: 12px;
  color: #666;
}

/* 履职亮点 */
.highlights-section {
  margin-bottom: 40px;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.highlight-card {
  border-left: 4px solid #c62d2d;
  transition: transform 0.3s ease;
}

.highlight-card:hover {
  transform: translateY(-3px);
}

.highlight-content {
  display: flex;
  gap: 15px;
}

.highlight-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #fff2f2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.highlight-text h4 {
  color: #c62d2d;
  font-size: 16px;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.highlight-text p {
  color: #666;
  line-height: 1.6;
  margin: 0 0 15px 0;
}

.highlight-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.highlight-metric {
  font-size: 12px;
  color: #999;
}

.highlight-metric strong {
  color: #c62d2d;
}

/* 关键词云 */
.keywords-section {
  margin-bottom: 40px;
}

.keywords-card {
  min-height: 200px;
}

.keywords-cloud {
  padding: 30px;
  text-align: center;
  line-height: 2;
}

.keyword-tag {
  display: inline-block;
  margin: 5px 10px;
  padding: 8px 16px;
  background: #f5f5f5;
  color: #333;
  border-radius: 20px;
  transition: all 0.3s ease;
  cursor: default;
}

.keyword-tag:hover {
  background: #c62d2d;
  color: white;
  transform: scale(1.1);
}

.keyword-tag.weight-5 {
  font-size: 24px;
  font-weight: bold;
  background: #c62d2d;
  color: white;
}

.keyword-tag.weight-4 {
  font-size: 20px;
  font-weight: bold;
  background: #e85555;
  color: white;
}

.keyword-tag.weight-3 {
  font-size: 16px;
  font-weight: 500;
  background: #ffebee;
  color: #c62d2d;
}

.keyword-tag.weight-2 {
  font-size: 14px;
  background: #f5f5f5;
  color: #666;
}

.keyword-tag.weight-1 {
  font-size: 12px;
  background: #fafafa;
  color: #999;
}

/* AI总结 */
.summary-section {
  margin-bottom: 40px;
}

.summary-card {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
}

.summary-content {
  display: flex;
  gap: 30px;
  position: relative;
}

.summary-text {
  flex: 1;
}

.summary-text h4 {
  color: #c62d2d;
  font-size: 18px;
  margin: 0 0 15px 0;
  font-weight: bold;
}

.summary-text p {
  color: #333;
  line-height: 1.8;
  margin-bottom: 25px;
  font-size: 15px;
}

.achievement-list,
.suggestion-list {
  padding-left: 20px;
  margin-bottom: 25px;
}

.achievement-list li,
.suggestion-list li {
  color: #555;
  line-height: 1.8;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-decoration {
  position: absolute;
  right: 20px;
  top: 20px;
}

/* 操作按钮 */
.actions-section {
  margin-bottom: 20px;
}

.actions-card {
  background: transparent;
  border: none;
  box-shadow: none;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  padding: 12px 30px;
  font-size: 16px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-card {
  width: 400px;
  text-align: center;
}

.loading-content {
  padding: 40px 20px;
}

.loading-icon {
  animation: spin 2s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-content h3 {
  color: #c62d2d;
  margin-bottom: 10px;
  font-size: 20px;
}

.loading-content p {
  color: #666;
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .annual-achievements {
    padding: 15px;
  }
  
  .charts-container {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .highlights-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .year-controls {
    flex-direction: column;
  }
}
</style> 