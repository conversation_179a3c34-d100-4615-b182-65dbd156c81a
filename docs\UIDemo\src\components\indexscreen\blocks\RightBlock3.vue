<template>
  <div class="right-block-3">
    <!-- 标题区域 -->
    <div class="knowledge-header">
      <div class="header-left">
        <!-- <div class="knowledge-icon">🤖</div> -->
        <div class="knowledge-title">{{ knowledgeTitle }}</div>
      </div>
      <div class="knowledge-subtitle" @click="handleAIClick">{{ aiSubtitle }}</div>
    </div>
    
    <!-- 法律条文列表容器 -->
    <div class="knowledge-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">数据加载中...</div>
      </div>
      
      <!-- 法律条文列表 - 轮播容器 -->
      <div v-else class="legal-carousel">
        <div 
          class="legal-list"
          :style="{ transform: `translateY(-${currentIndex * itemHeight}px)` }"
        >
          <div 
            v-for="(item, index) in legalList" 
            :key="item.id"
            class="legal-item"
            :ref="el => setItemRef(el, index)"
            @click="handleLegalClick(item)"
          >
            <!-- 分类标识 -->
            <div class="category-indicator" :class="getCategoryClass(item.category)"></div>
            
            <!-- 法律条文内容 -->
            <div class="legal-content">
              <div class="legal-name">{{ item.name }}</div>
              <div class="legal-category">{{ item.category }}</div>
            </div>
            
            <!-- 更新时间 -->
            <div class="legal-meta">
              <div class="update-time">{{ formatTime(item.updateTime) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { getLegalKnowledgeList, getAIQuestionLink } from '@/api/indexscreen/legalKnowledge.js'

// 响应式数据
const knowledgeTitle = ref('AI知识库')
const aiSubtitle = ref('AI问答入口')
const loading = ref(false)
const legalList = ref([])
const totalCount = ref(0)
const currentIndex = ref(0)
const itemHeight = ref(50) // 每个项目的高度
const carouselTimer = ref(null)
const itemRefs = ref([])
const containerHeight = ref(0) // 容器高度
const visibleItemCount = ref(0) // 可见项目数量

// 设置项目引用
const setItemRef = (el, index) => {
  if (el) {
    itemRefs.value[index] = el
  }
}

// 计算可见项目数量
const calculateVisibleItems = () => {
  const container = document.querySelector('.right-block-3 .legal-carousel')
  if (container && itemRefs.value[0]) {
    containerHeight.value = container.offsetHeight
    // 获取实际的项目高度（包括margin和gap）
    const firstItem = itemRefs.value[0]
    const itemRect = firstItem.getBoundingClientRect()
    const computedStyle = window.getComputedStyle(firstItem)
    const marginTop = parseFloat(computedStyle.marginTop) || 0
    const marginBottom = parseFloat(computedStyle.marginBottom) || 0
    
    // 实际项目高度 = 元素高度 + margin + gap（6px）
    itemHeight.value = itemRect.height + marginTop + marginBottom + 6
    visibleItemCount.value = Math.floor(containerHeight.value / itemHeight.value)
    
    console.log('🤖 容器高度:', containerHeight.value)
    console.log('🤖 项目实际高度:', itemRect.height)
    console.log('🤖 项目总高度(含gap):', itemHeight.value)
    console.log('🤖 可见项目数:', visibleItemCount.value)
  }
}

// 获取法律条文列表数据
const fetchLegalList = async () => {
  try {
    loading.value = true
    const response = await getLegalKnowledgeList({ 
      page: 1, 
      pageSize: 12 // 获取12条用于轮播
    })
    
    if (response.code === 200) {
      legalList.value = response.data.list
      totalCount.value = response.data.pagination.total
      console.log('🤖 AI知识库法律条文数据加载成功:', legalList.value)
      
      // 数据加载完成后启动轮播
      await nextTick()
      // 延迟一点时间确保DOM完全渲染
      setTimeout(() => {
        calculateVisibleItems()
        startCarousel()
      }, 100)
    } else {
      console.error('❌ 获取数据失败:', response.message)
    }
  } catch (error) {
    console.error('❌ 数据请求异常:', error)
  } finally {
    loading.value = false
  }
}

// 启动轮播
const startCarousel = () => {
  // 如果总项目数小于等于可见项目数，不需要轮播
  if (legalList.value.length <= visibleItemCount.value) {
    console.log('🤖 项目数量不足，无需轮播. 总数:', legalList.value.length, '可见数:', visibleItemCount.value)
    return
  }
  
  carouselTimer.value = setInterval(() => {
    // 逐项滚动，但要确保不会出现空白区域
    const nextIndex = currentIndex.value + 1
    
    // 如果下一个位置会导致底部空白，则重新开始
    // 改进判断条件：当滚动位置 + 可见项目数 >= 总项目数时重新开始
    if (nextIndex + visibleItemCount.value > legalList.value.length) {
      currentIndex.value = 0
      console.log('🤖 重新开始轮播. 下一个索引:', nextIndex, '可见数:', visibleItemCount.value, '总数:', legalList.value.length)
    } else {
      currentIndex.value = nextIndex
      console.log('🤖 轮播到索引:', currentIndex.value)
    }
  }, 3500) // 每3.5秒切换一次
}

// 停止轮播
const stopCarousel = () => {
  if (carouselTimer.value) {
    clearInterval(carouselTimer.value)
    carouselTimer.value = null
  }
}

// 处理AI问答入口点击
const handleAIClick = () => {
  const aiLink = getAIQuestionLink()
  console.log('🤖 跳转到AI问答入口:', aiLink)
  window.open(aiLink, '_blank')
}

// 处理法律条文点击
const handleLegalClick = (item) => {
  console.log('📖 点击法律条文:', item.name, '分类:', item.category)
  // 这里可以添加跳转到具体法律条文详情的逻辑
}

// 格式化时间显示
const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now - date
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return '今天'
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return timeStr.split(' ')[0] // 返回日期部分
  }
}

// 获取分类样式类
const getCategoryClass = (category) => {
  const categoryMap = {
    '宪法类': 'category-constitution',
    '民法类': 'category-civil',
    '刑法类': 'category-criminal',
    '行政法类': 'category-administrative',
    '劳动法类': 'category-labor',
    '商法类': 'category-commercial',
    '环境法类': 'category-environmental',
    '特别法类': 'category-special'
  }
  return categoryMap[category] || 'category-other'
}

// 组件挂载
onMounted(async () => {
  await nextTick()
  await fetchLegalList()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopCarousel()
})
</script>

<style scoped>
/* 主容器 - 使用flex布局 */
.right-block-3 {
  /* background: rgba(60, 24, 24, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(220, 20, 60, 0.15); */
  border-radius: 12px;
  padding: 16px;
  color: #ffffff;
  transition: all 0.3s ease;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  min-height: 0;
}

.right-block-3:hover {
  background: rgba(0, 20, 40, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 13, 74, 0.2);
  border-color: rgba(2, 166, 181, 0.3);
}

/* 知识库头部 - flex布局 */
.knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  position: relative;
  flex-shrink: 0;
}

/* 美观的分割线设计 */
.knowledge-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(73, 188, 247, 0.3) 10%,
    rgba(73, 188, 247, 0.8) 30%,
    rgba(73, 188, 247, 1) 50%,
    rgba(73, 188, 247, 0.8) 70%,
    rgba(73, 188, 247, 0.3) 90%,
    transparent 100%
  );
  border-radius: 2px;
  box-shadow: 
    0 0 8px rgba(73, 188, 247, 0.4),
    0 2px 4px rgba(73, 188, 247, 0.2);
  animation: glow-pulse 3s ease-in-out infinite alternate;
}

/* 添加一个细的上层装饰线 */
.knowledge-header::before {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  border-radius: 1px;
  animation: shimmer 2s ease-in-out infinite alternate;
}

/* 分割线发光动画 */
@keyframes glow-pulse {
  0% {
    box-shadow: 
      0 0 8px rgba(73, 188, 247, 0.4),
      0 2px 4px rgba(73, 188, 247, 0.2);
  }
  100% {
    box-shadow: 
      0 0 12px rgba(73, 188, 247, 0.6),
      0 2px 6px rgba(73, 188, 247, 0.3);
  }
}

/* 上层装饰线闪烁动画 */
@keyframes shimmer {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.knowledge-icon {
  font-size: 1.2rem;
  filter: drop-shadow(0 0 4px rgba(73, 188, 247, 0.8));
}

.knowledge-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.knowledge-subtitle {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  /* background: rgba(220, 20, 60, 0.2); */
  padding: 2px 8px;
  border-radius: 10px;
  /* border: 1px solid rgba(255, 215, 0, 0.3); */
  border: 1px solid #49bcf7;
  cursor: pointer;
  transition: all 0.3s ease;
}

.knowledge-subtitle:hover {
  background: rgba(2, 166, 181, 0.3);
  border-color: rgba(73, 188, 247, 0.5);
  transform: scale(1.05);
}

/* 知识库容器 - flex自适应 */
.knowledge-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  position: relative;
}

/* 轮播容器 */
.legal-carousel {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.legal-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  transition: transform 0.8s ease-in-out;
}

/* 法律条文项目 */
.legal-item {
  display: flex;
  align-items: center;
  gap: 8px;
  /* padding: 6px 8px; */
  border-radius: 6px;
  /* border: 1px solid rgba(220, 20, 60, 0.08);
  background: rgba(60, 24, 24, 0.3); */
  min-height: 24px;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.legal-item:hover {
  background: rgba(0, 20, 40, 0.5);
  border-color: rgba(2, 166, 181, 0.2);
  transform: translateX(2px);
}

/* 分类指示器 */
.category-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

/* 使用蓝色科技主题色系 */
.category-constitution {
  background: #02a6b5; /* 青蓝色 - 宪法类 */
  box-shadow: 0 0 6px rgba(2, 166, 181, 0.6);
}

.category-civil {
  background: #1a5490; /* 中蓝色 - 民法类 */
  box-shadow: 0 0 6px rgba(26, 84, 144, 0.6);
}

.category-criminal {
  background: #000d4a; /* 深蓝色 - 刑法类 */
  box-shadow: 0 0 6px rgba(0, 13, 74, 0.6);
}

.category-administrative {
  background: #49bcf7; /* 亮蓝色 - 行政法类 */
  box-shadow: 0 0 6px rgba(73, 188, 247, 0.6);
}

.category-labor {
  background: #FFA500; /* 橙色 - 劳动法类 */
  box-shadow: 0 0 6px rgba(255, 165, 0, 0.6);
}

.category-commercial {
  background: #CD5C5C; /* 印度红 - 商法类 */
  box-shadow: 0 0 6px rgba(205, 92, 92, 0.6);
}

.category-environmental {
  background: #A0522D; /* 赭色 - 环境法类 */
  box-shadow: 0 0 6px rgba(160, 82, 45, 0.6);
}

.category-special {
  background: #DAA520; /* 金麒麟色 - 特别法类 */
  box-shadow: 0 0 6px rgba(218, 165, 32, 0.6);
}

.category-other {
  background: #B8860B; /* 暗金色 - 其他类 */
  box-shadow: 0 0 6px rgba(184, 134, 11, 0.6);
}

/* 法律条文内容 */
.legal-content {
  flex: 1;
  min-width: 0;
}

.legal-name {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.legal-category {
  font-size: 0.65rem;
  color: rgba(255, 255, 255, 0.6);
}

/* 更新时间 */
.legal-meta {
  flex-shrink: 0;
  min-width: 50px;
  text-align: right;
}

.update-time {
  font-size: 0.6rem;
  color: rgba(255, 255, 255, 0.5);
}

/* 加载状态样式 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  min-height: 120px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(2, 166, 181, 0.2);
  border-top: 3px solid rgba(2, 166, 181, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .right-block-3 {
    padding: 12px;
  }
  
  .legal-item {
    padding: 5px 6px;
    gap: 6px;
    min-height: 32px;
  }
  
  .legal-name {
    font-size: 0.7rem;
  }
  
  .legal-category {
    font-size: 0.6rem;
  }
  
  .update-time {
    font-size: 0.55rem;
  }
}

@media (max-width: 768px) {
  .right-block-3 {
    padding: 10px;
  }
  
  .knowledge-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    margin-bottom: 8px;
  }
  
  .knowledge-subtitle {
    align-self: flex-end;
    font-size: 0.65rem;
  }
  
  .legal-item {
    padding: 4px 6px;
    gap: 6px;
    min-height: 30px;
  }
  
  .legal-name {
    font-size: 0.65rem;
  }
  
  .legal-category {
    font-size: 0.55rem;
  }
  
  .legal-meta {
    min-width: 40px;
  }
  
  .update-time {
    font-size: 0.5rem;
  }
}

/* 轮播动画效果 - 党建红主题 */
.right-block-3::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(2, 166, 181, 0.15),
    transparent
  );
  transition: left 0.5s ease;
}

.right-block-3:hover::after {
  left: 100%;
}
</style> 