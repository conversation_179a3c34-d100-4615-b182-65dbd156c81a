import { mockRequest } from './request.js'

/**
 * 获取AI知识库法律条文列表
 * @param {Object} params - 请求参数
 * @param {number} params.page - 页码，默认1
 * @param {number} params.pageSize - 每页数量，默认10
 * @returns {Promise} 返回法律条文数据
 */
export const getLegalKnowledgeList = (params = {}) => {
  // 模拟法律条文数据
  const mockData = {
    1: "中华人民共和国宪法",
    2: "中华人民共和国民法典",
    3: "中华人民共和国刑法",
    4: "中华人民共和国行政法",
    5: "中华人民共和国劳动法",
    6: "中华人民共和国合同法",
    7: "中华人民共和国公司法",
    8: "中华人民共和国物权法",
    9: "中华人民共和国侵权责任法",
    10: "中华人民共和国婚姻法",
    11: "中华人民共和国环境保护法",
    12: "中华人民共和国消费者权益保护法",
    13: "中华人民共和国知识产权法",
    14: "中华人民共和国税法",
    15: "中华人民共和国证券法"
  }

  // 转换为数组格式，便于前端处理
  const legalList = Object.entries(mockData).map(([id, name]) => ({
    id: parseInt(id),
    name: name,
    category: getCategoryByName(name),
    updateTime: generateRandomTime(),
    status: 'active',
    description: `${name}相关条文内容，包含详细的法律条款和实施细则。`
  }))

  // 分页处理
  const { page = 1, pageSize = 10 } = params
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedList = legalList.slice(startIndex, endIndex)

  const responseData = {
    code: 200,
    message: '获取法律条文列表成功',
    data: {
      list: paginatedList,
      pagination: {
        total: legalList.length,
        pageSize: pageSize,
        pageNum: page,
        pages: Math.ceil(legalList.length / pageSize)
      }
    },
    timestamp: new Date().toISOString()
  }

  // 使用mockRequest模拟网络请求
  return mockRequest('GET', '/api/legal/knowledge/list', params, responseData)
}

/**
 * 根据法律名称获取分类
 * @param {string} name - 法律名称
 * @returns {string} 法律分类
 */
function getCategoryByName(name) {
  if (name.includes('宪法')) return '宪法类'
  if (name.includes('民法') || name.includes('婚姻') || name.includes('合同')) return '民法类'
  if (name.includes('刑法')) return '刑法类'
  if (name.includes('行政')) return '行政法类'
  if (name.includes('劳动')) return '劳动法类'
  if (name.includes('公司') || name.includes('证券')) return '商法类'
  if (name.includes('环境')) return '环境法类'
  if (name.includes('消费者') || name.includes('知识产权')) return '特别法类'
  return '其他法类'
}

/**
 * 生成随机更新时间
 * @returns {string} 格式化的时间字符串
 */
function generateRandomTime() {
  const now = new Date()
  const randomDays = Math.floor(Math.random() * 30) // 最近30天内
  const randomDate = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000)
  
  return randomDate.toISOString().split('T')[0] + ' ' + 
         randomDate.toTimeString().split(' ')[0].substring(0, 5)
}

/**
 * 获取AI问答入口链接
 * @returns {string} AI问答链接
 */
export const getAIQuestionLink = () => {
  return 'https://dify.gxaigc.cn/chat/BcwlrHx1D4bYfJ2h'
} 