# 人大代表履职服务与管理平台

本项目是基于 Vue 3 + Element Plus 构建的人大代表履职服务与管理平台。

## 项目特点

- ✅ 基于 Vue 3 Composition API
- ✅ 使用 Element Plus UI 组件库
- ✅ 中国红主题设计
- ✅ 响应式布局设计
- ✅ 前端路由配置
- ✅ 用户状态管理
- ✅ 模拟数据接口
- ✅ 规范化API目录结构

## 技术栈

- **框架**: Vue 3
- **UI 组件库**: Element Plus
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **构建工具**: Vite
- **样式**: CSS3 + Element Plus
- **HTTP客户端**: Axios
- **API架构**: 模块化RESTful API

## API集成

### API模块化结构

前端API采用模块化设计，按功能分组：

```
src/api/
├── http/                    # HTTP核心配置
│   ├── client.js           # Axios实例
│   ├── config.js           # API配置和端点
│   └── interceptors.js     # 请求/响应拦截器
├── modules/                # 业务模块API
│   ├── auth/              # 用户认证模块
│   │   ├── api.js         # 认证API方法
│   │   └── index.js       # 模块入口
│   └── performance/       # 履职管理模块
│       ├── api.js         # 履职管理API方法
│       └── index.js       # 模块入口
└── index.js               # 全局API入口
```
### 群众意见提交路径
- `http://localhost:3000/people-opinion`

### 履职管理API

所有API接口已与后端Django `/api/v1/performance/` 路由对接：

- GET `/api/v1/performance/records/` - 获取履职记录列表
- POST `/api/v1/performance/records/` - 创建履职记录
- GET `/api/v1/performance/records/{id}/` - 获取履职记录详情
- PUT/PATCH `/api/v1/performance/records/{id}/` - 更新履职记录
- DELETE `/api/v1/performance/records/{id}/` - 删除履职记录
- GET `/api/v1/performance/records/stats/` - 获取统计数据
- GET `/api/v1/performance/records/export/` - 导出数据
- POST `/api/v1/performance/upload/` - 上传附件
- GET `/api/v1/performance/attachments/` - 获取附件列表
- DELETE `/api/v1/performance/attachments/{id}/` - 删除附件

### AI知识库API

集成Dify平台的智能法律政策问答服务：

- POST `/api/v1/aiknowledge/chat/sse/` - SSE流式AI聊天
- POST `/api/v1/aiknowledge/audio-to-text/` - 语音转文字

**核心特性**：
- 🤖 **SSE流式对话**：实时AI回答流，支持长对话上下文
- 🎙️ **语音转文字**：支持多格式音频（mp3/wav/webm等），最大50MB
- 🧠 **深度思考展示**：自动解析`<think></think>`标签，可折叠思考过程
- 📚 **参考文件管理**：智能显示AI回答的参考资料来源
- 🔒 **安全认证**：JWT token保护，自动刷新机制

### API使用示例

```javascript
// 导入API方法
import { performanceAPI, performanceFileAPI } from '@/api/modules/performance'
import { aiKnowledgeAPI } from '@/api/modules/aiknowledge'

// 获取履职记录列表
const records = await performanceAPI.getRecordsList({
  page: 1,
  page_size: 20,
  performance_type: '会议参与'
})

// 上传文件
const uploadResult = await performanceFileAPI.uploadFile({
  performance_record_id: recordId,
  file_type: 'image',
  file: fileObject
})

// AI智能问答
await aiKnowledgeAPI.chatSSE(
  { query: '人大代表的主要职责是什么？' },
  (data) => {
    if (data.event === 'message') {
      console.log('AI回答:', data.answer)
    } else if (data.event === 'message_end') {
      console.log('参考文件:', data.metadata?.retriever_resources)
    }
  }
)

// 语音转文字
const result = await aiKnowledgeAPI.audioToText(audioFile)
if (result.success) {
  console.log('转换结果:', result.text)
}
```

## 快速开始

### 环境配置

在项目根目录创建 `.env.development` 文件：

```bash
# 开发环境配置
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_APP_TITLE=人大代表履职服务与管理平台
```

### 启动步骤

1. 确保后端Django服务已启动 (http://localhost:8000)
2. 安装依赖：`npm install`
3. 启动开发服务器：`npm run dev`
4. 访问：http://localhost:5173

### 构建生产版本
```bash
npm run build
```

## 功能模块

### 已实现功能

#### 1. 用户登录系统（F-UM-001）
- **状态**: ✅ 已完成
- **功能**: 支持人大代表和站点工作人员登录
- **测试账号**:
  - 代表账号：representative1/123456
  - 工作人员账号：staff1/123456

#### 2. 工作台概览模块（F-DB-001, F-DB-002）
- **状态**: ✅ 已完成 **[2025-01-20 重大更新]**
- **优先级**: P0
- **功能描述**: 为人大代表和站点工作人员提供类似dashboard的工作台概览页面
- **实现文件**:
  - `/views/representative/Index.vue` - 代表端工作台概览
  - `/views/staff/Index.vue` - 站点工作人员工作台概览（已重构）

#### 🚀 2025-01-20 工作人员工作台重大更新：

**真实数据集成**:
- **统计数据**: 将模拟统计数据替换为真实的意见建议统计API数据
  - 待审核意见：实时获取`submitted`状态的意见数量
  - 处理中意见：已转交+处理中状态的意见总数
  - 已办结意见：已完成状态的意见数量  
  - 本月处理：当月办结的意见数量
- **待审核列表**: 获取真实的待审核意见建议列表（最多5条）
  - 显示标题、提交代表、提交时间、分类信息
  - 支持点击标题或审核按钮跳转到审核页面
- **最近活动**: 根据真实的处理记录生成活动时间线
  - 审核通过、转交部门、更新进展、办结等操作记录
  - 按时间倒序展示，不同状态显示不同颜色

**用户体验优化**:
- **加载状态**: 各区块独立loading状态，用户体验更流畅
- **错误处理**: 完善的错误处理机制和降级策略
- **交互优化**: 标题可点击、hover效果、分类标签展示
- **响应式布局**: 完美适配各种屏幕尺寸

**技术实现亮点**:
- **API集成**: 复用意见建议审核模块的统计API
- **数据转换**: 后端数据格式到前端展示格式的智能转换
- **容错处理**: API失败时的默认数据展示
- **性能优化**: 并行加载多个数据源，提升页面加载速度

- **主要特点**:
  - **红色欢迎区域**: 采用中国红渐变背景的欢迎卡片，展示用户基本信息
  - **统计数据卡片**: 现代化设计的数据统计卡片，不同颜色区分不同指标
  - **活动动态展示**: 最近履职记录、待处理事项等动态信息展示
  - **快捷操作入口**: 提供常用功能的快捷访问按钮
  - **响应式设计**: 适配不同屏幕尺寸的设备

#### 3. 人大代表个人信息管理（F-RM-001）
- **状态**: ✅ 已完成
- **优先级**: P0
- **功能描述**: 代表管理完整的11个属性字段的基本信息
- **实现文件**: `/views/representative/Profile.vue`
- **主要特点**:
  - **完整属性字段**: 包含代表层级、姓名、性别、民族、出生日期、籍贯、党派、现任职务、移动电话号码、学历、毕业院校、所学专业等11个字段
  - **权限控制**: 代表层级和姓名为只读字段，其他字段可编辑
  - **验证规则**: 必填项验证、手机号格式验证等
  - **下拉选择**: 性别、民族、党派、学历等字段提供标准选项
  - **日期选择**: 出生日期使用日期选择器
  - **统计展示**: 履职记录总数、提交意见数、已办结意见、本年履职次数等统计信息
- **菜单名称**: "个人信息管理"（已从"个人信息"更名）
- **验收标准**: 
  - ✅ 支持11个属性字段的完整管理
  - ✅ 只读字段和可编辑字段权限控制正确
  - ✅ 表单验证规则完整有效
  - ✅ 选项数据完整（56个民族、10个党派等）
  - ✅ 保存功能正常，数据更新成功

#### 4. 履职记录管理（F-RM-002）
- **状态**: ✅ 已完成 **[2025-01-20 重大更新]**
- **优先级**: P0
- **功能描述**: 代表可以录入、查看、编辑和删除自己的履职记录，包含完整的6个履职属性字段和多媒体附件管理
- **实现文件**: 
  - `/views/representative/Records.vue` - 履职记录管理主页面（完全重构）
  - `/components/performance/AttachmentUploader.vue` - 附件上传组件
  - `/api/modules/performance/` - 履职管理API模块

#### 🚀 2025-01-20 重大功能更新：

**核心功能实现**:
- **完整CRUD操作**: 新增、查看、编辑、删除履职记录，与后端API完全对接
- **搜索筛选系统**: 支持按履职类型、状态、日期范围进行多维度筛选
- **分页展示**: 支持大量数据的高效分页显示
- **实时数据同步**: 所有操作实时与后端同步，确保数据一致性

**多媒体附件管理**:
- 📸 **图片附件**: JPG、PNG、GIF（最大10MB，最多9张）
- 🎤 **录音文件**: MP3、WAV、AAC（最大50MB，最多3个）
- 📹 **视频文件**: MP4、AVI、MOV（最大100MB，最多2个）
- 📄 **文档文件**: PDF、Word、Excel、TXT（最大20MB，最多5个）
- **附件预览**: 图片支持预览，所有文件支持下载
- **拖拽上传**: 支持拖拽上传，进度条显示

**用户界面优化**:
- **统一主题风格**: 采用中国红主题配色，界面简洁现代
- **响应式设计**: 完美适配桌面端和移动端
- **表单验证**: 完整的前端验证，友好的错误提示
- **操作反馈**: 详细的成功/失败提示，提升用户体验

**技术实现**:
- **Vue 3 Composition API**: 使用现代化的组合式API架构
- **Element Plus**: 统一的UI组件库，保证界面一致性
- **模块化API**: 完全对接后端Django REST API
- **错误处理**: 完善的错误处理机制和用户反馈

- **主要特点**:
  - **完整属性字段**: 履职日期、履职类型、履职内容、活动地点、详细描述、履职状态等6个字段
  - **11种履职类型**: 视察调研、学习培训、接待走访、执法检查、主题活动、述职、法规征询意见、政策法规宣传、议案建议办理督办、会议、其它
  - **3种履职状态**: 进行中、已完成、已暂停
  - **智能排序**: 按履职日期倒序排列，最新记录优先显示
  - **批量操作**: 支持批量删除等操作（管理员权限）

- **菜单名称**: "履职记录管理"（已从"履职记录"更名）
- **验收标准**: 
  - ✅ 支持6个履职属性字段的完整录入和管理
  - ✅ 列表支持查看、编辑、删除操作，实时数据同步
  - ✅ 表单验证规则完整有效，用户体验友好
  - ✅ 数据按时间排序和分页显示，性能优秀
  - ✅ 多媒体附件上传和管理功能完整
  - ✅ 高级筛选和搜索功能，支持多条件组合
  - ✅ 与后端Django API完全对接，权限控制严格
  - ✅ 响应式设计，支持各种设备访问

#### 5. 年度履职AI分析展示（F-RM-004）
- **状态**: ✅ 已完成
- **优先级**: P1
- **功能描述**: AI智能分析年度履职记录并生成图文并茂的精美展示报告
- **实现文件**: `/views/representative/AnnualAchievements.vue`
- **主要特点**:
  - **AI智能分析**: 基于履职记录数据自动进行AI分析
  - **个性化头部**: 包含代表信息和年度标识的美观头部设计
  - **核心成就指标**: 多维度数据统计卡片展示
  - **数据可视化**: 活动类型分布图、时间投入分析等图表
  - **履职亮点**: AI提炼的典型事例和关键成就
  - **关键词云**: 履职内容的智能词汇分析
  - **AI智能总结**: 专业的年度履职总结和展望
  - **导出分享**: 支持重新生成、导出PDF、分享功能
- **菜单名称**: "年度履职AI分析展示"（已从"年度履职成果展示"更名）
- **业务流程**: 选择年度 → 检查履职数据 → AI智能分析 → 生成图文展示
- **验收标准**: 
  - ✅ 支持年度选择和数据检查
  - ✅ AI分析过程模拟和结果展示
  - ✅ 图文并茂的成果展示页面
  - ✅ 完整的导出和分享功能

#### 6. 账号密码修改（F-UM-003）
- **状态**: ✅ 已完成
- **优先级**: P0
- **功能描述**: 人大代表可以修改自己登录账号的密码，确保账号安全
- **实现文件**: 
  - `/views/representative/PasswordChange.vue` - 密码修改页面
  - `/api/modules/auth/api.js` - 认证模块API接口（已规范化）
- **主要特点**:
  - **后端API对接**: 与Django后端密码修改API完全对接
  - **安全验证**: 需要输入当前密码进行身份验证
  - **密码强度检查**: 验证新密码长度和复杂度要求（后端验证）
  - **确认验证**: 两次新密码输入必须一致
  - **安全提示**: 提供密码安全建议和使用指南
  - **自动退出**: 密码修改成功后自动退出登录，需要重新登录
  - **完善错误处理**: 支持字段级别错误提示和详细的错误引导
- **菜单名称**: "账号密码修改"
- **API对接**: 
  - 使用后端 `/users/password/change/` 接口
  - 支持字段：`old_password`、`new_password`、`confirm_password`
  - 完整的Django密码验证器支持
- **业务规则**:
  - 代表用户登录后可访问密码修改页面
  - 必须验证当前密码的正确性
  - 新密码长度至少6位，支持Django密码复杂度验证
  - 新密码不能与当前密码相同
  - 修改成功后更新数据库并强制重新登录
- **验收标准**: 
  - ✅ 当前密码验证功能正常
  - ✅ 新密码强度检查有效（使用Django验证器）
  - ✅ 确认密码一致性验证
  - ✅ 修改成功后自动退出登录
  - ✅ 错误场景处理完善（支持字段级别错误）
  - ✅ 与后端API完全对接，移除Mock数据

#### 7. 意见建议处理（F-IM-001 ~ F-IM-006）
- **状态**: ✅ 已完成
- **功能**: 
  - 代表端：录入意见建议信息，AI辅助生成高质量意见建议
  - 工作人员端：审核意见建议，管理处理流程
- **文件**: 
  - `/views/representative/Opinions.vue`
  - `/views/staff/Review.vue`
  - `/views/staff/Management.vue`

#### 8. 辅助诉前调解模块（F-PM-001, F-PM-002） - 已删除
- **状态**: ❌ 已删除
- **优先级**: 已移除
- **删除原因**: 根据新需求分析文档，辅助诉前调解功能不再需要
- **删除日期**: 2024-12-23
- **影响说明**: 
  - 删除了代表端和工作人员端的"辅助诉前调解"菜单
  - 移除相关路由配置和页面文件
  - 清理相关API接口和业务逻辑

#### 9. AI智能法律政策助手（F-KQ-001）
- **状态**: ✅ 已完成 **[2025-01-20 重大升级]**
- **优先级**: P1
- **功能描述**: 基于Dify平台的智能法律政策问答系统，集成语音转文字和SSE流式对话功能
- **实现文件**:
  - `/api/modules/aiknowledge/` - AI知识库API模块
  - `/stores/knowledgeQA.js` - 知识问答状态管理
  - `/views/KnowledgeQA.vue` - AI智能助手主页面

#### 🚀 2025-01-20 重大功能升级：

**语音交互功能**：
- 🎙️ **语音输入**：支持实时录音，自动转换为文字
- 🔊 **多格式支持**：支持mp3、wav、webm等音频格式（最大50MB）
- 🎯 **智能识别**：高精度语音识别，自动填入输入框
- 🛡️ **权限管理**：自动请求麦克风权限，友好错误提示

**SSE流式对话**：
- ⚡ **实时响应**：SSE流式技术，实时显示AI回答过程
- 🧠 **深度思考展示**：自动解析AI思考过程，可折叠显示
- 📚 **参考文件智能展示**：折叠式参考资料，不干扰主要内容
- 💬 **上下文对话**：支持多轮对话，保持会话上下文

**智能内容处理**：
- 📝 **Markdown渲染**：支持富文本格式，代码高亮
- 🔍 **智能解析**：自动分离思考内容和回答内容
- 📋 **参考溯源**：显示AI回答的参考文件来源、相关度评分
- 📱 **自适应滚动**：智能页面滚动，用户体验优化

**技术特点**：
- 🔐 **安全认证**：JWT token保护，自动刷新机制
- 🚀 **性能优化**：局部Loading，避免全局阻塞
- 🔄 **错误恢复**：网络异常自动重试，完善错误处理
- 📊 **调试友好**：详细的调试日志，便于问题排查

#### 10. 后端服务断连自动处理（Network Error Handling）
- **状态**: ✅ 已完成
- **优先级**: P0（系统稳定性）
- **问题描述**: 后端服务停掉的情况下，刷新已登录的前端页面还是正常显示
- **解决方案**: 极简化的网络错误处理机制
- **实现文件**:
  - `/api/http/interceptors.js` - 改进错误处理，网络连接失败时自动跳转错误页面
  - `/views/system/NetworkError.vue` - 专门的网络错误页面
  - `/views/system/NotFound.vue` - 404错误页面
  - `/router/index.js` - 添加错误页面路由配置
- **主要特点**:
  - **自动检测网络错误**: 识别无响应、502、503等网络连接问题
  - **自动跳转错误页**: 检测到网络连接失败时跳转到专门的错误页面
  - **保持登录状态**: 不清理用户登录状态，保持用户体验
  - **友好错误提示**: 专门的错误页面，提供重试、返回等操作选项
  - **简洁明了**: 最简实现，只处理网络连接问题
- **技术实现**:
  - 在axios响应拦截器中检测网络错误类型
  - 无response或502/503状态码视为网络连接问题
  - 跳转到网络错误页面时传递来源页面路径（query.from参数）
  - 错误页面的重试按钮跳转回原来的页面，而不是重新加载错误页面
- **验收标准**: 
  - ✅ 后端服务停止时，HTTP请求失败自动跳转到网络错误页面
  - ✅ 不清理用户登录状态，保持登录信息
  - ✅ 错误页面提供友好的操作选项（重试、返回等）
  - ✅ 不影响正常的业务错误处理（如表单验证错误）
  - **布局集成**: 完全集成到现有页面布局，保持与其他功能模块一致的设计风格
- **用户角色**: 人大代表、站点工作人员
- **访问路径**: 
  - 代表端：`/representative/knowledge-qa`
  - 工作人员端：`/staff/knowledge-qa`
- **AI问答流程**: 输入法律政策问题 → AI智能分析 → 返回结构化专业解答 → 可复制收藏便于后续查阅
- **用户角色**: 人大代表、站点工作人员
- **访问路径**: 
  - 代表端：`/representative/knowledge-qa`
  - 工作人员端：`/staff/knowledge-qa`

**核心交互流程**：
1. **文字输入**：直接输入法律政策问题
2. **语音输入**：点击录音→自动转文字→AI分析回答
3. **实时对话**：SSE流式显示AI思考和回答过程
4. **参考查看**：点击展开查看AI回答的参考文件来源

**验收标准**：
- ✅ **语音转文字功能**：录音、转换、填入输入框完整流程
- ✅ **SSE流式对话**：实时显示AI回答，支持长文本流式输出
- ✅ **深度思考解析**：自动识别`<think></think>`标签，分离展示
- ✅ **参考文件管理**：折叠式设计，显示来源文档和相关度
- ✅ **多轮对话支持**：保持会话上下文，支持连续对话
- ✅ **Markdown渲染**：代码高亮、表格、列表等格式支持
- ✅ **智能滚动**：自动滚动到新内容，用户体验优化
- ✅ **权限和安全**：JWT认证、麦克风权限、文件格式验证
- ✅ **错误处理**：网络异常、语音识别失败等完善处理
- ✅ **界面集成**：与现有页面布局完美融合，风格统一

#### 10. 站内通知系统（F-NT-001）
- **状态**: ✅ 已完成
- **优先级**: P1
- **功能描述**: 业务流程中的关键事件站内通知
- **实现文件**:
  - `/api/notification.js` - 通知模块API接口
  - `/stores/notification.js` - 通知状态管理
  - `/views/NotificationCenter.vue` - 通知中心页面
  - `/components/NotificationIcon.vue` - 导航栏通知图标
- **主要特点**:
  - 实时未读通知数量显示
  - 通知列表查看、筛选、标记已读
  - 通知详情弹窗和业务跳转
  - 批量操作和分页功能
  - 5分钟自动刷新机制
- **业务集成**: 
  - 意见提交、审核、办理结果通知
  - 调解案件和AI分析完成通知
  - 系统维护和重要公告通知
- **验收标准**: 
  - ✅ 关键业务事件触发通知
  - ✅ 界面显示未读数量提示
  - ✅ 通知列表支持查看、筛选、标记已读
  - ✅ 点击通知跳转到相关业务页面

#### 11. 账号管理（F-UM-004）
- **状态**: ✅ 已完成
- **优先级**: P0
- **功能描述**: 站点工作人员管理所有用户账号，包括人大代表和工作人员的账号信息
- **实现文件**:
  - `/views/staff/AccountManagement.vue` - 账号管理主页面
  - `/api/accountManagement.js` - 账号管理API接口
- **主要特点**:
  - **账号列表管理**: 展示所有用户账号，包括用户名、角色、状态等信息
  - **新增账号**: 支持创建新的代表或工作人员账号，设置初始密码
  - **编辑账号**: 修改账号基本信息，如姓名、部门、联系方式等
  - **重置密码**: 为任意账号重置新密码，确保账号安全
  - **启用/禁用**: 灵活控制账号的启用和禁用状态
  - **搜索筛选**: 支持按用户名或姓名搜索账号
  - **分页显示**: 大量账号数据的分页展示和管理
  - **操作日志**: 所有管理操作都有记录和追溯
  - **表单验证**: 完整的输入验证规则，确保数据质量
  - **权限控制**: 只有站点工作人员可以访问账号管理功能
- **菜单位置**: 站点工作人员端 → "账号管理"
- **业务规则**:
  - 站点工作人员可以管理所有角色的账号
  - 用户名创建后不可修改，确保唯一性
  - 密码重置需要二次确认，保证安全性
  - 账号状态变更有确认提示，防止误操作
  - 支持两种角色：人大代表、站点工作人员
- **验收标准**: 
  - ✅ 支持查看所有用户账号列表
  - ✅ 新增账号功能完整，包含必要字段验证
  - ✅ 编辑账号信息功能正常
  - ✅ 重置密码功能安全可靠
  - ✅ 启用/禁用账号状态切换正常
  - ✅ 搜索和分页功能有效
  - ✅ 操作确认和错误处理完善
  - ✅ 界面风格与系统保持一致

## 项目结构

```
src/
├── api/                    # API接口
│   ├── http/              # HTTP核心配置
│   │   ├── client.js      # Axios实例配置
│   │   ├── config.js      # API配置和常量
│   │   ├── interceptors.js # 请求/响应拦截器
│   │   └── errorHandler.js # 统一错误处理
│   ├── modules/           # 业务模块
│   │   ├── auth/          # 认证模块（包含密码管理）
│   │   │   ├── index.js   # 模块入口
│   │   │   ├── api.js     # API请求方法
│   │   │   └── mock.js    # Mock数据
│   │   ├── aiknowledge/   # AI知识库模块
│   │   │   ├── index.js   # 模块入口
│   │   │   ├── api.js     # SSE聊天和语音转文字API
│   │   │   └── README.md  # 模块详细文档
│   │   ├── performance/   # 履职管理模块
│   │   │   ├── index.js   # 模块入口
│   │   │   └── api.js     # 履职记录和附件API
│   │   └── opinion/       # 意见建议模块
│   │       ├── index.js   # 模块入口
│   │       └── api.js     # 意见建议相关API
│   ├── achievement.js     # 年度履职AI分析展示相关
│   ├── accountManagement.js # 账号管理相关
│   ├── knowledgeQA.js     # 法律政策互动AI问答相关（已弃用）
│   ├── notification.js    # 通知模块相关（包含意见建议通知）
│   ├── records.js         # 履职记录相关
│   ├── workplan.js        # 工作计划管理相关
│   └── sms.js             # 短信通知相关（P2功能预留）
├── stores/                # 状态管理
│   ├── user.js           # 用户状态
│   ├── notification.js   # 通知状态
│   └── knowledgeQA.js    # 知识问答状态
├── components/            # 公共组件
│   └── NotificationIcon.vue      # 通知图标组件
├── views/                 # 页面组件
│   ├── Login.vue         # 登录页
│   ├── Dashboard.vue     # 工作台概览
│   ├── NotificationCenter.vue    # 通知中心
│   ├── KnowledgeQA.vue           # 法律政策互动AI问答（共享）
│   ├── representative/   # 代表端页面
│   │   ├── Index.vue           # 代表工作台
│   │   ├── Profile.vue         # 个人信息管理
│   │   ├── Records.vue         # 履职记录管理
│   │   ├── AnnualAchievements.vue # 年度履职AI分析展示
│   │   ├── Opinions.vue        # 意见建议
│   │   └── PasswordChange.vue  # 账号密码修改
│   ├── staff/            # 工作人员端页面（3个功能菜单）
│   │   ├── Index.vue           # 工作人员工作台（包含概览功能）
│   │   ├── Review.vue          # 意见建议审核
│   │   └── WorkPlan.vue        # 工作计划管理
│   │   # 注：法律政策互动AI问答功能使用共享的KnowledgeQA.vue
│   └── system/          # 系统管理页面（空目录，待开发）
├── router/               # 路由配置
│   └── index.js
├── styles/              # 样式文件
│   └── global.css      # 全局样式
└── main.js             # 应用入口
```

## 设计规范

### 主题色彩
- **主色**: 中国红 (#c62d2d)
- **辅助色**: 深红 (#8b1e1e)
- **背景色**: 浅灰 (#f5f7fa)
- **边框色**: 淡灰 (#e4e7ed)

### 组件规范
- 卡片间距：20px
- 圆角：8px  
- 阴影：0 2px 8px rgba(0, 0, 0, 0.1)
- 按钮：中国红主题
- 表单：统一验证规则

## 开发记录

### 2024-01-15
- ✅ 完成F-RM-004年度履职成果AI展示功能
- ✅ 修复userStore.user结构兼容性问题
- ✅ 完善错误处理和用户体验

### 2024-01-16
- ✅ 完成F-PM-001纠纷信息录入与AI分析功能
- ✅ 完成F-PM-002 AI分析结果查看功能
- ✅ 创建调解案件API接口和模拟数据
- ✅ 实现代表端和工作人员端调解案件管理页面
- ✅ 添加路由配置和导航菜单
- ✅ 完成响应式设计和错误处理机制

### 2024-01-17
- ✅ 修复Scale图标导入错误，最终替换为Setting图标
- ✅ 解决调解功能导致的登录后页面加载问题
- ✅ 更新representative/Index.vue和staff/Index.vue中的图标引用
- ⚠️ 发现Balance图标在某些Element Plus版本中也可能不存在，改用通用的Setting图标

### 2024-01-18
- ✅ 完成F-KQ-001法律政策知识问答模块
- ✅ 创建知识问答API接口和模拟数据（/api/knowledgeQA.js）
- ✅ 实现Pinia状态管理（/stores/knowledgeQA.js）
- ✅ 创建知识问答主页面（/views/KnowledgeQA.vue）
- ✅ 实现知识详情弹窗组件（/components/KnowledgeDetailDialog.vue）
- ✅ 添加路由配置和导航菜单集成
- ✅ 完成搜索、分类、收藏、历史记录等完整功能
- ✅ 实现响应式设计和用户体验优化

### 2024-01-19
- ✅ 重新设计F-KQ-001法律政策知识问答模块为AI对话式问答

### 2024-12-22
- ✅ 根据新需求分析文档优化意见建议功能模块
- ✅ 将"群众意见"菜单名称改为"意见建议"
- ✅ 更新录入表单字段：意见建议标题、意见建议分类、反映人、意见建议内容
- ✅ 优化AI功能说明：AI辅助生成高质量意见建议
- ✅ 将工作人员端"意见审核"改为"意见建议审核"
- ✅ 同步更新用户故事清单（4.用户故事清单.md）、用户旅程图（5.用户旅程图.md）、功能规格说明书（6.功能规格说明书.md）
- ✅ 更新所有相关API和界面文案，保持功能的一致性
- ✅ 更新知识问答模块中的相关术语和热门关键词
- ✅ 将知识问答功能集成到代表端和工作人员端子路由中
- ✅ 修改页面布局，去掉独立页面头部，与其他功能页面保持风格一致
- ✅ 实现AI对话式界面，突出AI智能问答特性：
  - 对话式UI设计，用户问题和AI回答清晰分离
  - AI思考过程模拟，增强用户体验
  - 结构化AI回答展示，包含法规条文和应用指南
  - 快速提问和热门问题推荐
  - 回答复制收藏功能
- ✅ 更新路由配置：`/representative/knowledge-qa` 和 `/staff/knowledge-qa`
- ✅ 优化API接口，支持AI问答模式
- ✅ 删除不再需要的知识详情弹窗组件
- ✅ 完善响应式设计和用户交互体验

### 2024-12-20
- ✅ 完成F-DB-001代表工作台概览功能（P0优先级）
- ✅ 完成F-DB-002站点工作人员工作台概览功能（P0优先级）
- ✅ 实现红色欢迎区域设计，符合图片展示的需求分析要求
- ✅ 重构统计数据卡片为现代化设计，支持不同颜色区分指标
- ✅ 优化活动记录和待办事项列表展示，提升用户体验
- ✅ 添加快捷操作功能区，提供常用功能快速访问
- ✅ 完善响应式布局，适配移动端设备
- ✅ 更新README.md文档，记录工作台概览模块完成状态

### 2025-01-20
- ✅ **AI智能法律政策助手重大升级**
- ✅ 集成Dify平台SSE流式对话功能
- ✅ 新增语音转文字功能，支持多种音频格式
- ✅ 实现深度思考内容自动解析和折叠显示
- ✅ 添加参考文件智能管理，折叠式设计不干扰主要内容
- ✅ 优化用户交互体验：智能滚动、权限管理、错误处理
- ✅ 模块化API架构：创建`/api/modules/aiknowledge/`模块
- ✅ 完善技术文档：详细的README和使用示例
- ✅ 删除预提问词和测试功能，专注核心AI问答体验
- ✅ 后端参考文件数据转发修复，完整metadata支持
- ✅ 修改通知跳转逻辑，从ElMessage改为路由跳转到通知中心

### 2024-12-21
- ✅ 完成工作人员工作台概览欢迎卡片功能优化
- ✅ 替换模拟数据为后端真实API数据
- ✅ 欢迎卡片显示真实工作人员信息：姓名、职务、工作站点
- ✅ 添加staffInfo计算属性，从userInfo.staff_info获取工作人员信息
- ✅ 在组件挂载时自动获取最新用户信息
- ✅ 移除auth模块中mock数据的导出，完全基于后端API
- ✅ 数据来源：后端Django users API中的UserProfileSerializer
- ✅ 支持字段：name（姓名）、position（职务）、station_name（工作站点）
- ✅ 保持工作时间等静态信息的显示

### 2024-12-21 (账号管理功能重构)
- ✅ 完全重写工作人员账号管理功能，对接真实后端API
- ✅ 支持创建不同角色账号（人大代表/工作人员）及相关信息
- ✅ 新增AccountCreateSerializer：支持同时创建用户和角色相关信息
- ✅ 创建代表账号：包含层级、姓名、性别、民族、出生日期、籍贯、党派、职务、手机、学历等完整信息
- ✅ 创建工作人员账号：包含姓名、职位、手机、邮箱、工作站点等信息
- ✅ 智能表单验证：根据选择的角色动态生成验证规则
- ✅ 账号列表显示：真实姓名、角色、部门/层级、联系电话、状态、登录时间等
- ✅ 账号操作功能：查看详情、重置密码、启用/禁用账号
- ✅ 高级筛选功能：支持按角色、状态筛选，支持用户名搜索
- ✅ 用户详情弹窗：显示基础信息和角色相关完整信息
- ✅ 响应式设计：支持桌面和移动端的良好显示
- ✅ 删除旧的模拟API文件：accountManagement.js
- ✅ 完全基于后端Django REST Framework API，涉及User、Representative、StaffMember三个模型
- ✅ 事务处理：确保用户和角色信息创建的原子性
- ✅ 数据完整性：创建账号时必须填写角色相关的完整信息

### 2024-12-21
- ✅ 更新F-RM-001代表个人信息管理模块，符合新需求分析文档
- ✅ 实现11个属性字段的完整管理（代表层级、姓名、性别、民族、出生日期、籍贯、党派、现任职务、移动电话号码、学历、毕业院校、所学专业）
- ✅ 配置权限控制：代表层级和姓名为只读字段，其他字段可编辑
- ✅ 添加完整的表单验证规则：必填项验证、手机号格式验证等
- ✅ 提供标准选项数据：56个民族、10个党派、10种学历等
- ✅ 优化UI组件：性别、民族、党派、学历使用下拉选择，出生日期使用日期选择器
- ✅ 菜单名称更新：从"个人信息"改为"个人信息管理"
- ✅ 保持原有CSS风格不变，确保界面风格统一
- ✅ 更新路由配置和工作台菜单显示名称

### 2024-12-22
- ✅ 根据新需求分析文档优化年度履职功能模块
- ✅ 删除"年度履职AI分析"菜单项和相关路由配置
- ✅ 移除AnnualAnalysis.vue页面文件和analysis.js API文件
- ✅ 将"年度履职成果展示"重命名为"年度履职AI分析展示"
- ✅ 更新AnnualAchievements.vue页面标题和相关文案
- ✅ 修改业务逻辑：从依赖独立分析页面改为基于履职记录直接生成AI分析展示
- ✅ 优化用户引导：当无数据时跳转到履职记录录入而非分析页面
- ✅ 保持原有CSS风格和UI组件不变，确保界面风格统一
- ✅ 清理无用的图标导入，优化代码结构
- ✅ 更新F-RM-002履职记录管理模块菜单名称
- ✅ 将"履职记录"菜单名称更改为"履职记录管理"，符合需求分析文档要求
- ✅ 更新路由配置、工作台菜单和页面标题
- ✅ 保持原有CSS风格和功能逻辑不变，确保界面一致性

### 2024-12-22 (下午)
- ✅ 优化工作人员端意见建议管理功能
- ✅ 删除"意见管理"菜单及其页面文件（Management.vue）
- ✅ 将意见管理页面的统计卡片功能整合到"意见建议审核"页面
- ✅ 删除表格和详情页面中的"紧急程度"属性
- ✅ 删除模拟数据中的紧急程度字段
- ✅ 优化审核页面布局，添加4个统计卡片：待审核、已审核通过、已转交、已办结
- ✅ 简化审核表单，移除紧急程度选择
- ✅ 更新路由配置，移除意见管理路由
- ✅ 更新侧边栏菜单和快捷操作，移除意见管理入口
- ✅ 保持原有CSS风格不变，确保界面风格统一

### 2024-12-22 (晚上)
- ✅ 修复网络错误处理逻辑问题
- ✅ 改进NetworkError.vue所有按钮功能：添加网络连接状态检测
- ✅ 重试、返回上页、返回首页按钮在后端仍停止时不再直接跳转
- ✅ 所有按钮都先检测连接状态，只有连接恢复时才执行跳转
- ✅ 添加按钮加载状态和详细用户反馈
- ✅ 使用现有API端点(/users/profile/)进行连接检测
- ✅ 完善用户体验：避免在网络故障时跳转到会再次出错的页面
- ✅ 优化代表工作台概览欢迎卡片
- ✅ 集成后端用户API，替换欢迎卡片中的模拟数据
- ✅ 显示真实的代表信息：姓名、代表层级、民族、党派
- ✅ 移除旧的模拟字段：选区、届次、代表证号
- ✅ 在组件挂载时自动获取用户完整信息
- ✅ 与后端Django users API完全对接，数据来源真实可靠
- ✅ 完成代表个人信息管理页面与后端API集成
- ✅ 更新所有字段名称以匹配后端Representative模型
- ✅ 实现完整的CRUD操作：获取、显示、编辑、保存个人信息
- ✅ 添加updateProfile API方法和用户store方法
- ✅ 保持只读字段（代表层级、姓名）和可编辑字段的权限控制
- ✅ 移除所有模拟数据，完全基于后端真实API
- ✅ 完善表单验证和错误处理机制
- ✅ 进一步优化意见建议审核页面功能
- ✅ 添加表格列：转交时间、最后更新时间
- ✅ 新增第5个统计卡片："超期未办结"（转交超过3个月未办结）
- ✅ 优化统计卡片布局：从4列改为5列响应式布局
- ✅ 扩充模拟数据：新增3个样本数据，包含不同状态和时间
- ✅ 更新详情对话框：显示转交时间和最后更新时间
- ✅ 完善操作逻辑：审核和转交操作自动更新最后更新时间
- ✅ 实现超期计算逻辑：基于转交时间自动计算超过3个月的案例
- ✅ 导入Warning图标：用于超期未办结卡片
- ✅ 保持原有CSS风格和用户体验一致性

### 2024-12-23
- ✅ 根据新需求分析文档更新菜单结构
- ✅ 删除代表和工作站人员各自页面的"辅助诉前调解"菜单
- ✅ 将"AI法律政策问答"菜单名称改为"法律政策互动AI问答"
- ✅ 移除路由配置中的调解相关路由（mediation-cases, mediation-analysis）
- ✅ 删除不再需要的调解功能文件：
  - `/views/representative/MediationCases.vue`
  - `/views/representative/MediationAnalysis.vue`
  - `/views/staff/MediationCases.vue`
  - `/api/mediation.js`
- ✅ 清理工作台页面中不再使用的Setting图标导入
- ✅ 更新README.md项目结构注释，同步删除调解相关文件引用
- ✅ 保持原有CSS风格和界面布局不变，确保风格统一
- ✅ 实现代表端"账号密码修改"功能（F-UM-003）
  - 新增 `/views/representative/PasswordChange.vue` 密码修改页面
  - 新增 `/api/password.js` 密码管理API接口
  - 添加路由配置和菜单项
  - 实现完整的密码验证和修改流程
- ✅ 完成F-WP-001工作计划录入管理功能（P1优先级）
  - 新增 `/views/staff/WorkPlan.vue` 工作计划管理页面
  - 新增 `/api/workplan.js` 工作计划API接口
  - 实现年度/季度/月度工作计划的增删改查
  - 添加统计数据展示：总计划数、待开始、进行中、已完成
  - 支持计划类型和状态筛选、关键词搜索
  - 实现批量操作：批量标记为已完成
  - 添加提醒时间设置功能，支持站内通知
  - 完善表单验证：日期验证、必填项校验
  - 实现详情查看和编辑功能
  - 添加到工作人员端侧边栏菜单和快捷操作
  - 保持中国红主题和现有页面风格统一

### 2024-12-23 (下午)
- ✅ 完成F-WA-001站点工作总结功能（P1优先级）
  - 新增 `/views/staff/SiteSummary.vue` 站点工作总结页面
  - 新增 `/api/workAnalysis.js` 工作分析API接口
  - 实现AI智能分析站点年度工作数据功能
  - 添加年度选择和AI分析过程展示
  - 完成工作亮点、问题分析、改进建议等展示模块
  - 集成ECharts数据可视化图表
  - 实现报告导出和分享功能
  - 添加"工作分析"子菜单到站点工作人员端
- ✅ 开始实现F-WA-002代表工作总结功能（P1优先级）
  - 新增 `/views/staff/RepresentativeSummary.vue` 代表工作总结页面
  - 实现代表列表管理和筛选功能
  - 支持单个和批量生成代表履职分析
  - 添加分析进度展示和结果查看
  - 实现代表履职分析结果导出功能
  - 更新工作人员端菜单，添加"工作分析"主菜单
- ✅ 更新站点工作人员端导航结构
  - 将"工作分析"设为子菜单，包含"站点工作总结"和"代表工作总结"
  - 在侧边栏使用el-sub-menu实现层级菜单
  - 在快捷操作区域添加工作分析功能入口
  - 导入DataAnalysis和Document图标
- ✅ 完善API接口和数据模拟
  - 实现站点工作分析API：getSiteWorkData、generateSiteAnalysis
  - 实现代表工作分析API：getRepresentativesList、generateRepresentativeAnalysis
  - 支持批量生成和进度回调：batchGenerateAnalysis
  - 实现分析结果查看：getRepresentativeAnalysis
  - 添加导出和分享功能：exportAnalysisReport、shareAnalysisReport
- ✅ 保持UI风格统一，符合中国红主题设计

### 2024-12-23 (晚上)
- ✅ 统一站点工作总结页面颜色风格，与代表端保持一致
- ✅ 修复生成分析报告颜色风格杂乱问题
- ✅ 统一使用中国红主题（#c62d2d）和渐变设计风格：
  - 核心数据概览卡片：统一渐变背景、数字颜色为中国红、增加hover效果
  - 工作亮点卡片：移除紫色渐变，改为浅红色背景、左边框为中国红、图标圆形设计
  - 问题分析和改进建议：统一浅红色背景、边框和标题为中国红
  - AI智能总结：移除彩色渐变，采用浅灰色渐变、标题为中国红
  - 页面标题和卡片标题：统一为中国红色系
- ✅ 移除CSS变量引用，使用固定颜色值确保一致性
- ✅ 增强hover悬停效果，提升用户交互体验
- ✅ 保持与代表端"年度履职AI分析展示"相同的视觉风格

### 2024-12-23 (深夜)
- ✅ 统一代表工作总结查看分析报告的风格，与代表端保持完全一致
- ✅ 重新设计分析结果展示弹窗，采用与代表端相同的布局和风格：
  - 代表信息头部：使用中国红渐变背景、显示代表头像、姓名、层级等信息
  - 核心履职指标：采用卡片网格布局、增加图标和趋势展示
  - 履职突出亮点：使用网格布局、左边框高亮、图标装饰
  - AI智能总结：包含综合评价、主要成就、改进建议等详细内容
  - 履职关键词云：支持权重分级显示、hover交互效果
- ✅ 更新图标导入：DataLine、TrendCharts、Star、Trophy、User、Share
- ✅ 新增分享功能：支持生成分享链接，与导出功能并列
- ✅ 优化模拟数据结构：
  - 指标数据增加趋势百分比显示
  - 丰富履职亮点描述内容
  - 新增主要成就和改进建议列表
  - 添加关键词云数据支持权重分级
- ✅ 完善CSS样式系统：
  - 统一使用中国红主题色（#c62d2d）
  - 增加hover悬停效果和过渡动画
  - 采用现代化卡片设计风格
  - 保持响应式布局设计
- ✅ 提升用户体验：
  - 弹窗宽度增加到85%，内容更丰富
  - 操作按钮区域优化，增加分享功能
  - 保持与代表端完全一致的视觉体验

### 2024-12-23 (最新更新)
- ✅ 重新创建 DataVisualizationIndex.vue 布局模板页面
- ✅ 根据用户手绘草图实现精确的Flex布局结构
- ✅ 创建9个彩色占位组件框（左侧3个，中间2个，右侧3个，底部1个）
- ✅ 每个组件框使用不同颜色标识，便于后续内容填充：
  - 左侧：红色系（左上）、绿色系（左中）、紫色系（左下）
  - 中间：蓝色系（中上大图）、橙色系（中下）
  - 右侧：青色系（右上）、粉色系（右中）、棕色系（右下）
  - 底部：深紫色系（底部横向长条）
- ✅ 实现完整的响应式设计，支持大中小屏幕适配
- ✅ 科技感视觉设计：深蓝渐变背景、发光边框、悬停动画效果
- ✅ 完善的Flex布局：头部标题区、主要内容三列区、底部组件区
- ✅ 支持移动端自适应：小屏幕下自动调整为合适的布局方式
- ✅ 每个组件框具备hover效果：边框发光、轻微上浮、颜色加深
- ✅ 顶部装饰线条和圆角边框设计，科技感十足
- ✅ 更新路由配置，/datav 路径指向新的布局模板页面

## 待开发功能

### 计划中功能  
- [ ] F-WP-002: 工作计划提醒功能（定时任务）
- [ ] F-NT-002: 短信通知功能  
- [ ] F-RP-001: 基础统计报表
- [ ] 系统管理模块
- [ ] 数据导出功能
- [ ] 移动端适配优化

## 部署说明

1. 构建项目：`npm run build`
2. 将 `dist` 目录部署到 Web 服务器
3. 配置服务器支持 SPA 路由
4. 在生产环境中替换模拟API为真实后端接口

## 注意事项

- 当前所有数据都是模拟数据，生产环境需要对接真实后端API
- AI分析功能当前为模拟实现，需要对接真实AI服务
- 文件上传功能需要配置对象存储服务
- 短信通知需要集成第三方短信服务商
- 数据统计需要配置数据库查询优化

## 贡献指南

1. Fork 本仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 提交 Pull Request

## 许可证

MIT License 

## 改动记录

### 2024-12-24 - API架构重构与模拟功能移除 - 用户登录认证模块
- **主要改动**: 重构API文件组织结构，建立标准化的API调用架构，移除所有模拟登录功能
- **新增文件**:
  - `src/api/index.js` - axios实例主配置文件
  - `src/api/config.js` - API配置和常量定义
  - `src/api/interceptors.js` - 请求响应拦截器
  - `src/api/modules/auth.js` - 用户认证API模块
  - `src/api/modules/index.js` - API模块统一导出
- **修改文件**:
  - `src/api/auth.js` - 重构为兼容性入口文件
  - `src/stores/user.js` - 使用新的配置常量
  - `src/main.js` - 添加认证状态初始化
- **架构特点**:
  - ✅ **模块化设计**: API按功能模块组织，职责清晰
  - ✅ **统一配置**: 集中管理API配置、端点和错误消息
  - ✅ **标准拦截器**: 统一处理认证、loading、错误等逻辑
  - ✅ **分层架构**: 配置层 → 拦截器层 → 模块层 → 入口层
  - ✅ **环境适配**: 支持开发、测试、生产环境配置
  - ✅ **向后兼容**: 保持原有API调用方式不变
- **核心功能**:
  - ✅ **真实API对接**: 优先调用后端API (`http://localhost:8000/api/v1`)
  - ✅ **JWT认证**: 支持access_token和refresh_token自动管理
  - ✅ **自动刷新**: token过期时自动刷新，失败时跳转登录页
  - ✅ **纯API模式**: 完全使用真实后端API，不再依赖模拟数据
  - ✅ **状态持久化**: 用户登录状态在页面刷新后自动恢复
  - ✅ **统一错误处理**: 完善的网络错误和认证错误处理机制
  - ✅ **Loading管理**: 全局loading状态统一管理
  - ✅ **请求追踪**: 自动添加请求ID用于调试
- **API端点对接**:
  - `POST /api/v1/users/auth/login/` - 用户登录
  - `POST /api/v1/users/auth/logout/` - 用户登出
  - `POST /api/v1/users/auth/refresh/` - 刷新token
  - `GET /api/v1/users/profile/` - 获取用户信息
  - `POST /api/v1/users/password/change/` - 修改密码
  - `GET /api/v1/users/manage/` - 用户列表管理
  - `GET /api/v1/users/representatives/` - 人大代表列表
  - `GET /api/v1/users/staff/` - 工作人员列表
- **技术优势**:
  - 使用axios拦截器自动处理认证头和token刷新
  - 支持跨域请求配置（CORS）
  - 统一的错误处理和用户提示
  - 模块化API设计，便于扩展和维护
  - 开发环境下详细的请求日志
  - 移除模拟API和测试账号，专注真实后端对接
- **扩展性**:
  - 新增API模块只需在`modules/`目录添加对应文件
  - 统一的配置管理，便于环境切换
  - 标准化的错误处理，减少重复代码
- **使用要求**:
  1. **必须启动后端服务**: 后端服务必须在 `http://localhost:8000` 正常运行
  2. **使用真实账号**: 使用后端数据库中的真实用户账号登录
  3. **网络连接**: 确保前后端网络连接正常
  4. **CORS配置**: 后端需要正确配置跨域请求
- **测试建议**:
  1. 先启动后端服务，确保API接口可访问
  2. 使用后端管理命令创建测试用户账号
  3. 验证token刷新和自动登出功能
  4. 检查开发者工具中的API请求日志
  5. 测试网络断开时的错误处理

## 开发日志

### 2024-12-23 - 密码修改功能后端API对接

**任务目标**: 完成前端代表账号密码修改功能与后端Django API的完全对接

**实现内容**:
- ✅ 删除旧的mock API文件 `/api/password.js`
- ✅ 更新密码修改组件使用规范化的auth模块API
- ✅ 修正前端请求数据格式，与后端API字段完全匹配
- ✅ 实现完善的错误处理，支持字段级别错误提示
- ✅ 更新Mock数据以匹配后端API格式

**技术细节**:
- 使用 `authAPI.changePassword()` 替代旧的 `changePassword()` 函数
- 前端字段映射：`currentPassword` → `old_password`，`newPassword` → `new_password`
- 支持后端返回的验证错误：`old_password`、`new_password`、`confirm_password`、`non_field_errors`
- 保持原有的用户体验：密码修改成功后自动退出登录

**验收确认**:
- ✅ 与后端 `/users/password/change/` API完全对接
- ✅ 支持Django密码验证器的所有验证规则
- ✅ 错误处理覆盖所有后端可能返回的错误类型
- ✅ 删除所有Mock数据依赖，使用真实后端API

### 2024-12-23 - API目录结构规范化重构

**重构目标**: 按照Vue3前端工程师最佳实践，对现有的API目录结构进行规范化重构

**重构范围**: 
- ✅ 完成登录认证模块API目录结构重构
- ✅ 建立标准化的HTTP核心配置架构  
- ✅ 实现模块化的API组织方式

**重构成果**:

#### 1. 新的API目录结构
```
src/api/
├── http/                    # HTTP核心配置
│   ├── client.js           # Axios实例配置
│   ├── config.js           # API配置和常量
│   ├── interceptors.js     # 请求/响应拦截器
│   └── errorHandler.js     # 统一错误处理
├── modules/                # 业务模块
│   └── auth/              # 认证模块
│       ├── index.js       # 模块入口
│       ├── api.js         # API请求方法
│       └── mock.js        # Mock数据
└── index.js               # 全局API入口
```

#### 2. 重构优势
- **关注点分离**: HTTP配置、业务逻辑、错误处理分离管理
- **模块化设计**: 按业务功能组织API，便于维护和扩展
- **统一入口**: 通过index.js提供统一的API导入入口
- **Mock支持**: 集成Mock数据，支持开发阶段的前端独立开发
- **错误处理**: 统一的错误处理机制，提升用户体验
- **类型安全**: 完善的JSDoc注释，提升代码可读性

#### 3. 文件功能说明

**HTTP核心层**:
- `http/client.js`: 创建和配置axios实例，支持工厂模式
- `http/config.js`: API基础配置、端点定义、HTTP状态码映射
- `http/interceptors.js`: 请求/响应拦截器，处理认证、错误、Loading等
- `http/errorHandler.js`: 统一错误处理工具，支持多种错误类型

**业务模块层**:
- `modules/auth/api.js`: 认证相关API方法，包含完整的CRUD操作
- `modules/auth/mock.js`: 认证模块Mock数据，支持开发和测试
- `modules/auth/index.js`: 认证模块统一入口

#### 4. 导入方式更新
```javascript
// 旧方式
import { authAPI } from '@/api/modules/auth'
import { STORAGE_KEYS } from '@/api/config'

// 新方式 - 统一从api入口导入
import { authAPI, STORAGE_KEYS } from '@/api'
```

#### 5. 代码质量提升
- **完整的JSDoc注释**: 所有API方法都有详细的参数说明
- **错误处理增强**: 支持网络错误、HTTP错误、业务错误的统一处理
- **Mock数据完善**: 提供丰富的测试数据和模拟场景
- **代码复用**: 通用工具函数提取，减少重复代码

**影响范围**:
- ✅ 更新了用户状态管理store的导入路径
- ✅ 删除了废弃的配置和拦截器文件
- ✅ 保持了对外API接口的兼容性

**后续规划**:
- 其他业务模块（意见建议、工作计划等）将按此标准逐步重构
- 建立API文档生成机制
- 集成接口测试工具

**验收标准**:
- ✅ 登录功能正常，认证流程无异常
- ✅ 错误处理机制正常工作
- ✅ Mock数据功能正常
- ✅ 代码导入路径更新完成
- ✅ 开发体验提升，代码更易维护 