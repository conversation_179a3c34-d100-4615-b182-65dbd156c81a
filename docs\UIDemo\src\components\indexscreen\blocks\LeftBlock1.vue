<template>
  <div class="left-block-1">
    <!-- 标题区域 -->
    <div class="chart-header">
      <div class="header-left">
        <!-- <div class="chart-icon">📊</div> -->
        <div class="chart-title">{{ chartTitle }}</div>
      </div>
      <div class="chart-subtitle">{{ chartSubtitle }}</div>
    </div>
    
    <!-- 图表容器 -->
    <div class="chart-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">数据加载中...</div>
      </div>
      
      <!-- 图表内容 -->
      <div v-else ref="chartRef" class="chart-content"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import { getRepresentativeStatistics } from '@/api/indexscreen/representativeStatistics.js'

// 响应式数据
const chartTitle = ref('代表统计')
const chartRef = ref(null)
const loading = ref(false)
const chartData = ref([])
const totalCount = ref(0)
let chartInstance = null

// 计算属性：动态显示总人数
const chartSubtitle = computed(() => {
  return `代表人数：${totalCount.value}`
})

// 获取图表数据
const fetchChartData = async () => {
  try {
    loading.value = true
    const response = await getRepresentativeStatistics()
    
    if (response.code === 200) {
      chartData.value = response.data
      totalCount.value = response.totalCount || 0
      console.log('🏛️ 人大代表统计数据加载成功:', chartData.value)
    } else {
      console.error('❌ 获取数据失败:', response.message)
    }
  } catch (error) {
    console.error('❌ 数据请求异常:', error)
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value || chartData.value.length === 0) return
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    backgroundColor: 'transparent',
    grid: {
      top: '15%',
      left: '8%',
      right: '8%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.value.map(item => item.name),
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10,
        interval: 0,
        rotate: 0,
        margin: 8
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 10
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'bar',
        data: chartData.value.map((item, index) => ({
          value: item.value,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: item.color },
              { offset: 1, color: item.color + '40' }
            ]),
            borderRadius: [4, 4, 0, 0],
            shadowColor: item.color,
            shadowBlur: 8,
            shadowOffsetY: 2
          }
        })),
        barWidth: '45%',
        label: {
          show: true,
          position: 'top',
          color: '#ffffff',
          fontSize: 11,
          fontWeight: 'bold',
          distance: 5
        },
        animationDelay: (idx) => idx * 100,
        animationDuration: 1000,
        animationEasing: 'elasticOut'
      }
    ],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'rgba(255, 255, 255, 0.2)',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      formatter: function(params) {
        const data = params[0]
        return `
          <div style="padding: 5px;">
            <div style="color: ${chartData.value[data.dataIndex].color}; font-weight: bold;">
              ${data.name}
            </div>
            <div style="margin-top: 5px;">
              数量: <span style="color: #49bcf7; font-weight: bold;">${data.value}</span>
            </div>
          </div>
        `
      }
    }
  }
  
  chartInstance.setOption(option)
  
  // 添加点击事件
  chartInstance.on('click', (params) => {
    console.log('点击了柱状图:', params.name, '数值:', params.value)
  })
}

// 响应式调整
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 组件挂载
onMounted(async () => {
  await nextTick()
  // 先获取数据，再初始化图表
  await fetchChartData()
  initChart()
  window.addEventListener('resize', resizeChart)
})

// 组件卸载
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
/* 主容器 - 使用flex布局 */
.left-block-1 {
  /* background: rgba(60, 24, 24, 0.6);
  backdrop-filter: blur(10px); */
  border: none; /* 移除边框 */
  border-radius: 12px;
  padding: 16px;
  color: #ffffff;
  transition: all 0.3s ease;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  min-height: 0; /* 确保flex子项可以收缩 */
}

/* 移除四角装饰 */
.left-block-1::before {
  display: none;
}

.left-block-1:hover {
  background: rgba(0, 20, 40, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 13, 74, 0.2);
}

/* 图表头部 - flex布局 */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  position: relative;
  flex-shrink: 0; /* 防止头部被压缩 */
}

/* 美观的分割线设计 */
.chart-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(73, 188, 247, 0.3) 10%,
    rgba(73, 188, 247, 0.8) 30%,
    rgba(73, 188, 247, 1) 50%,
    rgba(73, 188, 247, 0.8) 70%,
    rgba(73, 188, 247, 0.3) 90%,
    transparent 100%
  );
  border-radius: 2px;
  box-shadow: 
    0 0 8px rgba(73, 188, 247, 0.4),
    0 2px 4px rgba(73, 188, 247, 0.2);
  animation: glow-pulse 3s ease-in-out infinite alternate;
}

/* 添加一个细的上层装饰线 */
.chart-header::before {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  border-radius: 1px;
  animation: shimmer 2s ease-in-out infinite alternate;
}

/* 分割线发光动画 */
@keyframes glow-pulse {
  0% {
    box-shadow: 
      0 0 8px rgba(73, 188, 247, 0.4),
      0 2px 4px rgba(73, 188, 247, 0.2);
  }
  100% {
    box-shadow: 
      0 0 12px rgba(73, 188, 247, 0.6),
      0 2px 6px rgba(73, 188, 247, 0.3);
  }
}

/* 上层装饰线闪烁动画 */
@keyframes shimmer {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-icon {
  font-size: 1.2rem;
  filter: drop-shadow(0 0 4px rgba(73, 188, 247, 0.8));
}

.chart-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.chart-subtitle {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  /* background: rgba(2, 166, 181, 0.2); */
  padding: 2px 8px;
  border-radius: 10px;
  /* border: 1px solid rgba(73, 188, 247, 0.3); */
  border: 1px solid #49bcf7;
}

/* 图表容器 - flex自适应 */
.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许容器收缩 */
  position: relative;
}

.chart-content {
  flex: 1;
  min-height: 120px; /* 最小高度确保图表可见 */
  width: 100%;
  position: relative;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-block-1 {
    padding: 12px;
  }
  
  .chart-title {
    font-size: 0.9rem;
  }
  
  .chart-subtitle {
    font-size: 0.7rem;
    padding: 1px 6px;
  }
  
  .chart-content {
    min-height: 100px;
  }
}

@media (max-width: 768px) {
  .left-block-1 {
    padding: 10px;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    margin-bottom: 8px;
  }
  
  .header-left {
    gap: 6px;
  }
  
  .chart-title {
    font-size: 0.85rem;
  }
  
  .chart-subtitle {
    font-size: 0.65rem;
    align-self: flex-end;
  }
  
  .chart-content {
    min-height: 80px;
  }
}

/* 加载动画效果 - 党建红主题 */
.left-block-1::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(2, 166, 181, 0.15),
    transparent
  );
  transition: left 0.5s ease;
}

.left-block-1:hover::after {
  left: 100%;
}

/* 确保图表在容器变化时正确响应 */
.chart-content > div {
  width: 100% !important;
  height: 100% !important;
}

/* 加载状态样式 */
.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  min-height: 120px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(2, 166, 181, 0.2);
  border-top: 3px solid rgba(2, 166, 181, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 