from rest_framework import serializers
from .models import PeopleOpinion


class PeopleOpinionSerializer(serializers.ModelSerializer):
    """群众意见序列化器"""
    
    short_content = serializers.ReadOnlyField()
    
    class Meta:
        model = PeopleOpinion
        fields = [
            'id',
            'title', 
            'content',
            'contact_info',
            'name',
            'created_at',
            'short_content'
        ]
        read_only_fields = ['id', 'created_at', 'short_content']
        
    def validate_title(self, value):
        """验证标题不能为空且长度合理"""
        if not value or not value.strip():
            raise serializers.ValidationError("意见标题不能为空")
        if len(value.strip()) < 5:
            raise serializers.ValidationError("意见标题至少需要5个字符")
        return value.strip()
    
    def validate_content(self, value):
        """验证内容不能为空且长度合理"""
        if not value or not value.strip():
            raise serializers.ValidationError("意见内容不能为空")
        if len(value.strip()) < 10:
            raise serializers.ValidationError("意见内容至少需要10个字符")
        return value.strip()
    
    def validate_name(self, value):
        """验证姓名"""
        if not value or not value.strip():
            raise serializers.ValidationError("姓名不能为空")
        return value.strip()
    
    def validate_contact_info(self, value):
        """验证联系方式"""
        if not value or not value.strip():
            raise serializers.ValidationError("联系方式不能为空")
        return value.strip()


class PeopleOpinionCreateSerializer(serializers.ModelSerializer):
    """群众意见创建序列化器（用于匿名提交）"""
    
    class Meta:
        model = PeopleOpinion
        fields = ['title', 'content', 'contact_info', 'name']
        
    def validate_title(self, value):
        """验证标题"""
        if not value or not value.strip():
            raise serializers.ValidationError("意见标题不能为空")
        if len(value.strip()) < 5:
            raise serializers.ValidationError("意见标题至少需要5个字符")
        if len(value.strip()) > 200:
            raise serializers.ValidationError("意见标题不能超过200个字符")
        return value.strip()
    
    def validate_content(self, value):
        """验证内容"""
        if not value or not value.strip():
            raise serializers.ValidationError("意见内容不能为空")
        if len(value.strip()) < 10:
            raise serializers.ValidationError("意见内容至少需要10个字符")
        return value.strip()
    
    def validate_name(self, value):
        """验证姓名"""
        if not value or not value.strip():
            raise serializers.ValidationError("姓名不能为空")
        if len(value.strip()) > 50:
            raise serializers.ValidationError("姓名不能超过50个字符")
        return value.strip()
    
    def validate_contact_info(self, value):
        """验证联系方式"""
        if not value or not value.strip():
            raise serializers.ValidationError("联系方式不能为空")
        if len(value.strip()) > 100:
            raise serializers.ValidationError("联系方式不能超过100个字符")
        return value.strip() 