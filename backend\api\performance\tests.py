"""
履职管理测试用例

包含以下测试类：
1. PerformanceRecordModelTest - 模型测试
2. PerformanceAttachmentModelTest - 附件模型测试
3. PerformanceRecordAPITest - API功能测试
4. PerformanceAttachmentAPITest - 附件API测试
5. FileUploadAPITest - 文件上传测试
6. PermissionTest - 权限控制测试
7. StatisticsAPITest - 统计API测试

测试原则：
- 覆盖核心功能
- 测试权限控制
- 测试数据验证
- 测试异常情况
- 适配APIView结构
uv run manage.py test api.performance.tests -v 2
"""

import os
import tempfile
from datetime import date, datetime
from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token

from api.users.models import Representative
from .models import PerformanceRecord, PerformanceAttachment

User = get_user_model()


def create_test_representative(user, name="测试代表"):
    """创建测试代表的辅助函数"""
    return Representative.objects.create(
        user=user,
        name=name,
        level='市级',
        gender='male',
        nationality='汉族',
        birth_date='1980-01-01',
        birthplace='测试地区',
        party='中国共产党',
        current_position='测试职务',
        mobile_phone='13800138000',
        education='本科'
    )


class PerformanceRecordModelTest(TestCase):
    """履职记录模型测试"""
    
    def setUp(self):
        """测试准备"""
        # 创建测试用户和代表
        self.user = User.objects.create_user(
            username='test_rep',
            password='testpass123',
            role='representative'
        )
        self.representative = create_test_representative(self.user)
    
    def test_create_performance_record(self):
        """测试创建履职记录"""
        record = PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date=date.today(),
            performance_type='会议参与',
            performance_content='参加人大常委会会议',
            activity_location='市人大会议室',
            performance_status='已完成'
        )
        
        self.assertEqual(record.representative, self.representative)
        self.assertEqual(record.performance_type, '会议参与')
        self.assertEqual(record.performance_status, '已完成')
        self.assertFalse(record.has_attachments)
    
    def test_record_str_representation(self):
        """测试字符串表示"""
        record = PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date=date.today(),
            performance_type='会议参与',
            performance_content='参加人大常委会会议'
        )
        
        expected = f"测试代表 - 会议参与 - {date.today()}"
        self.assertEqual(str(record), expected)
    
    def test_get_attachment_count(self):
        """测试获取附件数量"""
        record = PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date=date.today(),
            performance_type='会议参与',
            performance_content='参加人大常委会会议'
        )
        
        # 初始无附件
        self.assertEqual(record.get_attachment_count(), 0)
        
        # 创建附件
        PerformanceAttachment.objects.create(
            performance_record=record,
            file_type='image',
            original_filename='test.jpg',
            stored_filename='stored_test.jpg',
            file_path='test/path/test.jpg',
            file_size=1024,
            mime_type='image/jpeg',
            file_hash='test_hash'
        )
        
        self.assertEqual(record.get_attachment_count(), 1)
    
    def test_update_attachment_status_method(self):
        """测试更新附件状态方法"""
        record = PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date=date.today(),
            performance_type='会议参与',
            performance_content='参加人大常委会会议'
        )
        
        # 初始状态
        self.assertFalse(record.has_attachments)
        
        # 创建附件
        PerformanceAttachment.objects.create(
            performance_record=record,
            file_type='image',
            original_filename='test.jpg',
            stored_filename='stored_test.jpg',
            file_path='test/path/test.jpg',
            file_size=1024,
            mime_type='image/jpeg',
            file_hash='test_hash'
        )
        
        # 手动更新状态
        record.update_attachment_status()
        record.refresh_from_db()
        self.assertTrue(record.has_attachments)


class PerformanceAttachmentModelTest(TestCase):
    """附件模型测试"""
    
    def setUp(self):
        """测试准备"""
        self.user = User.objects.create_user(
            username='test_rep',
            password='testpass123',
            role='representative'
        )
        self.representative = create_test_representative(self.user)
        self.record = PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date=date.today(),
            performance_type='会议参与',
            performance_content='参加人大常委会会议'
        )
    
    def test_create_attachment(self):
        """测试创建附件"""
        attachment = PerformanceAttachment.objects.create(
            performance_record=self.record,
            file_type='image',
            original_filename='test_image.jpg',
            stored_filename='stored_test.jpg',
            file_path='performance/2024/01/01/images/stored_test.jpg',
            file_size=1024 * 500,  # 500KB
            mime_type='image/jpeg',
            file_hash='test_hash_123',
            width=1920,
            height=1080
        )
        
        self.assertEqual(attachment.performance_record, self.record)
        self.assertEqual(attachment.file_type, 'image')
        self.assertEqual(attachment.upload_status, 'uploaded')
    
    def test_file_size_display(self):
        """测试文件大小显示"""
        attachment = PerformanceAttachment.objects.create(
            performance_record=self.record,
            file_type='image',
            original_filename='test.jpg',
            stored_filename='stored_test.jpg',
            file_path='test/path/test.jpg',
            file_size=1024 * 1024 * 2,  # 2MB
            mime_type='image/jpeg',
            file_hash='test_hash'
        )
        
        self.assertEqual(attachment.get_file_size_display(), '2.0 MB')
    
    def test_duration_display(self):
        """测试时长显示"""
        attachment = PerformanceAttachment.objects.create(
            performance_record=self.record,
            file_type='video',
            original_filename='test.mp4',
            stored_filename='stored_test.mp4',
            file_path='test/path/test.mp4',
            file_size=1024 * 1024 * 10,
            mime_type='video/mp4',
            file_hash='test_hash',
            duration=125  # 2分5秒
        )
        
        self.assertEqual(attachment.get_duration_display(), '2:05')


class PerformanceRecordAPITest(APITestCase):
    """履职记录API测试"""
    
    def setUp(self):
        """测试准备"""
        # 创建测试用户和代表
        self.user = User.objects.create_user(
            username='test_rep',
            password='testpass123',
            role='representative'
        )
        self.representative = create_test_representative(self.user)
        
        # 创建另一个用户（用于权限测试）
        self.other_user = User.objects.create_user(
            username='other_rep',
            password='testpass123',
            role='representative'
        )
        self.other_representative = create_test_representative(self.other_user, "其他代表")
        
        # 创建测试数据
        self.record = PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date=date.today(),
            performance_type='会议参与',
            performance_content='参加人大常委会会议',
            activity_location='市人大会议室',
            performance_status='已完成'
        )
        
        # 设置认证
        self.client.force_authenticate(user=self.user)
    
    def test_list_performance_records(self):
        """测试获取履职记录列表"""
        url = reverse('performance:record-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertIn('count', response.data)
        self.assertIn('page', response.data)
    
    def test_create_performance_record(self):
        """测试创建履职记录"""
        url = reverse('performance:record-list-create')
        data = {
            'performance_date': date.today(),
            'performance_type': '实地调研',
            'performance_content': '到社区进行实地调研，了解民生问题和群众需求，收集意见建议',
            'activity_location': '某某社区',
            'performance_status': '已完成',
            'detailed_description': '详细了解了社区基础设施建设情况'
        }
        
        response = self.client.post(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(PerformanceRecord.objects.count(), 2)
        self.assertEqual(response.data['performance_type'], '实地调研')
    
    def test_retrieve_performance_record(self):
        """测试获取履职记录详情"""
        url = reverse('performance:record-detail', args=[self.record.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], self.record.id)
        self.assertIn('attachments', response.data)
    
    def test_update_performance_record_patch(self):
        """测试部分更新履职记录"""
        url = reverse('performance:record-detail', args=[self.record.id])
        data = {
            'performance_content': '更新后的履职内容，包含详细的履职活动描述信息'
        }
        
        response = self.client.patch(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.record.refresh_from_db()
        self.assertEqual(self.record.performance_content, '更新后的履职内容，包含详细的履职活动描述信息')
    
    def test_update_performance_record_put(self):
        """测试完整更新履职记录"""
        url = reverse('performance:record-detail', args=[self.record.id])
        data = {
            'performance_date': date.today(),
            'performance_type': '实地调研',
            'performance_content': '完整更新的内容，包含详细的履职活动描述和具体工作情况',
            'activity_location': '新地点',
            'performance_status': '进行中'
        }
        
        response = self.client.put(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.record.refresh_from_db()
        self.assertEqual(self.record.performance_type, '实地调研')
        self.assertEqual(self.record.performance_content, '完整更新的内容，包含详细的履职活动描述和具体工作情况')
    
    def test_delete_performance_record(self):
        """测试删除履职记录"""
        url = reverse('performance:record-detail', args=[self.record.id])
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(PerformanceRecord.objects.count(), 0)
    
    def test_permission_denied_for_other_user_record(self):
        """测试无法访问其他用户的记录"""
        # 切换到其他用户
        self.client.force_authenticate(user=self.other_user)
        
        url = reverse('performance:record-detail', args=[self.record.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_list_filtering(self):
        """测试列表筛选功能"""
        # 创建不同类型的记录
        PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date=date.today(),
            performance_type='实地调研',
            performance_content='调研内容',
            performance_status='进行中'
        )
        
        # 按类型筛选
        url = reverse('performance:record-list-create')
        response = self.client.get(url, {'performance_type': '实地调研'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['performance_type'], '实地调研')
    
    def test_list_search(self):
        """测试列表搜索功能"""
        url = reverse('performance:record-list-create')
        response = self.client.get(url, {'search': '常委会'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
    
    def test_list_pagination(self):
        """测试分页功能"""
        # 创建更多记录
        for i in range(25):
            PerformanceRecord.objects.create(
                representative=self.representative,
                performance_date=date.today(),
                performance_type='会议参与',
                performance_content=f'测试内容 {i}',
            )
        
        url = reverse('performance:record-list-create')
        response = self.client.get(url, {'page_size': 10})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 10)
        self.assertTrue(response.data['has_next'])
    
    def test_unauthenticated_access_denied(self):
        """测试未认证用户无法访问"""
        self.client.force_authenticate(user=None)
        
        url = reverse('performance:record-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class PerformanceAttachmentAPITest(APITestCase):
    """附件API测试"""
    
    def setUp(self):
        """测试准备"""
        self.user = User.objects.create_user(
            username='test_rep',
            password='testpass123',
            role='representative'
        )
        self.representative = create_test_representative(self.user)
        self.record = PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date=date.today(),
            performance_type='会议参与',
            performance_content='参加人大常委会会议'
        )
        self.attachment = PerformanceAttachment.objects.create(
            performance_record=self.record,
            file_type='image',
            original_filename='test.jpg',
            stored_filename='stored_test.jpg',
            file_path='test/path/test.jpg',
            file_size=1024,
            mime_type='image/jpeg',
            file_hash='test_hash'
        )
        
        self.client.force_authenticate(user=self.user)
    
    def test_list_attachments(self):
        """测试获取附件列表"""
        url = reverse('performance:attachment-list')
        response = self.client.get(url, {'performance_record_id': self.record.id})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(len(response.data['results']), 1)
    
    def test_list_attachments_without_record_id(self):
        """测试没有提供记录ID时的错误"""
        url = reverse('performance:attachment-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('缺少履职记录ID参数', response.data['detail'])
    
    def test_retrieve_attachment(self):
        """测试获取附件详情"""
        url = reverse('performance:attachment-detail', args=[self.attachment.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], self.attachment.id)
    
    def test_update_attachment_sort_order(self):
        """测试更新附件排序"""
        url = reverse('performance:attachment-detail', args=[self.attachment.id])
        data = {'sort_order': 5}
        
        response = self.client.patch(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.attachment.refresh_from_db()
        self.assertEqual(self.attachment.sort_order, 5)
    
    def test_delete_attachment(self):
        """测试删除附件"""
        url = reverse('performance:attachment-detail', args=[self.attachment.id])
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(PerformanceAttachment.objects.count(), 0)


class StatisticsAPITest(APITestCase):
    """统计API测试"""
    
    def setUp(self):
        """测试准备"""
        self.user = User.objects.create_user(
            username='test_rep',
            password='testpass123',
            role='representative'
        )
        self.representative = create_test_representative(self.user)
        
        # 创建测试数据
        PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date=date.today(),
            performance_type='会议参与',
            performance_content='参加会议1',
            performance_status='已完成'
        )
        PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date=date.today(),
            performance_type='实地调研',
            performance_content='实地调研1',
            performance_status='进行中'
        )
        
        self.client.force_authenticate(user=self.user)
    
    def test_stats_api(self):
        """测试统计API"""
        url = reverse('performance:record-stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_count', response.data)
        self.assertIn('monthly_count', response.data)
        self.assertIn('yearly_count', response.data)
        self.assertIn('type_stats', response.data)
        self.assertIn('status_stats', response.data)
        self.assertIn('monthly_trend', response.data)
        self.assertIn('attachment_stats', response.data)
        
        # 验证数据正确性
        self.assertEqual(response.data['total_count'], 2)
    
    def test_export_api(self):
        """测试导出API"""
        url = reverse('performance:record-export')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('count', response.data)
        self.assertIn('data', response.data)
        self.assertIn('export_time', response.data)
        self.assertEqual(response.data['count'], 2)


class ChoicesAPITest(APITestCase):
    """选择项API测试"""
    
    def setUp(self):
        """测试准备"""
        self.user = User.objects.create_user(
            username='test_rep',
            password='testpass123',
            role='representative'
        )
        self.representative = create_test_representative(self.user)
        
        self.client.force_authenticate(user=self.user)
    
    def test_performance_type_choices(self):
        """测试履职类型选择项"""
        url = reverse('performance:type-choices')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('choices', response.data)
        self.assertTrue(len(response.data['choices']) > 0)
    
    def test_performance_status_choices(self):
        """测试履职状态选择项"""
        url = reverse('performance:status-choices')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('choices', response.data)
        self.assertTrue(len(response.data['choices']) > 0)
    
    def test_file_type_limits(self):
        """测试文件类型限制"""
        url = reverse('performance:file-limits')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('limits', response.data)


@override_settings(MEDIA_ROOT=tempfile.mkdtemp())
class FileUploadAPITest(APITestCase):
    """文件上传API测试"""
    
    def setUp(self):
        """测试准备"""
        self.user = User.objects.create_user(
            username='test_rep',
            password='testpass123',
            role='representative'
        )
        self.representative = create_test_representative(self.user)
        self.record = PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date=date.today(),
            performance_type='会议参与',
            performance_content='参加人大常委会会议'
        )
        
        self.client.force_authenticate(user=self.user)
    
    def test_upload_image_file(self):
        """测试上传图片文件"""
        # 创建测试图片文件
        image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\x0bIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00\x9a\x08\x0c\x00\x00\x00\x00IEND\xaeB`\x82'
        
        uploaded_file = SimpleUploadedFile(
            name='test_image.png',
            content=image_content,
            content_type='image/png'
        )
        
        url = reverse('performance:file-upload')
        data = {
            'file': uploaded_file,
            'file_type': 'image',
            'performance_record_id': self.record.id
        }
        
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(PerformanceAttachment.objects.count(), 1)
        
        attachment = PerformanceAttachment.objects.first()
        self.assertEqual(attachment.file_type, 'image')
        self.assertEqual(attachment.original_filename, 'test_image.png')
    
    def test_upload_to_nonexistent_record(self):
        """测试上传到不存在的记录"""
        test_file = SimpleUploadedFile(
            name='test.jpg',
            content=b'fake image content',
            content_type='image/jpeg'
        )
        
        url = reverse('performance:file-upload')
        data = {
            'file': test_file,
            'file_type': 'image',
            'performance_record_id': 99999  # 不存在的ID
        }
        
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_upload_to_other_user_record(self):
        """测试无法为其他用户记录上传文件"""
        # 创建其他用户的记录
        other_user = User.objects.create_user(
            username='other_rep',
            password='testpass123',
            role='representative'
        )
        other_representative = create_test_representative(other_user, "其他代表")
        other_record = PerformanceRecord.objects.create(
            representative=other_representative,
            performance_date=date.today(),
            performance_type='会议参与',
            performance_content='其他记录'
        )
        
        test_file = SimpleUploadedFile(
            name='test.jpg',
            content=b'fake image content',
            content_type='image/jpeg'
        )
        
        url = reverse('performance:file-upload')
        data = {
            'file': test_file,
            'file_type': 'image',
            'performance_record_id': other_record.id
        }
        
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_upload_audio_file(self):
        """测试上传音频文件"""
        # 创建模拟音频文件
        audio_content = b'RIFF\x24\x00\x00\x00WAVE'  # WAV文件头
        
        uploaded_file = SimpleUploadedFile(
            name='test_audio.wav',
            content=audio_content,
            content_type='audio/wav'
        )
        
        url = reverse('performance:file-upload')
        data = {
            'file': uploaded_file,
            'file_type': 'audio',
            'performance_record_id': self.record.id
        }
        
        response = self.client.post(url, data, format='multipart')
        
        if response.status_code == status.HTTP_201_CREATED:
            attachment = PerformanceAttachment.objects.get(id=response.data['id'])
            self.assertEqual(attachment.file_type, 'audio')
            self.assertEqual(attachment.original_filename, 'test_audio.wav')
    
    def test_upload_video_file(self):
        """测试上传视频文件"""
        # 创建模拟视频文件
        video_content = b'\x00\x00\x00\x20ftypmp4'  # MP4文件头
        
        uploaded_file = SimpleUploadedFile(
            name='test_video.mp4',
            content=video_content,
            content_type='video/mp4'
        )
        
        url = reverse('performance:file-upload')
        data = {
            'file': uploaded_file,
            'file_type': 'video',
            'performance_record_id': self.record.id
        }
        
        response = self.client.post(url, data, format='multipart')
        
        if response.status_code == status.HTTP_201_CREATED:
            attachment = PerformanceAttachment.objects.get(id=response.data['id'])
            self.assertEqual(attachment.file_type, 'video')
            self.assertEqual(attachment.original_filename, 'test_video.mp4')
    
    def test_upload_document_file(self):
        """测试上传文档文件"""
        # 创建模拟PDF文档
        pdf_content = b'%PDF-1.4\n1 0 obj'  # PDF文件头
        
        uploaded_file = SimpleUploadedFile(
            name='test_document.pdf',
            content=pdf_content,
            content_type='application/pdf'
        )
        
        url = reverse('performance:file-upload')
        data = {
            'file': uploaded_file,
            'file_type': 'document',
            'performance_record_id': self.record.id
        }
        
        response = self.client.post(url, data, format='multipart')
        
        if response.status_code == status.HTTP_201_CREATED:
            attachment = PerformanceAttachment.objects.get(id=response.data['id'])
            self.assertEqual(attachment.file_type, 'document')
            self.assertEqual(attachment.original_filename, 'test_document.pdf')
    
    def test_upload_file_type_validation(self):
        """测试文件类型验证"""
        # 测试不支持的文件类型
        unsupported_file = SimpleUploadedFile(
            name='test.xyz',
            content=b'unknown file format',
            content_type='application/unknown'
        )
        
        url = reverse('performance:file-upload')
        data = {
            'file': unsupported_file,
            'file_type': 'unknown',  # 不支持的类型
            'performance_record_id': self.record.id
        }
        
        response = self.client.post(url, data, format='multipart')
        
        # 应该被拒绝
        self.assertNotEqual(response.status_code, status.HTTP_201_CREATED)
    
    def test_file_type_count_limits(self):
        """测试各文件类型数量限制"""
        url = reverse('performance:file-upload')
        
        # 测试图片文件数量限制（最多9个）
        for i in range(10):  # 尝试上传10个图片
            test_file = SimpleUploadedFile(
                name=f'test_image_{i}.jpg',
                content=b'fake image content',
                content_type='image/jpeg'
            )
            
            data = {
                'file': test_file,
                'file_type': 'image',
                'performance_record_id': self.record.id
            }
            
            response = self.client.post(url, data, format='multipart')
            
            if i < 9:  # 前9个应该成功
                if response.status_code != status.HTTP_201_CREATED:
                    # 如果上传失败，可能是由于验证机制
                    break
            else:  # 第10个应该被拒绝
                self.assertNotEqual(response.status_code, status.HTTP_201_CREATED)
    
    def test_mixed_file_type_uploads(self):
        """测试混合文件类型上传"""
        url = reverse('performance:file-upload')
        
        # 准备不同类型的文件
        test_files = [
            {
                'name': 'test_image.png',
                'content': b'\x89PNG\r\n\x1a\n',
                'content_type': 'image/png',
                'file_type': 'image'
            },
            {
                'name': 'test_audio.mp3',
                'content': b'ID3\x03\x00\x00\x00',
                'content_type': 'audio/mpeg',
                'file_type': 'audio'
            },
            {
                'name': 'test_video.avi',
                'content': b'RIFF\x00\x00\x00\x00AVI ',
                'content_type': 'video/avi',
                'file_type': 'video'
            },
            {
                'name': 'test_doc.docx',
                'content': b'PK\x03\x04',
                'content_type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'file_type': 'document'
            }
        ]
        
        upload_results = []
        
        for file_info in test_files:
            test_file = SimpleUploadedFile(
                name=file_info['name'],
                content=file_info['content'],
                content_type=file_info['content_type']
            )
            
            data = {
                'file': test_file,
                'file_type': file_info['file_type'],
                'performance_record_id': self.record.id
            }
            
            response = self.client.post(url, data, format='multipart')
            upload_results.append({
                'file_type': file_info['file_type'],
                'status_code': response.status_code,
                'success': response.status_code == status.HTTP_201_CREATED
            })
        
        # 验证至少有一些上传成功
        successful_uploads = [r for r in upload_results if r['success']]
        self.assertGreater(len(successful_uploads), 0, "至少应该有一些文件上传成功")
        
        # 验证不同文件类型都被正确处理
        uploaded_types = set(r['file_type'] for r in successful_uploads)
        self.assertGreater(len(uploaded_types), 1, "应该支持多种文件类型")


class PermissionTest(APITestCase):
    """权限控制测试"""
    
    def setUp(self):
        """测试准备"""
        # 创建代表用户
        self.rep_user = User.objects.create_user(
            username='rep_user',
            password='testpass123',
            role='representative'
        )
        self.representative = create_test_representative(self.rep_user)
        
        # 创建工作人员用户
        self.staff_user = User.objects.create_user(
            username='staff_user',
            password='testpass123',
            role='staff',
            is_staff=True
        )
        
        # 创建普通用户
        self.normal_user = User.objects.create_user(
            username='normal_user',
            password='testpass123'
        )
    
    def test_representative_access(self):
        """测试代表用户权限"""
        self.client.force_authenticate(user=self.rep_user)
        
        url = reverse('performance:record-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_staff_access_denied(self):
        """测试工作人员无法访问履职记录"""
        self.client.force_authenticate(user=self.staff_user)
        
        url = reverse('performance:record-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_normal_user_access_denied(self):
        """测试普通用户无法访问"""
        self.client.force_authenticate(user=self.normal_user)
        
        url = reverse('performance:record-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_choices_api_access(self):
        """测试选择项API权限"""
        self.client.force_authenticate(user=self.rep_user)
        
        # 代表用户可以访问
        url = reverse('performance:type-choices')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 工作人员无法访问
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_user_without_representative_profile(self):
        """测试没有代表资料的用户"""
        # 创建只有代表角色但没有代表资料的用户
        user_without_profile = User.objects.create_user(
            username='rep_no_profile',
            password='testpass123',
            role='representative'
        )
        
        self.client.force_authenticate(user=user_without_profile)
        
        url = reverse('performance:record-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class SecurityTest(APITestCase):
    """安全测试套件"""
    
    def setUp(self):
        """测试准备"""
        self.user = User.objects.create_user(
            username='test_rep',
            password='testpass123',
            role='representative'
        )
        self.representative = create_test_representative(self.user)
        self.record = PerformanceRecord.objects.create(
            representative=self.representative,
            performance_date=date.today(),
            performance_type='会议参与',
            performance_content='参加人大常委会会议'
        )
        
        self.client.force_authenticate(user=self.user)


@override_settings(MEDIA_ROOT=tempfile.mkdtemp())
class SQLInjectionTest(SecurityTest):
    """SQL注入防护测试"""
    
    def test_search_parameter_sql_injection(self):
        """测试搜索参数SQL注入防护"""
        # 尝试SQL注入攻击
        sql_injection_payloads = [
            "'; DROP TABLE performance_performancerecord; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM django_user; --",
            "'; UPDATE users SET password='hacked'; --",
            "' AND (SELECT COUNT(*) FROM django_user) > 0; --"
        ]
        
        url = reverse('performance:record-list-create')
        
        for payload in sql_injection_payloads:
            response = self.client.get(url, {'search': payload})
            
            # 检查服务器是否正常响应，没有被SQL注入攻击
            self.assertIn(response.status_code, [200, 400])
            
            # 确保没有数据库错误信息泄露
            if hasattr(response, 'data') and isinstance(response.data, dict):
                response_str = str(response.data).lower()
                self.assertNotIn('sql', response_str)
                self.assertNotIn('database', response_str)
                self.assertNotIn('syntax error', response_str)
    
    def test_filtering_parameter_sql_injection(self):
        """测试过滤参数SQL注入防护"""
        url = reverse('performance:record-list-create')
        
        # 尝试通过过滤参数进行SQL注入
        malicious_filters = {
            'performance_type': "'; DROP TABLE performance_performancerecord; --",
            'performance_date': "' OR '1'='1; --",
            'id': "1; DELETE FROM performance_performancerecord; --"
        }
        
        for param, payload in malicious_filters.items():
            response = self.client.get(url, {param: payload})
            
            # 确保请求被正确处理，没有执行恶意SQL
            self.assertIn(response.status_code, [200, 400])
    
    def test_record_id_sql_injection(self):
        """测试记录ID参数SQL注入防护"""
        # 尝试通过记录ID进行SQL注入
        # Django URL路由只接受数字，所以这些恶意ID会被URL路由拒绝
        malicious_ids = [
            "abc",  # 非数字字符
            "1'",   # 带引号
            "999999"  # 不存在的ID
        ]
        
        for malicious_id in malicious_ids:
            try:
                url = reverse('performance:record-detail', kwargs={'pk': malicious_id})
                response = self.client.get(url)
                # 应该返回404而不是数据库错误
                self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
            except:
                # URL反向解析失败也是预期的（说明路由保护有效）
                pass


class XSSPreventionTest(SecurityTest):
    """XSS攻击防护测试"""
    
    def test_performance_content_xss_prevention(self):
        """测试履职内容XSS防护"""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src='javascript:alert(\"XSS\")'></iframe>",
            "<%2Fscript%3E%3Cscript%3Ealert%28%27XSS%27%29%3C%2Fscript%3E"
        ]
        
        url = reverse('performance:record-list-create')
        
        for payload in xss_payloads:
            data = {
                'performance_date': date.today().isoformat(),
                'performance_type': '会议参与',
                'performance_content': payload,
                'description': f'测试XSS: {payload}'
            }
            
            response = self.client.post(url, data, format='json')
            
            if response.status_code == status.HTTP_201_CREATED:
                # 如果创建成功，检查返回的数据是否被正确转义
                created_record = PerformanceRecord.objects.get(id=response.data['id'])
                
                # 确保脚本标签被转义或移除
                self.assertNotIn('<script>', created_record.performance_content)
                self.assertNotIn('javascript:', created_record.performance_content)
                self.assertNotIn('onerror=', created_record.performance_content)
                self.assertNotIn('onload=', created_record.performance_content)
    
    def test_search_parameter_xss_prevention(self):
        """测试搜索参数XSS防护"""
        xss_payload = "<script>alert('XSS')</script>"
        
        url = reverse('performance:record-list-create')
        response = self.client.get(url, {'search': xss_payload})
        
        # 确保响应中不包含未转义的脚本
        response_content = str(response.content)
        self.assertNotIn('<script>alert(', response_content)
    
    def test_filename_xss_prevention(self):
        """测试文件名XSS防护"""
        # 创建包含XSS代码的文件名
        malicious_filename = "<script>alert('XSS')</script>.txt"
        
        test_file = SimpleUploadedFile(
            name=malicious_filename,
            content=b'test content',
            content_type='text/plain'
        )
        
        url = reverse('performance:file-upload')
        data = {
            'file': test_file,
            'file_type': 'document',
            'performance_record_id': self.record.id
        }
        
        response = self.client.post(url, data, format='multipart')
        
        if response.status_code == status.HTTP_201_CREATED:
            attachment = PerformanceAttachment.objects.get(id=response.data['id'])
            # 确保文件名被安全处理
            self.assertNotIn('<script>', attachment.original_filename)


@override_settings(MEDIA_ROOT=tempfile.mkdtemp())
class MaliciousFileUploadTest(SecurityTest):
    """恶意文件上传防护测试"""
    
    def test_executable_file_upload_prevention(self):
        """测试可执行文件上传防护"""
        # 尝试上传各种可执行文件
        malicious_files = [
            ('malware.exe', b'MZ\x90\x00', 'application/x-msdownload'),
            ('script.bat', b'@echo off\necho "malicious"', 'application/x-bat'),
            ('shell.sh', b'#!/bin/bash\necho "hack"', 'application/x-sh'),
            ('virus.com', b'malicious code', 'application/octet-stream'),
            ('trojan.scr', b'fake screensaver', 'application/octet-stream')
        ]
        
        url = reverse('performance:file-upload')
        
        for filename, content, content_type in malicious_files:
            test_file = SimpleUploadedFile(
                name=filename,
                content=content,
                content_type=content_type
            )
            
            data = {
                'file': test_file,
                'file_type': 'document',
                'performance_record_id': self.record.id
            }
            
            response = self.client.post(url, data, format='multipart')
            
            # 应该拒绝上传可执行文件
            self.assertNotEqual(response.status_code, status.HTTP_201_CREATED)
    
    def test_malicious_files_all_types(self):
        """测试各种文件类型的恶意文件上传防护"""
        url = reverse('performance:file-upload')
        
        # 为每种文件类型测试恶意文件
        test_cases = [
            # 伪装成图片的恶意文件
            {
                'filename': 'fake_image.jpg.exe',
                'content': b'MZ\x90\x00',  # PE可执行文件头
                'content_type': 'image/jpeg',
                'file_type': 'image'
            },
            # 伪装成音频的恶意文件
            {
                'filename': 'fake_audio.mp3.bat',
                'content': b'@echo malicious',
                'content_type': 'audio/mpeg',
                'file_type': 'audio'
            },
            # 伪装成视频的恶意文件
            {
                'filename': 'fake_video.mp4.sh',
                'content': b'#!/bin/bash\nrm -rf /',
                'content_type': 'video/mp4',
                'file_type': 'video'
            },
            # 伪装成文档的恶意文件
            {
                'filename': 'fake_doc.pdf.exe',
                'content': b'MZ\x90\x00',
                'content_type': 'application/pdf',
                'file_type': 'document'
            }
        ]
        
        for test_case in test_cases:
            test_file = SimpleUploadedFile(
                name=test_case['filename'],
                content=test_case['content'],
                content_type=test_case['content_type']
            )
            
            data = {
                'file': test_file,
                'file_type': test_case['file_type'],
                'performance_record_id': self.record.id
            }
            
            response = self.client.post(url, data, format='multipart')
            
            # 应该拒绝这些恶意文件
            self.assertNotEqual(
                response.status_code, 
                status.HTTP_201_CREATED,
                f"恶意文件 {test_case['filename']} 不应该被上传成功"
            )
    
    def test_file_size_limit_enforcement(self):
        """测试文件大小限制强制执行"""
        # 创建超大文件
        large_content = b'x' * (100 * 1024 * 1024)  # 100MB
        
        large_file = SimpleUploadedFile(
            name='large_file.txt',
            content=large_content,
            content_type='text/plain'
        )
        
        url = reverse('performance:file-upload')
        data = {
            'file': large_file,
            'file_type': 'document',
            'performance_record_id': self.record.id
        }
        
        response = self.client.post(url, data, format='multipart')
        
        # 应该拒绝过大的文件
        self.assertNotEqual(response.status_code, status.HTTP_201_CREATED)
    
    def test_fake_image_file_detection(self):
        """测试伪造图片文件检测"""
        # 创建伪造的图片文件（实际是可执行文件）
        fake_image = SimpleUploadedFile(
            name='fake_image.jpg',
            content=b'MZ\x90\x00\x03\x00\x00\x00',  # PE文件头
            content_type='image/jpeg'
        )
        
        url = reverse('performance:file-upload')
        data = {
            'file': fake_image,
            'file_type': 'image',
            'performance_record_id': self.record.id
        }
        
        response = self.client.post(url, data, format='multipart')
        
        # 应该检测到文件类型不匹配并拒绝
        self.assertNotEqual(response.status_code, status.HTTP_201_CREATED)
    
    def test_file_with_double_extension(self):
        """测试双扩展名文件防护"""
        double_ext_file = SimpleUploadedFile(
            name='document.pdf.exe',
            content=b'fake pdf content',
            content_type='application/pdf'
        )
        
        url = reverse('performance:file-upload')
        data = {
            'file': double_ext_file,
            'file_type': 'document',
            'performance_record_id': self.record.id
        }
        
        response = self.client.post(url, data, format='multipart')
        
        # 应该检测到可疑的双扩展名并拒绝
        self.assertNotEqual(response.status_code, status.HTTP_201_CREATED)


class PathTraversalTest(SecurityTest):
    """路径遍历攻击防护测试"""
    
    def test_filename_path_traversal_prevention(self):
        """测试文件名路径遍历防护"""
        # 尝试使用路径遍历的文件名
        malicious_filenames = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\config\\sam',
            '....//....//....//etc//passwd',
            '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
            '..%252f..%252f..%252fetc%252fpasswd'
        ]
        
        url = reverse('performance:file-upload')
        
        for malicious_filename in malicious_filenames:
            test_file = SimpleUploadedFile(
                name=malicious_filename,
                content=b'malicious content',
                content_type='text/plain'
            )
            
            data = {
                'file': test_file,
                'file_type': 'document',
                'performance_record_id': self.record.id
            }
            
            response = self.client.post(url, data, format='multipart')
            
            if response.status_code == status.HTTP_201_CREATED:
                # 如果上传成功，检查实际保存的文件名是否安全
                attachment = PerformanceAttachment.objects.get(id=response.data['id'])
                saved_filename = attachment.original_filename
                
                # 确保文件名不包含路径遍历字符
                self.assertNotIn('..', saved_filename)
                self.assertNotIn('/', saved_filename)
                self.assertNotIn('\\', saved_filename)


class PrivilegeEscalationTest(SecurityTest):
    """权限提升防护测试"""
    
    def test_role_tampering_prevention(self):
        """测试角色篡改防护"""
        url = reverse('performance:record-list-create')
        
        # 尝试在请求中添加角色相关参数
        malicious_data = {
            'performance_date': date.today().isoformat(),
            'performance_type': '会议参与',
            'performance_content': '测试内容',
            'user_role': 'staff',  # 尝试篡改角色
            'is_staff': True,      # 尝试提升权限
            'is_superuser': True   # 尝试获取超级用户权限
        }
        
        response = self.client.post(url, malicious_data, format='json')
        
        # 确保用户权限没有被提升
        self.user.refresh_from_db()
        self.assertEqual(self.user.role, 'representative')
        self.assertFalse(self.user.is_staff)
        self.assertFalse(self.user.is_superuser)
    
    def test_other_user_data_modification_prevention(self):
        """测试防止修改其他用户数据"""
        # 创建其他用户和记录
        other_user = User.objects.create_user(
            username='other_rep',
            password='testpass123',
            role='representative'
        )
        other_representative = create_test_representative(other_user, "其他代表")
        other_record = PerformanceRecord.objects.create(
            representative=other_representative,
            performance_date=date.today(),
            performance_type='会议参与',
            performance_content='其他用户的记录'
        )
        
        # 尝试修改其他用户的记录
        url = reverse('performance:record-detail', kwargs={'pk': other_record.id})
        data = {
            'performance_content': '被篡改的内容'
        }
        
        response = self.client.patch(url, data, format='json')
        
        # 应该拒绝访问（403或404都是安全的）
        self.assertIn(response.status_code, [status.HTTP_403_FORBIDDEN, status.HTTP_404_NOT_FOUND])
        
        # 确保数据没有被修改
        other_record.refresh_from_db()
        self.assertEqual(other_record.performance_content, '其他用户的记录')
    
    def test_representative_parameter_injection(self):
        """测试代表参数注入防护"""
        # 尝试在创建记录时指定其他代表
        other_user = User.objects.create_user(
            username='other_rep2',
            password='testpass123',
            role='representative'
        )
        other_representative = create_test_representative(other_user, "目标代表")
        
        url = reverse('performance:record-list-create')
        malicious_data = {
            'performance_date': date.today().isoformat(),
            'performance_type': '会议参与',
            'performance_content': '恶意记录',
            'representative': other_representative.id,  # 尝试为其他代表创建记录
            'representative_id': other_representative.id
        }
        
        response = self.client.post(url, malicious_data, format='json')
        
        if response.status_code == status.HTTP_201_CREATED:
            # 如果创建成功，确保记录属于当前用户
            created_record = PerformanceRecord.objects.get(id=response.data['id'])
            self.assertEqual(created_record.representative, self.representative)
            self.assertNotEqual(created_record.representative, other_representative)


class DataLeakageTest(SecurityTest):
    """敏感数据泄露防护测试"""
    
    def test_error_message_information_disclosure(self):
        """测试错误消息信息泄露防护"""
        # 尝试访问不存在的记录
        url = reverse('performance:record-detail', kwargs={'pk': 99999})
        response = self.client.get(url)
        
        # 确保错误消息不泄露敏感信息
        if hasattr(response, 'data') and isinstance(response.data, dict):
            error_message = str(response.data).lower()
            
            # 不应该泄露数据库结构信息
            self.assertNotIn('sql', error_message)
            self.assertNotIn('database', error_message)
            self.assertNotIn('table', error_message)
            self.assertNotIn('column', error_message)
            
            # 不应该泄露系统路径信息
            self.assertNotIn('c:', error_message)
            self.assertNotIn('/home/', error_message)
            self.assertNotIn('traceback', error_message)
    
    def test_debug_information_not_exposed(self):
        """测试调试信息不暴露"""
        # 发送格式错误的请求
        url = reverse('performance:record-list-create')
        
        response = self.client.post(url, {'invalid': 'data'}, format='json')
        
        # 确保响应不包含调试信息
        response_content = str(response.content).lower()
        
        self.assertNotIn('traceback', response_content)
        self.assertNotIn('django', response_content)
        self.assertNotIn('stack trace', response_content)
        self.assertNotIn('python', response_content)
    
    def test_pagination_information_security(self):
        """测试分页信息安全性"""
        # 创建多个记录用于分页测试
        for i in range(15):
            PerformanceRecord.objects.create(
                representative=self.representative,
                performance_date=date.today(),
                performance_type='会议参与',
                performance_content=f'测试记录 {i}'
            )
        
        url = reverse('performance:record-list-create')
        response = self.client.get(url, {'page': 1})
        
        if response.status_code == status.HTTP_200_OK:
            # 确保分页信息不泄露总数据量等敏感信息给普通用户
            self.assertIn('results', response.data)
            
            # 检查是否有过多的元数据泄露
            if 'count' in response.data:
                # 总数不应该暴露给代表用户（只能看自己的数据）
                self.assertLessEqual(response.data['count'], 16)  # 包括setUp中创建的1个


class FileIntegrityTest(SecurityTest):
    """文件完整性测试"""
    
    def test_file_corruption_detection(self):
        """测试文件损坏检测"""
        # 创建一个正常的文件
        test_content = b'This is a test file content for integrity check.'
        test_file = SimpleUploadedFile(
            name='integrity_test.txt',
            content=test_content,
            content_type='text/plain'
        )
        
        url = reverse('performance:file-upload')
        data = {
            'file': test_file,
            'file_type': 'document',
            'performance_record_id': self.record.id
        }
        
        response = self.client.post(url, data, format='multipart')
        
        if response.status_code == status.HTTP_201_CREATED:
            attachment = PerformanceAttachment.objects.get(id=response.data['id'])
            
            # 验证文件大小正确记录
            self.assertEqual(attachment.file_size, len(test_content))
            
            # 验证文件类型正确识别
            self.assertEqual(attachment.file_type, 'document')
    
    def test_concurrent_file_upload_safety(self):
        """测试并发文件上传安全性"""
        # 模拟并发上传相同文件名的文件
        test_files = []
        for i in range(3):
            test_file = SimpleUploadedFile(
                name='concurrent_test.txt',
                content=f'Content {i}'.encode(),
                content_type='text/plain'
            )
            test_files.append(test_file)
        
        url = reverse('performance:file-upload')
        responses = []
        
        for test_file in test_files:
            data = {
                'file': test_file,
                'file_type': 'document',
                'performance_record_id': self.record.id
            }
            
            response = self.client.post(url, data, format='multipart')
            responses.append(response)
        
        # 验证所有上传都被正确处理，没有冲突
        successful_uploads = [r for r in responses if r.status_code == status.HTTP_201_CREATED]
        
        # 应该有成功的上传
        self.assertGreater(len(successful_uploads), 0)
        
        # 验证文件名重复处理机制
        attachment_filenames = []
        for response in successful_uploads:
            attachment = PerformanceAttachment.objects.get(id=response.data['id'])
            attachment_filenames.append(attachment.stored_filename)
        
        # 确保没有文件被覆盖（文件路径应该不同）
        self.assertEqual(len(attachment_filenames), len(set(attachment_filenames)))


class BoundaryValueSecurityTest(SecurityTest):
    """边界值安全测试"""
    
    def test_extremely_long_content_handling(self):
        """测试极长内容处理"""
        # 创建超长内容
        extremely_long_content = 'A' * 100000  # 100KB文本
        
        url = reverse('performance:record-list-create')
        data = {
            'performance_date': date.today().isoformat(),
            'performance_type': '会议参与',
            'performance_content': extremely_long_content
        }
        
        response = self.client.post(url, data, format='json')
        
        # 应该有适当的长度限制
        if response.status_code != status.HTTP_201_CREATED:
            # 如果被拒绝，确保是因为长度限制而不是服务器错误
            self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def test_unicode_and_special_characters_handling(self):
        """测试Unicode和特殊字符处理"""
        special_contents = [
            '测试中文内容 🎉',
            '特殊字符: ©®™€£¥',
            'Русский текст',
            'العربية',
            '日本語テスト',
            '\x00\x01\x02',  # 控制字符
            '🚀🌟💻📊',  # Emoji
        ]
        
        url = reverse('performance:record-list-create')
        
        for content in special_contents:
            data = {
                'performance_date': date.today().isoformat(),
                'performance_type': '会议参与',
                'performance_content': content
            }
            
            response = self.client.post(url, data, format='json')
            
            # 应该正确处理或优雅拒绝
            self.assertIn(response.status_code, [
                status.HTTP_201_CREATED,
                status.HTTP_400_BAD_REQUEST
            ])
            
            # 如果成功创建，验证数据完整性
            if response.status_code == status.HTTP_201_CREATED:
                created_record = PerformanceRecord.objects.get(id=response.data['id'])
                
                # 验证特殊字符没有被意外修改（除了安全过滤）
                if not any(ord(c) < 32 for c in content):  # 忽略控制字符测试
                    self.assertIn(content.strip(), created_record.performance_content)
    
    def test_negative_and_zero_values_handling(self):
        """测试负数和零值处理"""
        url = reverse('performance:record-list-create')
        
        # 测试各种边界值
        boundary_values = {
            'page': [-1, 0, 999999],
            'page_size': [-1, 0, 999999]
        }
        
        for param, values in boundary_values.items():
            for value in values:
                response = self.client.get(url, {param: value})
                
                # 应该优雅处理边界值，不应该导致服务器错误
                self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)


class CSRFAndCORSTest(SecurityTest):
    """CSRF和CORS安全测试"""
    
    def test_csrf_protection_enabled(self):
        """测试CSRF保护是否启用"""
        # 对于API，通常使用Token认证而不是CSRF
        # 但我们测试确保没有意外的CSRF漏洞
        
        # 创建未认证的客户端
        unauth_client = APIClient()
        
        url = reverse('performance:record-list-create')
        data = {
            'performance_date': date.today().isoformat(),
            'performance_type': '会议参与',
            'performance_content': '未授权请求'
        }
        
        response = unauth_client.post(url, data, format='json')
        
        # 应该因为未认证而被拒绝
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_authentication_header_validation(self):
        """测试认证头验证"""
        # 创建带有无效token的请求
        client_with_invalid_token = APIClient()
        client_with_invalid_token.credentials(HTTP_AUTHORIZATION='Token invalid_token_12345')
        
        url = reverse('performance:record-list-create')
        response = client_with_invalid_token.get(url)
        
        # 应该因为无效token被拒绝
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class RateLimitingTest(SecurityTest):
    """速率限制测试"""
    
    def test_api_rate_limiting_simulation(self):
        """模拟API速率限制测试"""
        url = reverse('performance:record-list-create')
        
        # 发送多个快速请求
        responses = []
        for i in range(50):  # 发送50个请求
            response = self.client.get(url)
            responses.append(response.status_code)
        
        # 至少大部分请求应该成功（如果没有速率限制）
        # 如果有速率限制，可能会有429状态码
        successful_requests = responses.count(200)
        rate_limited_requests = responses.count(429)
        
        # 验证服务器能够处理大量请求而不崩溃
        server_errors = responses.count(500)
        self.assertEqual(server_errors, 0, "服务器不应该因为大量请求而出现500错误")
    
    def test_file_upload_rate_limiting(self):
        """测试文件上传速率限制"""
        url = reverse('performance:file-upload')
        
        # 尝试快速上传多个文件
        upload_results = []
        for i in range(10):
            test_file = SimpleUploadedFile(
                name=f'rate_test_{i}.txt',
                content=f'Content {i}'.encode(),
                content_type='text/plain'
            )
            
            data = {
                'file': test_file,
                'file_type': 'document',
                'performance_record_id': self.record.id
            }
            
            response = self.client.post(url, data, format='multipart')
            upload_results.append(response.status_code)
        
        # 验证没有服务器错误
        server_errors = upload_results.count(500)
        self.assertEqual(server_errors, 0, "文件上传不应该导致服务器错误")
