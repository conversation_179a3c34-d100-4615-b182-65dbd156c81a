"""
用户管理数据模型

包含以下模型：
1. User - 系统用户基础模型（继承AbstractBaseUser）
2. Representative - 人大代表信息模型
3. StaffMember - 站点工作人员信息模型
"""

from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin, BaseUserManager
from django.db import models
from django.core.validators import RegexValidator
from django.utils import timezone


class UserManager(BaseUserManager):
    """自定义用户管理器"""
    
    def create_user(self, username, password=None, **extra_fields):
        """创建普通用户"""
        if not username:
            raise ValueError('用户名不能为空')
        
        user = self.model(
            username=username,
            **extra_fields
        )
        user.set_password(password)
        user.save(using=self._db)
        return user
    
    def create_superuser(self, username, password=None, **extra_fields):
        """创建超级用户"""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)
        
        if extra_fields.get('is_staff') is not True:
            raise ValueError('超级用户必须设置is_staff=True')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('超级用户必须设置is_superuser=True')
        
        return self.create_user(username, password, **extra_fields)


class User(AbstractBaseUser, PermissionsMixin):
    """
    用户基础模型
    继承Django的AbstractBaseUser和PermissionsMixin
    """
    
    # 用户角色选择
    ROLE_CHOICES = [
        ('representative', '人大代表'),
        ('staff', '站点工作人员'),
    ]
    
    # 主键ID（自动生成）
    id = models.BigAutoField(
        primary_key=True,
        verbose_name='用户ID'
    )
    
    # 用户名（登录凭据）
    username = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='用户名',
        help_text='用于登录的唯一标识符，最多50个字符'
    )
    
    # 用户角色
    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        verbose_name='用户角色',
        help_text='用户在系统中的角色类型'
    )
    
    # 账号状态
    is_active = models.BooleanField(
        default=True,
        verbose_name='账号激活状态',
        help_text='指定用户是否应被视为活跃。取消选择此项而不是删除账户。'
    )
    
    # Django权限系统需要的字段
    is_staff = models.BooleanField(
        default=False,
        verbose_name='管理员状态',
        help_text='指定用户是否可以登录管理站点。'
    )
    
    # 最后登录时间
    last_login_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='最后登录时间'
    )
    
    # 时间戳字段
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    # 指定用户管理器
    objects = UserManager()
    
    # 指定用于登录的字段
    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = ['role']
    
    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        indexes = [
            models.Index(fields=['username']),
            models.Index(fields=['role', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"
    
    def save(self, *args, **kwargs):
        """保存时更新最后登录时间"""
        if self.last_login and self.last_login != self.last_login_at:
            self.last_login_at = self.last_login
        super().save(*args, **kwargs)


class Representative(models.Model):
    """
    人大代表信息模型
    对应数据库设计中的representatives表
    """
    
    # 性别选择
    GENDER_CHOICES = [
        ('male', '男'),
        ('female', '女'),
    ]

    # 所属片区选择
    DISTRICT_CHOICES = [
        ('那洪片区', '那洪片区'),
        ('那历片区', '那历片区'),
        ('沛鸿片区', '沛鸿片区'),
    ]

    # 代表层级选择
    LEVEL_CHOICES = [
        ('乡镇人大代表', '乡镇人大代表'),
        ('县区人大代表', '县区人大代表'),
        ('市人大代表', '市人大代表'),
        ('自治区人大代表', '自治区人大代表'),
        ('全国人大代表', '全国人大代表'),
    ]

    # 代表构成选择
    COMPOSITION_CHOICES = [
        # 基层代表5种
        ('一线工人', '一线工人'),
        ('农民', '农民'),
        ('村委会村党支部组成人员', '村委会村党支部组成人员'),
        ('专业技术人员', '专业技术人员'),
        ('其他基层代表', '其他基层代表'),
        # 其他6种
        ('公务员', '公务员'),
        ('国有和集体企业负责人', '国有和集体企业负责人'),
        ('非共有制经济人士', '非共有制经济人士'),
        ('事业单位负责人', '事业单位负责人'),
        ('解放军和武警部队', '解放军和武警部队'),
        ('其他', '其他'),
    ]
    
    # 主键ID
    id = models.BigAutoField(
        primary_key=True,
        verbose_name='代表ID'
    )
    
    # 关联用户表（一对一关系）
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        verbose_name='关联用户',
        help_text='与用户表的一对一关联'
    )
    
    # 代表层级（支持多层级，用逗号分隔）
    level = models.CharField(
        max_length=200,  # 增加长度以支持多个层级
        verbose_name='代表层级',
        help_text='代表的层级：乡镇、县区、市、自治区、全国（多个层级用逗号分隔）'
    )

    # 代表构成（支持多构成，用逗号分隔）
    composition = models.CharField(
        max_length=300,  # 足够长度以支持多个构成标签
        null=True,
        blank=True,
        verbose_name='代表构成',
        help_text='代表的构成类型：一线工人、农民、专业技术人员等（多个构成用逗号分隔）'
    )
    
    # 姓名
    name = models.CharField(
        max_length=50,
        verbose_name='姓名'
    )
    
    # 性别
    gender = models.CharField(
        max_length=10,
        choices=GENDER_CHOICES,
        verbose_name='性别'
    )
    
    # 民族（可选）
    nationality = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name='民族'
    )

    # 所属片区（可选）
    district = models.CharField(
        max_length=20,
        choices=DISTRICT_CHOICES,
        null=True,
        blank=True,
        verbose_name='所属片区',
        help_text='代表所属的片区，可选字段'
    )

    # 出生日期
    birth_date = models.DateField(
        verbose_name='出生日期'
    )
    
    # 籍贯
    birthplace = models.CharField(
        max_length=100,
        verbose_name='籍贯'
    )
    
    # 党派
    party = models.CharField(
        max_length=50,
        verbose_name='党派'
    )
    
    # 现任职务
    current_position = models.CharField(
        max_length=100,
        verbose_name='现任职务'
    )
    
    # 移动电话号码
    mobile_phone = models.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r'^1[3-9]\d{9}$',
                message='请输入有效的手机号码格式'
            )
        ],
        verbose_name='移动电话号码'
    )
    
    # 学历（可选）
    education = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name='学历'
    )
    
    # 毕业院校（可选）
    graduated_school = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name='毕业院校'
    )
    
    # 所学专业（可选）
    major = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name='所学专业'
    )

    # 头像（base64格式存储）
    avatar = models.TextField(
        null=True,
        blank=True,
        verbose_name='头像',
        help_text='base64格式的头像数据'
    )

    # 时间戳字段
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    class Meta:
        db_table = 'representatives'
        verbose_name = '人大代表'
        verbose_name_plural = '人大代表'
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['level']),
            models.Index(fields=['composition']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.level})"
    
    @property
    def age(self):
        """计算年龄"""
        if self.birth_date:
            today = timezone.now().date()
            return today.year - self.birth_date.year - (
                (today.month, today.day) < (self.birth_date.month, self.birth_date.day)
            )
        return None

    def get_level_list(self):
        """获取层级列表"""
        if not self.level:
            return []
        return [level.strip() for level in self.level.split(',') if level.strip()]

    def set_level_list(self, level_list):
        """设置层级列表"""
        if isinstance(level_list, list):
            self.level = ','.join(level_list)
        else:
            self.level = level_list

    @classmethod
    def validate_levels(cls, level_string):
        """验证层级字符串是否有效"""
        if not level_string:
            return False, "层级不能为空"

        valid_levels = [choice[0] for choice in cls.LEVEL_CHOICES]
        levels = [level.strip() for level in level_string.split(',') if level.strip()]

        if not levels:
            return False, "层级不能为空"

        for level in levels:
            if level not in valid_levels:
                return False, f"无效的层级: {level}"

        # 检查是否有重复层级
        if len(levels) != len(set(levels)):
            return False, "不能选择重复的层级"

        return True, "验证通过"

    def get_composition_list(self):
        """获取构成列表"""
        if not self.composition:
            return []
        return [comp.strip() for comp in self.composition.split(',') if comp.strip()]

    def set_composition_list(self, composition_list):
        """设置构成列表"""
        if isinstance(composition_list, list):
            self.composition = ','.join(composition_list)
        else:
            self.composition = composition_list

    @classmethod
    def validate_compositions(cls, composition_string):
        """验证构成字符串是否有效"""
        if not composition_string:
            return True, "构成可以为空"  # 构成字段可以为空

        valid_compositions = [choice[0] for choice in cls.COMPOSITION_CHOICES]
        compositions = [comp.strip() for comp in composition_string.split(',') if comp.strip()]

        for composition in compositions:
            if composition not in valid_compositions:
                return False, f"无效的构成: {composition}"

        # 检查是否有重复构成
        if len(compositions) != len(set(compositions)):
            return False, "不能选择重复的构成"

        return True, "验证通过"


class StaffMember(models.Model):
    """
    站点工作人员信息模型
    对应数据库设计中的staff_members表
    """
    
    # 主键ID
    id = models.BigAutoField(
        primary_key=True,
        verbose_name='工作人员ID'
    )
    
    # 关联用户表（一对一关系）
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        verbose_name='关联用户',
        help_text='与用户表的一对一关联'
    )
    
    # 姓名
    name = models.CharField(
        max_length=50,
        verbose_name='姓名'
    )
    
    # 职位
    position = models.CharField(
        max_length=100,
        verbose_name='职位'
    )
    
    # 移动电话号码
    mobile_phone = models.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r'^1[3-9]\d{9}$',
                message='请输入有效的手机号码格式'
            )
        ],
        verbose_name='移动电话号码'
    )
    
    # 邮箱（可选）
    email = models.EmailField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name='邮箱地址'
    )
    
    # 所属站点名称
    station_name = models.CharField(
        max_length=100,
        verbose_name='所属站点名称'
    )
    
    # 时间戳字段
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    
    class Meta:
        db_table = 'staff_members'
        verbose_name = '站点工作人员'
        verbose_name_plural = '站点工作人员'
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['station_name']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.station_name})" 