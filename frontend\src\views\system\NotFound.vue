<template>
  <div class="not-found-container">
    <el-result
      icon="warning"
      title="404"
      sub-title="抱歉，您访问的页面不存在"
    >
      <!-- <template #extra>
        <el-button type="primary" @click="goHome">
          返回首页
        </el-button>
        <el-button @click="goBack">
          返回上页
        </el-button>
      </template> -->
    </el-result>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

/**
 * 404页面组件
 * 当用户访问不存在的页面时显示
 */

const router = useRouter()
const userStore = useUserStore()

/**
 * 返回首页
 */
const goHome = () => {
  if (userStore.isLoggedIn) {
    // 根据用户角色重定向到相应首页
    const homePath = userStore.isRepresentative ? '/representative' : '/staff'
    router.push(homePath)
  } else {
    router.push('/login')
  }
}

/**
 * 返回上一页
 */
const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}
</style> 